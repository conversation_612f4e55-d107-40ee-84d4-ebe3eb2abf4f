CREATE TABLE IF NOT EXISTS tags (tag TEXT NOT NULL, path TEXT NOT NULL, UNIQUE(tag, path) ON CONFLICT REPLACE);
     CREATE TABLE IF NOT EXISTS revalidations (tag TEXT NOT NULL, revalidatedAt INTEGER NOT NULL, UNIQUE(tag) ON CONFLICT REPLACE);
INSERT INTO tags (tag, path) VALUES ("Mz8aZrvyX7kD7MiUo0vt-/_N_T_/layout", "Mz8aZrvyX7kD7MiUo0vt-/_not-found"), ("Mz8aZrvyX7kD7MiUo0vt-/_N_T_/_not-found/layout", "Mz8aZrvyX7kD7MiUo0vt-/_not-found"), ("Mz8aZrvyX7kD7MiUo0vt-/_N_T_/_not-found/page", "Mz8aZrvyX7kD7MiUo0vt-/_not-found"), ("Mz8aZrvyX7kD7MiUo0vt-/_N_T_/_not-found", "Mz8aZrvyX7kD7MiUo0vt-/_not-found");