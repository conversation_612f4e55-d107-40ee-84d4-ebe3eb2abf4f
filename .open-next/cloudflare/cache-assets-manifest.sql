CREATE TABLE IF NOT EXISTS tags (tag TEXT NOT NULL, path TEXT NOT NULL, UNIQUE(tag, path) ON CONFLICT REPLACE);
     CREATE TABLE IF NOT EXISTS revalidations (tag TEXT NOT NULL, revalidatedAt INTEGER NOT NULL, UNIQUE(tag) ON CONFLICT REPLACE);
INSERT INTO tags (tag, path) VALUES ("Dp_EjZ4J5q76S6vVMjk6b/_N_T_/layout", "Dp_EjZ4J5q76S6vVMjk6b/_not-found"), ("Dp_EjZ4J5q76S6vVMjk6b/_N_T_/_not-found/layout", "Dp_EjZ4J5q76S6vVMjk6b/_not-found"), ("Dp_EjZ4J5q76S6vVMjk6b/_N_T_/_not-found/page", "Dp_EjZ4J5q76S6vVMjk6b/_not-found"), ("Dp_EjZ4J5q76S6vVMjk6b/_N_T_/_not-found", "Dp_EjZ4J5q76S6vVMjk6b/_not-found");