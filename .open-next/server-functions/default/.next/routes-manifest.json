{"version": 3, "pages404": true, "caseSensitive": false, "basePath": "", "redirects": [{"source": "/:path+/", "destination": "/:path+", "internal": true, "statusCode": 308, "regex": "^(?:/((?:[^/]+?)(?:/(?:[^/]+?))*))/$"}], "headers": [], "dynamicRoutes": [{"page": "/api/auth/[...next<PERSON>h]", "regex": "^/api/auth/(.+?)(?:/)?$", "routeKeys": {"nxtPnextauth": "nxtPnextauth"}, "namedRegex": "^/api/auth/(?<nxtPnextauth>.+?)(?:/)?$"}, {"page": "/[locale]", "regex": "^/([^/]+?)(?:/)?$", "routeKeys": {"nxtPlocale": "nxtPlocale"}, "namedRegex": "^/(?<nxtPlocale>[^/]+?)(?:/)?$"}, {"page": "/[locale]/admin", "regex": "^/([^/]+?)/admin(?:/)?$", "routeKeys": {"nxtPlocale": "nxtPlocale"}, "namedRegex": "^/(?<nxtPlocale>[^/]+?)/admin(?:/)?$"}, {"page": "/[locale]/admin/feedbacks", "regex": "^/([^/]+?)/admin/feedbacks(?:/)?$", "routeKeys": {"nxtPlocale": "nxtPlocale"}, "namedRegex": "^/(?<nxtPlocale>[^/]+?)/admin/feedbacks(?:/)?$"}, {"page": "/[locale]/admin/orders", "regex": "^/([^/]+?)/admin/orders(?:/)?$", "routeKeys": {"nxtPlocale": "nxtPlocale"}, "namedRegex": "^/(?<nxtPlocale>[^/]+?)/admin/orders(?:/)?$"}, {"page": "/[locale]/admin/posts", "regex": "^/([^/]+?)/admin/posts(?:/)?$", "routeKeys": {"nxtPlocale": "nxtPlocale"}, "namedRegex": "^/(?<nxtPlocale>[^/]+?)/admin/posts(?:/)?$"}, {"page": "/[locale]/admin/posts/add", "regex": "^/([^/]+?)/admin/posts/add(?:/)?$", "routeKeys": {"nxtPlocale": "nxtPlocale"}, "namedRegex": "^/(?<nxtPlocale>[^/]+?)/admin/posts/add(?:/)?$"}, {"page": "/[locale]/admin/posts/[uuid]/edit", "regex": "^/([^/]+?)/admin/posts/([^/]+?)/edit(?:/)?$", "routeKeys": {"nxtPlocale": "nxtPlocale", "nxtPuuid": "nxtPuuid"}, "namedRegex": "^/(?<nxtPlocale>[^/]+?)/admin/posts/(?<nxtPuuid>[^/]+?)/edit(?:/)?$"}, {"page": "/[locale]/admin/translations", "regex": "^/([^/]+?)/admin/translations(?:/)?$", "routeKeys": {"nxtPlocale": "nxtPlocale"}, "namedRegex": "^/(?<nxtPlocale>[^/]+?)/admin/translations(?:/)?$"}, {"page": "/[locale]/admin/users", "regex": "^/([^/]+?)/admin/users(?:/)?$", "routeKeys": {"nxtPlocale": "nxtPlocale"}, "namedRegex": "^/(?<nxtPlocale>[^/]+?)/admin/users(?:/)?$"}, {"page": "/[locale]/ai-dashboard", "regex": "^/([^/]+?)/ai\\-dashboard(?:/)?$", "routeKeys": {"nxtPlocale": "nxtPlocale"}, "namedRegex": "^/(?<nxtPlocale>[^/]+?)/ai\\-dashboard(?:/)?$"}, {"page": "/[locale]/api-keys", "regex": "^/([^/]+?)/api\\-keys(?:/)?$", "routeKeys": {"nxtPlocale": "nxtPlocale"}, "namedRegex": "^/(?<nxtPlocale>[^/]+?)/api\\-keys(?:/)?$"}, {"page": "/[locale]/api-keys/create", "regex": "^/([^/]+?)/api\\-keys/create(?:/)?$", "routeKeys": {"nxtPlocale": "nxtPlocale"}, "namedRegex": "^/(?<nxtPlocale>[^/]+?)/api\\-keys/create(?:/)?$"}, {"page": "/[locale]/auth/signin", "regex": "^/([^/]+?)/auth/signin(?:/)?$", "routeKeys": {"nxtPlocale": "nxtPlocale"}, "namedRegex": "^/(?<nxtPlocale>[^/]+?)/auth/signin(?:/)?$"}, {"page": "/[locale]/components-demo", "regex": "^/([^/]+?)/components\\-demo(?:/)?$", "routeKeys": {"nxtPlocale": "nxtPlocale"}, "namedRegex": "^/(?<nxtPlocale>[^/]+?)/components\\-demo(?:/)?$"}, {"page": "/[locale]/components-demo/blog-detail", "regex": "^/([^/]+?)/components\\-demo/blog\\-detail(?:/)?$", "routeKeys": {"nxtPlocale": "nxtPlocale"}, "namedRegex": "^/(?<nxtPlocale>[^/]+?)/components\\-demo/blog\\-detail(?:/)?$"}, {"page": "/[locale]/components-demo/content", "regex": "^/([^/]+?)/components\\-demo/content(?:/)?$", "routeKeys": {"nxtPlocale": "nxtPlocale"}, "namedRegex": "^/(?<nxtPlocale>[^/]+?)/components\\-demo/content(?:/)?$"}, {"page": "/[locale]/components-demo/data", "regex": "^/([^/]+?)/components\\-demo/data(?:/)?$", "routeKeys": {"nxtPlocale": "nxtPlocale"}, "namedRegex": "^/(?<nxtPlocale>[^/]+?)/components\\-demo/data(?:/)?$"}, {"page": "/[locale]/components-demo/editors", "regex": "^/([^/]+?)/components\\-demo/editors(?:/)?$", "routeKeys": {"nxtPlocale": "nxtPlocale"}, "namedRegex": "^/(?<nxtPlocale>[^/]+?)/components\\-demo/editors(?:/)?$"}, {"page": "/[locale]/components-demo/interactive", "regex": "^/([^/]+?)/components\\-demo/interactive(?:/)?$", "routeKeys": {"nxtPlocale": "nxtPlocale"}, "namedRegex": "^/(?<nxtPlocale>[^/]+?)/components\\-demo/interactive(?:/)?$"}, {"page": "/[locale]/components-demo/layout", "regex": "^/([^/]+?)/components\\-demo/layout(?:/)?$", "routeKeys": {"nxtPlocale": "nxtPlocale"}, "namedRegex": "^/(?<nxtPlocale>[^/]+?)/components\\-demo/layout(?:/)?$"}, {"page": "/[locale]/components-demo/tools", "regex": "^/([^/]+?)/components\\-demo/tools(?:/)?$", "routeKeys": {"nxtPlocale": "nxtPlocale"}, "namedRegex": "^/(?<nxtPlocale>[^/]+?)/components\\-demo/tools(?:/)?$"}, {"page": "/[locale]/i/[code]", "regex": "^/([^/]+?)/i/([^/]+?)(?:/)?$", "routeKeys": {"nxtPlocale": "nxtPlocale", "nxtPcode": "nxtPcode"}, "namedRegex": "^/(?<nxtPlocale>[^/]+?)/i/(?<nxtPcode>[^/]+?)(?:/)?$"}, {"page": "/[locale]/my-credits", "regex": "^/([^/]+?)/my\\-credits(?:/)?$", "routeKeys": {"nxtPlocale": "nxtPlocale"}, "namedRegex": "^/(?<nxtPlocale>[^/]+?)/my\\-credits(?:/)?$"}, {"page": "/[locale]/my-invites", "regex": "^/([^/]+?)/my\\-invites(?:/)?$", "routeKeys": {"nxtPlocale": "nxtPlocale"}, "namedRegex": "^/(?<nxtPlocale>[^/]+?)/my\\-invites(?:/)?$"}, {"page": "/[locale]/my-orders", "regex": "^/([^/]+?)/my\\-orders(?:/)?$", "routeKeys": {"nxtPlocale": "nxtPlocale"}, "namedRegex": "^/(?<nxtPlocale>[^/]+?)/my\\-orders(?:/)?$"}, {"page": "/[locale]/pay-success/[session_id]", "regex": "^/([^/]+?)/pay\\-success/([^/]+?)(?:/)?$", "routeKeys": {"nxtPlocale": "nxtPlocale", "nxtPsession_id": "nxtPsession_id"}, "namedRegex": "^/(?<nxtPlocale>[^/]+?)/pay\\-success/(?<nxtPsession_id>[^/]+?)(?:/)?$"}, {"page": "/[locale]/posts", "regex": "^/([^/]+?)/posts(?:/)?$", "routeKeys": {"nxtPlocale": "nxtPlocale"}, "namedRegex": "^/(?<nxtPlocale>[^/]+?)/posts(?:/)?$"}, {"page": "/[locale]/posts/[slug]", "regex": "^/([^/]+?)/posts/([^/]+?)(?:/)?$", "routeKeys": {"nxtPlocale": "nxtPlocale", "nxtPslug": "nxtPslug"}, "namedRegex": "^/(?<nxtPlocale>[^/]+?)/posts/(?<nxtPslug>[^/]+?)(?:/)?$"}, {"page": "/[locale]/pricing", "regex": "^/([^/]+?)/pricing(?:/)?$", "routeKeys": {"nxtPlocale": "nxtPlocale"}, "namedRegex": "^/(?<nxtPlocale>[^/]+?)/pricing(?:/)?$"}, {"page": "/[locale]/showcase", "regex": "^/([^/]+?)/showcase(?:/)?$", "routeKeys": {"nxtPlocale": "nxtPlocale"}, "namedRegex": "^/(?<nxtPlocale>[^/]+?)/showcase(?:/)?$"}, {"page": "/[locale]/showcase-demo", "regex": "^/([^/]+?)/showcase\\-demo(?:/)?$", "routeKeys": {"nxtPlocale": "nxtPlocale"}, "namedRegex": "^/(?<nxtPlocale>[^/]+?)/showcase\\-demo(?:/)?$"}, {"page": "/[locale]/test-i18n", "regex": "^/([^/]+?)/test\\-i18n(?:/)?$", "routeKeys": {"nxtPlocale": "nxtPlocale"}, "namedRegex": "^/(?<nxtPlocale>[^/]+?)/test\\-i18n(?:/)?$"}, {"page": "/[locale]/test-language-detection", "regex": "^/([^/]+?)/test\\-language\\-detection(?:/)?$", "routeKeys": {"nxtPlocale": "nxtPlocale"}, "namedRegex": "^/(?<nxtPlocale>[^/]+?)/test\\-language\\-detection(?:/)?$"}], "staticRoutes": [{"page": "/_not-found", "regex": "^/_not\\-found(?:/)?$", "routeKeys": {}, "namedRegex": "^/_not\\-found(?:/)?$"}, {"page": "/privacy-policy", "regex": "^/privacy\\-policy(?:/)?$", "routeKeys": {}, "namedRegex": "^/privacy\\-policy(?:/)?$"}, {"page": "/terms-of-service", "regex": "^/terms\\-of\\-service(?:/)?$", "routeKeys": {}, "namedRegex": "^/terms\\-of\\-service(?:/)?$"}], "dataRoutes": [], "rsc": {"header": "RSC", "varyHeader": "RSC, Next-Router-State-Tree, Next-Router-Prefetch, Next-Router-Segment-Prefetch", "prefetchHeader": "Next-Router-Prefetch", "didPostponeHeader": "x-nextjs-postponed", "contentTypeHeader": "text/x-component", "suffix": ".rsc", "prefetchSuffix": ".prefetch.rsc", "prefetchSegmentHeader": "Next-Router-Segment-Prefetch", "prefetchSegmentSuffix": ".segment.rsc", "prefetchSegmentDirSuffix": ".segments"}, "rewriteHeaders": {"pathHeader": "x-nextjs-rewritten-path", "queryHeader": "x-nextjs-rewritten-query"}, "rewrites": []}