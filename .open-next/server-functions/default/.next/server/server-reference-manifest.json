{"node": {"00880263a376180560adeff1fdd2511a7543726d4a": {"workers": {"app/api/add-feedback/route": {"moduleId": "21734", "async": false}, "app/api/admin/models/translations/route": {"moduleId": "21734", "async": false}, "app/api/ai/result/route": {"moduleId": "21734", "async": false}, "app/api/ai/generate/route": {"moduleId": "21734", "async": false}, "app/api/ai/upload-image/route": {"moduleId": "21734", "async": false}, "app/api/ai/estimate-cost/route": {"moduleId": "21734", "async": false}, "app/api/ai/usage/route": {"moduleId": "21734", "async": false}, "app/api/checkout/route": {"moduleId": "21734", "async": false}, "app/api/get-user-credits/route": {"moduleId": "21734", "async": false}, "app/api/ping/route": {"moduleId": "21734", "async": false}, "app/api/update-invite-code/route": {"moduleId": "21734", "async": false}, "app/api/get-user-info/route": {"moduleId": "21734", "async": false}, "app/api/volcengine/asr/submit/route": {"moduleId": "21734", "async": false}, "app/api/volcengine/tts-async/query/route": {"moduleId": "21734", "async": false}, "app/api/volcengine/asr/query/route": {"moduleId": "21734", "async": false}, "app/api/volcengine/tts/route": {"moduleId": "21734", "async": false}, "app/api/volcengine/voice-clone/status/route": {"moduleId": "21734", "async": false}, "app/api/volcengine/tts-async/submit/route": {"moduleId": "21734", "async": false}, "app/api/volcengine/voice-clone/upload/route": {"moduleId": "21734", "async": false}, "app/api/auth/[...nextauth]/route": {"moduleId": "21734", "async": false}, "app/[locale]/auth/signin/page": {"moduleId": "21734", "async": false}, "app/[locale]/(admin)/admin/feedbacks/page": {"moduleId": "21734", "async": false}, "app/[locale]/(admin)/admin/posts/page": {"moduleId": "21734", "async": false}, "app/[locale]/(admin)/admin/posts/[uuid]/edit/page": {"moduleId": "30299", "async": false}, "app/[locale]/(admin)/admin/posts/add/page": {"moduleId": "7772", "async": false}, "app/[locale]/(admin)/admin/orders/page": {"moduleId": "21734", "async": false}, "app/[locale]/(admin)/admin/users/page": {"moduleId": "21734", "async": false}, "app/[locale]/(admin)/admin/page": {"moduleId": "21734", "async": false}, "app/[locale]/(default)/(console)/my-credits/page": {"moduleId": "21734", "async": false}, "app/[locale]/(default)/(console)/api-keys/page": {"moduleId": "21734", "async": false}, "app/[locale]/(default)/(console)/my-invites/page": {"moduleId": "21734", "async": false}, "app/[locale]/(default)/(console)/my-orders/page": {"moduleId": "21734", "async": false}, "app/[locale]/(default)/(console)/api-keys/create/page": {"moduleId": "58358", "async": false}}, "layer": {"app/api/add-feedback/route": "rsc", "app/api/admin/models/translations/route": "rsc", "app/api/ai/result/route": "rsc", "app/api/ai/generate/route": "rsc", "app/api/ai/upload-image/route": "rsc", "app/api/ai/estimate-cost/route": "rsc", "app/api/ai/usage/route": "rsc", "app/api/checkout/route": "rsc", "app/api/get-user-credits/route": "rsc", "app/api/ping/route": "rsc", "app/api/update-invite-code/route": "rsc", "app/api/get-user-info/route": "rsc", "app/api/volcengine/asr/submit/route": "rsc", "app/api/volcengine/tts-async/query/route": "rsc", "app/api/volcengine/asr/query/route": "rsc", "app/api/volcengine/tts/route": "rsc", "app/api/volcengine/voice-clone/status/route": "rsc", "app/api/volcengine/tts-async/submit/route": "rsc", "app/api/volcengine/voice-clone/upload/route": "rsc", "app/api/auth/[...nextauth]/route": "rsc", "app/[locale]/auth/signin/page": "rsc", "app/[locale]/(admin)/admin/feedbacks/page": "rsc", "app/[locale]/(admin)/admin/posts/page": "rsc", "app/[locale]/(admin)/admin/posts/[uuid]/edit/page": "rsc", "app/[locale]/(admin)/admin/posts/add/page": "rsc", "app/[locale]/(admin)/admin/orders/page": "rsc", "app/[locale]/(admin)/admin/users/page": "rsc", "app/[locale]/(admin)/admin/page": "rsc", "app/[locale]/(default)/(console)/my-credits/page": "rsc", "app/[locale]/(default)/(console)/api-keys/page": "rsc", "app/[locale]/(default)/(console)/my-invites/page": "rsc", "app/[locale]/(default)/(console)/my-orders/page": "rsc", "app/[locale]/(default)/(console)/api-keys/create/page": "rsc"}}, "60fa488dfd6057e54aa495ae74d6031511879a7a67": {"workers": {"app/[locale]/(admin)/admin/posts/[uuid]/edit/page": {"moduleId": "30299", "async": false}}, "layer": {"app/[locale]/(admin)/admin/posts/[uuid]/edit/page": "rsc"}}, "6060a7b3ae196a5f18dc037c45f6672364bde1c2cd": {"workers": {"app/[locale]/(admin)/admin/posts/add/page": {"moduleId": "7772", "async": false}}, "layer": {"app/[locale]/(admin)/admin/posts/add/page": "rsc"}}, "604ce8d80bdd01aed835850ccf14913b8e23190012": {"workers": {"app/[locale]/(default)/(console)/api-keys/create/page": {"moduleId": "58358", "async": false}}, "layer": {"app/[locale]/(default)/(console)/api-keys/create/page": "rsc"}}}, "edge": {}, "encryptionKey": "dv7dWAk9T2M+/LsPh/t8l/c14E4NhTVpbZj8PiBGmPM="}