{"/_not-found/page": "app/_not-found/page.js", "/api/add-feedback/route": "app/api/add-feedback/route.js", "/api/admin/models/translations/route": "app/api/admin/models/translations/route.js", "/api/ai/models/route": "app/api/ai/models/route.js", "/api/ai/result/route": "app/api/ai/result/route.js", "/api/ai/generate/route": "app/api/ai/generate/route.js", "/api/ai/upload-image/route": "app/api/ai/upload-image/route.js", "/api/ai/estimate-cost/route": "app/api/ai/estimate-cost/route.js", "/api/ai/transfer-file/route": "app/api/ai/transfer-file/route.js", "/api/ai/usage/route": "app/api/ai/usage/route.js", "/api/demo/gen-image/route": "app/api/demo/gen-image/route.js", "/api/demo/gen-text/route": "app/api/demo/gen-text/route.js", "/api/demo/gen-stream-text/route": "app/api/demo/gen-stream-text/route.js", "/api/checkout/route": "app/api/checkout/route.js", "/api/stripe-notify/route": "app/api/stripe-notify/route.js", "/api/get-user-credits/route": "app/api/get-user-credits/route.js", "/api/ping/route": "app/api/ping/route.js", "/api/test/replicate-unified/route": "app/api/test/replicate-unified/route.js", "/api/update-invite-code/route": "app/api/update-invite-code/route.js", "/api/update-invite/route": "app/api/update-invite/route.js", "/api/test/replicate/route": "app/api/test/replicate/route.js", "/api/get-user-info/route": "app/api/get-user-info/route.js", "/api/volcengine/asr/submit/route": "app/api/volcengine/asr/submit/route.js", "/api/volcengine/tts-async/query/route": "app/api/volcengine/tts-async/query/route.js", "/api/volcengine/asr/query/route": "app/api/volcengine/asr/query/route.js", "/api/volcengine/callback/route": "app/api/volcengine/callback/route.js", "/api/volcengine/tts/route": "app/api/volcengine/tts/route.js", "/api/volcengine/voice-clone/status/route": "app/api/volcengine/voice-clone/status/route.js", "/api/volcengine/tts-async/submit/route": "app/api/volcengine/tts-async/submit/route.js", "/api/download-proxy/route": "app/api/download-proxy/route.js", "/api/volcengine/voice-clone/upload/route": "app/api/volcengine/voice-clone/upload/route.js", "/api/auth/[...nextauth]/route": "app/api/auth/[...nextauth]/route.js", "/(legal)/terms-of-service/page": "app/(legal)/terms-of-service/page.js", "/(legal)/privacy-policy/page": "app/(legal)/privacy-policy/page.js", "/[locale]/auth/signin/page": "app/[locale]/auth/signin/page.js", "/[locale]/test-language-detection/page": "app/[locale]/test-language-detection/page.js", "/[locale]/pay-success/[session_id]/page": "app/[locale]/pay-success/[session_id]/page.js", "/[locale]/test-i18n/page": "app/[locale]/test-i18n/page.js", "/[locale]/(admin)/admin/feedbacks/page": "app/[locale]/(admin)/admin/feedbacks/page.js", "/[locale]/(admin)/admin/posts/page": "app/[locale]/(admin)/admin/posts/page.js", "/[locale]/(admin)/admin/posts/[uuid]/edit/page": "app/[locale]/(admin)/admin/posts/[uuid]/edit/page.js", "/[locale]/(admin)/admin/posts/add/page": "app/[locale]/(admin)/admin/posts/add/page.js", "/[locale]/(admin)/admin/orders/page": "app/[locale]/(admin)/admin/orders/page.js", "/[locale]/(admin)/admin/users/page": "app/[locale]/(admin)/admin/users/page.js", "/[locale]/(default)/ai-dashboard/page": "app/[locale]/(default)/ai-dashboard/page.js", "/[locale]/(default)/components-demo/blog-detail/page": "app/[locale]/(default)/components-demo/blog-detail/page.js", "/[locale]/(default)/components-demo/tools/page": "app/[locale]/(default)/components-demo/tools/page.js", "/[locale]/(default)/components-demo/interactive/page": "app/[locale]/(default)/components-demo/interactive/page.js", "/[locale]/(default)/posts/[slug]/page": "app/[locale]/(default)/posts/[slug]/page.js", "/[locale]/(default)/components-demo/editors/page": "app/[locale]/(default)/components-demo/editors/page.js", "/[locale]/(default)/components-demo/page": "app/[locale]/(default)/components-demo/page.js", "/[locale]/(default)/posts/page": "app/[locale]/(default)/posts/page.js", "/[locale]/(default)/i/[code]/page": "app/[locale]/(default)/i/[code]/page.js", "/[locale]/(default)/pricing/page": "app/[locale]/(default)/pricing/page.js", "/[locale]/(default)/showcase/page": "app/[locale]/(default)/showcase/page.js", "/[locale]/(default)/page": "app/[locale]/(default)/page.js", "/[locale]/(admin)/admin/page": "app/[locale]/(admin)/admin/page.js", "/[locale]/(default)/components-demo/content/page": "app/[locale]/(default)/components-demo/content/page.js", "/[locale]/(default)/components-demo/data/page": "app/[locale]/(default)/components-demo/data/page.js", "/[locale]/(default)/components-demo/layout/page": "app/[locale]/(default)/components-demo/layout/page.js", "/[locale]/(default)/showcase-demo/page": "app/[locale]/(default)/showcase-demo/page.js", "/[locale]/(default)/admin/translations/page": "app/[locale]/(default)/admin/translations/page.js", "/[locale]/(default)/(console)/my-credits/page": "app/[locale]/(default)/(console)/my-credits/page.js", "/[locale]/(default)/(console)/api-keys/page": "app/[locale]/(default)/(console)/api-keys/page.js", "/[locale]/(default)/(console)/my-invites/page": "app/[locale]/(default)/(console)/my-invites/page.js", "/[locale]/(default)/(console)/my-orders/page": "app/[locale]/(default)/(console)/my-orders/page.js", "/[locale]/(default)/(console)/api-keys/create/page": "app/[locale]/(default)/(console)/api-keys/create/page.js"}