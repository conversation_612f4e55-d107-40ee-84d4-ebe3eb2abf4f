(()=>{var e={};e.id=146,e.ids=[146],e.modules={3295:e=>{"use strict";e.exports=require("next/dist/server/app-render/after-task-async-storage.external.js")},4573:e=>{"use strict";e.exports=require("node:buffer")},10846:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},11997:e=>{"use strict";e.exports=require("punycode")},12667:(e,t,r)=>{var a={"./ai-dashboard/en.json":[45989,5989],"./ai-dashboard/zh.json":[9776,9776],"./landing/en.json":[42881,2881],"./landing/zh.json":[30956,956],"./pricing/en.json":[71718,1718],"./pricing/zh.json":[42743,2743],"./showcase/en.json":[20651,651],"./showcase/zh.json":[10817,3198]};function n(e){if(!r.o(a,e))return Promise.resolve().then(()=>{var t=Error("Cannot find module '"+e+"'");throw t.code="MODULE_NOT_FOUND",t});var t=a[e],n=t[0];return r.e(t[1]).then(()=>r.t(n,19))}n.keys=()=>Object.keys(a),n.id=12667,e.exports=n},13373:()=>{},19121:e=>{"use strict";e.exports=require("next/dist/server/app-render/action-async-storage.external.js")},25435:(e,t,r)=>{"use strict";r.r(t),r.d(t,{patchFetch:()=>w,routeModule:()=>_,serverHooks:()=>g,workAsyncStorage:()=>y,workUnitAsyncStorage:()=>h});var a={};r.r(a),r.d(a,{POST:()=>m});var n=r(79341),i=r(91514),s=r(95673),o=r(60736),u=r(40109),c=r(29042),d=r(4085),l=r(35050),p=r(29452),f=r(64476);async function m(e){try{let{credits:t,currency:r,amount:a,interval:n,product_id:i,product_name:s,valid_months:m,cancel_url:_}=await e.json();if(_||(_="http://localhost:3000/#pricing"),!a||!n||!r||!i)return(0,c.YS)("invalid params");let y=await (0,f.eI)("en");if(!y||!y.pricing||!y.pricing.items)return(0,c.YS)("invalid pricing table");let h=y.pricing.items.find(e=>e.product_id===i);if(!h||!h.amount||!h.interval||!h.currency||h.amount!==a||h.interval!==n||h.currency!==r)return(0,c.YS)("invalid checkout params");if(!["year","month","one-time"].includes(n))return(0,c.YS)("invalid interval");let g="month"===n||"year"===n;if("year"===n&&12!==m||"month"===n&&1!==m)return(0,c.YS)("invalid valid_months");let w=await (0,o.TG)();if(!w)return(0,c.YS)("no auth, please sign-in");let v=await (0,o.qo)();if(!v){let e=await (0,l.pX)(w);e&&(v=e.email)}if(!v)return(0,c.YS)("invalid user");let x=(0,p.ZK)(),k=new Date,j=k.toISOString(),q="",S=new Date(k);S.setMonth(k.getMonth()+m);let O=S.getTime(),E=0;g&&(E=864e5);let b=O+E;q=new Date(b).toISOString();let A={order_no:x,created_at:j,user_uuid:w,user_email:v,amount:a,interval:n,expired_at:q,status:"created",credits:t,currency:r,product_id:i,product_name:s,valid_months:m};await (0,u.El)(A);let T=new d.A(process.env.STRIPE_PRIVATE_KEY||""),I={payment_method_types:["card"],line_items:[{price_data:{currency:r,product_data:{name:s},unit_amount:a,recurring:g?{interval:n}:void 0},quantity:1}],allow_promotion_codes:!0,metadata:{project:"KREA FLUX",product_name:s,order_no:x.toString(),user_email:v,credits:t,user_uuid:w},mode:g?"subscription":"payment",success_url:"http://localhost:3000/pay-success/{CHECKOUT_SESSION_ID}",cancel_url:_};v&&(I.customer_email=v),g&&(I.subscription_data={metadata:I.metadata}),"cny"===r&&(I.payment_method_types=["wechat_pay","alipay","card"],I.payment_method_options={wechat_pay:{client:"web"},alipay:{}});let U=JSON.stringify(I),D=(await T.checkout.sessions.create(I)).id;return await (0,u.I5)(x,D,U),(0,c.DQ)({public_key:process.env.STRIPE_PUBLIC_KEY,order_no:x,session_id:D})}catch(e){return console.log("checkout failed: ",e),(0,c.YS)("checkout failed: "+e.message)}}let _=new n.AppRouteRouteModule({definition:{kind:i.RouteKind.APP_ROUTE,page:"/api/checkout/route",pathname:"/api/checkout",filename:"route",bundlePath:"app/api/checkout/route"},resolvedPagePath:"/Users/<USER>/My-Project/fluxkrea_test/app/api/checkout/route.ts",nextConfigOutput:"standalone",userland:a}),{workAsyncStorage:y,workUnitAsyncStorage:h,serverHooks:g}=_;function w(){return(0,s.patchFetch)({workAsyncStorage:y,workUnitAsyncStorage:h})}},26820:(e,t,r)=>{"use strict";r.r(t),r.d(t,{"00880263a376180560adeff1fdd2511a7543726d4a":()=>a.T});var a=r(39201)},27910:e=>{"use strict";e.exports=require("stream")},28354:e=>{"use strict";e.exports=require("util")},28432:(e,t,r)=>{"use strict";r.d(t,{j2:()=>m,Y9:()=>l});var a=r(62364),n=r(36157),i=r(72648),s=r(39201),o=r(4961),u=r(29452),c=r(60736);let d=[];d.push((0,n.A)({id:"google-one-tap",name:"google-one-tap",credentials:{credential:{type:"text"}},async authorize(e,t){let r=e.credential,a=await fetch("https://oauth2.googleapis.com/tokeninfo?id_token="+r);if(!a.ok)return console.log("Failed to verify token"),null;let n=await a.json();if(!n)return console.log("invalid payload from token"),null;let{email:i,sub:s,given_name:o,family_name:u,email_verified:c,picture:d}=n;return i?{id:s,name:[o,u].join(" "),email:i,image:d,emailVerified:c?new Date:null}:(console.log("invalid email in payload"),null)}})),process.env.AUTH_GOOGLE_ID&&process.env.AUTH_GOOGLE_SECRET&&d.push((0,i.A)({clientId:process.env.AUTH_GOOGLE_ID,clientSecret:process.env.AUTH_GOOGLE_SECRET,authorization:{params:{prompt:"consent",access_type:"offline",response_type:"code"}}})),d.map(e=>{if("function"!=typeof e)return{id:e.id,name:e.name};{let t=e();return{id:t.id,name:t.name}}}).filter(e=>"google-one-tap"!==e.id);let{handlers:l,signIn:p,signOut:f,auth:m}=(0,a.Ay)({providers:d,pages:{signIn:"/auth/signin"},callbacks:{signIn:async({user:e,account:t,profile:r,email:a,credentials:n})=>!0,redirect:async({url:e,baseUrl:t})=>e.startsWith("/")?`${t}${e}`:new URL(e).origin===t?e:t,session:async({session:e,token:t,user:r})=>(t&&t.user&&t.user&&(e.user=t.user),e),async jwt({token:e,user:t,account:r}){try{if(t&&t.email&&r){let a={uuid:(0,u.YJ)(),email:t.email,nickname:t.name||"",avatar_url:t.image||"",signin_type:r.type,signin_provider:r.provider,signin_openid:r.providerAccountId,created_at:(0,o.iq)(),signin_ip:await (0,s.T)()};try{let t=await (0,c.$c)(a);e.user={uuid:t.uuid,email:t.email,nickname:t.nickname,avatar_url:t.avatar_url,created_at:t.created_at}}catch(e){console.error("save user failed:",e)}}return e}catch(t){return console.error("jwt callback error:",t),e}}}})},29042:(e,t,r)=>{"use strict";function a(e){return s(0,"ok",e||[])}function n(){return s(0,"ok")}function i(e){return s(-1,e)}function s(e,t,r){let a={code:e,message:t,data:r};return r&&(a.data=r),Response.json(a)}r.d(t,{DQ:()=>a,YS:()=>i,j7:()=>s,rn:()=>n})},29294:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-async-storage.external.js")},34631:e=>{"use strict";e.exports=require("tls")},39201:(e,t,r)=>{"use strict";r.d(t,{T:()=>i});var a=r(72784);r(97468);var n=r(62225);async function i(){let e=await (0,n.b3)();return e.get("cf-connecting-ip")||e.get("x-real-ip")||(e.get("x-forwarded-for")||"127.0.0.1").split(",")[0]}(0,r(78564).D)([i]),(0,a.A)(i,"00880263a376180560adeff1fdd2511a7543726d4a",null)},44870:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-route.runtime.prod.js")},55511:e=>{"use strict";e.exports=require("crypto")},55591:e=>{"use strict";e.exports=require("https")},57975:e=>{"use strict";e.exports=require("node:util")},60736:(e,t,r)=>{"use strict";r.d(t,{$c:()=>c,TG:()=>d,qo:()=>p,ug:()=>f});var a=r(87830),n=r(35050),i=r(28432),s=r(4961),o=r(78054),u=r(62225);async function c(e){try{let t=await (0,n.Gs)(e.email);return t?(e.id=t.id,e.uuid=t.uuid,e.created_at=t.created_at):(await (0,n.HW)(e),await (0,a.d_)({user_uuid:e.uuid||"",trans_type:a.H3.NewUser,credits:a.rN.NewUserGet,expired_at:(0,s.MI)()})),e}catch(e){throw console.log("save user failed: ",e),e}}async function d(){let e="",t=await l();if(t&&t.startsWith("sk-"))return await (0,o.zz)(t)||"";let r=await (0,i.j2)();return r&&r.user&&r.user.uuid&&(e=r.user.uuid),e}async function l(){let e=(await (0,u.b3)()).get("Authorization");return e?e.replace("Bearer ",""):""}async function p(){let e="",t=await (0,i.j2)();return t&&t.user&&t.user.email&&(e=t.user.email),e}async function f(){let e=await d();if(e)return await (0,n.pX)(e)}},63033:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-unit-async-storage.external.js")},64476:(e,t,r)=>{"use strict";async function a(e){return await s("landing",e)}async function n(e){return await s("pricing",e)}async function i(e){return await s("showcase",e)}async function s(e,t){try{return"zh-CN"===t&&(t="zh"),await r(12667)(`./${e}/${t.toLowerCase()}.json`).then(e=>e.default)}catch(a){return console.warn(`Failed to load ${t}.json, falling back to en.json`),await r(87188)(`./${e}/en.json`).then(e=>e.default)}}r.d(t,{DN:()=>i,JE:()=>a,eI:()=>n})},74075:e=>{"use strict";e.exports=require("zlib")},77598:e=>{"use strict";e.exports=require("node:crypto")},78054:(e,t,r)=>{"use strict";r.d(t,{N1:()=>i,ai:()=>n,ox:()=>s,zz:()=>o});var a=r(4995),n=function(e){return e.Created="created",e.Deleted="deleted",e}({});async function i(e){let t=(0,a.A)(),{data:r,error:n}=await t.from("apikeys").insert(e);if(n)throw n;return r}async function s(e,t=1,r=50){let n=(t-1)*r,i=(0,a.A)(),{data:o,error:u}=await i.from("apikeys").select("*").eq("user_uuid",e).neq("status","deleted").order("created_at",{ascending:!1}).range(n,n+r-1);if(!u)return o}async function o(e){let t=(0,a.A)(),{data:r,error:n}=await t.from("apikeys").select("user_uuid").eq("api_key",e).eq("status","created").limit(1).single();if(!n)return r?.user_uuid}},79341:(e,t,r)=>{"use strict";e.exports=r(44870)},79428:e=>{"use strict";e.exports=require("buffer")},79551:e=>{"use strict";e.exports=require("url")},79646:e=>{"use strict";e.exports=require("child_process")},81630:e=>{"use strict";e.exports=require("http")},87188:(e,t,r)=>{var a={"./ai-dashboard/en.json":[45989,5989],"./landing/en.json":[42881,2881],"./pricing/en.json":[71718,1718],"./showcase/en.json":[20651,651]};function n(e){if(!r.o(a,e))return Promise.resolve().then(()=>{var t=Error("Cannot find module '"+e+"'");throw t.code="MODULE_NOT_FOUND",t});var t=a[e],n=t[0];return r.e(t[1]).then(()=>r.t(n,19))}n.keys=()=>Object.keys(a),n.id=87188,e.exports=n},91645:e=>{"use strict";e.exports=require("net")},94735:e=>{"use strict";e.exports=require("events")},94813:()=>{}};var t=require("../../../webpack-runtime.js");t.C(e);var r=e=>t(t.s=e),a=t.X(0,[5673,7711,8734,5327,2041,4085,4524],()=>r(25435));module.exports=a})();