(()=>{var e={};e.id=8067,e.ids=[8067],e.modules={10846:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},13373:()=>{},29042:(e,t,r)=>{"use strict";function a(e){return o(0,"ok",e||[])}function n(){return o(0,"ok")}function s(e){return o(-1,e)}function o(e,t,r){let a={code:e,message:t,data:r};return r&&(a.data=r),Response.json(a)}r.d(t,{DQ:()=>a,YS:()=>s,j7:()=>o,rn:()=>n})},29294:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-async-storage.external.js")},44870:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-route.runtime.prod.js")},53764:(e,t,r)=>{"use strict";r.r(t),r.d(t,{patchFetch:()=>v,routeModule:()=>g,serverHooks:()=>f,workAsyncStorage:()=>k,workUnitAsyncStorage:()=>x});var a={};r.r(a),r.d(a,{POST:()=>m});var n=r(79341),s=r(91514),o=r(95673),i=r(41902),p=r(73785),u=r(93797),d=r(75399),c=r(91135),l=r(29042);async function m(e){try{let t;let{prompt:r,provider:a,model:n}=await e.json();if(!r||!a||!n)return(0,l.YS)("invalid params");switch(a){case"openai":t=(0,c.N)(n);break;case"deepseek":t=(0,d.Z)(n);break;case"openrouter":t=(0,u.ER)({apiKey:process.env.OPENROUTER_API_KEY})(n),"deepseek/deepseek-r1"===n&&(t=(0,i.ae)({model:t,middleware:(0,i.Ol)({tagName:"think"})}));break;case"siliconflow":t=(0,p.jr)({name:"siliconflow",apiKey:process.env.SILICONFLOW_API_KEY,baseURL:process.env.SILICONFLOW_BASE_URL})(n),"deepseek-ai/DeepSeek-R1"===n&&(t=(0,i.ae)({model:t,middleware:(0,i.Ol)({tagName:"reasoning_content"})}));break;default:return(0,l.YS)("invalid provider")}let s=await (0,i.gM)({model:t,prompt:r,onChunk:async e=>{console.log("chunk",e)},onFinish:async()=>{console.log("finish",await s.text)}});return s.toDataStreamResponse({sendReasoning:!0})}catch(e){return console.log("gen text stream failed:",e),(0,l.YS)("gen text stream failed")}}let g=new n.AppRouteRouteModule({definition:{kind:s.RouteKind.APP_ROUTE,page:"/api/demo/gen-stream-text/route",pathname:"/api/demo/gen-stream-text",filename:"route",bundlePath:"app/api/demo/gen-stream-text/route"},resolvedPagePath:"/Users/<USER>/My-Project/fluxkrea_test/app/api/demo/gen-stream-text/route.ts",nextConfigOutput:"standalone",userland:a}),{workAsyncStorage:k,workUnitAsyncStorage:x,serverHooks:f}=g;function v(){return(0,o.patchFetch)({workAsyncStorage:k,workUnitAsyncStorage:x})}},63033:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-unit-async-storage.external.js")},94813:()=>{}};var t=require("../../../../webpack-runtime.js");t.C(e);var r=e=>t(t.s=e),a=t.X(0,[5673,2051,5683],()=>r(53764));module.exports=a})();