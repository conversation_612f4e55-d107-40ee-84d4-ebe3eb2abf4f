(()=>{var e={};e.id=4548,e.ids=[4548],e.modules={10846:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},13373:()=>{},29042:(e,t,r)=>{"use strict";function n(e){return o(0,"ok",e||[])}function a(){return o(0,"ok")}function s(e){return o(-1,e)}function o(e,t,r){let n={code:e,message:t,data:r};return r&&(n.data=r),Response.json(n)}r.d(t,{DQ:()=>n,YS:()=>s,j7:()=>o,rn:()=>a})},29294:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-async-storage.external.js")},44870:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-route.runtime.prod.js")},63033:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-unit-async-storage.external.js")},80973:(e,t,r)=>{"use strict";r.r(t),r.d(t,{patchFetch:()=>v,routeModule:()=>m,serverHooks:()=>f,workAsyncStorage:()=>x,workUnitAsyncStorage:()=>k});var n={};r.r(n),r.d(n,{POST:()=>g});var a=r(79341),s=r(91514),o=r(95673),i=r(41902),d=r(29042),p=r(73785),u=r(93797),c=r(75399),l=r(91135);async function g(e){try{let t;let{prompt:r,provider:n,model:a}=await e.json();if(!r||!n||!a)return(0,d.YS)("invalid params");switch(n){case"openai":t=(0,l.N)(a);break;case"deepseek":t=(0,c.Z)(a);break;case"openrouter":t=(0,u.ER)({apiKey:process.env.OPENROUTER_API_KEY})(a),"deepseek/deepseek-r1"===a&&(t=(0,i.ae)({model:t,middleware:(0,i.Ol)({tagName:"think"})}));break;case"siliconflow":t=(0,p.jr)({name:"siliconflow",apiKey:process.env.SILICONFLOW_API_KEY,baseURL:process.env.SILICONFLOW_BASE_URL})(a),"deepseek-ai/DeepSeek-R1"===a&&(t=(0,i.ae)({model:t,middleware:(0,i.Ol)({tagName:"reasoning_content"})}));break;default:return(0,d.YS)("invalid provider")}let{reasoning:s,text:o,warnings:g}=await (0,i.Df)({model:t,prompt:r});if(g&&g.length>0)return console.log("gen text warnings:",n,g),(0,d.YS)("gen text failed");return(0,d.DQ)({text:o,reasoning:s})}catch(e){return console.log("gen text failed:",e),(0,d.YS)("gen text failed")}}let m=new a.AppRouteRouteModule({definition:{kind:s.RouteKind.APP_ROUTE,page:"/api/demo/gen-text/route",pathname:"/api/demo/gen-text",filename:"route",bundlePath:"app/api/demo/gen-text/route"},resolvedPagePath:"/Users/<USER>/My-Project/fluxkrea_test/app/api/demo/gen-text/route.ts",nextConfigOutput:"standalone",userland:n}),{workAsyncStorage:x,workUnitAsyncStorage:k,serverHooks:f}=m;function v(){return(0,o.patchFetch)({workAsyncStorage:x,workUnitAsyncStorage:k})}},94813:()=>{}};var t=require("../../../../webpack-runtime.js");t.C(e);var r=e=>t(t.s=e),n=t.X(0,[5673,2051,5683],()=>r(80973));module.exports=n})();