(()=>{var e={};e.id=1032,e.ids=[1032],e.modules={10846:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},13373:()=>{},19867:(e,t,r)=>{"use strict";t.F=void 0;let s=r(37198);Object.defineProperty(t,"F",{enumerable:!0,get:function(){return s.SnowflakeIdv1}})},21820:e=>{"use strict";e.exports=require("os")},27910:e=>{"use strict";e.exports=require("stream")},28467:(e,t,r)=>{"use strict";r.r(t),r.d(t,{patchFetch:()=>L,routeModule:()=>q,serverHooks:()=>A,workAsyncStorage:()=>O,workUnitAsyncStorage:()=>K});var s={};r.r(s),r.d(s,{POST:()=>E});var i=r(79341),a=r(91514),n=r(95673),o=r(41902),c=r(29042),u=r(29452);let l="https://api.klingai.com";async function h(e){try{let t=Math.floor(Date.now()/1e3),r={iss:e.accessKey,exp:t+1800,nbf:t-5},s=Buffer.from(JSON.stringify({alg:"HS256",typ:"JWT"})).toString("base64url"),i=Buffer.from(JSON.stringify(r)).toString("base64url"),a=`${s}.${i}`,n=new TextEncoder().encode(e.secretKey),o=await crypto.subtle.importKey("raw",n,{name:"HMAC",hash:"SHA-256"},!1,["sign"]),c=await crypto.subtle.sign("HMAC",o,new TextEncoder().encode(a)),u=Buffer.from(c).toString("base64url");return`${a}.${u}`}catch(e){throw console.error("Failed to generate JWT token:",e),e}}class d{constructor(e){this.token=e}async createTask({model:e="kling-v1",prompt:t,negative_prompt:r,image:s,image_fidelity:i,n:a,aspect_ratio:n="16:9",callback_url:o,...c}){try{let u=`${l}/v1/images/generations`,h={model:e,prompt:t,negative_prompt:r,image:s,image_fidelity:i,n:a,aspect_ratio:n,callback_url:o,...c};console.log("Image Generation request:",this.token,u,h);let d=await fetch(u,{method:"POST",headers:{Authorization:`Bearer ${this.token}`,"Content-Type":"application/json"},body:JSON.stringify(h)});if(!d.ok)throw Error(`HTTP error! status: ${d.status}`);return await d.json()}catch(e){throw console.error("Image Generation API call failed:",e),e}}async queryTask({task_id:e}){try{let t=`${l}/v1/images/generations/${e}`,r=await fetch(t,{method:"GET",headers:{Authorization:`Bearer ${this.token}`}});if(!r.ok)throw Error(`HTTP error! status: ${r.status}`);return await r.json()}catch(e){throw console.error("Image Generation Query Task API call failed:",e),e}}async queryTasks({page:e=1,limit:t=30}){try{if(e<1||e>1e3)throw Error("invalid page");if(t<1||t>500)throw Error("invalid limit");let r=`${l}/v1/images/generations?pageNum=${e}&pageSize=${t}`,s=await fetch(r,{method:"GET",headers:{Authorization:`Bearer ${this.token}`}});if(!s.ok)throw Error(`HTTP error! status: ${s.status}`);return await s.json()}catch(e){throw console.error("Image Generation Query Tasks API call failed:",e),e}}}async function m(e){return e||(e={accessKey:process.env.KLING_ACCESS_KEY,secretKey:process.env.KLING_SECRET_KEY}),new d(await h(e))}class g{get provider(){return this.config.provider}get maxImagesPerCall(){return this.settings.maxImagesPerCall??1}constructor(e,t,r){this.specificationVersion="v1",this.modelId=e,this.settings=t,this.config=r}async doGenerate({prompt:e,n:t,aspectRatio:r,size:s,seed:i,providerOptions:a,headers:n,abortSignal:o}){let c=[],u=[],l=[],h=new Date;try{if(!this.config.accessKey||!this.config.secretKey)throw Error("Kling access key or secret key is not set");let i=await m({accessKey:this.config.accessKey,secretKey:this.config.secretKey}),n=await i.createTask({model:this.modelId,prompt:e,n:t,size:s,aspect_ratio:r,...a.kling??{}});if(!n.data||!n.data.task_id)throw Error(n.message);let c=n.data.task_id,u=0;for(;u<20;){let e=await i.queryTask({task_id:c});if(e.data&&e.data.task_status){if("succeed"===e.data.task_status){e.data.task_result&&e.data.task_result.images&&e.data.task_result.images.forEach(e=>{l.push(e.url)});break}if("failed"===e.data.task_status)throw Error(e.data.task_status_msg||"Task failed");if(o?.aborted)throw Error("Operation aborted");u++,await new Promise(e=>setTimeout(e,3e3))}}if(u>=20)throw Error("Task timed out")}catch(e){c.push({type:"other",message:e.message})}return 0===l.length?c.push({type:"other",message:"No images generated"}):u=await Promise.all(l.map(async e=>{let t=await fetch(e);return new Uint8Array(await t.arrayBuffer())})),{images:u,warnings:c,response:{timestamp:h,modelId:this.modelId,headers:void 0}}}}class p{constructor(e){this.token=e}async createTask({model:e="kling-v1",prompt:t,negative_prompt:r,cfg_scale:s=.5,mode:i="std",camera_control:a,aspect_ratio:n="16:9",duration:o=5,callback_url:c,external_task_id:u,...h}){try{let d=`${l}/v1/videos/text2video`,m={model_name:e,prompt:t,negative_prompt:r,cfg_scale:s,mode:i,camera_control:a,aspect_ratio:n,duration:o,callback_url:c,external_task_id:u,...h};console.log("request text2video:",d,m);let g=await fetch(d,{method:"POST",headers:{Authorization:`Bearer ${this.token}`,"Content-Type":"application/json"},body:JSON.stringify(m)});if(!g.ok)throw Error(`HTTP error! status: ${g.status}`);return await g.json()}catch(e){throw console.error("Text2Video API call failed:",e),e}}async queryTask({task_id:e}){try{let t=`${l}/v1/videos/text2video/${e}`;console.log("query task:",t);let r=await fetch(t,{method:"GET",headers:{Authorization:`Bearer ${this.token}`}});if(!r.ok)throw Error(`HTTP error! status: ${r.status}`);return await r.json()}catch(e){throw console.error("Text2Video API call failed:",e),e}}}async function f(e){return e||(e={accessKey:process.env.KLING_ACCESS_KEY,secretKey:process.env.KLING_SECRET_KEY}),new p(await h(e))}class T{get provider(){return this.config.provider}get maxVideosPerCall(){return this.settings.maxVideosPerCall??1}constructor(e,t,r){this.specificationVersion="v1",this.modelId=e,this.settings=t,this.config=r}async doGenerate({prompt:e,n:t,providerOptions:r,headers:s,abortSignal:i}){let a=[],n=[],o=[];if(!this.config.accessKey||!this.config.secretKey)return a.push({type:"other",message:"Kling access key or secret key is not set"}),{videos:n,warnings:a};try{let s=await f({accessKey:this.config.accessKey,secretKey:this.config.secretKey}),c=await s.createTask({model:this.modelId,prompt:e,n:t,...r?.kling??{}});if(!c.data||!c.data.task_id)return a.push({type:"other",message:c.message}),{videos:n,warnings:a};let u=c.data.task_id,l=0;for(;l<20;){let e=await s.queryTask({task_id:u});if(console.log("kling gen videos result:",JSON.stringify(e)),e.data&&e.data.task_status){if("succeed"===e.data.task_status){e.data.task_result&&e.data.task_result.videos&&e.data.task_result.videos.forEach(e=>{o.push(e.url)});break}if("failed"===e.data.task_status){a.push({type:"other",message:e.data.task_status_msg||"Task failed"});break}if(i?.aborted)throw Error("Operation aborted");l++,await new Promise(e=>setTimeout(e,3e4))}}l>=20&&a.push({type:"other",message:"Task timed out"})}catch(e){console.error("Kling generate video failed:",e),a.push({type:"other",message:e.message})}return 0===o.length?(a.push({type:"other",message:"No videos generated"}),{videos:n,warnings:a}):{videos:n=await Promise.all(o.map(async e=>{let t=await fetch(e);return new Uint8Array(await t.arrayBuffer())})),warnings:a}}}var k=r(92229);let y=function(e={}){let t=()=>(0,k.Fv)({settingValue:e.accessKey,settingName:"accessKey",environmentVariableName:"KLING_ACCESS_KEY",description:"Kling access key"}),r=()=>(0,k.Fv)({settingValue:e.secretKey,settingName:"secretKey",environmentVariableName:"KLING_SECRET_KEY",description:"Kling secret key"});return{image:(s,i)=>new g(s,i??{},{accessKey:t(),secretKey:r(),provider:"kling",baseURL:e.baseURL??"https://api.klingai.com",headers:{...e.headers},fetch:e.fetch}),video:(s,i)=>new T(s,{maxVideosPerCall:1},{accessKey:t(),secretKey:r(),provider:"kling",baseURL:e.baseURL??"https://api.klingai.com",headers:{...e.headers},fetch:e.fetch})}}();var v=r(37423),I=r(91135),w=r(74306),_=r(86230),C=r(12206),S=C.z.object({detail:C.z.string().optional(),error:C.z.string().optional()}),b=(0,_.sl)({errorSchema:S,errorToMessage:e=>{var t,r;return null!=(r=null!=(t=e.detail)?t:e.error)?r:"Unknown Replicate error"}}),B=class{constructor(e,t,r){this.modelId=e,this.settings=t,this.config=r,this.specificationVersion="v1"}get provider(){return this.config.provider}get maxImagesPerCall(){var e;return null!=(e=this.settings.maxImagesPerCall)?e:1}async doGenerate({prompt:e,n:t,aspectRatio:r,size:s,seed:i,providerOptions:a,headers:n,abortSignal:o}){var c,u,l,h;let[d,m]=this.modelId.split(":"),g=null!=(l=null==(u=null==(c=this.config._internal)?void 0:c.currentDate)?void 0:u.call(c))?l:new Date,{value:{output:p},responseHeaders:f}=await (0,_.GU)({url:null!=m?`${this.config.baseURL}/predictions`:`${this.config.baseURL}/models/${d}/predictions`,headers:(0,_.m2)(await (0,_.hd)(this.config.headers),n,{prefer:"wait"}),body:{input:{prompt:e,aspect_ratio:r,size:s,seed:i,num_outputs:t,...null!=(h=a.replicate)?h:{}},...null!=m?{version:m}:{}},successfulResponseHandler:(0,_.cV)(N),failedResponseHandler:b,abortSignal:o,fetch:this.config.fetch}),T=Array.isArray(p)?p:[p];return{images:await Promise.all(T.map(async e=>{let{value:t}=await (0,_.$b)({url:e,successfulResponseHandler:(0,_.HD)(),failedResponseHandler:b,abortSignal:o,fetch:this.config.fetch});return t})),warnings:[],response:{timestamp:g,modelId:this.modelId,headers:f}}}},N=C.z.object({output:C.z.union([C.z.array(C.z.string()),C.z.string()])}),x=function(e={}){let t=(t,r)=>{var s;return new B(t,null!=r?r:{},{provider:"replicate",baseURL:null!=(s=e.baseURL)?s:"https://api.replicate.com/v1",headers:{Authorization:`Bearer ${(0,_.WL)({apiKey:e.apiToken,environmentVariableName:"REPLICATE_API_TOKEN",description:"Replicate"})}`,...e.headers},fetch:e.fetch})};return{image:t,imageModel:t,languageModel:()=>{throw new w.eM({modelId:"languageModel",modelType:"languageModel"})},textEmbeddingModel:()=>{throw new w.eM({modelId:"textEmbeddingModel",modelType:"textEmbeddingModel"})}}}();async function E(e){try{let t;let{prompt:r,provider:s,model:i}=await e.json();if(!r||!s||!i)return(0,c.YS)("invalid params");let a={};switch(s){case"openai":t=I.N.image(i),a={openai:{quality:"hd",style:"natural"}};break;case"replicate":t=x.image(i),a={replicate:{output_quality:90}};break;case"kling":t=y.image(i),a={kling:{}};break;default:return(0,c.YS)("invalid provider")}let{images:n,warnings:l}=await (0,o.gr)({model:t,prompt:r,n:1,providerOptions:a});if(l.length>0)return console.log("gen images warnings:",s,l),(0,c.YS)("gen images failed");let h=(0,v.m)(),d=(0,u.YJ)(),m=await Promise.all(n.map(async(e,t)=>{let r=`${s}_image_${d}_${t}.png`,i=`shipany/${r}`,a=Buffer.from(e.base64,"base64");try{return{...await h.uploadFile({body:a,key:i,contentType:"image/png",disposition:"inline"}),provider:s,filename:r}}catch(e){return console.log("upload file failed:",e),{provider:s,filename:r}}}));return(0,c.DQ)(m)}catch(e){return console.log("gen image failed:",e),(0,c.YS)("gen image failed")}}let q=new i.AppRouteRouteModule({definition:{kind:a.RouteKind.APP_ROUTE,page:"/api/demo/gen-image/route",pathname:"/api/demo/gen-image",filename:"route",bundlePath:"app/api/demo/gen-image/route"},resolvedPagePath:"/Users/<USER>/My-Project/fluxkrea_test/app/api/demo/gen-image/route.ts",nextConfigOutput:"standalone",userland:s}),{workAsyncStorage:O,workUnitAsyncStorage:K,serverHooks:A}=q;function L(){return(0,n.patchFetch)({workAsyncStorage:O,workUnitAsyncStorage:K})}},29021:e=>{"use strict";e.exports=require("fs")},29042:(e,t,r)=>{"use strict";function s(e){return n(0,"ok",e||[])}function i(){return n(0,"ok")}function a(e){return n(-1,e)}function n(e,t,r){let s={code:e,message:t,data:r};return r&&(s.data=r),Response.json(s)}r.d(t,{DQ:()=>s,YS:()=>a,j7:()=>n,rn:()=>i})},29294:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-async-storage.external.js")},29452:(e,t,r)=>{"use strict";r.d(t,{YJ:()=>a,ZK:()=>o,f1:()=>n});var s=r(19867),i=r(58246);function a(){return(0,i.A)()}function n(e){let t="ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789",r="",s=t.length;for(let i=0;i<e;i++)r+=t[Math.floor(Math.random()*s)];return r}function o(){return new s.F({workerId:1}).NextId().toString()}},33873:e=>{"use strict";e.exports=require("path")},37198:(e,t)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),t.SnowflakeIdv1=void 0;class r{constructor(e){if(void 0===e.workerId)throw Error("lost WorkerId");(!e.baseTime||e.baseTime<0)&&(e.baseTime=15778368e5),(!e.workerIdBitLength||e.workerIdBitLength<0)&&(e.workerIdBitLength=6),(!e.seqBitLength||e.seqBitLength<0)&&(e.seqBitLength=6),(void 0==e.maxSeqNumber||e.maxSeqNumber<=0)&&(e.maxSeqNumber=63),(void 0==e.minSeqNumber||e.minSeqNumber<0)&&(e.minSeqNumber=5),(void 0==e.topOverCostCount||e.topOverCostCount<0)&&(e.topOverCostCount=2e3),2!==e.method?e.method=1:e.method=2,this.Method=BigInt(e.method),this.BaseTime=BigInt(e.baseTime),this.WorkerId=BigInt(e.workerId),this.WorkerIdBitLength=BigInt(e.workerIdBitLength),this.SeqBitLength=BigInt(e.seqBitLength),this.MaxSeqNumber=BigInt(e.maxSeqNumber),this.MinSeqNumber=BigInt(e.minSeqNumber),this.TopOverCostCount=BigInt(e.topOverCostCount);let t=this.WorkerIdBitLength+this.SeqBitLength,r=this.MinSeqNumber;this._TimestampShift=t,this._CurrentSeqNumber=r,this._LastTimeTick=BigInt(0),this._TurnBackTimeTick=BigInt(0),this._TurnBackIndex=0,this._IsOverCost=!1,this._OverCostCountInOneTerm=0}BeginOverCostAction(e){}EndOverCostAction(e){}BeginTurnBackAction(e){}EndTurnBackAction(e){}NextOverCostId(){let e=this.GetCurrentTimeTick();return e>this._LastTimeTick?(this.EndOverCostAction(e),this._LastTimeTick=e,this._CurrentSeqNumber=this.MinSeqNumber,this._IsOverCost=!1,this._OverCostCountInOneTerm=0):this._OverCostCountInOneTerm>=this.TopOverCostCount?(this.EndOverCostAction(e),this._LastTimeTick=this.GetNextTimeTick(),this._CurrentSeqNumber=this.MinSeqNumber,this._IsOverCost=!1,this._OverCostCountInOneTerm=0):this._CurrentSeqNumber>this.MaxSeqNumber&&(this._LastTimeTick++,this._CurrentSeqNumber=this.MinSeqNumber,this._IsOverCost=!0,this._OverCostCountInOneTerm++),this.CalcId(this._LastTimeTick)}NextNormalId(){let e=this.GetCurrentTimeTick();return e<this._LastTimeTick?(this._TurnBackTimeTick<1&&(this._TurnBackTimeTick=this._LastTimeTick-BigInt(1),this._TurnBackIndex++,this._TurnBackIndex>4&&(this._TurnBackIndex=1),this.BeginTurnBackAction(this._TurnBackTimeTick)),this.CalcTurnBackId(this._TurnBackTimeTick)):((this._TurnBackTimeTick>0&&(this.EndTurnBackAction(this._TurnBackTimeTick),this._TurnBackTimeTick=BigInt(0)),e>this._LastTimeTick)?(this._LastTimeTick=e,this._CurrentSeqNumber=this.MinSeqNumber):this._CurrentSeqNumber>this.MaxSeqNumber&&(this.BeginOverCostAction(e),this._LastTimeTick++,this._CurrentSeqNumber=this.MinSeqNumber,this._IsOverCost=!0,this._OverCostCountInOneTerm=1),this.CalcId(this._LastTimeTick))}CalcId(e){let t=BigInt(e<<this._TimestampShift)+BigInt(this.WorkerId<<this.SeqBitLength)+BigInt(this._CurrentSeqNumber);return this._CurrentSeqNumber++,t}CalcTurnBackId(e){let t=BigInt(e<<this._TimestampShift)+BigInt(this.WorkerId<<this.SeqBitLength)+BigInt(this._TurnBackIndex);return this._TurnBackTimeTick--,t}GetCurrentTimeTick(){return BigInt(new Date().valueOf())-this.BaseTime}GetNextTimeTick(){let e=this.GetCurrentTimeTick();for(;e<=this._LastTimeTick;)e=this.GetCurrentTimeTick();return e}NextNumber(){if(this._IsOverCost){let e=this.NextOverCostId();if(e>=9007199254740992n)throw Error(`${e.toString()} over max of Number 9007199254740992`);return parseInt(e.toString())}{let e=this.NextNormalId();if(e>=9007199254740992n)throw Error(`${e.toString()} over max of Number 9007199254740992`);return parseInt(e.toString())}}NextId(){if(this._IsOverCost){let e=this.NextOverCostId();return e>=9007199254740992n?e:parseInt(e.toString())}{let e=this.NextNormalId();return e>=9007199254740992n?e:parseInt(e.toString())}}NextBigId(){return this._IsOverCost?this.NextOverCostId():this.NextNormalId()}}t.SnowflakeIdv1=r},37423:(e,t,r)=>{"use strict";r.d(t,{m:()=>a});var s=r(91043),i=r(43637);function a(e){return new n(e)}class n{constructor(e){this.s3=new s.S3Client({endpoint:e?.endpoint||process.env.STORAGE_ENDPOINT||"",region:e?.region||process.env.STORAGE_REGION||"auto",credentials:{accessKeyId:e?.accessKey||process.env.STORAGE_ACCESS_KEY||"",secretAccessKey:e?.secretKey||process.env.STORAGE_SECRET_KEY||""}})}async uploadFile({body:e,key:t,contentType:r,bucket:s,onProgress:a,disposition:n="inline"}){if(s||(s=process.env.STORAGE_BUCKET||""),!s)throw Error("Bucket is required");let o=new i._({client:this.s3,params:{Bucket:s,Key:t,Body:e,ContentDisposition:n,...r&&{ContentType:r}}});a&&o.on("httpUploadProgress",e=>{a((e.loaded||0)/(e.total||1)*100)});let c=await o.done();return{location:c.Location,bucket:c.Bucket,key:c.Key,filename:c.Key?.split("/").pop(),url:process.env.STORAGE_DOMAIN?`${process.env.STORAGE_DOMAIN}/${s}/${c.Key}`:c.Location}}async downloadAndUpload({url:e,key:t,bucket:r,contentType:s,disposition:i="inline"}){let a=await fetch(e);if(!a.ok)throw Error(`HTTP error! status: ${a.status}`);if(!a.body)throw Error("No body in response");let n=await a.arrayBuffer(),o=Buffer.from(n);return this.uploadFile({body:o,key:t,bucket:r,contentType:s,disposition:i})}}},44870:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-route.runtime.prod.js")},55511:e=>{"use strict";e.exports=require("crypto")},55591:e=>{"use strict";e.exports=require("https")},57075:e=>{"use strict";e.exports=require("node:stream")},58246:(e,t,r)=>{"use strict";r.d(t,{A:()=>c});var s=r(55511);let i={randomUUID:s.randomUUID},a=new Uint8Array(256),n=a.length,o=[];for(let e=0;e<256;++e)o.push((e+256).toString(16).slice(1));let c=function(e,t,r){if(i.randomUUID&&!t&&!e)return i.randomUUID();let c=(e=e||{}).random??e.rng?.()??(n>a.length-16&&((0,s.randomFillSync)(a),n=0),a.slice(n,n+=16));if(c.length<16)throw Error("Random bytes length must be >= 16");if(c[6]=15&c[6]|64,c[8]=63&c[8]|128,t){if((r=r||0)<0||r+16>t.length)throw RangeError(`UUID byte range ${r}:${r+15} is out of buffer bounds`);for(let e=0;e<16;++e)t[r+e]=c[e];return t}return function(e,t=0){return(o[e[t+0]]+o[e[t+1]]+o[e[t+2]]+o[e[t+3]]+"-"+o[e[t+4]]+o[e[t+5]]+"-"+o[e[t+6]]+o[e[t+7]]+"-"+o[e[t+8]]+o[e[t+9]]+"-"+o[e[t+10]]+o[e[t+11]]+o[e[t+12]]+o[e[t+13]]+o[e[t+14]]+o[e[t+15]]).toLowerCase()}(c)}},63033:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-unit-async-storage.external.js")},73496:e=>{"use strict";e.exports=require("http2")},79428:e=>{"use strict";e.exports=require("buffer")},81630:e=>{"use strict";e.exports=require("http")},91043:e=>{"use strict";e.exports=require("@aws-sdk/client-s3")},94735:e=>{"use strict";e.exports=require("events")},94813:()=>{}};var t=require("../../../../webpack-runtime.js");t.C(e);var r=e=>t(t.s=e),s=t.X(0,[5673,6329,2051],()=>r(28467));module.exports=s})();