(()=>{var e={};e.id=893,e.ids=[893],e.modules={10846:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},13373:()=>{},29042:(e,t,r)=>{"use strict";function o(e){return a(0,"ok",e||[])}function n(){return a(0,"ok")}function s(e){return a(-1,e)}function a(e,t,r){let o={code:e,message:t,data:r};return r&&(o.data=r),Response.json(o)}r.d(t,{DQ:()=>o,YS:()=>s,j7:()=>a,rn:()=>n})},29294:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-async-storage.external.js")},44870:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-route.runtime.prod.js")},63033:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-unit-async-storage.external.js")},79341:(e,t,r)=>{"use strict";e.exports=r(44870)},94813:()=>{},96826:(e,t,r)=>{"use strict";r.r(t),r.d(t,{patchFetch:()=>x,routeModule:()=>d,serverHooks:()=>l,workAsyncStorage:()=>p,workUnitAsyncStorage:()=>c});var o={};r.r(o),r.d(o,{POST:()=>u});var n=r(79341),s=r(91514),a=r(95673),i=r(29042);async function u(e){try{let{url:t,fileName:r}=await e.json();if(!t)return(0,i.YS)("URL is required");console.log(`[Download Proxy] Downloading: ${t}`);let o=await fetch(t);if(!o.ok)throw Error(`HTTP error! status: ${o.status}`);let n=o.headers.get("content-type")||"application/octet-stream",s=new Headers({"Content-Type":n,"Content-Disposition":`attachment; filename="${r||"download"}"`,"Cache-Control":"no-cache"});return new Response(o.body,{status:200,headers:s})}catch(e){return console.error("[Download Proxy] Error:",e),(0,i.YS)("Download failed")}}let d=new n.AppRouteRouteModule({definition:{kind:s.RouteKind.APP_ROUTE,page:"/api/download-proxy/route",pathname:"/api/download-proxy",filename:"route",bundlePath:"app/api/download-proxy/route"},resolvedPagePath:"/Users/<USER>/My-Project/fluxkrea_test/app/api/download-proxy/route.ts",nextConfigOutput:"standalone",userland:o}),{workAsyncStorage:p,workUnitAsyncStorage:c,serverHooks:l}=d;function x(){return(0,a.patchFetch)({workAsyncStorage:p,workUnitAsyncStorage:c})}}};var t=require("../../../webpack-runtime.js");t.C(e);var r=e=>t(t.s=e),o=t.X(0,[5673],()=>r(96826));module.exports=o})();