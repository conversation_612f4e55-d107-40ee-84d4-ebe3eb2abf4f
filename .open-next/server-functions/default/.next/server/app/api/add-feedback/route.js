(()=>{var e={};e.id=301,e.ids=[301],e.modules={3295:e=>{"use strict";e.exports=require("next/dist/server/app-render/after-task-async-storage.external.js")},4573:e=>{"use strict";e.exports=require("node:buffer")},10846:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},11997:e=>{"use strict";e.exports=require("punycode")},13373:()=>{},19121:e=>{"use strict";e.exports=require("next/dist/server/app-render/action-async-storage.external.js")},26820:(e,t,r)=>{"use strict";r.r(t),r.d(t,{"00880263a376180560adeff1fdd2511a7543726d4a":()=>a.T});var a=r(39201)},27910:e=>{"use strict";e.exports=require("stream")},28432:(e,t,r)=>{"use strict";r.d(t,{j2:()=>m,Y9:()=>l});var a=r(62364),i=r(36157),s=r(72648),n=r(39201),u=r(4961),o=r(29452),c=r(60736);let d=[];d.push((0,i.A)({id:"google-one-tap",name:"google-one-tap",credentials:{credential:{type:"text"}},async authorize(e,t){let r=e.credential,a=await fetch("https://oauth2.googleapis.com/tokeninfo?id_token="+r);if(!a.ok)return console.log("Failed to verify token"),null;let i=await a.json();if(!i)return console.log("invalid payload from token"),null;let{email:s,sub:n,given_name:u,family_name:o,email_verified:c,picture:d}=i;return s?{id:n,name:[u,o].join(" "),email:s,image:d,emailVerified:c?new Date:null}:(console.log("invalid email in payload"),null)}})),process.env.AUTH_GOOGLE_ID&&process.env.AUTH_GOOGLE_SECRET&&d.push((0,s.A)({clientId:process.env.AUTH_GOOGLE_ID,clientSecret:process.env.AUTH_GOOGLE_SECRET,authorization:{params:{prompt:"consent",access_type:"offline",response_type:"code"}}})),d.map(e=>{if("function"!=typeof e)return{id:e.id,name:e.name};{let t=e();return{id:t.id,name:t.name}}}).filter(e=>"google-one-tap"!==e.id);let{handlers:l,signIn:p,signOut:f,auth:m}=(0,a.Ay)({providers:d,pages:{signIn:"/auth/signin"},callbacks:{signIn:async({user:e,account:t,profile:r,email:a,credentials:i})=>!0,redirect:async({url:e,baseUrl:t})=>e.startsWith("/")?`${t}${e}`:new URL(e).origin===t?e:t,session:async({session:e,token:t,user:r})=>(t&&t.user&&t.user&&(e.user=t.user),e),async jwt({token:e,user:t,account:r}){try{if(t&&t.email&&r){let a={uuid:(0,o.YJ)(),email:t.email,nickname:t.name||"",avatar_url:t.image||"",signin_type:r.type,signin_provider:r.provider,signin_openid:r.providerAccountId,created_at:(0,u.iq)(),signin_ip:await (0,n.T)()};try{let t=await (0,c.$c)(a);e.user={uuid:t.uuid,email:t.email,nickname:t.nickname,avatar_url:t.avatar_url,created_at:t.created_at}}catch(e){console.error("save user failed:",e)}}return e}catch(t){return console.error("jwt callback error:",t),e}}}})},29042:(e,t,r)=>{"use strict";function a(e){return n(0,"ok",e||[])}function i(){return n(0,"ok")}function s(e){return n(-1,e)}function n(e,t,r){let a={code:e,message:t,data:r};return r&&(a.data=r),Response.json(a)}r.d(t,{DQ:()=>a,YS:()=>s,j7:()=>n,rn:()=>i})},29294:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-async-storage.external.js")},34631:e=>{"use strict";e.exports=require("tls")},37318:(e,t,r)=>{"use strict";r.d(t,{$0:()=>s,NW:()=>n,lU:()=>u});var a=r(4995),i=r(35050);async function s(e){let t=(0,a.A)(),{data:r,error:i}=await t.from("feedbacks").insert(e);if(i)throw i;return r}async function n(e=1,t=50){e<1&&(e=1),t<=0&&(t=50);let r=(e-1)*t,s=(0,a.A)(),{data:u,error:o}=await s.from("feedbacks").select("*").order("created_at",{ascending:!1}).range(r,r+t-1);if(o||!u||0===u.length)return[];let c=Array.from(new Set(u.map(e=>e.user_uuid))),d=await (0,i.QZ)(c);return u.map(e=>{let t=d.find(t=>t.uuid===e.user_uuid);return{...e,user:t}})}async function u(){let e=(0,a.A)(),{data:t,error:r}=await e.from("feedbacks").select("count",{count:"exact"});if(!r)return t[0].count}},39201:(e,t,r)=>{"use strict";r.d(t,{T:()=>s});var a=r(72784);r(97468);var i=r(62225);async function s(){let e=await (0,i.b3)();return e.get("cf-connecting-ip")||e.get("x-real-ip")||(e.get("x-forwarded-for")||"127.0.0.1").split(",")[0]}(0,r(78564).D)([s]),(0,a.A)(s,"00880263a376180560adeff1fdd2511a7543726d4a",null)},44870:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-route.runtime.prod.js")},55511:e=>{"use strict";e.exports=require("crypto")},55591:e=>{"use strict";e.exports=require("https")},57975:e=>{"use strict";e.exports=require("node:util")},58568:(e,t,r)=>{"use strict";r.r(t),r.d(t,{patchFetch:()=>g,routeModule:()=>p,serverHooks:()=>y,workAsyncStorage:()=>f,workUnitAsyncStorage:()=>m});var a={};r.r(a),r.d(a,{POST:()=>l});var i=r(79341),s=r(91514),n=r(95673),u=r(29042),o=r(4961),c=r(60736),d=r(37318);async function l(e){try{let{content:t,rating:r}=await e.json();if(!t)return(0,u.YS)("invalid params");let a={user_uuid:await (0,c.TG)(),content:t,rating:r||5,created_at:(0,o.iq)(),status:"created"};return await (0,d.$0)(a),(0,u.DQ)(a)}catch(e){return console.log("add feedback failed",e),(0,u.YS)("add feedback failed")}}let p=new i.AppRouteRouteModule({definition:{kind:s.RouteKind.APP_ROUTE,page:"/api/add-feedback/route",pathname:"/api/add-feedback",filename:"route",bundlePath:"app/api/add-feedback/route"},resolvedPagePath:"/Users/<USER>/My-Project/fluxkrea_test/app/api/add-feedback/route.ts",nextConfigOutput:"standalone",userland:a}),{workAsyncStorage:f,workUnitAsyncStorage:m,serverHooks:y}=p;function g(){return(0,n.patchFetch)({workAsyncStorage:f,workUnitAsyncStorage:m})}},60736:(e,t,r)=>{"use strict";r.d(t,{$c:()=>c,TG:()=>d,qo:()=>p,ug:()=>f});var a=r(87830),i=r(35050),s=r(28432),n=r(4961),u=r(78054),o=r(62225);async function c(e){try{let t=await (0,i.Gs)(e.email);return t?(e.id=t.id,e.uuid=t.uuid,e.created_at=t.created_at):(await (0,i.HW)(e),await (0,a.d_)({user_uuid:e.uuid||"",trans_type:a.H3.NewUser,credits:a.rN.NewUserGet,expired_at:(0,n.MI)()})),e}catch(e){throw console.log("save user failed: ",e),e}}async function d(){let e="",t=await l();if(t&&t.startsWith("sk-"))return await (0,u.zz)(t)||"";let r=await (0,s.j2)();return r&&r.user&&r.user.uuid&&(e=r.user.uuid),e}async function l(){let e=(await (0,o.b3)()).get("Authorization");return e?e.replace("Bearer ",""):""}async function p(){let e="",t=await (0,s.j2)();return t&&t.user&&t.user.email&&(e=t.user.email),e}async function f(){let e=await d();if(e)return await (0,i.pX)(e)}},63033:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-unit-async-storage.external.js")},74075:e=>{"use strict";e.exports=require("zlib")},77598:e=>{"use strict";e.exports=require("node:crypto")},78054:(e,t,r)=>{"use strict";r.d(t,{N1:()=>s,ai:()=>i,ox:()=>n,zz:()=>u});var a=r(4995),i=function(e){return e.Created="created",e.Deleted="deleted",e}({});async function s(e){let t=(0,a.A)(),{data:r,error:i}=await t.from("apikeys").insert(e);if(i)throw i;return r}async function n(e,t=1,r=50){let i=(t-1)*r,s=(0,a.A)(),{data:u,error:o}=await s.from("apikeys").select("*").eq("user_uuid",e).neq("status","deleted").order("created_at",{ascending:!1}).range(i,i+r-1);if(!o)return u}async function u(e){let t=(0,a.A)(),{data:r,error:i}=await t.from("apikeys").select("user_uuid").eq("api_key",e).eq("status","created").limit(1).single();if(!i)return r?.user_uuid}},79341:(e,t,r)=>{"use strict";e.exports=r(44870)},79428:e=>{"use strict";e.exports=require("buffer")},79551:e=>{"use strict";e.exports=require("url")},81630:e=>{"use strict";e.exports=require("http")},91645:e=>{"use strict";e.exports=require("net")},94735:e=>{"use strict";e.exports=require("events")},94813:()=>{}};var t=require("../../../webpack-runtime.js");t.C(e);var r=e=>t(t.s=e),a=t.X(0,[5673,7711,8734,5327,2041,4524],()=>r(58568));module.exports=a})();