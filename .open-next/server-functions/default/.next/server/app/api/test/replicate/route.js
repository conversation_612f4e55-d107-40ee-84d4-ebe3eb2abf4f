(()=>{var e={};e.id=6142,e.ids=[6142],e.modules={10846:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},13373:()=>{},19867:(e,t,r)=>{"use strict";t.F=void 0;let i=r(37198);Object.defineProperty(t,"F",{enumerable:!0,get:function(){return i.SnowflakeIdv1}})},20248:(e,t,r)=>{"use strict";r.d(t,{N:()=>n,X:()=>a});var i=r(6158),o=r.n(i),s=r(29452);class n{constructor(){let e=process.env.REPLICATE_API_TOKEN;if(!e)throw Error("REPLICATE_API_TOKEN environment variable is required");this.client=new(o())({auth:e})}async generateImage(e){let t=(0,s.YJ)();try{console.log("[Replicate Provider] ========== STARTING IMAGE GENERATION =========="),console.log(`[Replicate Provider] Request ID: ${t}`),console.log("[Replicate Provider] Full request:",JSON.stringify(e,null,2));let r={prompt:e.prompt};e.options?.aspect_ratio&&(r.aspect_ratio=e.options.aspect_ratio),e.options?.image&&(r.image=e.options.image),e.options?.prompt_strength!==void 0&&(r.prompt_strength=e.options.prompt_strength),e.options?.num_outputs!==void 0&&(r.num_outputs=e.options.num_outputs),e.options?.num_inference_steps!==void 0&&(r.num_inference_steps=e.options.num_inference_steps),e.options?.guidance!==void 0&&(r.guidance=e.options.guidance),e.options?.seed!==void 0&&(r.seed=e.options.seed),e.options?.output_format&&(r.output_format=e.options.output_format),e.options?.output_quality!==void 0&&(r.output_quality=e.options.output_quality),e.options?.disable_safety_checker!==void 0&&(r.disable_safety_checker=e.options.disable_safety_checker),e.options?.go_fast!==void 0&&(r.go_fast=e.options.go_fast),e.options?.megapixels&&(r.megapixels=e.options.megapixels),console.log("[Replicate Provider] ========== CALLING REPLICATE API =========="),console.log(`[Replicate Provider] Model: ${e.model}`),console.log("[Replicate Provider] Input parameters:",JSON.stringify(r,null,2));let i=await this.client.run(e.model,{input:r});if(console.log("[Replicate Provider] ========== REPLICATE API RESPONSE =========="),console.log("[Replicate Provider] Raw output type:",typeof i),console.log("[Replicate Provider] Raw output:",JSON.stringify(i,null,2)),console.log("[Replicate Provider] ========== PROCESSING OUTPUT =========="),console.log("[Replicate Provider] Output is array:",Array.isArray(i)),console.log("[Replicate Provider] Output length:",Array.isArray(i)?i.length:"N/A"),Array.isArray(i)&&i.length>0){console.log("[Replicate Provider] Processing array output...");let r=i.map((e,t)=>{if(console.log(`[Replicate Provider] Item ${t}:`,typeof e,e),"string"==typeof e)return console.log(`[Replicate Provider] Item ${t} is string URL:`,e),e;if(e&&"object"==typeof e){if("function"==typeof e.url){let r=e.url();return console.log(`[Replicate Provider] Item ${t} has url() method, result:`,r),r.toString()}if("url"in e&&"string"==typeof e.url)return console.log(`[Replicate Provider] Item ${t} is object with URL property:`,e.url),e.url}return console.log(`[Replicate Provider] Item ${t} is invalid, skipping`),null}).filter(Boolean);console.log("[Replicate Provider] Extracted URLs:",r);let o={id:t,status:"success",urls:r,usage:{credits_consumed:this.calculateCredits(e.model)}};return console.log("[Replicate Provider] ========== FINAL SUCCESS RESULT =========="),console.log("[Replicate Provider] Result:",JSON.stringify(o,null,2)),o}throw console.log("[Replicate Provider] ========== OUTPUT FORMAT ERROR =========="),console.log("[Replicate Provider] Expected array with length > 0, got:",typeof i,i),Error(`Invalid output format from Replicate API. Expected array, got: ${typeof i}`)}catch(r){console.log("[Replicate Provider] ========== ERROR OCCURRED =========="),console.error("[Replicate Provider] Error type:",typeof r),console.error("[Replicate Provider] Error message:",r instanceof Error?r.message:"Unknown error"),console.error("[Replicate Provider] Full error:",r),console.error("[Replicate Provider] Error stack:",r instanceof Error?r.stack:"No stack");let e={id:t,status:"failed",error:r instanceof Error?r.message:"Unknown error",usage:{credits_consumed:0}};return console.log("[Replicate Provider] ========== FINAL ERROR RESULT =========="),console.log("[Replicate Provider] Error result:",JSON.stringify(e,null,2)),e}}calculateCredits(e){return"black-forest-labs/flux-krea-dev"===e?25:20}isModelSupported(e){return["black-forest-labs/flux-krea-dev"].includes(e)}getSupportedModels(){return["black-forest-labs/flux-krea-dev"]}}let a=new n},29042:(e,t,r)=>{"use strict";function i(e){return n(0,"ok",e||[])}function o(){return n(0,"ok")}function s(e){return n(-1,e)}function n(e,t,r){let i={code:e,message:t,data:r};return r&&(i.data=r),Response.json(i)}r.d(t,{DQ:()=>i,YS:()=>s,j7:()=>n,rn:()=>o})},29294:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-async-storage.external.js")},29345:e=>{function t(e){var t=Error("Cannot find module '"+e+"'");throw t.code="MODULE_NOT_FOUND",t}t.keys=()=>[],t.resolve=t,t.id=29345,e.exports=t},29452:(e,t,r)=>{"use strict";r.d(t,{YJ:()=>s,ZK:()=>a,f1:()=>n});var i=r(19867),o=r(58246);function s(){return(0,o.A)()}function n(e){let t="ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789",r="",i=t.length;for(let o=0;o<e;o++)r+=t[Math.floor(Math.random()*i)];return r}function a(){return new i.F({workerId:1}).NextId().toString()}},37198:(e,t)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),t.SnowflakeIdv1=void 0;class r{constructor(e){if(void 0===e.workerId)throw Error("lost WorkerId");(!e.baseTime||e.baseTime<0)&&(e.baseTime=15778368e5),(!e.workerIdBitLength||e.workerIdBitLength<0)&&(e.workerIdBitLength=6),(!e.seqBitLength||e.seqBitLength<0)&&(e.seqBitLength=6),(void 0==e.maxSeqNumber||e.maxSeqNumber<=0)&&(e.maxSeqNumber=63),(void 0==e.minSeqNumber||e.minSeqNumber<0)&&(e.minSeqNumber=5),(void 0==e.topOverCostCount||e.topOverCostCount<0)&&(e.topOverCostCount=2e3),2!==e.method?e.method=1:e.method=2,this.Method=BigInt(e.method),this.BaseTime=BigInt(e.baseTime),this.WorkerId=BigInt(e.workerId),this.WorkerIdBitLength=BigInt(e.workerIdBitLength),this.SeqBitLength=BigInt(e.seqBitLength),this.MaxSeqNumber=BigInt(e.maxSeqNumber),this.MinSeqNumber=BigInt(e.minSeqNumber),this.TopOverCostCount=BigInt(e.topOverCostCount);let t=this.WorkerIdBitLength+this.SeqBitLength,r=this.MinSeqNumber;this._TimestampShift=t,this._CurrentSeqNumber=r,this._LastTimeTick=BigInt(0),this._TurnBackTimeTick=BigInt(0),this._TurnBackIndex=0,this._IsOverCost=!1,this._OverCostCountInOneTerm=0}BeginOverCostAction(e){}EndOverCostAction(e){}BeginTurnBackAction(e){}EndTurnBackAction(e){}NextOverCostId(){let e=this.GetCurrentTimeTick();return e>this._LastTimeTick?(this.EndOverCostAction(e),this._LastTimeTick=e,this._CurrentSeqNumber=this.MinSeqNumber,this._IsOverCost=!1,this._OverCostCountInOneTerm=0):this._OverCostCountInOneTerm>=this.TopOverCostCount?(this.EndOverCostAction(e),this._LastTimeTick=this.GetNextTimeTick(),this._CurrentSeqNumber=this.MinSeqNumber,this._IsOverCost=!1,this._OverCostCountInOneTerm=0):this._CurrentSeqNumber>this.MaxSeqNumber&&(this._LastTimeTick++,this._CurrentSeqNumber=this.MinSeqNumber,this._IsOverCost=!0,this._OverCostCountInOneTerm++),this.CalcId(this._LastTimeTick)}NextNormalId(){let e=this.GetCurrentTimeTick();return e<this._LastTimeTick?(this._TurnBackTimeTick<1&&(this._TurnBackTimeTick=this._LastTimeTick-BigInt(1),this._TurnBackIndex++,this._TurnBackIndex>4&&(this._TurnBackIndex=1),this.BeginTurnBackAction(this._TurnBackTimeTick)),this.CalcTurnBackId(this._TurnBackTimeTick)):((this._TurnBackTimeTick>0&&(this.EndTurnBackAction(this._TurnBackTimeTick),this._TurnBackTimeTick=BigInt(0)),e>this._LastTimeTick)?(this._LastTimeTick=e,this._CurrentSeqNumber=this.MinSeqNumber):this._CurrentSeqNumber>this.MaxSeqNumber&&(this.BeginOverCostAction(e),this._LastTimeTick++,this._CurrentSeqNumber=this.MinSeqNumber,this._IsOverCost=!0,this._OverCostCountInOneTerm=1),this.CalcId(this._LastTimeTick))}CalcId(e){let t=BigInt(e<<this._TimestampShift)+BigInt(this.WorkerId<<this.SeqBitLength)+BigInt(this._CurrentSeqNumber);return this._CurrentSeqNumber++,t}CalcTurnBackId(e){let t=BigInt(e<<this._TimestampShift)+BigInt(this.WorkerId<<this.SeqBitLength)+BigInt(this._TurnBackIndex);return this._TurnBackTimeTick--,t}GetCurrentTimeTick(){return BigInt(new Date().valueOf())-this.BaseTime}GetNextTimeTick(){let e=this.GetCurrentTimeTick();for(;e<=this._LastTimeTick;)e=this.GetCurrentTimeTick();return e}NextNumber(){if(this._IsOverCost){let e=this.NextOverCostId();if(e>=9007199254740992n)throw Error(`${e.toString()} over max of Number 9007199254740992`);return parseInt(e.toString())}{let e=this.NextNormalId();if(e>=9007199254740992n)throw Error(`${e.toString()} over max of Number 9007199254740992`);return parseInt(e.toString())}}NextId(){if(this._IsOverCost){let e=this.NextOverCostId();return e>=9007199254740992n?e:parseInt(e.toString())}{let e=this.NextNormalId();return e>=9007199254740992n?e:parseInt(e.toString())}}NextBigId(){return this._IsOverCost?this.NextOverCostId():this.NextNormalId()}}t.SnowflakeIdv1=r},44870:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-route.runtime.prod.js")},55511:e=>{"use strict";e.exports=require("crypto")},55742:(e,t,r)=>{"use strict";r.r(t),r.d(t,{patchFetch:()=>m,routeModule:()=>c,serverHooks:()=>h,workAsyncStorage:()=>p,workUnitAsyncStorage:()=>d});var i={};r.r(i),r.d(i,{POST:()=>l});var o=r(79341),s=r(91514),n=r(95673),a=r(29042),u=r(20248);async function l(e){try{let{prompt:t}=await e.json();if(!t)return(0,a.YS)("Prompt is required");console.log(`[Test Replicate] Testing with prompt: ${t}`);let r=await u.X.generateImage({model:"black-forest-labs/flux-krea-dev",prompt:t,options:{output_quality:90,output_format:"webp"}});return console.log("[Test Replicate] Response:",r),(0,a.DQ)({success:!0,response:r})}catch(e){return console.error("[Test Replicate] Error:",e),(0,a.YS)(e instanceof Error?e.message:"Unknown error")}}let c=new o.AppRouteRouteModule({definition:{kind:s.RouteKind.APP_ROUTE,page:"/api/test/replicate/route",pathname:"/api/test/replicate",filename:"route",bundlePath:"app/api/test/replicate/route"},resolvedPagePath:"/Users/<USER>/My-Project/fluxkrea_test/app/api/test/replicate/route.ts",nextConfigOutput:"standalone",userland:i}),{workAsyncStorage:p,workUnitAsyncStorage:d,serverHooks:h}=c;function m(){return(0,n.patchFetch)({workAsyncStorage:p,workUnitAsyncStorage:d})}},58246:(e,t,r)=>{"use strict";r.d(t,{A:()=>u});var i=r(55511);let o={randomUUID:i.randomUUID},s=new Uint8Array(256),n=s.length,a=[];for(let e=0;e<256;++e)a.push((e+256).toString(16).slice(1));let u=function(e,t,r){if(o.randomUUID&&!t&&!e)return o.randomUUID();let u=(e=e||{}).random??e.rng?.()??(n>s.length-16&&((0,i.randomFillSync)(s),n=0),s.slice(n,n+=16));if(u.length<16)throw Error("Random bytes length must be >= 16");if(u[6]=15&u[6]|64,u[8]=63&u[8]|128,t){if((r=r||0)<0||r+16>t.length)throw RangeError(`UUID byte range ${r}:${r+15} is out of buffer bounds`);for(let e=0;e<16;++e)t[r+e]=u[e];return t}return function(e,t=0){return(a[e[t+0]]+a[e[t+1]]+a[e[t+2]]+a[e[t+3]]+"-"+a[e[t+4]]+a[e[t+5]]+"-"+a[e[t+6]]+a[e[t+7]]+"-"+a[e[t+8]]+a[e[t+9]]+"-"+a[e[t+10]]+a[e[t+11]]+a[e[t+12]]+a[e[t+13]]+a[e[t+14]]+a[e[t+15]]).toLowerCase()}(u)}},63033:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-unit-async-storage.external.js")},79341:(e,t,r)=>{"use strict";e.exports=r(44870)},94813:()=>{}};var t=require("../../../../webpack-runtime.js");t.C(e);var r=e=>t(t.s=e),i=t.X(0,[5673,6158],()=>r(55742));module.exports=i})();