(()=>{var e={};e.id=5591,e.ids=[5591],e.modules={4961:(e,t,r)=>{"use strict";function i(){return new Date().toISOString()}r.d(t,{MI:()=>s,iq:()=>i});let s=()=>{let e=new Date,t=new Date(e);return t.setFullYear(e.getFullYear()+1),t.toISOString()}},4995:(e,t,r)=>{"use strict";r.d(t,{A:()=>s});var i=r(5327);function s(){let e=process.env.SUPABASE_URL||"",t=process.env.SUPABASE_ANON_KEY||"";if(process.env.SUPABASE_SERVICE_ROLE_KEY&&(t=process.env.SUPABASE_SERVICE_ROLE_KEY),!e||!t)throw Error("Supabase URL or key is not set");return(0,i.UU)(e,t)}},10846:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},11997:e=>{"use strict";e.exports=require("punycode")},19867:(e,t,r)=>{"use strict";t.F=void 0;let i=r(37198);Object.defineProperty(t,"F",{enumerable:!0,get:function(){return i.SnowflakeIdv1}})},21573:(e,t,r)=>{"use strict";r.r(t),r.d(t,{patchFetch:()=>l,routeModule:()=>c,serverHooks:()=>T,workAsyncStorage:()=>m,workUnitAsyncStorage:()=>d});var i={};r.r(i),r.d(i,{POST:()=>h});var s=r(79341),n=r(91514),o=r(95673),u=r(29042),a=r(99353);async function h(e){try{let{prompt:t,options:r}=await e.json();if(!t)return(0,u.YS)("Prompt is required");console.log(`[Test Replicate Unified] Testing with prompt: ${t}`);let i={model:"black-forest-labs/flux-krea-dev",type:"image",prompt:t,options:{output_quality:90,output_format:"webp",...r}},s=new a.U;console.log("[Test Replicate Unified] Calling AI service with request:",i);let n=await s.processRequest("test-user-uuid",i);return console.log("[Test Replicate Unified] AI service response:",n),(0,u.DQ)({success:!0,response:n})}catch(e){return console.error("[Test Replicate Unified] Error:",e),(0,u.YS)(e instanceof Error?e.message:"Unknown error")}}let c=new s.AppRouteRouteModule({definition:{kind:n.RouteKind.APP_ROUTE,page:"/api/test/replicate-unified/route",pathname:"/api/test/replicate-unified",filename:"route",bundlePath:"app/api/test/replicate-unified/route"},resolvedPagePath:"/Users/<USER>/My-Project/fluxkrea_test/app/api/test/replicate-unified/route.ts",nextConfigOutput:"standalone",userland:i}),{workAsyncStorage:m,workUnitAsyncStorage:d,serverHooks:T}=c;function l(){return(0,o.patchFetch)({workAsyncStorage:m,workUnitAsyncStorage:d})}},27910:e=>{"use strict";e.exports=require("stream")},29294:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-async-storage.external.js")},29452:(e,t,r)=>{"use strict";r.d(t,{YJ:()=>n,ZK:()=>u,f1:()=>o});var i=r(19867),s=r(58246);function n(){return(0,s.A)()}function o(e){let t="ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789",r="",i=t.length;for(let s=0;s<e;s++)r+=t[Math.floor(Math.random()*i)];return r}function u(){return new i.F({workerId:1}).NextId().toString()}},34631:e=>{"use strict";e.exports=require("tls")},37198:(e,t)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),t.SnowflakeIdv1=void 0;class r{constructor(e){if(void 0===e.workerId)throw Error("lost WorkerId");(!e.baseTime||e.baseTime<0)&&(e.baseTime=15778368e5),(!e.workerIdBitLength||e.workerIdBitLength<0)&&(e.workerIdBitLength=6),(!e.seqBitLength||e.seqBitLength<0)&&(e.seqBitLength=6),(void 0==e.maxSeqNumber||e.maxSeqNumber<=0)&&(e.maxSeqNumber=63),(void 0==e.minSeqNumber||e.minSeqNumber<0)&&(e.minSeqNumber=5),(void 0==e.topOverCostCount||e.topOverCostCount<0)&&(e.topOverCostCount=2e3),2!==e.method?e.method=1:e.method=2,this.Method=BigInt(e.method),this.BaseTime=BigInt(e.baseTime),this.WorkerId=BigInt(e.workerId),this.WorkerIdBitLength=BigInt(e.workerIdBitLength),this.SeqBitLength=BigInt(e.seqBitLength),this.MaxSeqNumber=BigInt(e.maxSeqNumber),this.MinSeqNumber=BigInt(e.minSeqNumber),this.TopOverCostCount=BigInt(e.topOverCostCount);let t=this.WorkerIdBitLength+this.SeqBitLength,r=this.MinSeqNumber;this._TimestampShift=t,this._CurrentSeqNumber=r,this._LastTimeTick=BigInt(0),this._TurnBackTimeTick=BigInt(0),this._TurnBackIndex=0,this._IsOverCost=!1,this._OverCostCountInOneTerm=0}BeginOverCostAction(e){}EndOverCostAction(e){}BeginTurnBackAction(e){}EndTurnBackAction(e){}NextOverCostId(){let e=this.GetCurrentTimeTick();return e>this._LastTimeTick?(this.EndOverCostAction(e),this._LastTimeTick=e,this._CurrentSeqNumber=this.MinSeqNumber,this._IsOverCost=!1,this._OverCostCountInOneTerm=0):this._OverCostCountInOneTerm>=this.TopOverCostCount?(this.EndOverCostAction(e),this._LastTimeTick=this.GetNextTimeTick(),this._CurrentSeqNumber=this.MinSeqNumber,this._IsOverCost=!1,this._OverCostCountInOneTerm=0):this._CurrentSeqNumber>this.MaxSeqNumber&&(this._LastTimeTick++,this._CurrentSeqNumber=this.MinSeqNumber,this._IsOverCost=!0,this._OverCostCountInOneTerm++),this.CalcId(this._LastTimeTick)}NextNormalId(){let e=this.GetCurrentTimeTick();return e<this._LastTimeTick?(this._TurnBackTimeTick<1&&(this._TurnBackTimeTick=this._LastTimeTick-BigInt(1),this._TurnBackIndex++,this._TurnBackIndex>4&&(this._TurnBackIndex=1),this.BeginTurnBackAction(this._TurnBackTimeTick)),this.CalcTurnBackId(this._TurnBackTimeTick)):((this._TurnBackTimeTick>0&&(this.EndTurnBackAction(this._TurnBackTimeTick),this._TurnBackTimeTick=BigInt(0)),e>this._LastTimeTick)?(this._LastTimeTick=e,this._CurrentSeqNumber=this.MinSeqNumber):this._CurrentSeqNumber>this.MaxSeqNumber&&(this.BeginOverCostAction(e),this._LastTimeTick++,this._CurrentSeqNumber=this.MinSeqNumber,this._IsOverCost=!0,this._OverCostCountInOneTerm=1),this.CalcId(this._LastTimeTick))}CalcId(e){let t=BigInt(e<<this._TimestampShift)+BigInt(this.WorkerId<<this.SeqBitLength)+BigInt(this._CurrentSeqNumber);return this._CurrentSeqNumber++,t}CalcTurnBackId(e){let t=BigInt(e<<this._TimestampShift)+BigInt(this.WorkerId<<this.SeqBitLength)+BigInt(this._TurnBackIndex);return this._TurnBackTimeTick--,t}GetCurrentTimeTick(){return BigInt(new Date().valueOf())-this.BaseTime}GetNextTimeTick(){let e=this.GetCurrentTimeTick();for(;e<=this._LastTimeTick;)e=this.GetCurrentTimeTick();return e}NextNumber(){if(this._IsOverCost){let e=this.NextOverCostId();if(e>=9007199254740992n)throw Error(`${e.toString()} over max of Number 9007199254740992`);return parseInt(e.toString())}{let e=this.NextNormalId();if(e>=9007199254740992n)throw Error(`${e.toString()} over max of Number 9007199254740992`);return parseInt(e.toString())}}NextId(){if(this._IsOverCost){let e=this.NextOverCostId();return e>=9007199254740992n?e:parseInt(e.toString())}{let e=this.NextNormalId();return e>=9007199254740992n?e:parseInt(e.toString())}}NextBigId(){return this._IsOverCost?this.NextOverCostId():this.NextNormalId()}}t.SnowflakeIdv1=r},44870:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-route.runtime.prod.js")},45590:()=>{},52367:()=>{},55511:e=>{"use strict";e.exports=require("crypto")},55591:e=>{"use strict";e.exports=require("https")},58246:(e,t,r)=>{"use strict";r.d(t,{A:()=>a});var i=r(55511);let s={randomUUID:i.randomUUID},n=new Uint8Array(256),o=n.length,u=[];for(let e=0;e<256;++e)u.push((e+256).toString(16).slice(1));let a=function(e,t,r){if(s.randomUUID&&!t&&!e)return s.randomUUID();let a=(e=e||{}).random??e.rng?.()??(o>n.length-16&&((0,i.randomFillSync)(n),o=0),n.slice(o,o+=16));if(a.length<16)throw Error("Random bytes length must be >= 16");if(a[6]=15&a[6]|64,a[8]=63&a[8]|128,t){if((r=r||0)<0||r+16>t.length)throw RangeError(`UUID byte range ${r}:${r+15} is out of buffer bounds`);for(let e=0;e<16;++e)t[r+e]=a[e];return t}return function(e,t=0){return(u[e[t+0]]+u[e[t+1]]+u[e[t+2]]+u[e[t+3]]+"-"+u[e[t+4]]+u[e[t+5]]+"-"+u[e[t+6]]+u[e[t+7]]+"-"+u[e[t+8]]+u[e[t+9]]+"-"+u[e[t+10]]+u[e[t+11]]+u[e[t+12]]+u[e[t+13]]+u[e[t+14]]+u[e[t+15]]).toLowerCase()}(a)}},63033:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-unit-async-storage.external.js")},74075:e=>{"use strict";e.exports=require("zlib")},79341:(e,t,r)=>{"use strict";e.exports=r(44870)},79428:e=>{"use strict";e.exports=require("buffer")},79551:e=>{"use strict";e.exports=require("url")},81630:e=>{"use strict";e.exports=require("http")},91645:e=>{"use strict";e.exports=require("net")},94735:e=>{"use strict";e.exports=require("events")}};var t=require("../../../../webpack-runtime.js");t.C(e);var r=e=>t(t.s=e),i=t.X(0,[5673,5327,1294],()=>r(21573));module.exports=i})();