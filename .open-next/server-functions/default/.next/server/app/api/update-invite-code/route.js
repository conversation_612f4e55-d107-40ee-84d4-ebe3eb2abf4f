(()=>{var e={};e.id=5441,e.ids=[5441],e.modules={3295:e=>{"use strict";e.exports=require("next/dist/server/app-render/after-task-async-storage.external.js")},4573:e=>{"use strict";e.exports=require("node:buffer")},10846:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},11997:e=>{"use strict";e.exports=require("punycode")},13373:()=>{},19121:e=>{"use strict";e.exports=require("next/dist/server/app-render/action-async-storage.external.js")},26820:(e,t,r)=>{"use strict";r.r(t),r.d(t,{"00880263a376180560adeff1fdd2511a7543726d4a":()=>i.T});var i=r(39201)},27910:e=>{"use strict";e.exports=require("stream")},28432:(e,t,r)=>{"use strict";r.d(t,{j2:()=>v,Y9:()=>l});var i=r(62364),a=r(36157),n=r(72648),s=r(39201),u=r(4961),o=r(29452),c=r(60736);let d=[];d.push((0,a.A)({id:"google-one-tap",name:"google-one-tap",credentials:{credential:{type:"text"}},async authorize(e,t){let r=e.credential,i=await fetch("https://oauth2.googleapis.com/tokeninfo?id_token="+r);if(!i.ok)return console.log("Failed to verify token"),null;let a=await i.json();if(!a)return console.log("invalid payload from token"),null;let{email:n,sub:s,given_name:u,family_name:o,email_verified:c,picture:d}=a;return n?{id:s,name:[u,o].join(" "),email:n,image:d,emailVerified:c?new Date:null}:(console.log("invalid email in payload"),null)}})),process.env.AUTH_GOOGLE_ID&&process.env.AUTH_GOOGLE_SECRET&&d.push((0,n.A)({clientId:process.env.AUTH_GOOGLE_ID,clientSecret:process.env.AUTH_GOOGLE_SECRET,authorization:{params:{prompt:"consent",access_type:"offline",response_type:"code"}}})),d.map(e=>{if("function"!=typeof e)return{id:e.id,name:e.name};{let t=e();return{id:t.id,name:t.name}}}).filter(e=>"google-one-tap"!==e.id);let{handlers:l,signIn:p,signOut:f,auth:v}=(0,i.Ay)({providers:d,pages:{signIn:"/auth/signin"},callbacks:{signIn:async({user:e,account:t,profile:r,email:i,credentials:a})=>!0,redirect:async({url:e,baseUrl:t})=>e.startsWith("/")?`${t}${e}`:new URL(e).origin===t?e:t,session:async({session:e,token:t,user:r})=>(t&&t.user&&t.user&&(e.user=t.user),e),async jwt({token:e,user:t,account:r}){try{if(t&&t.email&&r){let i={uuid:(0,o.YJ)(),email:t.email,nickname:t.name||"",avatar_url:t.image||"",signin_type:r.type,signin_provider:r.provider,signin_openid:r.providerAccountId,created_at:(0,u.iq)(),signin_ip:await (0,s.T)()};try{let t=await (0,c.$c)(i);e.user={uuid:t.uuid,email:t.email,nickname:t.nickname,avatar_url:t.avatar_url,created_at:t.created_at}}catch(e){console.error("save user failed:",e)}}return e}catch(t){return console.error("jwt callback error:",t),e}}}})},29042:(e,t,r)=>{"use strict";function i(e){return s(0,"ok",e||[])}function a(){return s(0,"ok")}function n(e){return s(-1,e)}function s(e,t,r){let i={code:e,message:t,data:r};return r&&(i.data=r),Response.json(i)}r.d(t,{DQ:()=>i,YS:()=>n,j7:()=>s,rn:()=>a})},29294:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-async-storage.external.js")},34631:e=>{"use strict";e.exports=require("tls")},39201:(e,t,r)=>{"use strict";r.d(t,{T:()=>n});var i=r(72784);r(97468);var a=r(62225);async function n(){let e=await (0,a.b3)();return e.get("cf-connecting-ip")||e.get("x-real-ip")||(e.get("x-forwarded-for")||"127.0.0.1").split(",")[0]}(0,r(78564).D)([n]),(0,i.A)(n,"00880263a376180560adeff1fdd2511a7543726d4a",null)},44870:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-route.runtime.prod.js")},50125:(e,t,r)=>{"use strict";r.r(t),r.d(t,{patchFetch:()=>g,routeModule:()=>l,serverHooks:()=>v,workAsyncStorage:()=>p,workUnitAsyncStorage:()=>f});var i={};r.r(i),r.d(i,{POST:()=>d});var a=r(79341),n=r(91514),s=r(95673),u=r(35050),o=r(29042),c=r(60736);async function d(e){try{let{invite_code:t}=await e.json();if(!t)return(0,o.YS)("invalid params");if(t.length<2||t.length>16)return(0,o.YS)("invalid invite code, length must be between 2 and 16");let r=await (0,c.TG)();if(!r)return(0,o.YS)("no auth");let i=await (0,u.pX)(r);if(!i||!i.email)return(0,o.YS)("invalid user");if(i.invite_code===t)return(0,o.DQ)(i);let a=await (0,u.y_)(t);if(a){if(a.uuid!==r)return(0,o.YS)("invite code already exists");return(0,o.DQ)(a)}return await (0,u.XQ)(r,t),i.invite_code=t,(0,o.DQ)(i)}catch(e){return console.log("update invite code failed",e),(0,o.YS)("update invite code failed")}}let l=new a.AppRouteRouteModule({definition:{kind:n.RouteKind.APP_ROUTE,page:"/api/update-invite-code/route",pathname:"/api/update-invite-code",filename:"route",bundlePath:"app/api/update-invite-code/route"},resolvedPagePath:"/Users/<USER>/My-Project/fluxkrea_test/app/api/update-invite-code/route.ts",nextConfigOutput:"standalone",userland:i}),{workAsyncStorage:p,workUnitAsyncStorage:f,serverHooks:v}=l;function g(){return(0,s.patchFetch)({workAsyncStorage:p,workUnitAsyncStorage:f})}},55511:e=>{"use strict";e.exports=require("crypto")},55591:e=>{"use strict";e.exports=require("https")},57975:e=>{"use strict";e.exports=require("node:util")},60736:(e,t,r)=>{"use strict";r.d(t,{$c:()=>c,TG:()=>d,qo:()=>p,ug:()=>f});var i=r(87830),a=r(35050),n=r(28432),s=r(4961),u=r(78054),o=r(62225);async function c(e){try{let t=await (0,a.Gs)(e.email);return t?(e.id=t.id,e.uuid=t.uuid,e.created_at=t.created_at):(await (0,a.HW)(e),await (0,i.d_)({user_uuid:e.uuid||"",trans_type:i.H3.NewUser,credits:i.rN.NewUserGet,expired_at:(0,s.MI)()})),e}catch(e){throw console.log("save user failed: ",e),e}}async function d(){let e="",t=await l();if(t&&t.startsWith("sk-"))return await (0,u.zz)(t)||"";let r=await (0,n.j2)();return r&&r.user&&r.user.uuid&&(e=r.user.uuid),e}async function l(){let e=(await (0,o.b3)()).get("Authorization");return e?e.replace("Bearer ",""):""}async function p(){let e="",t=await (0,n.j2)();return t&&t.user&&t.user.email&&(e=t.user.email),e}async function f(){let e=await d();if(e)return await (0,a.pX)(e)}},63033:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-unit-async-storage.external.js")},74075:e=>{"use strict";e.exports=require("zlib")},77598:e=>{"use strict";e.exports=require("node:crypto")},78054:(e,t,r)=>{"use strict";r.d(t,{N1:()=>n,ai:()=>a,ox:()=>s,zz:()=>u});var i=r(4995),a=function(e){return e.Created="created",e.Deleted="deleted",e}({});async function n(e){let t=(0,i.A)(),{data:r,error:a}=await t.from("apikeys").insert(e);if(a)throw a;return r}async function s(e,t=1,r=50){let a=(t-1)*r,n=(0,i.A)(),{data:u,error:o}=await n.from("apikeys").select("*").eq("user_uuid",e).neq("status","deleted").order("created_at",{ascending:!1}).range(a,a+r-1);if(!o)return u}async function u(e){let t=(0,i.A)(),{data:r,error:a}=await t.from("apikeys").select("user_uuid").eq("api_key",e).eq("status","created").limit(1).single();if(!a)return r?.user_uuid}},79341:(e,t,r)=>{"use strict";e.exports=r(44870)},79428:e=>{"use strict";e.exports=require("buffer")},79551:e=>{"use strict";e.exports=require("url")},81630:e=>{"use strict";e.exports=require("http")},91645:e=>{"use strict";e.exports=require("net")},94735:e=>{"use strict";e.exports=require("events")},94813:()=>{}};var t=require("../../../webpack-runtime.js");t.C(e);var r=e=>t(t.s=e),i=t.X(0,[5673,7711,8734,5327,2041,4524],()=>r(50125));module.exports=i})();