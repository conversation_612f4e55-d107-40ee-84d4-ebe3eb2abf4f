(()=>{var e={};e.id=1217,e.ids=[1217],e.modules={4961:(e,t,r)=>{"use strict";function i(){return new Date().toISOString()}r.d(t,{MI:()=>n,iq:()=>i});let n=()=>{let e=new Date,t=new Date(e);return t.setFullYear(e.getFullYear()+1),t.toISOString()}},4995:(e,t,r)=>{"use strict";r.d(t,{A:()=>n});var i=r(5327);function n(){let e=process.env.SUPABASE_URL||"",t=process.env.SUPABASE_ANON_KEY||"";if(process.env.SUPABASE_SERVICE_ROLE_KEY&&(t=process.env.SUPABASE_SERVICE_ROLE_KEY),!e||!t)throw Error("Supabase URL or key is not set");return(0,i.UU)(e,t)}},10846:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},11997:e=>{"use strict";e.exports=require("punycode")},13373:()=>{},21456:(e,t,r)=>{"use strict";r.r(t),r.d(t,{patchFetch:()=>v,routeModule:()=>f,serverHooks:()=>m,workAsyncStorage:()=>_,workUnitAsyncStorage:()=>w});var i={};r.r(i),r.d(i,{POST:()=>p});var n=r(79341),a=r(91514),s=r(95673),u=r(37947),o=r(35050),d=r(29042),c=r(4961),l=r(80310);async function p(e){try{let{invite_code:t,user_uuid:r}=await e.json();if(!t||!r)return(0,d.YS)("invalid params");let i=await (0,o.y_)(t);if(!i)return(0,d.YS)("invite user not found");let n=await (0,o.pX)(r);if(!n)return(0,d.YS)("user not found");if(n.uuid===i.uuid||n.email===i.email)return(0,d.YS)("can't invite yourself");if(n.invited_by)return(0,d.YS)("user already has invite user");return n.invited_by=i.uuid,await (0,o.Nh)(r,i.uuid),await (0,l.Rt)({user_uuid:r,invited_by:i.uuid,created_at:(0,c.iq)(),status:u.OJ.Pending,paid_order_no:"",paid_amount:0,reward_percent:u.oR.Invited,reward_amount:u.h1.Invited}),(0,d.DQ)(n)}catch(e){return console.error("update invited by failed: ",e),(0,d.YS)("update invited by failed")}}let f=new n.AppRouteRouteModule({definition:{kind:a.RouteKind.APP_ROUTE,page:"/api/update-invite/route",pathname:"/api/update-invite",filename:"route",bundlePath:"app/api/update-invite/route"},resolvedPagePath:"/Users/<USER>/My-Project/fluxkrea_test/app/api/update-invite/route.ts",nextConfigOutput:"standalone",userland:i}),{workAsyncStorage:_,workUnitAsyncStorage:w,serverHooks:m}=f;function v(){return(0,s.patchFetch)({workAsyncStorage:_,workUnitAsyncStorage:w})}},27910:e=>{"use strict";e.exports=require("stream")},29042:(e,t,r)=>{"use strict";function i(e){return s(0,"ok",e||[])}function n(){return s(0,"ok")}function a(e){return s(-1,e)}function s(e,t,r){let i={code:e,message:t,data:r};return r&&(i.data=r),Response.json(i)}r.d(t,{DQ:()=>i,YS:()=>a,j7:()=>s,rn:()=>n})},29294:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-async-storage.external.js")},34631:e=>{"use strict";e.exports=require("tls")},35050:(e,t,r)=>{"use strict";r.d(t,{Gs:()=>s,HW:()=>a,Nh:()=>c,PD:()=>f,QZ:()=>l,XQ:()=>d,in:()=>_,lo:()=>o,pX:()=>u,y_:()=>p});var i=r(4961),n=r(4995);async function a(e){let t=(0,n.A)(),{data:r,error:i}=await t.from("users").insert(e);if(i)throw i;return r}async function s(e){let t=(0,n.A)(),{data:r,error:i}=await t.from("users").select("*").eq("email",e).limit(1).single();if(!i)return r}async function u(e){let t=(0,n.A)(),{data:r,error:i}=await t.from("users").select("*").eq("uuid",e).single();if(!i)return r}async function o(e=1,t=50){e<1&&(e=1),t<=0&&(t=50);let r=(e-1)*t,i=(0,n.A)(),{data:a,error:s}=await i.from("users").select("*").order("created_at",{ascending:!1}).range(r,r+t-1);if(!s)return a}async function d(e,t){let r=(0,n.A)(),a=(0,i.iq)(),{data:s,error:u}=await r.from("users").update({invite_code:t,updated_at:a}).eq("uuid",e);if(u)throw u;return s}async function c(e,t){let r=(0,n.A)(),a=(0,i.iq)(),{data:s,error:u}=await r.from("users").update({invited_by:t,updated_at:a}).eq("uuid",e);if(u)throw u;return s}async function l(e){let t=(0,n.A)(),{data:r,error:i}=await t.from("users").select("*").in("uuid",e);return i?[]:r}async function p(e){let t=(0,n.A)(),{data:r,error:i}=await t.from("users").select("*").eq("invite_code",e).single();if(!i)return r}async function f(){let e=(0,n.A)(),{data:t,error:r}=await e.from("users").select("count",{count:"exact"});if(!r)return t[0].count}async function _(e){let t=(0,n.A)().from("users").select("created_at").gte("created_at",e);t=t.order("created_at",{ascending:!0});let{data:r,error:i}=await t;if(i)return;let a=new Map;return r.forEach(e=>{let t=e.created_at.split("T")[0];a.set(t,(a.get(t)||0)+1)}),a}},37947:(e,t,r)=>{"use strict";r.d(t,{OJ:()=>i,h1:()=>a,oR:()=>n});let i={Pending:"pending",Completed:"completed"},n={Invited:0,Paied:20},a={Invited:0,Paied:5e3}},44870:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-route.runtime.prod.js")},45590:()=>{},52367:()=>{},55511:e=>{"use strict";e.exports=require("crypto")},55591:e=>{"use strict";e.exports=require("https")},63033:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-unit-async-storage.external.js")},74075:e=>{"use strict";e.exports=require("zlib")},79341:(e,t,r)=>{"use strict";e.exports=r(44870)},79428:e=>{"use strict";e.exports=require("buffer")},79551:e=>{"use strict";e.exports=require("url")},80310:(e,t,r)=>{"use strict";r.d(t,{A:()=>u,Ex:()=>s,N$:()=>o,Rt:()=>a});var i=r(4995),n=r(35050);async function a(e){let t=(0,i.A)(),{data:r,error:n}=await t.from("affiliates").insert({user_uuid:e.user_uuid,invited_by:e.invited_by,created_at:e.created_at,status:e.status,paid_order_no:e.paid_order_no,paid_amount:e.paid_amount,reward_percent:e.reward_percent,reward_amount:e.reward_amount});if(n)throw n;return r}async function s(e,t=1,r=50){let a=(0,i.A)(),{data:u,error:o}=await a.from("affiliates").select("*").eq("invited_by",e).order("created_at",{ascending:!1}).range((t-1)*r,t*r);if(o)return console.error("Error fetching user invites:",o),[];if(!u||0===u.length)return;let d=Array.from(new Set(u.map(e=>e.user_uuid))),c=await (0,n.QZ)(d);return u.map(e=>{let t=c.find(t=>t.uuid===e.user_uuid);return{...e,user:t}})}async function u(e){let t=(0,i.A)(),{data:r,error:n}=await t.from("affiliates").select("*").eq("invited_by",e),a={total_invited:0,total_paid:0,total_reward:0};if(n)return a;let s=new Set,u=new Set;return r.forEach(e=>{s.add(e.user_uuid),e.paid_amount>0&&(u.add(e.user_uuid),a.total_reward+=e.reward_amount)}),a.total_invited=s.size,a.total_paid=u.size,a}async function o(e){let t=(0,i.A)(),{data:r,error:n}=await t.from("affiliates").select("*").eq("paid_order_no",e).single();if(!n)return r}},81630:e=>{"use strict";e.exports=require("http")},91645:e=>{"use strict";e.exports=require("net")},94735:e=>{"use strict";e.exports=require("events")},94813:()=>{}};var t=require("../../../webpack-runtime.js");t.C(e);var r=e=>t(t.s=e),i=t.X(0,[5673,5327],()=>r(21456));module.exports=i})();