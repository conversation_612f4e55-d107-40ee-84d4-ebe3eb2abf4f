(()=>{var e={};e.id=2712,e.ids=[2712],e.modules={592:(e,r,o)=>{"use strict";o.r(r),o.d(r,{patchFetch:()=>f,routeModule:()=>d,serverHooks:()=>k,workAsyncStorage:()=>p,workUnitAsyncStorage:()=>g});var t={};o.r(t),o.d(t,{POST:()=>i});var s=o(79341),n=o(91514),a=o(95673),c=o(29042);async function i(e){try{let r=await e.json();return console.log("Volcengine callback received:",{timestamp:new Date().toISOString(),body:r}),r.task_id?await l(r):r.speaker_id&&await u(r),(0,c.DQ)({received:!0})}catch(e){return console.error("Volcengine callback processing error:",e),(0,c.YS)(`Callback processing failed: ${e instanceof Error?e.message:"Unknown error"}`)}}async function l(e){try{let{task_id:r,status:o,code:t,message:s}=e;console.log(`Task ${r} completed with status: ${o}`),"success"===o?e.audio_url?console.log(`TTS task ${r} completed. Audio URL: ${e.audio_url}`):e.result&&console.log(`ASR task ${r} completed. Text: ${e.result.text}`):"failed"===o&&console.error(`Task ${r} failed: ${s}`)}catch(e){console.error("Task callback processing error:",e)}}async function u(e){try{let{speaker_id:r,status:o,demo_audio:t}=e;console.log(`Voice clone ${r} status: ${o}`),"success"===o||2===o?(console.log(`Voice clone ${r} training completed successfully`),t&&console.log(`Demo audio available: ${t}`)):("failed"===o||3===o)&&console.error(`Voice clone ${r} training failed`)}catch(e){console.error("Voice clone callback processing error:",e)}}let d=new s.AppRouteRouteModule({definition:{kind:n.RouteKind.APP_ROUTE,page:"/api/volcengine/callback/route",pathname:"/api/volcengine/callback",filename:"route",bundlePath:"app/api/volcengine/callback/route"},resolvedPagePath:"/Users/<USER>/My-Project/fluxkrea_test/app/api/volcengine/callback/route.ts",nextConfigOutput:"standalone",userland:t}),{workAsyncStorage:p,workUnitAsyncStorage:g,serverHooks:k}=d;function f(){return(0,a.patchFetch)({workAsyncStorage:p,workUnitAsyncStorage:g})}},10846:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},13373:()=>{},29042:(e,r,o)=>{"use strict";function t(e){return a(0,"ok",e||[])}function s(){return a(0,"ok")}function n(e){return a(-1,e)}function a(e,r,o){let t={code:e,message:r,data:o};return o&&(t.data=o),Response.json(t)}o.d(r,{DQ:()=>t,YS:()=>n,j7:()=>a,rn:()=>s})},29294:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-async-storage.external.js")},44870:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-route.runtime.prod.js")},63033:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-unit-async-storage.external.js")},79341:(e,r,o)=>{"use strict";e.exports=o(44870)},94813:()=>{}};var r=require("../../../../webpack-runtime.js");r.C(e);var o=e=>r(r.s=e),t=r.X(0,[5673],()=>o(592));module.exports=t})();