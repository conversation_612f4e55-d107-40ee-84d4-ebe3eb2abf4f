"use strict";(()=>{var e={};e.id=5396,e.ids=[5396],e.modules={3295:e=>{e.exports=require("next/dist/server/app-render/after-task-async-storage.external.js")},4573:e=>{e.exports=require("node:buffer")},10846:e=>{e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},11997:e=>{e.exports=require("punycode")},19121:e=>{e.exports=require("next/dist/server/app-render/action-async-storage.external.js")},27910:e=>{e.exports=require("stream")},29294:e=>{e.exports=require("next/dist/server/app-render/work-async-storage.external.js")},34631:e=>{e.exports=require("tls")},44870:e=>{e.exports=require("next/dist/compiled/next-server/app-route.runtime.prod.js")},55511:e=>{e.exports=require("crypto")},55591:e=>{e.exports=require("https")},57975:e=>{e.exports=require("node:util")},63033:e=>{e.exports=require("next/dist/server/app-render/work-unit-async-storage.external.js")},66545:(e,r,t)=>{t.r(r),t.d(r,{patchFetch:()=>q,routeModule:()=>c,serverHooks:()=>g,workAsyncStorage:()=>x,workUnitAsyncStorage:()=>l});var o={};t.r(o),t.d(o,{POST:()=>d});var i=t(79341),n=t(91514),s=t(95673),a=t(29042),u=t(60736),p=t(74245);async function d(e){try{let{text:r,voice_type:t,options:o}=await e.json();if(!r)return(0,a.YS)("Missing required parameter: text");if(!await (0,u.TG)())return(0,a.j7)(-2,"Authentication required");if(r.length>1024)return(0,a.YS)("Text length exceeds 1024 bytes limit for short TTS");let i=(0,p.QR)(),n={text:r,voice_type:t||"BV700_streaming",encoding:o?.encoding||"mp3",rate:o?.rate||24e3,speed_ratio:o?.speed_ratio||1,volume_ratio:o?.volume_ratio||1,pitch_ratio:o?.pitch_ratio||1,emotion:o?.emotion,language:o?.language||"cn"},s=await i.synthesizeText(n);if(3e3===s.code)return(0,a.DQ)({type:"audio",reqid:s.reqid,audio_data:s.data,duration:s.addition?.duration,format:n.encoding});return(0,a.YS)(`TTS failed: ${s.message}`)}catch(e){return console.error("Volcengine TTS error:",e),(0,a.YS)(`TTS service error: ${e instanceof Error?e.message:"Unknown error"}`)}}let c=new i.AppRouteRouteModule({definition:{kind:n.RouteKind.APP_ROUTE,page:"/api/volcengine/tts/route",pathname:"/api/volcengine/tts",filename:"route",bundlePath:"app/api/volcengine/tts/route"},resolvedPagePath:"/Users/<USER>/My-Project/fluxkrea_test/app/api/volcengine/tts/route.ts",nextConfigOutput:"standalone",userland:o}),{workAsyncStorage:x,workUnitAsyncStorage:l,serverHooks:g}=c;function q(){return(0,s.patchFetch)({workAsyncStorage:x,workUnitAsyncStorage:l})}},74075:e=>{e.exports=require("zlib")},77598:e=>{e.exports=require("node:crypto")},79428:e=>{e.exports=require("buffer")},79551:e=>{e.exports=require("url")},81630:e=>{e.exports=require("http")},91645:e=>{e.exports=require("net")},94735:e=>{e.exports=require("events")}};var r=require("../../../../webpack-runtime.js");r.C(e);var t=e=>r(r.s=e),o=r.X(0,[5673,7711,8734,5327,2041,4524,2805],()=>t(66545));module.exports=o})();