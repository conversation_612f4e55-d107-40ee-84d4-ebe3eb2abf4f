"use strict";(()=>{var e={};e.id=3698,e.ids=[3698],e.modules={3295:e=>{e.exports=require("next/dist/server/app-render/after-task-async-storage.external.js")},4573:e=>{e.exports=require("node:buffer")},10846:e=>{e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},11997:e=>{e.exports=require("punycode")},19121:e=>{e.exports=require("next/dist/server/app-render/action-async-storage.external.js")},27910:e=>{e.exports=require("stream")},29294:e=>{e.exports=require("next/dist/server/app-render/work-async-storage.external.js")},34631:e=>{e.exports=require("tls")},44870:e=>{e.exports=require("next/dist/compiled/next-server/app-route.runtime.prod.js")},55511:e=>{e.exports=require("crypto")},55591:e=>{e.exports=require("https")},57975:e=>{e.exports=require("node:util")},63033:e=>{e.exports=require("next/dist/server/app-render/work-unit-async-storage.external.js")},74075:e=>{e.exports=require("zlib")},77598:e=>{e.exports=require("node:crypto")},79428:e=>{e.exports=require("buffer")},79551:e=>{e.exports=require("url")},81003:(e,r,t)=>{t.r(r),t.d(r,{patchFetch:()=>q,routeModule:()=>l,serverHooks:()=>m,workAsyncStorage:()=>d,workUnitAsyncStorage:()=>x});var s={};t.r(s),t.d(s,{POST:()=>c});var n=t(79341),i=t(91514),a=t(95673),o=t(29042),u=t(60736),p=t(74245);async function c(e){try{let{text:r,voice_type:t,options:s}=await e.json();if(!r)return(0,o.YS)("Missing required parameter: text");if(!await (0,u.TG)())return(0,o.j7)(-2,"Authentication required");if(r.length>1e5)return(0,o.YS)("Text length exceeds 100,000 characters limit for async TTS");let n=(0,p.QR)(),i={text:r,voice_type:t||"BV701_streaming",format:s?.format||"mp3",sample_rate:s?.sample_rate||24e3,volume:s?.volume||1,speed:s?.speed||1,pitch:s?.pitch||1,enable_subtitle:s?.enable_subtitle||0,callback_url:s?.enable_callback?s.callback_url:void 0},a=await n.submitAsyncTTS(i);if(2e7===a.code)return(0,o.DQ)({task_id:a.task_id,status:"submitted"});return(0,o.YS)(`Async TTS submission failed: ${a.message}`)}catch(e){return console.error("Volcengine Async TTS submission error:",e),(0,o.YS)(`Async TTS service error: ${e instanceof Error?e.message:"Unknown error"}`)}}let l=new n.AppRouteRouteModule({definition:{kind:i.RouteKind.APP_ROUTE,page:"/api/volcengine/tts-async/submit/route",pathname:"/api/volcengine/tts-async/submit",filename:"route",bundlePath:"app/api/volcengine/tts-async/submit/route"},resolvedPagePath:"/Users/<USER>/My-Project/fluxkrea_test/app/api/volcengine/tts-async/submit/route.ts",nextConfigOutput:"standalone",userland:s}),{workAsyncStorage:d,workUnitAsyncStorage:x,serverHooks:m}=l;function q(){return(0,a.patchFetch)({workAsyncStorage:d,workUnitAsyncStorage:x})}},81630:e=>{e.exports=require("http")},91645:e=>{e.exports=require("net")},94735:e=>{e.exports=require("events")}};var r=require("../../../../../webpack-runtime.js");r.C(e);var t=e=>r(r.s=e),s=r.X(0,[5673,7711,8734,5327,2041,4524,2805],()=>t(81003));module.exports=s})();