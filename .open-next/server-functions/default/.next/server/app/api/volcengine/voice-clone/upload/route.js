"use strict";(()=>{var e={};e.id=1943,e.ids=[1943],e.modules={3295:e=>{e.exports=require("next/dist/server/app-render/after-task-async-storage.external.js")},4573:e=>{e.exports=require("node:buffer")},10846:e=>{e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},11997:e=>{e.exports=require("punycode")},18171:(e,r,t)=>{t.r(r),t.d(r,{patchFetch:()=>f,routeModule:()=>l,serverHooks:()=>g,workAsyncStorage:()=>c,workUnitAsyncStorage:()=>x});var o={};t.r(o),t.d(o,{POST:()=>d});var a=t(79341),s=t(91514),i=t(95673),n=t(29042),p=t(60736),u=t(74245);async function d(e){try{let r=await e.formData(),t=r.get("audio"),o=r.get("speaker_id"),a=r.get("reference_text"),s=parseInt(r.get("language"))||0,i=parseInt(r.get("model_type"))||1;if(!t||!o)return(0,n.YS)("Missing required parameters: audio file and speaker_id");if(!await (0,p.TG)())return(0,n.j7)(-2,"Authentication required");if(t.size>0xa00000)return(0,n.YS)("Audio file size exceeds 10MB limit");let d=t.name.split(".").pop()?.toLowerCase();if(!d||!["wav","mp3","ogg","m4a","aac","pcm"].includes(d))return(0,n.YS)("Unsupported audio format. Supported formats: wav, mp3, ogg, m4a, aac, pcm");let l=await t.arrayBuffer(),c=Buffer.from(l).toString("base64"),x=(0,u.QR)(),g=await x.uploadVoiceClone({speaker_id:o,audio_bytes:c,audio_format:d,text:a,language:s,model_type:i});if(0===g.BaseResp.StatusCode)return(0,n.DQ)({speaker_id:g.speaker_id,status:"uploaded",message:"Voice clone training started"});return(0,n.YS)(`Voice clone upload failed: ${g.BaseResp.StatusMessage}`)}catch(e){return console.error("Volcengine Voice Clone upload error:",e),(0,n.YS)(`Voice clone service error: ${e instanceof Error?e.message:"Unknown error"}`)}}let l=new a.AppRouteRouteModule({definition:{kind:s.RouteKind.APP_ROUTE,page:"/api/volcengine/voice-clone/upload/route",pathname:"/api/volcengine/voice-clone/upload",filename:"route",bundlePath:"app/api/volcengine/voice-clone/upload/route"},resolvedPagePath:"/Users/<USER>/My-Project/fluxkrea_test/app/api/volcengine/voice-clone/upload/route.ts",nextConfigOutput:"standalone",userland:o}),{workAsyncStorage:c,workUnitAsyncStorage:x,serverHooks:g}=l;function f(){return(0,i.patchFetch)({workAsyncStorage:c,workUnitAsyncStorage:x})}},19121:e=>{e.exports=require("next/dist/server/app-render/action-async-storage.external.js")},27910:e=>{e.exports=require("stream")},29294:e=>{e.exports=require("next/dist/server/app-render/work-async-storage.external.js")},34631:e=>{e.exports=require("tls")},44870:e=>{e.exports=require("next/dist/compiled/next-server/app-route.runtime.prod.js")},55511:e=>{e.exports=require("crypto")},55591:e=>{e.exports=require("https")},57975:e=>{e.exports=require("node:util")},63033:e=>{e.exports=require("next/dist/server/app-render/work-unit-async-storage.external.js")},74075:e=>{e.exports=require("zlib")},77598:e=>{e.exports=require("node:crypto")},79428:e=>{e.exports=require("buffer")},79551:e=>{e.exports=require("url")},81630:e=>{e.exports=require("http")},91645:e=>{e.exports=require("net")},94735:e=>{e.exports=require("events")}};var r=require("../../../../../webpack-runtime.js");r.C(e);var t=e=>r(r.s=e),o=r.X(0,[5673,7711,8734,5327,2041,4524,2805],()=>t(18171));module.exports=o})();