"use strict";(()=>{var e={};e.id=1244,e.ids=[1244],e.modules={3295:e=>{e.exports=require("next/dist/server/app-render/after-task-async-storage.external.js")},4573:e=>{e.exports=require("node:buffer")},10846:e=>{e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},11997:e=>{e.exports=require("punycode")},19121:e=>{e.exports=require("next/dist/server/app-render/action-async-storage.external.js")},27910:e=>{e.exports=require("stream")},29294:e=>{e.exports=require("next/dist/server/app-render/work-async-storage.external.js")},34631:e=>{e.exports=require("tls")},38065:(e,r,t)=>{t.r(r),t.d(r,{patchFetch:()=>y,routeModule:()=>l,serverHooks:()=>q,workAsyncStorage:()=>x,workUnitAsyncStorage:()=>c});var s={};t.r(s),t.d(s,{GET:()=>d});var o=t(79341),n=t(91514),a=t(95673),i=t(29042),u=t(60736),p=t(74245);async function d(e){try{let r;let{searchParams:t}=new URL(e.url),s=t.get("task_id"),o=t.get("model_type")||"fast";if(!s)return(0,i.YS)("Missing required parameter: task_id");if(!await (0,u.TG)())return(0,i.j7)(-2,"Authentication required");let n=(0,p.QR)();if(r="bigmodel"===o?await n.queryASRBigModel(s):await n.queryASRStandard(s),1e4===r.code)return(0,i.DQ)({task_id:s,model_type:o,status:r.status,result:r.result});return(0,i.YS)(`Query failed: ${r.message}`)}catch(e){return console.error("Volcengine ASR query error:",e),(0,i.YS)(`Query service error: ${e instanceof Error?e.message:"Unknown error"}`)}}let l=new o.AppRouteRouteModule({definition:{kind:n.RouteKind.APP_ROUTE,page:"/api/volcengine/asr/query/route",pathname:"/api/volcengine/asr/query",filename:"route",bundlePath:"app/api/volcengine/asr/query/route"},resolvedPagePath:"/Users/<USER>/My-Project/fluxkrea_test/app/api/volcengine/asr/query/route.ts",nextConfigOutput:"standalone",userland:s}),{workAsyncStorage:x,workUnitAsyncStorage:c,serverHooks:q}=l;function y(){return(0,a.patchFetch)({workAsyncStorage:x,workUnitAsyncStorage:c})}},44870:e=>{e.exports=require("next/dist/compiled/next-server/app-route.runtime.prod.js")},55511:e=>{e.exports=require("crypto")},55591:e=>{e.exports=require("https")},57975:e=>{e.exports=require("node:util")},63033:e=>{e.exports=require("next/dist/server/app-render/work-unit-async-storage.external.js")},74075:e=>{e.exports=require("zlib")},77598:e=>{e.exports=require("node:crypto")},79428:e=>{e.exports=require("buffer")},79551:e=>{e.exports=require("url")},81630:e=>{e.exports=require("http")},91645:e=>{e.exports=require("net")},94735:e=>{e.exports=require("events")}};var r=require("../../../../../webpack-runtime.js");r.C(e);var t=e=>r(r.s=e),s=r.X(0,[5673,7711,8734,5327,2041,4524,2805],()=>t(38065));module.exports=s})();