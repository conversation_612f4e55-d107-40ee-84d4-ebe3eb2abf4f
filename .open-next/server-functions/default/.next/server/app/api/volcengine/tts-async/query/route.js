"use strict";(()=>{var e={};e.id=910,e.ids=[910],e.modules={3295:e=>{e.exports=require("next/dist/server/app-render/after-task-async-storage.external.js")},4573:e=>{e.exports=require("node:buffer")},10846:e=>{e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},11997:e=>{e.exports=require("punycode")},19121:e=>{e.exports=require("next/dist/server/app-render/action-async-storage.external.js")},26730:(e,r,t)=>{t.r(r),t.d(r,{patchFetch:()=>y,routeModule:()=>c,serverHooks:()=>q,workAsyncStorage:()=>l,workUnitAsyncStorage:()=>x});var s={};t.r(s),t.d(s,{GET:()=>d});var n=t(79341),i=t(91514),o=t(95673),u=t(29042),a=t(60736),p=t(74245);async function d(e){try{let{searchParams:r}=new URL(e.url),t=r.get("task_id");if(!t)return(0,u.YS)("Missing required parameter: task_id");if(!await (0,a.TG)())return(0,u.j7)(-2,"Authentication required");let s=(0,p.QR)(),n=await s.queryAsyncTTS(t);if(2e7===n.code)return(0,u.DQ)({task_id:t,status:n.status,audio_url:n.audio_url,subtitle_url:n.subtitle_url,duration:n.duration,file_size:n.file_size});return(0,u.YS)(`Query failed: ${n.message}`)}catch(e){return console.error("Volcengine Async TTS query error:",e),(0,u.YS)(`Query service error: ${e instanceof Error?e.message:"Unknown error"}`)}}let c=new n.AppRouteRouteModule({definition:{kind:i.RouteKind.APP_ROUTE,page:"/api/volcengine/tts-async/query/route",pathname:"/api/volcengine/tts-async/query",filename:"route",bundlePath:"app/api/volcengine/tts-async/query/route"},resolvedPagePath:"/Users/<USER>/My-Project/fluxkrea_test/app/api/volcengine/tts-async/query/route.ts",nextConfigOutput:"standalone",userland:s}),{workAsyncStorage:l,workUnitAsyncStorage:x,serverHooks:q}=c;function y(){return(0,o.patchFetch)({workAsyncStorage:l,workUnitAsyncStorage:x})}},27910:e=>{e.exports=require("stream")},29294:e=>{e.exports=require("next/dist/server/app-render/work-async-storage.external.js")},34631:e=>{e.exports=require("tls")},44870:e=>{e.exports=require("next/dist/compiled/next-server/app-route.runtime.prod.js")},55511:e=>{e.exports=require("crypto")},55591:e=>{e.exports=require("https")},57975:e=>{e.exports=require("node:util")},63033:e=>{e.exports=require("next/dist/server/app-render/work-unit-async-storage.external.js")},74075:e=>{e.exports=require("zlib")},77598:e=>{e.exports=require("node:crypto")},79428:e=>{e.exports=require("buffer")},79551:e=>{e.exports=require("url")},81630:e=>{e.exports=require("http")},91645:e=>{e.exports=require("net")},94735:e=>{e.exports=require("events")}};var r=require("../../../../../webpack-runtime.js");r.C(e);var t=e=>r(r.s=e),s=r.X(0,[5673,7711,8734,5327,2041,4524,2805],()=>t(26730));module.exports=s})();