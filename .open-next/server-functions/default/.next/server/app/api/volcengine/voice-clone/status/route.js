"use strict";(()=>{var e={};e.id=8246,e.ids=[8246],e.modules={3295:e=>{e.exports=require("next/dist/server/app-render/after-task-async-storage.external.js")},4573:e=>{e.exports=require("node:buffer")},4662:(e,r,t)=>{t.r(r),t.d(r,{patchFetch:()=>v,routeModule:()=>d,serverHooks:()=>q,workAsyncStorage:()=>l,workUnitAsyncStorage:()=>x});var s={};t.r(s),t.d(s,{GET:()=>c});var o=t(79341),n=t(91514),a=t(95673),i=t(29042),u=t(60736),p=t(74245);async function c(e){try{let{searchParams:r}=new URL(e.url),t=r.get("speaker_id");if(!t)return(0,i.YS)("Missing required parameter: speaker_id");if(!await (0,u.TG)())return(0,i.j7)(-2,"Authentication required");let s=(0,p.QR)(),o=await s.queryVoiceCloneStatus(t);if(0===o.BaseResp.StatusCode)return(0,i.DQ)({speaker_id:o.speaker_id,status:{0:"not_found",1:"training",2:"success",3:"failed",4:"active"}[o.status||0]||"unknown",create_time:o.create_time,version:o.version,demo_audio:o.demo_audio,ready_for_use:2===o.status||4===o.status});return(0,i.YS)(`Status query failed: ${o.BaseResp.StatusMessage}`)}catch(e){return console.error("Volcengine Voice Clone status query error:",e),(0,i.YS)(`Status query service error: ${e instanceof Error?e.message:"Unknown error"}`)}}let d=new o.AppRouteRouteModule({definition:{kind:n.RouteKind.APP_ROUTE,page:"/api/volcengine/voice-clone/status/route",pathname:"/api/volcengine/voice-clone/status",filename:"route",bundlePath:"app/api/volcengine/voice-clone/status/route"},resolvedPagePath:"/Users/<USER>/My-Project/fluxkrea_test/app/api/volcengine/voice-clone/status/route.ts",nextConfigOutput:"standalone",userland:s}),{workAsyncStorage:l,workUnitAsyncStorage:x,serverHooks:q}=d;function v(){return(0,a.patchFetch)({workAsyncStorage:l,workUnitAsyncStorage:x})}},10846:e=>{e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},11997:e=>{e.exports=require("punycode")},19121:e=>{e.exports=require("next/dist/server/app-render/action-async-storage.external.js")},27910:e=>{e.exports=require("stream")},29294:e=>{e.exports=require("next/dist/server/app-render/work-async-storage.external.js")},34631:e=>{e.exports=require("tls")},44870:e=>{e.exports=require("next/dist/compiled/next-server/app-route.runtime.prod.js")},55511:e=>{e.exports=require("crypto")},55591:e=>{e.exports=require("https")},57975:e=>{e.exports=require("node:util")},63033:e=>{e.exports=require("next/dist/server/app-render/work-unit-async-storage.external.js")},74075:e=>{e.exports=require("zlib")},77598:e=>{e.exports=require("node:crypto")},79428:e=>{e.exports=require("buffer")},79551:e=>{e.exports=require("url")},81630:e=>{e.exports=require("http")},91645:e=>{e.exports=require("net")},94735:e=>{e.exports=require("events")}};var r=require("../../../../../webpack-runtime.js");r.C(e);var t=e=>r(r.s=e),s=r.X(0,[5673,7711,8734,5327,2041,4524,2805],()=>t(4662));module.exports=s})();