"use strict";(()=>{var e={};e.id=6744,e.ids=[6744],e.modules={3295:e=>{e.exports=require("next/dist/server/app-render/after-task-async-storage.external.js")},4573:e=>{e.exports=require("node:buffer")},10846:e=>{e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},11997:e=>{e.exports=require("punycode")},13834:(e,r,t)=>{t.r(r),t.d(r,{patchFetch:()=>q,routeModule:()=>l,serverHooks:()=>m,workAsyncStorage:()=>c,workUnitAsyncStorage:()=>x});var s={};t.r(s),t.d(s,{POST:()=>d});var a=t(79341),i=t(91514),n=t(95673),o=t(29042),u=t(60736),p=t(74245);async function d(e){try{let r;let{audio_url:t,model_type:s,language:a,options:i}=await e.json();if(!t)return(0,o.YS)("Missing required parameter: audio_url");if(!await (0,u.TG)())return(0,o.j7)(-2,"Authentication required");try{new URL(t)}catch{return(0,o.YS)("Invalid audio URL format")}let n=(0,p.QR)(),d={url:t,language:a||"zh",use_itn:i?.use_itn!==!1,use_capitalize:i?.use_capitalize!==!1,max_lines:i?.max_lines||1,callback_url:i?.enable_callback?i.callback_url:void 0};switch(s){case"bigmodel":r=await n.submitASRBigModel(d);break;case"standard":r=await n.submitASRStandard(d,"standard");break;default:r=await n.submitASRStandard(d,"fast")}if(1e4===r.code)return(0,o.DQ)({task_id:r.id,model_type:s||"fast",status:"submitted"});return(0,o.YS)(`ASR submission failed: ${r.message}`)}catch(e){return console.error("Volcengine ASR submission error:",e),(0,o.YS)(`ASR service error: ${e instanceof Error?e.message:"Unknown error"}`)}}let l=new a.AppRouteRouteModule({definition:{kind:i.RouteKind.APP_ROUTE,page:"/api/volcengine/asr/submit/route",pathname:"/api/volcengine/asr/submit",filename:"route",bundlePath:"app/api/volcengine/asr/submit/route"},resolvedPagePath:"/Users/<USER>/My-Project/fluxkrea_test/app/api/volcengine/asr/submit/route.ts",nextConfigOutput:"standalone",userland:s}),{workAsyncStorage:c,workUnitAsyncStorage:x,serverHooks:m}=l;function q(){return(0,n.patchFetch)({workAsyncStorage:c,workUnitAsyncStorage:x})}},19121:e=>{e.exports=require("next/dist/server/app-render/action-async-storage.external.js")},27910:e=>{e.exports=require("stream")},29294:e=>{e.exports=require("next/dist/server/app-render/work-async-storage.external.js")},34631:e=>{e.exports=require("tls")},44870:e=>{e.exports=require("next/dist/compiled/next-server/app-route.runtime.prod.js")},55511:e=>{e.exports=require("crypto")},55591:e=>{e.exports=require("https")},57975:e=>{e.exports=require("node:util")},63033:e=>{e.exports=require("next/dist/server/app-render/work-unit-async-storage.external.js")},74075:e=>{e.exports=require("zlib")},77598:e=>{e.exports=require("node:crypto")},79428:e=>{e.exports=require("buffer")},79551:e=>{e.exports=require("url")},81630:e=>{e.exports=require("http")},91645:e=>{e.exports=require("net")},94735:e=>{e.exports=require("events")}};var r=require("../../../../../webpack-runtime.js");r.C(e);var t=e=>r(r.s=e),s=r.X(0,[5673,7711,8734,5327,2041,4524,2805],()=>t(13834));module.exports=s})();