(()=>{var e={};e.id=8885,e.ids=[8885],e.modules={3295:e=>{"use strict";e.exports=require("next/dist/server/app-render/after-task-async-storage.external.js")},4573:e=>{"use strict";e.exports=require("node:buffer")},10846:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},11997:e=>{"use strict";e.exports=require("punycode")},13373:()=>{},19121:e=>{"use strict";e.exports=require("next/dist/server/app-render/action-async-storage.external.js")},26820:(e,t,r)=>{"use strict";r.r(t),r.d(t,{"00880263a376180560adeff1fdd2511a7543726d4a":()=>i.T});var i=r(39201)},27910:e=>{"use strict";e.exports=require("stream")},28432:(e,t,r)=>{"use strict";r.d(t,{j2:()=>f,Y9:()=>d});var i=r(62364),s=r(36157),a=r(72648),n=r(39201),o=r(4961),u=r(29452),c=r(60736);let l=[];l.push((0,s.A)({id:"google-one-tap",name:"google-one-tap",credentials:{credential:{type:"text"}},async authorize(e,t){let r=e.credential,i=await fetch("https://oauth2.googleapis.com/tokeninfo?id_token="+r);if(!i.ok)return console.log("Failed to verify token"),null;let s=await i.json();if(!s)return console.log("invalid payload from token"),null;let{email:a,sub:n,given_name:o,family_name:u,email_verified:c,picture:l}=s;return a?{id:n,name:[o,u].join(" "),email:a,image:l,emailVerified:c?new Date:null}:(console.log("invalid email in payload"),null)}})),process.env.AUTH_GOOGLE_ID&&process.env.AUTH_GOOGLE_SECRET&&l.push((0,a.A)({clientId:process.env.AUTH_GOOGLE_ID,clientSecret:process.env.AUTH_GOOGLE_SECRET,authorization:{params:{prompt:"consent",access_type:"offline",response_type:"code"}}})),l.map(e=>{if("function"!=typeof e)return{id:e.id,name:e.name};{let t=e();return{id:t.id,name:t.name}}}).filter(e=>"google-one-tap"!==e.id);let{handlers:d,signIn:p,signOut:_,auth:f}=(0,i.Ay)({providers:l,pages:{signIn:"/auth/signin"},callbacks:{signIn:async({user:e,account:t,profile:r,email:i,credentials:s})=>!0,redirect:async({url:e,baseUrl:t})=>e.startsWith("/")?`${t}${e}`:new URL(e).origin===t?e:t,session:async({session:e,token:t,user:r})=>(t&&t.user&&t.user&&(e.user=t.user),e),async jwt({token:e,user:t,account:r}){try{if(t&&t.email&&r){let i={uuid:(0,u.YJ)(),email:t.email,nickname:t.name||"",avatar_url:t.image||"",signin_type:r.type,signin_provider:r.provider,signin_openid:r.providerAccountId,created_at:(0,o.iq)(),signin_ip:await (0,n.T)()};try{let t=await (0,c.$c)(i);e.user={uuid:t.uuid,email:t.email,nickname:t.nickname,avatar_url:t.avatar_url,created_at:t.created_at}}catch(e){console.error("save user failed:",e)}}return e}catch(t){return console.error("jwt callback error:",t),e}}}})},29042:(e,t,r)=>{"use strict";function i(e){return n(0,"ok",e||[])}function s(){return n(0,"ok")}function a(e){return n(-1,e)}function n(e,t,r){let i={code:e,message:t,data:r};return r&&(i.data=r),Response.json(i)}r.d(t,{DQ:()=>i,YS:()=>a,j7:()=>n,rn:()=>s})},29294:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-async-storage.external.js")},34631:e=>{"use strict";e.exports=require("tls")},39201:(e,t,r)=>{"use strict";r.d(t,{T:()=>a});var i=r(72784);r(97468);var s=r(62225);async function a(){let e=await (0,s.b3)();return e.get("cf-connecting-ip")||e.get("x-real-ip")||(e.get("x-forwarded-for")||"127.0.0.1").split(",")[0]}(0,r(78564).D)([a]),(0,i.A)(a,"00880263a376180560adeff1fdd2511a7543726d4a",null)},44870:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-route.runtime.prod.js")},55511:e=>{"use strict";e.exports=require("crypto")},55591:e=>{"use strict";e.exports=require("https")},57975:e=>{"use strict";e.exports=require("node:util")},60270:(e,t,r)=>{"use strict";r.d(t,{lC:()=>l,b:()=>d,om:()=>u,zQ:()=>c,kl:()=>_,Kp:()=>o,nv:()=>n,oY:()=>f,c9:()=>m,it:()=>p});var i=r(4995);function s(e,t="en",r="zh"){if(!e)return"";if("string"==typeof e)return e;if("object"==typeof e){if(e[t])return e[t];if(e[r])return e[r];let i=Object.values(e).filter(Boolean);if(i.length>0)return i[0]}return""}function a(e,t="en"){return{...e,model_name:s(e.model_name_i18n||e.model_name,t),description:s(e.description_i18n||e.description,t)}}async function n(e="en"){let t=(0,i.A)(),{data:r,error:s}=await t.from("ai_models").select("*").eq("is_active",!0).order("model_type",{ascending:!0}).order("credits_per_unit",{ascending:!0});if(s)throw s;return(r||[]).map(t=>a(t,e))}async function o(e,t="en"){let r=(0,i.A)(),{data:s,error:n}=await r.from("ai_models").select("*").eq("model_type",e).eq("is_active",!0).order("credits_per_unit",{ascending:!0});if(n)throw n;return(s||[]).map(e=>a(e,t))}async function u(e,t="en"){let r=(0,i.A)(),{data:s,error:n}=await r.from("ai_models").select("*").eq("model_id",e).eq("is_active",!0).single();return n?null:s?a(s,t):null}async function c(e,t="en"){let r=(0,i.A)(),{data:s,error:n}=await r.from("ai_models").select("*").eq("model_id",e).single();return n?null:s?a(s,t):null}async function l(e,t,r){let s=(0,i.A)(),{data:a,error:n}=await s.rpc("calculate_model_cost",{p_model_id:e,p_input_size:t,p_output_size:r||0});if(n)throw n;return a||1}async function d(e){let t=(0,i.A)(),{data:r,error:s}=await t.from("ai_model_usage").insert(e).select().single();if(s)throw s;return r}async function p(e,t){let r=(0,i.A)(),{data:s,error:a}=await r.from("ai_model_usage").update(t).eq("request_id",e).select().single();return a?null:s}async function _(e){let t=(0,i.A)(),{data:r,error:s}=await t.from("ai_model_usage").select("*").eq("request_id",e).single();return s?null:r}async function f(e,t=1,r=50){let s=(0,i.A)(),{data:a,error:n}=await s.from("ai_model_usage").select("*").eq("user_uuid",e).order("created_at",{ascending:!1}).range((t-1)*r,t*r-1);if(n)throw n;return a||[]}async function m(e){let t=(0,i.A)(),{data:r,error:s}=await t.from("user_credits_usage_stats").select("*").eq("user_uuid",e).order("total_credits_consumed",{ascending:!1});if(s)throw s;return r||[]}},60736:(e,t,r)=>{"use strict";r.d(t,{$c:()=>c,TG:()=>l,qo:()=>p,ug:()=>_});var i=r(87830),s=r(35050),a=r(28432),n=r(4961),o=r(78054),u=r(62225);async function c(e){try{let t=await (0,s.Gs)(e.email);return t?(e.id=t.id,e.uuid=t.uuid,e.created_at=t.created_at):(await (0,s.HW)(e),await (0,i.d_)({user_uuid:e.uuid||"",trans_type:i.H3.NewUser,credits:i.rN.NewUserGet,expired_at:(0,n.MI)()})),e}catch(e){throw console.log("save user failed: ",e),e}}async function l(){let e="",t=await d();if(t&&t.startsWith("sk-"))return await (0,o.zz)(t)||"";let r=await (0,a.j2)();return r&&r.user&&r.user.uuid&&(e=r.user.uuid),e}async function d(){let e=(await (0,u.b3)()).get("Authorization");return e?e.replace("Bearer ",""):""}async function p(){let e="",t=await (0,a.j2)();return t&&t.user&&t.user.email&&(e=t.user.email),e}async function _(){let e=await l();if(e)return await (0,s.pX)(e)}},63033:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-unit-async-storage.external.js")},73402:(e,t,r)=>{"use strict";r.r(t),r.d(t,{patchFetch:()=>g,routeModule:()=>p,serverHooks:()=>m,workAsyncStorage:()=>_,workUnitAsyncStorage:()=>f});var i={};r.r(i),r.d(i,{POST:()=>d});var s=r(79341),a=r(91514),n=r(95673),o=r(29042),u=r(60736),c=r(60270),l=r(87830);async function d(e){try{let t;let{model:r,type:i,prompt:s,options:a}=await e.json();if(!r||!i)return(0,o.YS)("Missing required parameters: model, type");let n=await (0,u.TG)();if(!n)return(0,o.j7)(-2,"Authentication required");let d=await (0,c.om)(r);if(!d)return(0,o.YS)(`Model ${r} not found or inactive`);let p=await (0,l.mP)(n),_={};try{switch(i){case"text":let e=s?function(e){if(!e)return 0;let t=(e.match(/[\u4e00-\u9fff]/g)||[]).length;return Math.ceil(t+1.3*e.replace(/[\u4e00-\u9fff]/g,"").split(/\s+/).filter(e=>e.length>0).length)}(s):0,n=a?.max_tokens||1e3;t=await (0,c.lC)(r,e,n),_={input_tokens:e,estimated_output_tokens:n,total_tokens:e+n,credits_per_1k_tokens:d.credits_per_unit,calculation:`(${e} + ${n}) / 1000 * ${d.credits_per_unit}`};break;case"image":let o=a?.variants||1;t=d.credits_per_unit*o,_={base_cost:d.credits_per_unit,variants:o,calculation:`${d.credits_per_unit} * ${o}`};break;case"video":t=d.credits_per_unit,_={base_cost:d.credits_per_unit,calculation:`${d.credits_per_unit} (fixed cost per video)`};break;default:t=d.credits_per_unit,_={base_cost:d.credits_per_unit}}}catch(e){return(0,o.YS)("Failed to calculate cost")}let f=p.left_credits>=t,m=f?0:t-p.left_credits;return(0,o.DQ)({model:{id:d.model_id,name:d.model_name,type:d.model_type,provider:d.provider},cost_estimate:{estimated_credits:t,breakdown:_,unit_type:d.unit_type},user_credits:{available:p.left_credits,can_afford:f,shortfall:m,is_pro:p.is_pro||!1},recommendations:function(e,t,r){let i=[];return r.left_credits<t&&i.push("积分不足，建议充值或选择成本更低的模型"),"text"===e.model_type?t>10&&i.push("对于简单对话，可以考虑使用 gemini-2.5-flash-lite 以节省积分"):"image"===e.model_type&&t>60&&i.push("对于一般图像生成，可以考虑使用 flux-pro-1.1 以节省积分"),r.left_credits<100&&i.push("积分余额较低，建议及时充值以避免服务中断"),!r.is_pro&&r.left_credits<=10&&i.push("新用户建议先尝试低成本模型熟悉功能"),i}(d,t,p)})}catch(e){return console.error("Cost estimation failed:",e),(0,o.YS)("Cost estimation failed")}}let p=new s.AppRouteRouteModule({definition:{kind:a.RouteKind.APP_ROUTE,page:"/api/ai/estimate-cost/route",pathname:"/api/ai/estimate-cost",filename:"route",bundlePath:"app/api/ai/estimate-cost/route"},resolvedPagePath:"/Users/<USER>/My-Project/fluxkrea_test/app/api/ai/estimate-cost/route.ts",nextConfigOutput:"standalone",userland:i}),{workAsyncStorage:_,workUnitAsyncStorage:f,serverHooks:m}=p;function g(){return(0,n.patchFetch)({workAsyncStorage:_,workUnitAsyncStorage:f})}},74075:e=>{"use strict";e.exports=require("zlib")},77598:e=>{"use strict";e.exports=require("node:crypto")},78054:(e,t,r)=>{"use strict";r.d(t,{N1:()=>a,ai:()=>s,ox:()=>n,zz:()=>o});var i=r(4995),s=function(e){return e.Created="created",e.Deleted="deleted",e}({});async function a(e){let t=(0,i.A)(),{data:r,error:s}=await t.from("apikeys").insert(e);if(s)throw s;return r}async function n(e,t=1,r=50){let s=(t-1)*r,a=(0,i.A)(),{data:o,error:u}=await a.from("apikeys").select("*").eq("user_uuid",e).neq("status","deleted").order("created_at",{ascending:!1}).range(s,s+r-1);if(!u)return o}async function o(e){let t=(0,i.A)(),{data:r,error:s}=await t.from("apikeys").select("user_uuid").eq("api_key",e).eq("status","created").limit(1).single();if(!s)return r?.user_uuid}},79341:(e,t,r)=>{"use strict";e.exports=r(44870)},79428:e=>{"use strict";e.exports=require("buffer")},79551:e=>{"use strict";e.exports=require("url")},81630:e=>{"use strict";e.exports=require("http")},91645:e=>{"use strict";e.exports=require("net")},94735:e=>{"use strict";e.exports=require("events")},94813:()=>{}};var t=require("../../../../webpack-runtime.js");t.C(e);var r=e=>t(t.s=e),i=t.X(0,[5673,7711,8734,5327,2041,4524],()=>r(73402));module.exports=i})();