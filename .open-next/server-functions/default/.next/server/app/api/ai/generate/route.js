(()=>{var e={};e.id=6898,e.ids=[6898],e.modules={3295:e=>{"use strict";e.exports=require("next/dist/server/app-render/after-task-async-storage.external.js")},4573:e=>{"use strict";e.exports=require("node:buffer")},10846:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},11997:e=>{"use strict";e.exports=require("punycode")},19121:e=>{"use strict";e.exports=require("next/dist/server/app-render/action-async-storage.external.js")},20248:(e,t,r)=>{"use strict";r.d(t,{N:()=>a,X:()=>n});var o=r(6158),s=r.n(o),i=r(29452);class a{constructor(){let e=process.env.REPLICATE_API_TOKEN;if(!e)throw Error("REPLICATE_API_TOKEN environment variable is required");this.client=new(s())({auth:e})}async generateImage(e){let t=(0,i.YJ)();try{console.log("[Replicate Provider] ========== STARTING IMAGE GENERATION =========="),console.log(`[Replicate Provider] Request ID: ${t}`),console.log("[Replicate Provider] Full request:",JSON.stringify(e,null,2));let r={prompt:e.prompt};e.options?.aspect_ratio&&(r.aspect_ratio=e.options.aspect_ratio),e.options?.image&&(r.image=e.options.image),e.options?.prompt_strength!==void 0&&(r.prompt_strength=e.options.prompt_strength),e.options?.num_outputs!==void 0&&(r.num_outputs=e.options.num_outputs),e.options?.num_inference_steps!==void 0&&(r.num_inference_steps=e.options.num_inference_steps),e.options?.guidance!==void 0&&(r.guidance=e.options.guidance),e.options?.seed!==void 0&&(r.seed=e.options.seed),e.options?.output_format&&(r.output_format=e.options.output_format),e.options?.output_quality!==void 0&&(r.output_quality=e.options.output_quality),e.options?.disable_safety_checker!==void 0&&(r.disable_safety_checker=e.options.disable_safety_checker),e.options?.go_fast!==void 0&&(r.go_fast=e.options.go_fast),e.options?.megapixels&&(r.megapixels=e.options.megapixels),console.log("[Replicate Provider] ========== CALLING REPLICATE API =========="),console.log(`[Replicate Provider] Model: ${e.model}`),console.log("[Replicate Provider] Input parameters:",JSON.stringify(r,null,2));let o=await this.client.run(e.model,{input:r});if(console.log("[Replicate Provider] ========== REPLICATE API RESPONSE =========="),console.log("[Replicate Provider] Raw output type:",typeof o),console.log("[Replicate Provider] Raw output:",JSON.stringify(o,null,2)),console.log("[Replicate Provider] ========== PROCESSING OUTPUT =========="),console.log("[Replicate Provider] Output is array:",Array.isArray(o)),console.log("[Replicate Provider] Output length:",Array.isArray(o)?o.length:"N/A"),Array.isArray(o)&&o.length>0){console.log("[Replicate Provider] Processing array output...");let r=o.map((e,t)=>{if(console.log(`[Replicate Provider] Item ${t}:`,typeof e,e),"string"==typeof e)return console.log(`[Replicate Provider] Item ${t} is string URL:`,e),e;if(e&&"object"==typeof e){if("function"==typeof e.url){let r=e.url();return console.log(`[Replicate Provider] Item ${t} has url() method, result:`,r),r.toString()}if("url"in e&&"string"==typeof e.url)return console.log(`[Replicate Provider] Item ${t} is object with URL property:`,e.url),e.url}return console.log(`[Replicate Provider] Item ${t} is invalid, skipping`),null}).filter(Boolean);console.log("[Replicate Provider] Extracted URLs:",r);let s={id:t,status:"success",urls:r,usage:{credits_consumed:this.calculateCredits(e.model)}};return console.log("[Replicate Provider] ========== FINAL SUCCESS RESULT =========="),console.log("[Replicate Provider] Result:",JSON.stringify(s,null,2)),s}throw console.log("[Replicate Provider] ========== OUTPUT FORMAT ERROR =========="),console.log("[Replicate Provider] Expected array with length > 0, got:",typeof o,o),Error(`Invalid output format from Replicate API. Expected array, got: ${typeof o}`)}catch(r){console.log("[Replicate Provider] ========== ERROR OCCURRED =========="),console.error("[Replicate Provider] Error type:",typeof r),console.error("[Replicate Provider] Error message:",r instanceof Error?r.message:"Unknown error"),console.error("[Replicate Provider] Full error:",r),console.error("[Replicate Provider] Error stack:",r instanceof Error?r.stack:"No stack");let e={id:t,status:"failed",error:r instanceof Error?r.message:"Unknown error",usage:{credits_consumed:0}};return console.log("[Replicate Provider] ========== FINAL ERROR RESULT =========="),console.log("[Replicate Provider] Error result:",JSON.stringify(e,null,2)),e}}calculateCredits(e){return"black-forest-labs/flux-krea-dev"===e?25:20}isModelSupported(e){return["black-forest-labs/flux-krea-dev"].includes(e)}getSupportedModels(){return["black-forest-labs/flux-krea-dev"]}}let n=new a},21820:e=>{"use strict";e.exports=require("os")},26820:(e,t,r)=>{"use strict";r.r(t),r.d(t,{"00880263a376180560adeff1fdd2511a7543726d4a":()=>o.T});var o=r(39201)},27910:e=>{"use strict";e.exports=require("stream")},28432:(e,t,r)=>{"use strict";r.d(t,{j2:()=>f,Y9:()=>p});var o=r(62364),s=r(36157),i=r(72648),a=r(39201),n=r(4961),l=r(29452),u=r(60736);let c=[];c.push((0,s.A)({id:"google-one-tap",name:"google-one-tap",credentials:{credential:{type:"text"}},async authorize(e,t){let r=e.credential,o=await fetch("https://oauth2.googleapis.com/tokeninfo?id_token="+r);if(!o.ok)return console.log("Failed to verify token"),null;let s=await o.json();if(!s)return console.log("invalid payload from token"),null;let{email:i,sub:a,given_name:n,family_name:l,email_verified:u,picture:c}=s;return i?{id:a,name:[n,l].join(" "),email:i,image:c,emailVerified:u?new Date:null}:(console.log("invalid email in payload"),null)}})),process.env.AUTH_GOOGLE_ID&&process.env.AUTH_GOOGLE_SECRET&&c.push((0,i.A)({clientId:process.env.AUTH_GOOGLE_ID,clientSecret:process.env.AUTH_GOOGLE_SECRET,authorization:{params:{prompt:"consent",access_type:"offline",response_type:"code"}}})),c.map(e=>{if("function"!=typeof e)return{id:e.id,name:e.name};{let t=e();return{id:t.id,name:t.name}}}).filter(e=>"google-one-tap"!==e.id);let{handlers:p,signIn:d,signOut:g,auth:f}=(0,o.Ay)({providers:c,pages:{signIn:"/auth/signin"},callbacks:{signIn:async({user:e,account:t,profile:r,email:o,credentials:s})=>!0,redirect:async({url:e,baseUrl:t})=>e.startsWith("/")?`${t}${e}`:new URL(e).origin===t?e:t,session:async({session:e,token:t,user:r})=>(t&&t.user&&t.user&&(e.user=t.user),e),async jwt({token:e,user:t,account:r}){try{if(t&&t.email&&r){let o={uuid:(0,l.YJ)(),email:t.email,nickname:t.name||"",avatar_url:t.image||"",signin_type:r.type,signin_provider:r.provider,signin_openid:r.providerAccountId,created_at:(0,n.iq)(),signin_ip:await (0,a.T)()};try{let t=await (0,u.$c)(o);e.user={uuid:t.uuid,email:t.email,nickname:t.nickname,avatar_url:t.avatar_url,created_at:t.created_at}}catch(e){console.error("save user failed:",e)}}return e}catch(t){return console.error("jwt callback error:",t),e}}}})},29021:e=>{"use strict";e.exports=require("fs")},29294:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-async-storage.external.js")},29345:e=>{function t(e){var t=Error("Cannot find module '"+e+"'");throw t.code="MODULE_NOT_FOUND",t}t.keys=()=>[],t.resolve=t,t.id=29345,e.exports=t},33873:e=>{"use strict";e.exports=require("path")},34631:e=>{"use strict";e.exports=require("tls")},37423:(e,t,r)=>{"use strict";r.d(t,{m:()=>i});var o=r(91043),s=r(43637);function i(e){return new a(e)}class a{constructor(e){this.s3=new o.S3Client({endpoint:e?.endpoint||process.env.STORAGE_ENDPOINT||"",region:e?.region||process.env.STORAGE_REGION||"auto",credentials:{accessKeyId:e?.accessKey||process.env.STORAGE_ACCESS_KEY||"",secretAccessKey:e?.secretKey||process.env.STORAGE_SECRET_KEY||""}})}async uploadFile({body:e,key:t,contentType:r,bucket:o,onProgress:i,disposition:a="inline"}){if(o||(o=process.env.STORAGE_BUCKET||""),!o)throw Error("Bucket is required");let n=new s._({client:this.s3,params:{Bucket:o,Key:t,Body:e,ContentDisposition:a,...r&&{ContentType:r}}});i&&n.on("httpUploadProgress",e=>{i((e.loaded||0)/(e.total||1)*100)});let l=await n.done();return{location:l.Location,bucket:l.Bucket,key:l.Key,filename:l.Key?.split("/").pop(),url:process.env.STORAGE_DOMAIN?`${process.env.STORAGE_DOMAIN}/${o}/${l.Key}`:l.Location}}async downloadAndUpload({url:e,key:t,bucket:r,contentType:o,disposition:s="inline"}){let i=await fetch(e);if(!i.ok)throw Error(`HTTP error! status: ${i.status}`);if(!i.body)throw Error("No body in response");let a=await i.arrayBuffer(),n=Buffer.from(a);return this.uploadFile({body:n,key:t,bucket:r,contentType:o,disposition:s})}}},39201:(e,t,r)=>{"use strict";r.d(t,{T:()=>i});var o=r(72784);r(97468);var s=r(62225);async function i(){let e=await (0,s.b3)();return e.get("cf-connecting-ip")||e.get("x-real-ip")||(e.get("x-forwarded-for")||"127.0.0.1").split(",")[0]}(0,r(78564).D)([i]),(0,o.A)(i,"00880263a376180560adeff1fdd2511a7543726d4a",null)},44870:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-route.runtime.prod.js")},48786:(e,t,r)=>{"use strict";r.r(t),r.d(t,{patchFetch:()=>E,routeModule:()=>v,serverHooks:()=>w,workAsyncStorage:()=>h,workUnitAsyncStorage:()=>R});var o={};r.r(o),r.d(o,{POST:()=>y});var s=r(79341),i=r(91514),a=r(95673),n=r(29042),l=r(60736),u=r(99353),c=r(29452),p=r(4961),d=r(60270),g=r(20248),f=r(94909);class m{constructor(){this.replicateProvider=new g.N}async processRequest(e,t){let r=(0,c.YJ)();console.log(`[Replicate AI Service] Processing request for user: ${e}, model: ${t.model}`);let o=await (0,d.om)(t.model);if(!o)throw Error(`Model ${t.model} not found`);if("replicate"!==o.provider)throw Error(`Model ${t.model} is not a Replicate model`);let s=await this.estimateCost(t,o);console.log(`[Replicate AI Service] Estimated cost: ${s} credits`),await (0,d.b)({user_uuid:e,model_id:t.model,request_id:r,credits_consumed:s,status:"pending",request_params:t,started_at:(0,p.iq)()}),console.log(`[Replicate AI Service] Created usage record: ${r}`);try{let e;if("image"===t.type)e=await this.handleImageGeneration(t,r);else throw Error(`Unsupported request type: ${t.type} for Replicate provider`);return"success"===e.status&&(await (0,d.it)(r,{status:"success",output_size:this.calculateOutputSize(e),response_data:e,completed_at:(0,p.iq)()}),console.log("[Replicate AI Service] Updated usage record to success")),e}catch(e){throw console.error("[Replicate AI Service] Request failed:",e),await (0,d.it)(r,{status:"failed",error_reason:"error",error_detail:e instanceof Error?e.message:"Unknown error",completed_at:(0,p.iq)()}),e}}async handleImageGeneration(e,t){console.log(`[Replicate AI Service] Starting image generation for model: ${e.model}`);let r=await (0,d.om)(e.model);if(!r)throw Error(`Model ${e.model} not found`);let o={model:e.model,prompt:e.prompt,options:{aspect_ratio:e.options?.aspectRatio,num_outputs:e.options?.variants||1,output_format:e.options?.output_format||"webp",output_quality:e.options?.output_quality||90,guidance:e.options?.guidance,num_inference_steps:e.options?.num_inference_steps,prompt_strength:e.options?.prompt_strength,seed:e.options?.seed,disable_safety_checker:e.options?.disable_safety_checker,go_fast:e.options?.go_fast,megapixels:e.options?.megapixels,image:e.options?.uploadedImages?.[0]}};console.log("[Replicate AI Service] Calling Replicate API with request:",o);let s=await this.replicateProvider.generateImage(o);if(console.log("[Replicate AI Service] Replicate response:",s),"failed"===s.status)throw Error(s.error||"Replicate API failed");if("success"===s.status&&s.urls){console.log(`[Replicate AI Service] Transferring ${s.urls.length} files to our storage`);let o=await (0,f.fh)(s.urls,"image",t),i=o.filter(e=>e.success),a=o.filter(e=>!e.success);if(a.length>0&&console.warn("[Replicate AI Service] Some file transfers failed:",a),0===i.length)throw Error("All file transfers failed");let n=i.map(e=>({url:e.url,width:1024,height:1024}));return console.log(`[Replicate AI Service] Successfully transferred ${n.length} images`),{id:t,type:"image",status:"success",result:{images:n},usage:{credits_consumed:r.credits_per_unit*(e.options?.variants||1)}}}return"pending"===s.status?{id:t,type:"image",status:"pending",usage:{credits_consumed:r.credits_per_unit*(e.options?.variants||1)}}:{id:t,type:"image",status:"failed",error:{reason:"error",detail:"No images returned from Replicate API"},usage:{credits_consumed:r.credits_per_unit*(e.options?.variants||1)}}}async estimateCost(e,t){if("image"===e.type){let r=e.options?.variants||1;return t.credits_per_unit*r}return t.credits_per_unit}calculateOutputSize(e){return e.result?.images?e.result.images.length:0}isModelSupported(e){return this.replicateProvider.isModelSupported(e)}getSupportedModels(){return this.replicateProvider.getSupportedModels()}}new m;var _=r(87830);async function y(e){try{let t,r;let{model:o,type:s,prompt:i,options:a}=await e.json();if(!o||!s||!i)return(0,n.YS)("Missing required parameters: model, type, prompt");let c=await (0,l.TG)();if(!c)return(0,n.j7)(-2,"Authentication required");let p=await (0,d.om)(o);if(p||(p=await (0,d.zQ)(o)),!p)return(0,n.YS)(`Model ${o} not found`);let g={model:o,type:s,prompt:i,options:{...a,uploadedImages:a?.uploadedImages||[],referenceImages:a?.referenceImages||[],firstFrameUrl:a?.firstFrameUrl}};try{switch(s){case"text":let e=function(e){let t=(e.match(/[\u4e00-\u9fff]/g)||[]).length;return Math.ceil(t+1.3*e.replace(/[\u4e00-\u9fff]/g,"").split(/\s+/).filter(e=>e.length>0).length)}(i),r=a?.max_tokens||1e3;t=await (0,d.lC)(o,e,r);break;case"image":let n=a?.variants||1;t=p.credits_per_unit*n;break;default:t=p.credits_per_unit}}catch(e){return(0,n.YS)("Failed to calculate cost")}if(!await (0,_.ll)(c,t))return(0,n.j7)(-3,"Insufficient credits",{required_credits:t,error_code:"INSUFFICIENT_CREDITS"});"replicate"===p.provider?(console.log(`[AI Generate] Using Replicate provider for model: ${o}`),r=new m):(console.log(`[AI Generate] Using GRSAI provider for model: ${o}`),r=new u.U);try{let e=await r.processRequest(c,g);if("text"===s&&a?.stream)return new Response(JSON.stringify(e),{headers:{"Content-Type":"application/json"}});return"success"===e.status&&await (0,_.Ty)({user_uuid:c,model_id:o,request_id:e.id,credits:e.usage?.credits_consumed||t}),(0,n.DQ)(e)}catch(t){console.error("AI generation failed:",t);let e=t instanceof Error?t.message:"Unknown error";return(0,n.YS)(`AI generation failed: ${e}`)}}catch(e){return console.error("Request processing failed:",e),(0,n.YS)("Request processing failed")}}let v=new s.AppRouteRouteModule({definition:{kind:i.RouteKind.APP_ROUTE,page:"/api/ai/generate/route",pathname:"/api/ai/generate",filename:"route",bundlePath:"app/api/ai/generate/route"},resolvedPagePath:"/Users/<USER>/My-Project/fluxkrea_test/app/api/ai/generate/route.ts",nextConfigOutput:"standalone",userland:o}),{workAsyncStorage:h,workUnitAsyncStorage:R,serverHooks:w}=v;function E(){return(0,a.patchFetch)({workAsyncStorage:h,workUnitAsyncStorage:R})}},55511:e=>{"use strict";e.exports=require("crypto")},55591:e=>{"use strict";e.exports=require("https")},57075:e=>{"use strict";e.exports=require("node:stream")},57975:e=>{"use strict";e.exports=require("node:util")},60736:(e,t,r)=>{"use strict";r.d(t,{$c:()=>u,TG:()=>c,qo:()=>d,ug:()=>g});var o=r(87830),s=r(35050),i=r(28432),a=r(4961),n=r(78054),l=r(62225);async function u(e){try{let t=await (0,s.Gs)(e.email);return t?(e.id=t.id,e.uuid=t.uuid,e.created_at=t.created_at):(await (0,s.HW)(e),await (0,o.d_)({user_uuid:e.uuid||"",trans_type:o.H3.NewUser,credits:o.rN.NewUserGet,expired_at:(0,a.MI)()})),e}catch(e){throw console.log("save user failed: ",e),e}}async function c(){let e="",t=await p();if(t&&t.startsWith("sk-"))return await (0,n.zz)(t)||"";let r=await (0,i.j2)();return r&&r.user&&r.user.uuid&&(e=r.user.uuid),e}async function p(){let e=(await (0,l.b3)()).get("Authorization");return e?e.replace("Bearer ",""):""}async function d(){let e="",t=await (0,i.j2)();return t&&t.user&&t.user.email&&(e=t.user.email),e}async function g(){let e=await c();if(e)return await (0,s.pX)(e)}},63033:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-unit-async-storage.external.js")},73496:e=>{"use strict";e.exports=require("http2")},74075:e=>{"use strict";e.exports=require("zlib")},77598:e=>{"use strict";e.exports=require("node:crypto")},78054:(e,t,r)=>{"use strict";r.d(t,{N1:()=>i,ai:()=>s,ox:()=>a,zz:()=>n});var o=r(4995),s=function(e){return e.Created="created",e.Deleted="deleted",e}({});async function i(e){let t=(0,o.A)(),{data:r,error:s}=await t.from("apikeys").insert(e);if(s)throw s;return r}async function a(e,t=1,r=50){let s=(t-1)*r,i=(0,o.A)(),{data:n,error:l}=await i.from("apikeys").select("*").eq("user_uuid",e).neq("status","deleted").order("created_at",{ascending:!1}).range(s,s+r-1);if(!l)return n}async function n(e){let t=(0,o.A)(),{data:r,error:s}=await t.from("apikeys").select("user_uuid").eq("api_key",e).eq("status","created").limit(1).single();if(!s)return r?.user_uuid}},79428:e=>{"use strict";e.exports=require("buffer")},79551:e=>{"use strict";e.exports=require("url")},81630:e=>{"use strict";e.exports=require("http")},91043:e=>{"use strict";e.exports=require("@aws-sdk/client-s3")},91645:e=>{"use strict";e.exports=require("net")},94735:e=>{"use strict";e.exports=require("events")},94909:(e,t,r)=>{"use strict";r.d(t,{XW:()=>n,fh:()=>a,xO:()=>l,yy:()=>i});var o=r(37423),s=r(29452);async function i(e,t="image",r){try{console.log(`[File Transfer] Starting transfer from: ${e}`);let i=r||(0,s.YJ)(),a=function(e){try{return new URL(e).pathname.split(".").pop()||"bin"}catch(e){return"bin"}}(e),n={png:"image/png",jpg:"image/jpeg",jpeg:"image/jpeg",webp:"image/webp",gif:"image/gif",bmp:"image/bmp",svg:"image/svg+xml",mp4:"video/mp4",avi:"video/x-msvideo",mov:"video/quicktime",webm:"video/webm",mkv:"video/x-matroska"}[a.toLowerCase()]||"application/octet-stream",l=process.env.STORAGE_BUCKET||"shipany-test",u=`ai-generated/${t}/${i}.${a}`;console.log(`[File Transfer] Target bucket: ${l}, key: ${u}, Content-Type: ${n}`);let c=(0,o.m)(),p=await c.downloadAndUpload({url:e,key:u,bucket:l,contentType:n,disposition:"inline"});return console.log("[File Transfer] Transfer successful:",p),{success:!0,url:p.url,key:p.key}}catch(e){return console.error("[File Transfer] Transfer failed:",e),{success:!1,error:e instanceof Error?e.message:"Unknown error"}}}async function a(e,t="image",r){let o=[];for(let s=0;s<e.length;s++){let a=e[s],n=r?e.length>1?`${r}_${s}`:r:void 0,l=await i(a,t,n);o.push({originalUrl:a,...l})}return o}function n(e){let t=[];return e.url&&t.push(e.url),e.results&&Array.isArray(e.results)&&e.results.forEach(e=>{e.url&&t.push(e.url)}),e.data&&e.data.url&&t.push(e.data.url),t}function l(e,t){let r=JSON.parse(JSON.stringify(e));return r.url&&t[r.url]&&(r.url=t[r.url]),r.results&&Array.isArray(r.results)&&r.results.forEach(e=>{e.url&&t[e.url]&&(e.url=t[e.url])}),r.data&&r.data.url&&t[r.data.url]&&(r.data.url=t[r.data.url]),r}}};var t=require("../../../../webpack-runtime.js");t.C(e);var r=e=>t(t.s=e),o=t.X(0,[5673,7711,8734,5327,2041,6329,6158,4524,1294],()=>r(48786));module.exports=o})();