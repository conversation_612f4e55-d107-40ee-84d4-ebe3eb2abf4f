"use strict";(()=>{var e={};e.id=6384,e.ids=[6384],e.modules={3295:e=>{e.exports=require("next/dist/server/app-render/after-task-async-storage.external.js")},4573:e=>{e.exports=require("node:buffer")},10846:e=>{e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},11997:e=>{e.exports=require("punycode")},19121:e=>{e.exports=require("next/dist/server/app-render/action-async-storage.external.js")},21820:e=>{e.exports=require("os")},26820:(e,r,t)=>{t.r(r),t.d(r,{"00880263a376180560adeff1fdd2511a7543726d4a":()=>s.T});var s=t(39201)},27910:e=>{e.exports=require("stream")},28432:(e,r,t)=>{t.d(r,{j2:()=>g,Y9:()=>c});var s=t(62364),a=t(36157),i=t(72648),o=t(39201),n=t(4961),u=t(29452),l=t(60736);let d=[];d.push((0,a.A)({id:"google-one-tap",name:"google-one-tap",credentials:{credential:{type:"text"}},async authorize(e,r){let t=e.credential,s=await fetch("https://oauth2.googleapis.com/tokeninfo?id_token="+t);if(!s.ok)return console.log("Failed to verify token"),null;let a=await s.json();if(!a)return console.log("invalid payload from token"),null;let{email:i,sub:o,given_name:n,family_name:u,email_verified:l,picture:d}=a;return i?{id:o,name:[n,u].join(" "),email:i,image:d,emailVerified:l?new Date:null}:(console.log("invalid email in payload"),null)}})),process.env.AUTH_GOOGLE_ID&&process.env.AUTH_GOOGLE_SECRET&&d.push((0,i.A)({clientId:process.env.AUTH_GOOGLE_ID,clientSecret:process.env.AUTH_GOOGLE_SECRET,authorization:{params:{prompt:"consent",access_type:"offline",response_type:"code"}}})),d.map(e=>{if("function"!=typeof e)return{id:e.id,name:e.name};{let r=e();return{id:r.id,name:r.name}}}).filter(e=>"google-one-tap"!==e.id);let{handlers:c,signIn:p,signOut:f,auth:g}=(0,s.Ay)({providers:d,pages:{signIn:"/auth/signin"},callbacks:{signIn:async({user:e,account:r,profile:t,email:s,credentials:a})=>!0,redirect:async({url:e,baseUrl:r})=>e.startsWith("/")?`${r}${e}`:new URL(e).origin===r?e:r,session:async({session:e,token:r,user:t})=>(r&&r.user&&r.user&&(e.user=r.user),e),async jwt({token:e,user:r,account:t}){try{if(r&&r.email&&t){let s={uuid:(0,u.YJ)(),email:r.email,nickname:r.name||"",avatar_url:r.image||"",signin_type:t.type,signin_provider:t.provider,signin_openid:t.providerAccountId,created_at:(0,n.iq)(),signin_ip:await (0,o.T)()};try{let r=await (0,l.$c)(s);e.user={uuid:r.uuid,email:r.email,nickname:r.nickname,avatar_url:r.avatar_url,created_at:r.created_at}}catch(e){console.error("save user failed:",e)}}return e}catch(r){return console.error("jwt callback error:",r),e}}}})},29021:e=>{e.exports=require("fs")},29294:e=>{e.exports=require("next/dist/server/app-render/work-async-storage.external.js")},33873:e=>{e.exports=require("path")},34631:e=>{e.exports=require("tls")},37423:(e,r,t)=>{t.d(r,{m:()=>i});var s=t(91043),a=t(43637);function i(e){return new o(e)}class o{constructor(e){this.s3=new s.S3Client({endpoint:e?.endpoint||process.env.STORAGE_ENDPOINT||"",region:e?.region||process.env.STORAGE_REGION||"auto",credentials:{accessKeyId:e?.accessKey||process.env.STORAGE_ACCESS_KEY||"",secretAccessKey:e?.secretKey||process.env.STORAGE_SECRET_KEY||""}})}async uploadFile({body:e,key:r,contentType:t,bucket:s,onProgress:i,disposition:o="inline"}){if(s||(s=process.env.STORAGE_BUCKET||""),!s)throw Error("Bucket is required");let n=new a._({client:this.s3,params:{Bucket:s,Key:r,Body:e,ContentDisposition:o,...t&&{ContentType:t}}});i&&n.on("httpUploadProgress",e=>{i((e.loaded||0)/(e.total||1)*100)});let u=await n.done();return{location:u.Location,bucket:u.Bucket,key:u.Key,filename:u.Key?.split("/").pop(),url:process.env.STORAGE_DOMAIN?`${process.env.STORAGE_DOMAIN}/${s}/${u.Key}`:u.Location}}async downloadAndUpload({url:e,key:r,bucket:t,contentType:s,disposition:a="inline"}){let i=await fetch(e);if(!i.ok)throw Error(`HTTP error! status: ${i.status}`);if(!i.body)throw Error("No body in response");let o=await i.arrayBuffer(),n=Buffer.from(o);return this.uploadFile({body:n,key:r,bucket:t,contentType:s,disposition:a})}}},39201:(e,r,t)=>{t.d(r,{T:()=>i});var s=t(72784);t(97468);var a=t(62225);async function i(){let e=await (0,a.b3)();return e.get("cf-connecting-ip")||e.get("x-real-ip")||(e.get("x-forwarded-for")||"127.0.0.1").split(",")[0]}(0,t(78564).D)([i]),(0,s.A)(i,"00880263a376180560adeff1fdd2511a7543726d4a",null)},44870:e=>{e.exports=require("next/dist/compiled/next-server/app-route.runtime.prod.js")},54523:(e,r,t)=>{t.r(r),t.d(r,{patchFetch:()=>k,routeModule:()=>w,serverHooks:()=>A,workAsyncStorage:()=>h,workUnitAsyncStorage:()=>v});var s={};t.r(s),t.d(s,{POST:()=>g});var a=t(79341),i=t(91514),o=t(95673),n=t(29042),u=t(60736),l=t(99353),d=t(60270),c=t(4961),p=t(94909),f=t(87830);async function g(e){try{let r;let{request_id:t,task_id:s}=await e.json();if(console.log(`[Result API] Received request - request_id: ${t}, task_id: ${s}`),!t&&!s)return(0,n.YS)("Missing required parameter: request_id or task_id");let a=await (0,u.TG)();if(!a)return(0,n.j7)(-2,"Authentication required");let i=s;if(t){if(console.log(`[Result API] Looking up usage record for request_id: ${t}`),!(r=await (0,d.kl)(t)))return console.log(`[Result API] No usage record found for request_id: ${t}`),(0,n.YS)("Request not found");if(console.log("[Result API] Found usage record:",r),r.user_uuid!==a)return(0,n.YS)("Access denied");r.response_data&&r.response_data.data&&r.response_data.data.id?(i=r.response_data.data.id,console.log(`[Result API] Extracted GRSAI task ID from usage: ${i}`)):r.response_data&&r.response_data.id&&(i=r.response_data.id,console.log(`[Result API] Extracted GRSAI task ID from usage (direct): ${i}`))}if(!i&&t&&(console.log(`[Result API] Using request_id as task_id: ${t}`),i=t),!i)return console.log("[Result API] No task ID available"),(0,n.YS)("Task ID not found");try{console.log(`[Result API] Querying GRSAI result for task ID: ${i}`);let e=new l.g,o=await e.getResult(i);if(console.log("[Result API] GRSAI result response:",o),0!==o.code)return console.log(`[Result API] GRSAI API error: code=${o.code}, msg=${o.msg}`),(0,n.YS)(`GRSAI API error: ${o.msg}`);let u=o.data;if(console.log("[Result API] Task data:",u),!r&&s){console.log("[Result API] Direct task query without usage record");let e=m(s),r={id:s,type:e,status:y(u.status),progress:u.progress,result:_(u,e),error:"failed"===u.status?{reason:u.failure_reason||"error",detail:u.error||"Unknown error"}:void 0};return(0,n.DQ)(r)}let g=r?m(r.model_id):m(i),w={id:t||i,type:g,status:y(u.status),progress:u.progress,result:_(u,g),error:"failed"===u.status?{reason:u.failure_reason||"error",detail:u.error||"Unknown error"}:void 0};if("success"===w.status&&u.url){if(console.log("[Result API] Task completed successfully, starting credit deduction and file transfer"),r&&"success"!==r.status)try{await (0,f.Ty)({user_uuid:a,model_id:r.model_id,request_id:t,credits:r.credits_consumed}),console.log(`[Result API] Credits deducted: ${r.credits_consumed} for request ${t}`)}catch(e){console.error("[Result API] Failed to deduct credits:",e)}try{let e=(0,p.XW)(u);if(console.log("[Result API] URLs to transfer:",e),e.length>0){let s=r?.model_id?.includes("veo")?"video":"image",a=await (0,p.fh)(e,s,t);console.log("[Result API] Transfer results:",a);let i=a.filter(e=>e.success);if(i.length>0){let e={};i.forEach(r=>{r.url&&(e[r.originalUrl]=r.url)});let s=(0,p.xO)(u,e);console.log("[Result API] Updated URLs in response data"),w.result=_(s,g),r&&await (0,d.it)(t,{status:"success",response_data:{...r.response_data,...s},completed_at:(0,c.iq)()})}else console.log("[Result API] File transfer failed, using original URLs"),r&&await (0,d.it)(t,{status:"success",response_data:{...r.response_data,...u},completed_at:(0,c.iq)()})}}catch(e){console.error("[Result API] File transfer error:",e),r&&await (0,d.it)(t,{status:"success",response_data:{...r.response_data,...u},completed_at:(0,c.iq)()})}}else if(r&&r.status!==w.status){if("failed"===w.status&&w.error?.reason==="error"&&"failed"!==r.status)try{await (0,f.jL)({user_uuid:a,model_id:r.model_id,request_id:t,credits:r.credits_consumed,original_trans_no:`${t}_deduction`}),console.log(`[Result API] Credits refunded: ${r.credits_consumed} for failed request ${t}`)}catch(e){console.error("[Result API] Failed to refund credits:",e)}await (0,d.it)(t,{status:"success"===w.status?"success":"failed"===w.status?"failed":"pending",response_data:{...r.response_data,...u},completed_at:"success"===w.status||"failed"===w.status?(0,c.iq)():void 0})}return(0,n.DQ)(w)}catch(e){return console.error("Failed to query GRSAI result:",e),(0,n.YS)("Failed to query result")}}catch(e){return console.error("Request processing failed:",e),(0,n.YS)("Request processing failed")}}function m(e){return e.includes("veo")?"video":e.includes("flux")||e.includes("image")||e.includes("sora")||e.match(/^[0-9a-f]{8}-[0-9a-f]{4}-[0-9a-f]{4}-[0-9a-f]{4}-[0-9a-f]{12}$/i)?"image":"text"}function y(e){switch(e){case"succeeded":return"success";case"failed":return"failed";case"running":return"running";default:return"pending"}}function _(e,r){if("succeeded"!==e.status||!e.url)return;let t=function(e){try{let r=new URL(e).pathname.toLowerCase().split(".").pop();if(r&&["png","jpg","jpeg","webp","gif","bmp","svg","tiff","ico"].includes(r))return"image";if(r&&["mp4","avi","mov","wmv","flv","webm","mkv","m4v","3gp"].includes(r))return"video"}catch(e){}return"unknown"}(e.url),s=!1;if("video"===r||"image"!==r&&"multimodal"!==r&&"video"===t)return{video:{url:e.url}};{let r=[],t=new Set;return e.url&&!t.has(e.url)&&(r.push({url:e.url,width:e.width||1024,height:e.height||1024}),t.add(e.url)),e.results&&Array.isArray(e.results)&&e.results.forEach(e=>{e.url&&!t.has(e.url)&&(r.push({url:e.url,width:e.width||1024,height:e.height||1024}),t.add(e.url))}),{images:r}}}let w=new a.AppRouteRouteModule({definition:{kind:i.RouteKind.APP_ROUTE,page:"/api/ai/result/route",pathname:"/api/ai/result",filename:"route",bundlePath:"app/api/ai/result/route"},resolvedPagePath:"/Users/<USER>/My-Project/fluxkrea_test/app/api/ai/result/route.ts",nextConfigOutput:"standalone",userland:s}),{workAsyncStorage:h,workUnitAsyncStorage:v,serverHooks:A}=w;function k(){return(0,o.patchFetch)({workAsyncStorage:h,workUnitAsyncStorage:v})}},55511:e=>{e.exports=require("crypto")},55591:e=>{e.exports=require("https")},57075:e=>{e.exports=require("node:stream")},57975:e=>{e.exports=require("node:util")},60736:(e,r,t)=>{t.d(r,{$c:()=>l,TG:()=>d,qo:()=>p,ug:()=>f});var s=t(87830),a=t(35050),i=t(28432),o=t(4961),n=t(78054),u=t(62225);async function l(e){try{let r=await (0,a.Gs)(e.email);return r?(e.id=r.id,e.uuid=r.uuid,e.created_at=r.created_at):(await (0,a.HW)(e),await (0,s.d_)({user_uuid:e.uuid||"",trans_type:s.H3.NewUser,credits:s.rN.NewUserGet,expired_at:(0,o.MI)()})),e}catch(e){throw console.log("save user failed: ",e),e}}async function d(){let e="",r=await c();if(r&&r.startsWith("sk-"))return await (0,n.zz)(r)||"";let t=await (0,i.j2)();return t&&t.user&&t.user.uuid&&(e=t.user.uuid),e}async function c(){let e=(await (0,u.b3)()).get("Authorization");return e?e.replace("Bearer ",""):""}async function p(){let e="",r=await (0,i.j2)();return r&&r.user&&r.user.email&&(e=r.user.email),e}async function f(){let e=await d();if(e)return await (0,a.pX)(e)}},63033:e=>{e.exports=require("next/dist/server/app-render/work-unit-async-storage.external.js")},73496:e=>{e.exports=require("http2")},74075:e=>{e.exports=require("zlib")},77598:e=>{e.exports=require("node:crypto")},78054:(e,r,t)=>{t.d(r,{N1:()=>i,ai:()=>a,ox:()=>o,zz:()=>n});var s=t(4995),a=function(e){return e.Created="created",e.Deleted="deleted",e}({});async function i(e){let r=(0,s.A)(),{data:t,error:a}=await r.from("apikeys").insert(e);if(a)throw a;return t}async function o(e,r=1,t=50){let a=(r-1)*t,i=(0,s.A)(),{data:n,error:u}=await i.from("apikeys").select("*").eq("user_uuid",e).neq("status","deleted").order("created_at",{ascending:!1}).range(a,a+t-1);if(!u)return n}async function n(e){let r=(0,s.A)(),{data:t,error:a}=await r.from("apikeys").select("user_uuid").eq("api_key",e).eq("status","created").limit(1).single();if(!a)return t?.user_uuid}},79428:e=>{e.exports=require("buffer")},79551:e=>{e.exports=require("url")},81630:e=>{e.exports=require("http")},91043:e=>{e.exports=require("@aws-sdk/client-s3")},91645:e=>{e.exports=require("net")},94735:e=>{e.exports=require("events")},94909:(e,r,t)=>{t.d(r,{XW:()=>n,fh:()=>o,xO:()=>u,yy:()=>i});var s=t(37423),a=t(29452);async function i(e,r="image",t){try{console.log(`[File Transfer] Starting transfer from: ${e}`);let i=t||(0,a.YJ)(),o=function(e){try{return new URL(e).pathname.split(".").pop()||"bin"}catch(e){return"bin"}}(e),n={png:"image/png",jpg:"image/jpeg",jpeg:"image/jpeg",webp:"image/webp",gif:"image/gif",bmp:"image/bmp",svg:"image/svg+xml",mp4:"video/mp4",avi:"video/x-msvideo",mov:"video/quicktime",webm:"video/webm",mkv:"video/x-matroska"}[o.toLowerCase()]||"application/octet-stream",u=process.env.STORAGE_BUCKET||"shipany-test",l=`ai-generated/${r}/${i}.${o}`;console.log(`[File Transfer] Target bucket: ${u}, key: ${l}, Content-Type: ${n}`);let d=(0,s.m)(),c=await d.downloadAndUpload({url:e,key:l,bucket:u,contentType:n,disposition:"inline"});return console.log("[File Transfer] Transfer successful:",c),{success:!0,url:c.url,key:c.key}}catch(e){return console.error("[File Transfer] Transfer failed:",e),{success:!1,error:e instanceof Error?e.message:"Unknown error"}}}async function o(e,r="image",t){let s=[];for(let a=0;a<e.length;a++){let o=e[a],n=t?e.length>1?`${t}_${a}`:t:void 0,u=await i(o,r,n);s.push({originalUrl:o,...u})}return s}function n(e){let r=[];return e.url&&r.push(e.url),e.results&&Array.isArray(e.results)&&e.results.forEach(e=>{e.url&&r.push(e.url)}),e.data&&e.data.url&&r.push(e.data.url),r}function u(e,r){let t=JSON.parse(JSON.stringify(e));return t.url&&r[t.url]&&(t.url=r[t.url]),t.results&&Array.isArray(t.results)&&t.results.forEach(e=>{e.url&&r[e.url]&&(e.url=r[e.url])}),t.data&&t.data.url&&r[t.data.url]&&(t.data.url=r[t.data.url]),t}}};var r=require("../../../../webpack-runtime.js");r.C(e);var t=e=>r(r.s=e),s=r.X(0,[5673,7711,8734,5327,2041,6329,4524,1294],()=>t(54523));module.exports=s})();