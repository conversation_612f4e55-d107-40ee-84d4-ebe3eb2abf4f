(()=>{var e={};e.id=1736,e.ids=[1736],e.modules={3295:e=>{"use strict";e.exports=require("next/dist/server/app-render/after-task-async-storage.external.js")},4573:e=>{"use strict";e.exports=require("node:buffer")},10846:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},11997:e=>{"use strict";e.exports=require("punycode")},13373:()=>{},19121:e=>{"use strict";e.exports=require("next/dist/server/app-render/action-async-storage.external.js")},26820:(e,t,r)=>{"use strict";r.r(t),r.d(t,{"00880263a376180560adeff1fdd2511a7543726d4a":()=>a.T});var a=r(39201)},27910:e=>{"use strict";e.exports=require("stream")},28432:(e,t,r)=>{"use strict";r.d(t,{j2:()=>m,Y9:()=>d});var a=r(62364),s=r(36157),n=r(72648),i=r(39201),u=r(4961),o=r(29452),c=r(60736);let l=[];l.push((0,s.A)({id:"google-one-tap",name:"google-one-tap",credentials:{credential:{type:"text"}},async authorize(e,t){let r=e.credential,a=await fetch("https://oauth2.googleapis.com/tokeninfo?id_token="+r);if(!a.ok)return console.log("Failed to verify token"),null;let s=await a.json();if(!s)return console.log("invalid payload from token"),null;let{email:n,sub:i,given_name:u,family_name:o,email_verified:c,picture:l}=s;return n?{id:i,name:[u,o].join(" "),email:n,image:l,emailVerified:c?new Date:null}:(console.log("invalid email in payload"),null)}})),process.env.AUTH_GOOGLE_ID&&process.env.AUTH_GOOGLE_SECRET&&l.push((0,n.A)({clientId:process.env.AUTH_GOOGLE_ID,clientSecret:process.env.AUTH_GOOGLE_SECRET,authorization:{params:{prompt:"consent",access_type:"offline",response_type:"code"}}})),l.map(e=>{if("function"!=typeof e)return{id:e.id,name:e.name};{let t=e();return{id:t.id,name:t.name}}}).filter(e=>"google-one-tap"!==e.id);let{handlers:d,signIn:p,signOut:_,auth:m}=(0,a.Ay)({providers:l,pages:{signIn:"/auth/signin"},callbacks:{signIn:async({user:e,account:t,profile:r,email:a,credentials:s})=>!0,redirect:async({url:e,baseUrl:t})=>e.startsWith("/")?`${t}${e}`:new URL(e).origin===t?e:t,session:async({session:e,token:t,user:r})=>(t&&t.user&&t.user&&(e.user=t.user),e),async jwt({token:e,user:t,account:r}){try{if(t&&t.email&&r){let a={uuid:(0,o.YJ)(),email:t.email,nickname:t.name||"",avatar_url:t.image||"",signin_type:r.type,signin_provider:r.provider,signin_openid:r.providerAccountId,created_at:(0,u.iq)(),signin_ip:await (0,i.T)()};try{let t=await (0,c.$c)(a);e.user={uuid:t.uuid,email:t.email,nickname:t.nickname,avatar_url:t.avatar_url,created_at:t.created_at}}catch(e){console.error("save user failed:",e)}}return e}catch(t){return console.error("jwt callback error:",t),e}}}})},29042:(e,t,r)=>{"use strict";function a(e){return i(0,"ok",e||[])}function s(){return i(0,"ok")}function n(e){return i(-1,e)}function i(e,t,r){let a={code:e,message:t,data:r};return r&&(a.data=r),Response.json(a)}r.d(t,{DQ:()=>a,YS:()=>n,j7:()=>i,rn:()=>s})},29294:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-async-storage.external.js")},34631:e=>{"use strict";e.exports=require("tls")},39201:(e,t,r)=>{"use strict";r.d(t,{T:()=>n});var a=r(72784);r(97468);var s=r(62225);async function n(){let e=await (0,s.b3)();return e.get("cf-connecting-ip")||e.get("x-real-ip")||(e.get("x-forwarded-for")||"127.0.0.1").split(",")[0]}(0,r(78564).D)([n]),(0,a.A)(n,"00880263a376180560adeff1fdd2511a7543726d4a",null)},44870:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-route.runtime.prod.js")},55511:e=>{"use strict";e.exports=require("crypto")},55591:e=>{"use strict";e.exports=require("https")},57928:(e,t,r)=>{"use strict";r.r(t),r.d(t,{patchFetch:()=>y,routeModule:()=>_,serverHooks:()=>g,workAsyncStorage:()=>m,workUnitAsyncStorage:()=>f});var a={};r.r(a),r.d(a,{GET:()=>d});var s=r(79341),n=r(91514),i=r(95673),u=r(29042),o=r(60736),c=r(60270),l=r(53928);async function d(e){try{let t=new URL(e.url),r=parseInt(t.searchParams.get("page")||"1"),a=parseInt(t.searchParams.get("limit")||"20"),s=t.searchParams.get("type"),n=await (0,o.TG)();if(!n)return(0,u.j7)(-2,"Authentication required");switch(s){case"usage":let i=await (0,c.oY)(n,r,a);return(0,u.DQ)({records:i,page:r,limit:a,total:i.length});case"credits":let d=await (0,l._3)(n,r,a);return(0,u.DQ)({records:d,page:r,limit:a,total:d?.length||0});case"stats":let _=await (0,c.c9)(n);return(0,u.DQ)({stats:_,summary:p(_)});default:let[m,f,g]=await Promise.all([(0,c.oY)(n,1,10),(0,l._3)(n,1,10),(0,c.c9)(n)]);return(0,u.DQ)({recent_usage:m,recent_credits:f,stats:g,summary:p(g)})}}catch(e){return console.error("Failed to fetch usage data:",e),(0,u.YS)("Failed to fetch usage data")}}function p(e){if(!e||0===e.length)return{total_usage_count:0,total_credits_consumed:0,most_used_model:null,success_rate:0,model_breakdown:[]};let t=e.reduce((e,t)=>e+(t.usage_count||0),0),r=e.reduce((e,t)=>e+(t.total_credits_consumed||0),0),a=e.reduce((e,t)=>e+(t.success_count||0),0),s=e.reduce((e,t)=>(e.usage_count||0)>(t.usage_count||0)?e:t);return{total_usage_count:t,total_credits_consumed:r,most_used_model:s&&s.model_id?{model_id:s.model_id,model_name:s.model_name,usage_count:s.usage_count}:null,success_rate:Math.round(100*(t>0?a/t*100:0))/100,model_breakdown:e.map(e=>({model_name:e.model_name||"",model_type:e.model_type||"",usage_count:e.usage_count||0,credits_consumed:e.total_credits_consumed||0}))}}let _=new s.AppRouteRouteModule({definition:{kind:n.RouteKind.APP_ROUTE,page:"/api/ai/usage/route",pathname:"/api/ai/usage",filename:"route",bundlePath:"app/api/ai/usage/route"},resolvedPagePath:"/Users/<USER>/My-Project/fluxkrea_test/app/api/ai/usage/route.ts",nextConfigOutput:"standalone",userland:a}),{workAsyncStorage:m,workUnitAsyncStorage:f,serverHooks:g}=_;function y(){return(0,i.patchFetch)({workAsyncStorage:m,workUnitAsyncStorage:f})}},57975:e=>{"use strict";e.exports=require("node:util")},60270:(e,t,r)=>{"use strict";r.d(t,{lC:()=>l,b:()=>d,om:()=>o,zQ:()=>c,kl:()=>_,Kp:()=>u,nv:()=>i,oY:()=>m,c9:()=>f,it:()=>p});var a=r(4995);function s(e,t="en",r="zh"){if(!e)return"";if("string"==typeof e)return e;if("object"==typeof e){if(e[t])return e[t];if(e[r])return e[r];let a=Object.values(e).filter(Boolean);if(a.length>0)return a[0]}return""}function n(e,t="en"){return{...e,model_name:s(e.model_name_i18n||e.model_name,t),description:s(e.description_i18n||e.description,t)}}async function i(e="en"){let t=(0,a.A)(),{data:r,error:s}=await t.from("ai_models").select("*").eq("is_active",!0).order("model_type",{ascending:!0}).order("credits_per_unit",{ascending:!0});if(s)throw s;return(r||[]).map(t=>n(t,e))}async function u(e,t="en"){let r=(0,a.A)(),{data:s,error:i}=await r.from("ai_models").select("*").eq("model_type",e).eq("is_active",!0).order("credits_per_unit",{ascending:!0});if(i)throw i;return(s||[]).map(e=>n(e,t))}async function o(e,t="en"){let r=(0,a.A)(),{data:s,error:i}=await r.from("ai_models").select("*").eq("model_id",e).eq("is_active",!0).single();return i?null:s?n(s,t):null}async function c(e,t="en"){let r=(0,a.A)(),{data:s,error:i}=await r.from("ai_models").select("*").eq("model_id",e).single();return i?null:s?n(s,t):null}async function l(e,t,r){let s=(0,a.A)(),{data:n,error:i}=await s.rpc("calculate_model_cost",{p_model_id:e,p_input_size:t,p_output_size:r||0});if(i)throw i;return n||1}async function d(e){let t=(0,a.A)(),{data:r,error:s}=await t.from("ai_model_usage").insert(e).select().single();if(s)throw s;return r}async function p(e,t){let r=(0,a.A)(),{data:s,error:n}=await r.from("ai_model_usage").update(t).eq("request_id",e).select().single();return n?null:s}async function _(e){let t=(0,a.A)(),{data:r,error:s}=await t.from("ai_model_usage").select("*").eq("request_id",e).single();return s?null:r}async function m(e,t=1,r=50){let s=(0,a.A)(),{data:n,error:i}=await s.from("ai_model_usage").select("*").eq("user_uuid",e).order("created_at",{ascending:!1}).range((t-1)*r,t*r-1);if(i)throw i;return n||[]}async function f(e){let t=(0,a.A)(),{data:r,error:s}=await t.from("user_credits_usage_stats").select("*").eq("user_uuid",e).order("total_credits_consumed",{ascending:!1});if(s)throw s;return r||[]}},60736:(e,t,r)=>{"use strict";r.d(t,{$c:()=>c,TG:()=>l,qo:()=>p,ug:()=>_});var a=r(87830),s=r(35050),n=r(28432),i=r(4961),u=r(78054),o=r(62225);async function c(e){try{let t=await (0,s.Gs)(e.email);return t?(e.id=t.id,e.uuid=t.uuid,e.created_at=t.created_at):(await (0,s.HW)(e),await (0,a.d_)({user_uuid:e.uuid||"",trans_type:a.H3.NewUser,credits:a.rN.NewUserGet,expired_at:(0,i.MI)()})),e}catch(e){throw console.log("save user failed: ",e),e}}async function l(){let e="",t=await d();if(t&&t.startsWith("sk-"))return await (0,u.zz)(t)||"";let r=await (0,n.j2)();return r&&r.user&&r.user.uuid&&(e=r.user.uuid),e}async function d(){let e=(await (0,o.b3)()).get("Authorization");return e?e.replace("Bearer ",""):""}async function p(){let e="",t=await (0,n.j2)();return t&&t.user&&t.user.email&&(e=t.user.email),e}async function _(){let e=await l();if(e)return await (0,s.pX)(e)}},63033:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-unit-async-storage.external.js")},74075:e=>{"use strict";e.exports=require("zlib")},77598:e=>{"use strict";e.exports=require("node:crypto")},78054:(e,t,r)=>{"use strict";r.d(t,{N1:()=>n,ai:()=>s,ox:()=>i,zz:()=>u});var a=r(4995),s=function(e){return e.Created="created",e.Deleted="deleted",e}({});async function n(e){let t=(0,a.A)(),{data:r,error:s}=await t.from("apikeys").insert(e);if(s)throw s;return r}async function i(e,t=1,r=50){let s=(t-1)*r,n=(0,a.A)(),{data:u,error:o}=await n.from("apikeys").select("*").eq("user_uuid",e).neq("status","deleted").order("created_at",{ascending:!1}).range(s,s+r-1);if(!o)return u}async function u(e){let t=(0,a.A)(),{data:r,error:s}=await t.from("apikeys").select("user_uuid").eq("api_key",e).eq("status","created").limit(1).single();if(!s)return r?.user_uuid}},79341:(e,t,r)=>{"use strict";e.exports=r(44870)},79428:e=>{"use strict";e.exports=require("buffer")},79551:e=>{"use strict";e.exports=require("url")},81630:e=>{"use strict";e.exports=require("http")},91645:e=>{"use strict";e.exports=require("net")},94735:e=>{"use strict";e.exports=require("events")},94813:()=>{}};var t=require("../../../../webpack-runtime.js");t.C(e);var r=e=>t(t.s=e),a=t.X(0,[5673,7711,8734,5327,2041,4524],()=>r(57928));module.exports=a})();