(()=>{var e={};e.id=3616,e.ids=[3616],e.modules={3295:e=>{"use strict";e.exports=require("next/dist/server/app-render/after-task-async-storage.external.js")},4573:e=>{"use strict";e.exports=require("node:buffer")},7890:(e,t,r)=>{Promise.resolve().then(r.bind(r,79513)),Promise.resolve().then(r.bind(r,80081)),Promise.resolve().then(r.bind(r,61716)),Promise.resolve().then(r.t.bind(r,59316,23))},8377:(e,t,r)=>{"use strict";r.d(t,{default:()=>n});var s=r(29037);(0,s.registerClientReference)(function(){throw Error("Attempted to call description() from the server but description is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"/Users/<USER>/My-Project/fluxkrea_test/components/blocks/data-charts/index.tsx","description");let n=(0,s.registerClientReference)(function(){throw Error("Attempted to call the default export of \"/Users/<USER>/My-Project/fluxkrea_test/components/blocks/data-charts/index.tsx\" from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"/Users/<USER>/My-Project/fluxkrea_test/components/blocks/data-charts/index.tsx","default")},10846:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},11997:e=>{"use strict";e.exports=require("punycode")},15691:(e,t,r)=>{"use strict";r.d(t,{St:()=>l,W5:()=>i,aV:()=>o,gg:()=>a,gx:()=>u,j7:()=>c,nz:()=>n,zX:()=>d});var s=r(4995),n=function(e){return e.Created="created",e.Deleted="deleted",e.Online="online",e.Offline="offline",e}({});async function i(e){let t=(0,s.A)(),{data:r,error:n}=await t.from("posts").insert(e);if(n)throw n;return r}async function a(e,t){let r=(0,s.A)(),{data:n,error:i}=await r.from("posts").update(t).eq("uuid",e);if(i)throw i;return n}async function o(e){let t=(0,s.A)(),{data:r,error:n}=await t.from("posts").select("*").eq("uuid",e).limit(1).single();if(!n)return r}async function l(e,t){let r=(0,s.A)(),{data:n,error:i}=await r.from("posts").select("*").eq("slug",e).eq("locale",t).limit(1).single();if(!i)return n}async function d(e=1,t=50){let r=(0,s.A)(),{data:n,error:i}=await r.from("posts").select("*").order("created_at",{ascending:!1}).range((e-1)*t,e*t-1);return i?[]:n}async function c(e,t=1,r=50){let n=(0,s.A)(),{data:i,error:a}=await n.from("posts").select("*").eq("locale",e).eq("status","online").order("created_at",{ascending:!1}).range((t-1)*r,t*r-1);return a?[]:i}async function u(){let e=(0,s.A)(),{data:t,error:r}=await e.from("posts").select("count",{count:"exact"});if(!r)return t[0].count}},18320:(e,t,r)=>{"use strict";r.r(t),r.d(t,{default:()=>f});var s=r(34411),n=r(58243),i=r(18586);function a({dataCards:e}){return(0,s.jsx)("div",{className:"*:data-[slot=card]:from-primary/5 *:data-[slot=card]:to-card dark:*:data-[slot=card]:bg-card grid grid-cols-1 gap-4 px-4 *:data-[slot=card]:bg-gradient-to-t *:data-[slot=card]:shadow-xs lg:px-6 @xl/main:grid-cols-2 @5xl/main:grid-cols-4",children:e.map((e,t)=>(0,s.jsxs)(i.Zp,{className:"@container/card",children:[(0,s.jsxs)(i.aR,{children:[(0,s.jsx)(i.BT,{children:e.title}),(0,s.jsx)(i.ZB,{className:"text-2xl font-semibold tabular-nums @[250px]/card:text-3xl",children:e.value}),(0,s.jsx)(i.X9,{children:e.label&&(0,s.jsx)(n.E,{variant:"outline",children:e.label})})]}),(0,s.jsxs)(i.wL,{className:"flex-col items-start gap-1.5 text-sm",children:[(0,s.jsx)("div",{className:"line-clamp-1 flex gap-2 font-medium",children:e.description}),(0,s.jsx)("div",{className:"text-muted-foreground",children:e.tip})]})]},t))})}var o=r(8377),l=r(27419),d=r(40109),c=r(35050),u=r(37318),p=r(15691);async function f(){let e=await (0,d.Yc)(),t=await (0,c.PD)(),r=await (0,u.lU)(),n=await (0,p.gx)(),i=[{title:"Total Users",label:"",value:(t||0).toString(),description:"Total users registered in the system"},{title:"Paid Orders",label:"",value:(e||0).toString(),description:"User Paid Orders in total"},{title:"System Posts",label:"",value:(n||0).toString(),description:"Posts in total"},{title:"User Feedbacks",label:"",value:(r||0).toString(),description:"Feedbacks in total"}],f=new Date;f.setDate(f.getDate()-90);let m=await (0,d.nz)(f.toISOString(),"paid"),x=await (0,c.in)(f.toISOString()),v=Array.from(new Set([...m?Array.from(m.keys()):[],...x?Array.from(x.keys()):[]])).sort().map(e=>({date:e,users:x?.get(e)||0,orders:m?.get(e)||0}));return(0,s.jsxs)("div",{className:"flex flex-col gap-4",children:[(0,s.jsx)(l.A,{}),(0,s.jsx)("div",{className:"flex flex-1 flex-col",children:(0,s.jsx)("div",{className:"@container/main flex flex-1 flex-col gap-2",children:(0,s.jsxs)("div",{className:"flex flex-col gap-4 py-4 md:gap-6 md:py-6",children:[(0,s.jsx)(a,{dataCards:i}),(0,s.jsx)("div",{className:"px-4 lg:px-6",children:(0,s.jsx)(o.default,{data:v,fields:[{key:"users",label:"Users",color:"var(--primary)"},{key:"orders",label:"Orders",color:"var(--secondary)"}],title:"Users and Orders Overview",description:"Daily users and orders data",defaultTimeRange:"90d"})})]})})})]})}},19121:e=>{"use strict";e.exports=require("next/dist/server/app-render/action-async-storage.external.js")},20392:(e,t,r)=>{"use strict";r.d(t,{DX:()=>a});var s=r(37582);function n(e,t){if("function"==typeof e)return e(t);null!=e&&(e.current=t)}var i=r(34411),a=function(e){let t=function(e){let t=s.forwardRef((e,t)=>{let{children:r,...i}=e;if(s.isValidElement(r)){var a;let e,o;let l=(a=r,(o=(e=Object.getOwnPropertyDescriptor(a.props,"ref")?.get)&&"isReactWarning"in e&&e.isReactWarning)?a.ref:(o=(e=Object.getOwnPropertyDescriptor(a,"ref")?.get)&&"isReactWarning"in e&&e.isReactWarning)?a.props.ref:a.props.ref||a.ref),d=function(e,t){let r={...t};for(let s in t){let n=e[s],i=t[s];/^on[A-Z]/.test(s)?n&&i?r[s]=(...e)=>{let t=i(...e);return n(...e),t}:n&&(r[s]=n):"style"===s?r[s]={...n,...i}:"className"===s&&(r[s]=[n,i].filter(Boolean).join(" "))}return{...e,...r}}(i,r.props);return r.type!==s.Fragment&&(d.ref=t?function(...e){return t=>{let r=!1,s=e.map(e=>{let s=n(e,t);return r||"function"!=typeof s||(r=!0),s});if(r)return()=>{for(let t=0;t<s.length;t++){let r=s[t];"function"==typeof r?r():n(e[t],null)}}}}(t,l):l),s.cloneElement(r,d)}return s.Children.count(r)>1?s.Children.only(null):null});return t.displayName=`${e}.SlotClone`,t}(e),r=s.forwardRef((e,r)=>{let{children:n,...a}=e,o=s.Children.toArray(n),d=o.find(l);if(d){let e=d.props.children,n=o.map(t=>t!==d?t:s.Children.count(e)>1?s.Children.only(null):s.isValidElement(e)?e.props.children:null);return(0,i.jsx)(t,{...a,ref:r,children:s.isValidElement(e)?s.cloneElement(e,void 0,n):null})}return(0,i.jsx)(t,{...a,ref:r,children:n})});return r.displayName=`${e}.Slot`,r}("Slot"),o=Symbol("radix.slottable");function l(e){return s.isValidElement(e)&&"function"==typeof e.type&&"__radixId"in e.type&&e.type.__radixId===o}},26820:(e,t,r)=>{"use strict";r.r(t),r.d(t,{"00880263a376180560adeff1fdd2511a7543726d4a":()=>s.T});var s=r(39201)},27910:e=>{"use strict";e.exports=require("stream")},29294:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-async-storage.external.js")},33873:e=>{"use strict";e.exports=require("path")},34631:e=>{"use strict";e.exports=require("tls")},37318:(e,t,r)=>{"use strict";r.d(t,{$0:()=>i,NW:()=>a,lU:()=>o});var s=r(4995),n=r(35050);async function i(e){let t=(0,s.A)(),{data:r,error:n}=await t.from("feedbacks").insert(e);if(n)throw n;return r}async function a(e=1,t=50){e<1&&(e=1),t<=0&&(t=50);let r=(e-1)*t,i=(0,s.A)(),{data:o,error:l}=await i.from("feedbacks").select("*").order("created_at",{ascending:!1}).range(r,r+t-1);if(l||!o||0===o.length)return[];let d=Array.from(new Set(o.map(e=>e.user_uuid))),c=await (0,n.QZ)(d);return o.map(e=>{let t=c.find(t=>t.uuid===e.user_uuid);return{...e,user:t}})}async function o(){let e=(0,s.A)(),{data:t,error:r}=await e.from("feedbacks").select("count",{count:"exact"});if(!r)return t[0].count}},38e3:(e,t,r)=>{"use strict";r.d(t,{F:()=>a});var s=r(1832);let n=e=>"boolean"==typeof e?`${e}`:0===e?"0":e,i=s.$,a=(e,t)=>r=>{var s;if((null==t?void 0:t.variants)==null)return i(e,null==r?void 0:r.class,null==r?void 0:r.className);let{variants:a,defaultVariants:o}=t,l=Object.keys(a).map(e=>{let t=null==r?void 0:r[e],s=null==o?void 0:o[e];if(null===t)return null;let i=n(t)||n(s);return a[e][i]}),d=r&&Object.entries(r).reduce((e,t)=>{let[r,s]=t;return void 0===s||(e[r]=s),e},{});return i(e,l,null==t?void 0:null===(s=t.compoundVariants)||void 0===s?void 0:s.reduce((e,t)=>{let{class:r,className:s,...n}=t;return Object.entries(n).every(e=>{let[t,r]=e;return Array.isArray(r)?r.includes({...o,...d}[t]):({...o,...d})[t]===r})?[...e,r,s]:e},[]),null==r?void 0:r.class,null==r?void 0:r.className)}},44842:(e,t,r)=>{Promise.resolve().then(r.bind(r,8377)),Promise.resolve().then(r.bind(r,70439)),Promise.resolve().then(r.bind(r,21218)),Promise.resolve().then(r.t.bind(r,10050,23))},55511:e=>{"use strict";e.exports=require("crypto")},55591:e=>{"use strict";e.exports=require("https")},57975:e=>{"use strict";e.exports=require("node:util")},58243:(e,t,r)=>{"use strict";r.d(t,{E:()=>l});var s=r(34411);r(37582);var n=r(20392),i=r(38e3),a=r(25039);let o=(0,i.F)("inline-flex items-center justify-center rounded-md border px-2 py-0.5 text-xs font-medium w-fit whitespace-nowrap shrink-0 [&>svg]:size-3 gap-1 [&>svg]:pointer-events-none focus-visible:border-ring focus-visible:ring-ring/50 focus-visible:ring-[3px] aria-invalid:ring-destructive/20 dark:aria-invalid:ring-destructive/40 aria-invalid:border-destructive transition-[color,box-shadow] overflow-hidden",{variants:{variant:{default:"border-transparent bg-primary text-primary-foreground [a&]:hover:bg-primary/90",secondary:"border-transparent bg-secondary text-secondary-foreground [a&]:hover:bg-secondary/90",destructive:"border-transparent bg-destructive text-white [a&]:hover:bg-destructive/90 focus-visible:ring-destructive/20 dark:focus-visible:ring-destructive/40 dark:bg-destructive/60",outline:"text-foreground [a&]:hover:bg-accent [a&]:hover:text-accent-foreground"}},defaultVariants:{variant:"default"}});function l({className:e,variant:t,asChild:r=!1,...i}){let l=r?n.DX:"span";return(0,s.jsx)(l,{"data-slot":"badge",className:(0,a.cn)(o({variant:t}),e),...i})}},63033:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-unit-async-storage.external.js")},71694:(e,t,r)=>{"use strict";r.r(t),r.d(t,{GlobalError:()=>a.a,__next_app__:()=>u,pages:()=>c,routeModule:()=>p,tree:()=>d});var s=r(43533),n=r(91514),i=r(17220),a=r.n(i),o=r(62931),l={};for(let e in o)0>["default","tree","pages","GlobalError","__next_app__","routeModule"].indexOf(e)&&(l[e]=()=>o[e]);r.d(t,l);let d={children:["",{children:["[locale]",{children:["(admin)",{children:["admin",{children:["__PAGE__",{},{page:[()=>Promise.resolve().then(r.bind(r,18320)),"/Users/<USER>/My-Project/fluxkrea_test/app/[locale]/(admin)/admin/page.tsx"]}]},{}]},{layout:[()=>Promise.resolve().then(r.bind(r,58770)),"/Users/<USER>/My-Project/fluxkrea_test/app/[locale]/(admin)/layout.tsx"]}]},{layout:[()=>Promise.resolve().then(r.bind(r,83581)),"/Users/<USER>/My-Project/fluxkrea_test/app/[locale]/layout.tsx"]}]},{"not-found":[()=>Promise.resolve().then(r.t.bind(r,53488,23)),"next/dist/client/components/not-found-error"],forbidden:[()=>Promise.resolve().then(r.t.bind(r,99017,23)),"next/dist/client/components/forbidden-error"],unauthorized:[()=>Promise.resolve().then(r.t.bind(r,90934,23)),"next/dist/client/components/unauthorized-error"]}]}.children,c=["/Users/<USER>/My-Project/fluxkrea_test/app/[locale]/(admin)/admin/page.tsx"],u={require:r,loadChunk:()=>Promise.resolve()},p=new s.AppPageRouteModule({definition:{kind:n.RouteKind.APP_PAGE,page:"/[locale]/(admin)/admin/page",pathname:"/[locale]/admin",bundlePath:"",filename:"",appPaths:[]},userland:{loaderTree:d}})},74075:e=>{"use strict";e.exports=require("zlib")},77598:e=>{"use strict";e.exports=require("node:crypto")},79428:e=>{"use strict";e.exports=require("buffer")},79551:e=>{"use strict";e.exports=require("url")},81630:e=>{"use strict";e.exports=require("http")},91645:e=>{"use strict";e.exports=require("net")},94735:e=>{"use strict";e.exports=require("events")}};var t=require("../../../../webpack-runtime.js");t.C(e);var r=e=>t(t.s=e),s=t.X(0,[5673,7711,8734,5327,4457,7416,4626,2041,2303,4829,3748,2869,3916,4524,2794,7862,3351,9513],()=>r(71694));module.exports=s})();