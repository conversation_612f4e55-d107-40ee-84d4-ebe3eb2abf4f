(()=>{var e={};e.id=5932,e.ids=[5932],e.modules={3295:e=>{"use strict";e.exports=require("next/dist/server/app-render/after-task-async-storage.external.js")},4573:e=>{"use strict";e.exports=require("node:buffer")},10846:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},11274:(e,t,r)=>{"use strict";r.r(t),r.d(t,{GlobalError:()=>o.a,__next_app__:()=>c,pages:()=>d,routeModule:()=>p,tree:()=>u});var s=r(43533),a=r(91514),i=r(17220),o=r.n(i),n=r(62931),l={};for(let e in n)0>["default","tree","pages","GlobalError","__next_app__","routeModule"].indexOf(e)&&(l[e]=()=>n[e]);r.d(t,l);let u={children:["",{children:["[locale]",{children:["(admin)",{children:["admin",{children:["posts",{children:["add",{children:["__PAGE__",{},{page:[()=>Promise.resolve().then(r.bind(r,85251)),"/Users/<USER>/My-Project/fluxkrea_test/app/[locale]/(admin)/admin/posts/add/page.tsx"]}]},{}]},{}]},{}]},{layout:[()=>Promise.resolve().then(r.bind(r,58770)),"/Users/<USER>/My-Project/fluxkrea_test/app/[locale]/(admin)/layout.tsx"]}]},{layout:[()=>Promise.resolve().then(r.bind(r,83581)),"/Users/<USER>/My-Project/fluxkrea_test/app/[locale]/layout.tsx"]}]},{"not-found":[()=>Promise.resolve().then(r.t.bind(r,53488,23)),"next/dist/client/components/not-found-error"],forbidden:[()=>Promise.resolve().then(r.t.bind(r,99017,23)),"next/dist/client/components/forbidden-error"],unauthorized:[()=>Promise.resolve().then(r.t.bind(r,90934,23)),"next/dist/client/components/unauthorized-error"]}]}.children,d=["/Users/<USER>/My-Project/fluxkrea_test/app/[locale]/(admin)/admin/posts/add/page.tsx"],c={require:r,loadChunk:()=>Promise.resolve()},p=new s.AppPageRouteModule({definition:{kind:a.RouteKind.APP_PAGE,page:"/[locale]/(admin)/admin/posts/add/page",pathname:"/[locale]/admin/posts/add",bundlePath:"",filename:"",appPaths:[]},userland:{loaderTree:u}})},11997:e=>{"use strict";e.exports=require("punycode")},15691:(e,t,r)=>{"use strict";r.d(t,{St:()=>l,W5:()=>i,aV:()=>n,gg:()=>o,gx:()=>c,j7:()=>d,nz:()=>a,zX:()=>u});var s=r(4995),a=function(e){return e.Created="created",e.Deleted="deleted",e.Online="online",e.Offline="offline",e}({});async function i(e){let t=(0,s.A)(),{data:r,error:a}=await t.from("posts").insert(e);if(a)throw a;return r}async function o(e,t){let r=(0,s.A)(),{data:a,error:i}=await r.from("posts").update(t).eq("uuid",e);if(i)throw i;return a}async function n(e){let t=(0,s.A)(),{data:r,error:a}=await t.from("posts").select("*").eq("uuid",e).limit(1).single();if(!a)return r}async function l(e,t){let r=(0,s.A)(),{data:a,error:i}=await r.from("posts").select("*").eq("slug",e).eq("locale",t).limit(1).single();if(!i)return a}async function u(e=1,t=50){let r=(0,s.A)(),{data:a,error:i}=await r.from("posts").select("*").order("created_at",{ascending:!1}).range((e-1)*t,e*t-1);return i?[]:a}async function d(e,t=1,r=50){let a=(0,s.A)(),{data:i,error:o}=await a.from("posts").select("*").eq("locale",e).eq("status","online").order("created_at",{ascending:!1}).range((t-1)*r,t*r-1);return o?[]:i}async function c(){let e=(0,s.A)(),{data:t,error:r}=await e.from("posts").select("count",{count:"exact"});if(!r)return t[0].count}},19121:e=>{"use strict";e.exports=require("next/dist/server/app-render/action-async-storage.external.js")},27910:e=>{"use strict";e.exports=require("stream")},29294:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-async-storage.external.js")},33873:e=>{"use strict";e.exports=require("path")},34631:e=>{"use strict";e.exports=require("tls")},41944:(e,t,r)=>{Promise.resolve().then(r.bind(r,1647)),Promise.resolve().then(r.bind(r,70439)),Promise.resolve().then(r.bind(r,21218)),Promise.resolve().then(r.t.bind(r,10050,23))},51672:(e,t,r)=>{Promise.resolve().then(r.bind(r,78085)),Promise.resolve().then(r.bind(r,80081)),Promise.resolve().then(r.bind(r,61716)),Promise.resolve().then(r.t.bind(r,59316,23))},55511:e=>{"use strict";e.exports=require("crypto")},55591:e=>{"use strict";e.exports=require("https")},57975:e=>{"use strict";e.exports=require("node:util")},58390:(e,t,r)=>{"use strict";r.r(t),r.d(t,{"00880263a376180560adeff1fdd2511a7543726d4a":()=>s.T,"6060a7b3ae196a5f18dc037c45f6672364bde1c2cd":()=>a.$$RSC_SERVER_ACTION_0});var s=r(39201),a=r(85251)},63033:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-unit-async-storage.external.js")},74075:e=>{"use strict";e.exports=require("zlib")},77598:e=>{"use strict";e.exports=require("node:crypto")},79428:e=>{"use strict";e.exports=require("buffer")},79551:e=>{"use strict";e.exports=require("url")},81630:e=>{"use strict";e.exports=require("http")},85251:(e,t,r)=>{"use strict";r.r(t),r.d(t,{$$RSC_SERVER_ACTION_0:()=>p,default:()=>m});var s=r(34411),a=r(72784);r(97468);var i=r(15691),o=r(25253),n=r(92132),l=r(85651),u=r(4961),d=r(60736),c=r(29452);let p=async function(e,t){let r=e.get("title"),s=e.get("slug"),a=e.get("locale"),o=e.get("status"),n=e.get("description"),l=e.get("cover_url"),d=e.get("author_name"),p=e.get("author_avatar_url"),m=e.get("content");if(!r||!r.trim()||!s||!s.trim()||!a||!a.trim())throw Error("invalid form data");if(await (0,i.St)(s,a))throw Error("post with same slug already exists");let x={uuid:(0,c.YJ)(),created_at:(0,u.iq)(),status:o,title:r,slug:s,locale:a,description:n,cover_url:l,author_name:d,author_avatar_url:p,content:m};try{return await (0,i.W5)(x),{status:"success",message:"Post added",redirect_url:"/admin/posts"}}catch(e){throw Error(e.message)}};async function m(){let e=await (0,d.ug)();if(!e||!e.uuid)return(0,s.jsx)(n.A,{message:"no auth"});let t={title:"Add Post",crumb:{items:[{title:"Posts",url:"/admin/posts"},{title:"Add Post",is_active:!0}]},fields:[{name:"title",title:"Title",type:"text",placeholder:"Post Title",validation:{required:!0}},{name:"slug",title:"Slug",type:"text",placeholder:"what-is-shipany",validation:{required:!0},tip:"post slug should be unique, visit like: /blog/what-is-shipany"},{name:"locale",title:"Locale",type:"select",options:o.IB.map(e=>({title:o.L$[e],value:e})),value:"en",validation:{required:!0}},{name:"status",title:"Status",type:"select",options:Object.values(i.nz).map(e=>({title:e,value:e})),value:i.nz.Created},{name:"description",title:"Description",type:"textarea",placeholder:"Post Description"},{name:"cover_url",title:"Cover URL",type:"url",placeholder:"Post Cover Image URL"},{name:"author_name",title:"Author Name",type:"text",placeholder:"Author Name"},{name:"author_avatar_url",title:"Author Avatar URL",type:"url",placeholder:"Author Avatar Image URL"},{name:"content",title:"Content",type:"editor",placeholder:"Post Content"}],submit:{button:{title:"Submit"},handler:(0,a.A)(p,"6060a7b3ae196a5f18dc037c45f6672364bde1c2cd",null)}};return(0,s.jsx)(l.A,{...t})}},85651:(e,t,r)=>{"use strict";r.d(t,{A:()=>n});var s=r(34411),a=r(1647),i=r(27419),o=r(18586);function n({...e}){return(0,s.jsxs)(s.Fragment,{children:[(0,s.jsx)(i.A,{crumb:e.crumb}),(0,s.jsxs)("div",{className:"w-full px-4 md:px-8 py-8",children:[(0,s.jsx)("h1",{className:"text-2xl font-medium mb-8",children:e.title}),(0,s.jsx)(o.Zp,{className:"overflow-x-auto px-6",children:(0,s.jsx)(a.default,{fields:e.fields,data:e.data,passby:e.passby,submit:e.submit,loading:e.loading})})]})]})}},91645:e=>{"use strict";e.exports=require("net")},94735:e=>{"use strict";e.exports=require("events")}};var t=require("../../../../../../webpack-runtime.js");t.C(e);var r=e=>t(t.s=e),s=t.X(0,[5673,7711,8734,5327,4457,7416,4626,2041,2303,4829,3748,2869,3412,1518,4524,2794,7862,3351,995],()=>r(11274));module.exports=s})();