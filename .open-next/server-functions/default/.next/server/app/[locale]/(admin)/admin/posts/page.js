(()=>{var e={};e.id=3196,e.ids=[3196],e.modules={3295:e=>{"use strict";e.exports=require("next/dist/server/app-render/after-task-async-storage.external.js")},4573:e=>{"use strict";e.exports=require("node:buffer")},10846:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},11997:e=>{"use strict";e.exports=require("punycode")},15691:(e,t,r)=>{"use strict";r.d(t,{St:()=>l,W5:()=>i,aV:()=>a,gg:()=>o,gx:()=>u,j7:()=>d,nz:()=>n,zX:()=>c});var s=r(4995),n=function(e){return e.Created="created",e.Deleted="deleted",e.Online="online",e.Offline="offline",e}({});async function i(e){let t=(0,s.A)(),{data:r,error:n}=await t.from("posts").insert(e);if(n)throw n;return r}async function o(e,t){let r=(0,s.A)(),{data:n,error:i}=await r.from("posts").update(t).eq("uuid",e);if(i)throw i;return n}async function a(e){let t=(0,s.A)(),{data:r,error:n}=await t.from("posts").select("*").eq("uuid",e).limit(1).single();if(!n)return r}async function l(e,t){let r=(0,s.A)(),{data:n,error:i}=await r.from("posts").select("*").eq("slug",e).eq("locale",t).limit(1).single();if(!i)return n}async function c(e=1,t=50){let r=(0,s.A)(),{data:n,error:i}=await r.from("posts").select("*").order("created_at",{ascending:!1}).range((e-1)*t,e*t-1);return i?[]:n}async function d(e,t=1,r=50){let n=(0,s.A)(),{data:i,error:o}=await n.from("posts").select("*").eq("locale",e).eq("status","online").order("created_at",{ascending:!1}).range((t-1)*r,t*r-1);return o?[]:i}async function u(){let e=(0,s.A)(),{data:t,error:r}=await e.from("posts").select("count",{count:"exact"});if(!r)return t[0].count}},19121:e=>{"use strict";e.exports=require("next/dist/server/app-render/action-async-storage.external.js")},27278:(e,t,r)=>{"use strict";r.d(t,{default:()=>d});var s=r(2569),n=r(78341),i=r(92622),o=r(92227),a=r(59316),l=r.n(a);let c=(0,r(32381).A)("Ellipsis",[["circle",{cx:"12",cy:"12",r:"1",key:"41hilf"}],["circle",{cx:"19",cy:"12",r:"1",key:"1wjl8i"}],["circle",{cx:"5",cy:"12",r:"1",key:"1pcz8c"}]]);function d({items:e}){return(0,s.jsxs)(n.rI,{children:[(0,s.jsx)(n.ty,{asChild:!0,children:(0,s.jsxs)(i.$,{variant:"ghost",className:"flex h-8 w-8 p-0 data-[state=open]:bg-muted",children:[(0,s.jsx)(c,{}),(0,s.jsx)("span",{className:"sr-only",children:"Open menu"})]})}),(0,s.jsx)(n.SQ,{align:"end",className:"w-[160px]",children:e.map(e=>(0,s.jsx)(n._2,{children:(0,s.jsxs)(l(),{href:e.url||"",target:e.target||"_self",className:"flex items-center gap-2",children:[e.icon&&(0,s.jsx)(o.default,{name:e.icon,className:"w-4 h-4"}),e.title]})},e.title))})]})}},27910:e=>{"use strict";e.exports=require("stream")},29294:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-async-storage.external.js")},33737:(e,t,r)=>{Promise.resolve().then(r.bind(r,16976)),Promise.resolve().then(r.bind(r,96216)),Promise.resolve().then(r.bind(r,57595)),Promise.resolve().then(r.bind(r,70439)),Promise.resolve().then(r.bind(r,21218)),Promise.resolve().then(r.bind(r,60050)),Promise.resolve().then(r.bind(r,37032)),Promise.resolve().then(r.t.bind(r,10050,23))},33873:e=>{"use strict";e.exports=require("path")},34631:e=>{"use strict";e.exports=require("tls")},55511:e=>{"use strict";e.exports=require("crypto")},55591:e=>{"use strict";e.exports=require("https")},57975:e=>{"use strict";e.exports=require("node:util")},63033:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-unit-async-storage.external.js")},68387:(e,t,r)=>{"use strict";r.r(t),r.d(t,{default:()=>c});var s=r(34411),n=r(96216),i=r(65707),o=r(15691),a=r(22740),l=r.n(a);async function c(){let e=await (0,o.zX)();return(0,s.jsx)(i.A,{title:"Posts",toolbar:{items:[{title:"Add Post",icon:"RiAddLine",url:"/admin/posts/add"}]},columns:[{name:"title",title:"Title"},{name:"description",title:"Description"},{name:"slug",title:"Slug"},{name:"locale",title:"Locale"},{name:"status",title:"Status"},{name:"created_at",title:"Created At",callback:e=>l()(e.created_at).format("YYYY-MM-DD HH:mm:ss")},{callback:e=>{let t=[{title:"Edit",icon:"RiEditLine",url:`/admin/posts/${e.uuid}/edit`},{title:"View",icon:"RiEyeLine",url:`/${e.locale}/posts/${e.slug}`,target:"_blank"}];return(0,s.jsx)(n.default,{items:t})}}],data:e,empty_message:"No posts found"})}},74075:e=>{"use strict";e.exports=require("zlib")},75593:(e,t,r)=>{Promise.resolve().then(r.bind(r,14066)),Promise.resolve().then(r.bind(r,27278)),Promise.resolve().then(r.bind(r,92227)),Promise.resolve().then(r.bind(r,80081)),Promise.resolve().then(r.bind(r,61716)),Promise.resolve().then(r.bind(r,72568)),Promise.resolve().then(r.bind(r,20518)),Promise.resolve().then(r.t.bind(r,59316,23))},77598:e=>{"use strict";e.exports=require("node:crypto")},79428:e=>{"use strict";e.exports=require("buffer")},79551:e=>{"use strict";e.exports=require("url")},81630:e=>{"use strict";e.exports=require("http")},91645:e=>{"use strict";e.exports=require("net")},94735:e=>{"use strict";e.exports=require("events")},96216:(e,t,r)=>{"use strict";r.d(t,{default:()=>s});let s=(0,r(29037).registerClientReference)(function(){throw Error("Attempted to call the default export of \"/Users/<USER>/My-Project/fluxkrea_test/components/blocks/table/dropdown.tsx\" from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"/Users/<USER>/My-Project/fluxkrea_test/components/blocks/table/dropdown.tsx","default")},97546:(e,t,r)=>{"use strict";r.r(t),r.d(t,{GlobalError:()=>o.a,__next_app__:()=>u,pages:()=>d,routeModule:()=>p,tree:()=>c});var s=r(43533),n=r(91514),i=r(17220),o=r.n(i),a=r(62931),l={};for(let e in a)0>["default","tree","pages","GlobalError","__next_app__","routeModule"].indexOf(e)&&(l[e]=()=>a[e]);r.d(t,l);let c={children:["",{children:["[locale]",{children:["(admin)",{children:["admin",{children:["posts",{children:["__PAGE__",{},{page:[()=>Promise.resolve().then(r.bind(r,68387)),"/Users/<USER>/My-Project/fluxkrea_test/app/[locale]/(admin)/admin/posts/page.tsx"]}]},{}]},{}]},{layout:[()=>Promise.resolve().then(r.bind(r,58770)),"/Users/<USER>/My-Project/fluxkrea_test/app/[locale]/(admin)/layout.tsx"]}]},{layout:[()=>Promise.resolve().then(r.bind(r,83581)),"/Users/<USER>/My-Project/fluxkrea_test/app/[locale]/layout.tsx"]}]},{"not-found":[()=>Promise.resolve().then(r.t.bind(r,53488,23)),"next/dist/client/components/not-found-error"],forbidden:[()=>Promise.resolve().then(r.t.bind(r,99017,23)),"next/dist/client/components/forbidden-error"],unauthorized:[()=>Promise.resolve().then(r.t.bind(r,90934,23)),"next/dist/client/components/unauthorized-error"]}]}.children,d=["/Users/<USER>/My-Project/fluxkrea_test/app/[locale]/(admin)/admin/posts/page.tsx"],u={require:r,loadChunk:()=>Promise.resolve()},p=new s.AppPageRouteModule({definition:{kind:n.RouteKind.APP_PAGE,page:"/[locale]/(admin)/admin/posts/page",pathname:"/[locale]/admin/posts",bundlePath:"",filename:"",appPaths:[]},userland:{loaderTree:c}})}};var t=require("../../../../../webpack-runtime.js");t.C(e);var r=e=>t(t.s=e),s=t.X(0,[5673,7711,8734,5327,4457,7416,4626,2041,2303,3748,2490,2869,4524,2794,3351,2686],()=>r(97546));module.exports=s})();