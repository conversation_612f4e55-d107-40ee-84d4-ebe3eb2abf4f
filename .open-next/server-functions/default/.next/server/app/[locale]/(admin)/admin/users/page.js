(()=>{var e={};e.id=6323,e.ids=[6323],e.modules={3295:e=>{"use strict";e.exports=require("next/dist/server/app-render/after-task-async-storage.external.js")},4573:e=>{"use strict";e.exports=require("node:buffer")},10846:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},11997:e=>{"use strict";e.exports=require("punycode")},14682:(e,r,t)=>{Promise.resolve().then(t.bind(t,16976)),Promise.resolve().then(t.bind(t,57595)),Promise.resolve().then(t.bind(t,70439)),Promise.resolve().then(t.bind(t,21218)),Promise.resolve().then(t.bind(t,60050)),Promise.resolve().then(t.bind(t,37032)),Promise.resolve().then(t.t.bind(t,10050,23))},19121:e=>{"use strict";e.exports=require("next/dist/server/app-render/action-async-storage.external.js")},27910:e=>{"use strict";e.exports=require("stream")},29294:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-async-storage.external.js")},33873:e=>{"use strict";e.exports=require("path")},34631:e=>{"use strict";e.exports=require("tls")},36552:(e,r,t)=>{"use strict";t.r(r),t.d(r,{default:()=>l});var s=t(34411),i=t(65707),n=t(35050),o=t(22740),a=t.n(o);async function l(){let e=await (0,n.lo)(1,50),r=[{name:"uuid",title:"UUID"},{name:"email",title:"Email"},{name:"nickname",title:"Name"},{name:"avatar_url",title:"Avatar",callback:e=>(0,s.jsx)("img",{src:e.avatar_url,className:"w-10 h-10 rounded-full"})},{name:"created_at",title:"Created At",callback:e=>a()(e.created_at).format("YYYY-MM-DD HH:mm:ss")}];return(0,s.jsx)(i.A,{title:"All Users",columns:r,data:e})}},54850:(e,r,t)=>{Promise.resolve().then(t.bind(t,14066)),Promise.resolve().then(t.bind(t,92227)),Promise.resolve().then(t.bind(t,80081)),Promise.resolve().then(t.bind(t,61716)),Promise.resolve().then(t.bind(t,72568)),Promise.resolve().then(t.bind(t,20518)),Promise.resolve().then(t.t.bind(t,59316,23))},55511:e=>{"use strict";e.exports=require("crypto")},55591:e=>{"use strict";e.exports=require("https")},57975:e=>{"use strict";e.exports=require("node:util")},63033:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-unit-async-storage.external.js")},69206:(e,r,t)=>{"use strict";t.r(r),t.d(r,{GlobalError:()=>o.a,__next_app__:()=>p,pages:()=>d,routeModule:()=>c,tree:()=>u});var s=t(43533),i=t(91514),n=t(17220),o=t.n(n),a=t(62931),l={};for(let e in a)0>["default","tree","pages","GlobalError","__next_app__","routeModule"].indexOf(e)&&(l[e]=()=>a[e]);t.d(r,l);let u={children:["",{children:["[locale]",{children:["(admin)",{children:["admin",{children:["users",{children:["__PAGE__",{},{page:[()=>Promise.resolve().then(t.bind(t,36552)),"/Users/<USER>/My-Project/fluxkrea_test/app/[locale]/(admin)/admin/users/page.tsx"]}]},{}]},{}]},{layout:[()=>Promise.resolve().then(t.bind(t,58770)),"/Users/<USER>/My-Project/fluxkrea_test/app/[locale]/(admin)/layout.tsx"]}]},{layout:[()=>Promise.resolve().then(t.bind(t,83581)),"/Users/<USER>/My-Project/fluxkrea_test/app/[locale]/layout.tsx"]}]},{"not-found":[()=>Promise.resolve().then(t.t.bind(t,53488,23)),"next/dist/client/components/not-found-error"],forbidden:[()=>Promise.resolve().then(t.t.bind(t,99017,23)),"next/dist/client/components/forbidden-error"],unauthorized:[()=>Promise.resolve().then(t.t.bind(t,90934,23)),"next/dist/client/components/unauthorized-error"]}]}.children,d=["/Users/<USER>/My-Project/fluxkrea_test/app/[locale]/(admin)/admin/users/page.tsx"],p={require:t,loadChunk:()=>Promise.resolve()},c=new s.AppPageRouteModule({definition:{kind:i.RouteKind.APP_PAGE,page:"/[locale]/(admin)/admin/users/page",pathname:"/[locale]/admin/users",bundlePath:"",filename:"",appPaths:[]},userland:{loaderTree:u}})},74075:e=>{"use strict";e.exports=require("zlib")},77598:e=>{"use strict";e.exports=require("node:crypto")},79428:e=>{"use strict";e.exports=require("buffer")},79551:e=>{"use strict";e.exports=require("url")},81630:e=>{"use strict";e.exports=require("http")},91645:e=>{"use strict";e.exports=require("net")},94735:e=>{"use strict";e.exports=require("events")}};var r=require("../../../../../webpack-runtime.js");r.C(e);var t=e=>r(r.s=e),s=r.X(0,[5673,7711,8734,5327,4457,7416,4626,2041,2303,3748,2490,2869,4524,2794,3351,2686],()=>t(69206));module.exports=s})();