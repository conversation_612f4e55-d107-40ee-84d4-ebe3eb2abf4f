(()=>{var e={};e.id=2273,e.ids=[2273],e.modules={3295:e=>{"use strict";e.exports=require("next/dist/server/app-render/after-task-async-storage.external.js")},4573:e=>{"use strict";e.exports=require("node:buffer")},10846:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},11945:(e,t,r)=>{"use strict";r.r(t),r.d(t,{"00880263a376180560adeff1fdd2511a7543726d4a":()=>s.T,"60fa488dfd6057e54aa495ae74d6031511879a7a67":()=>i.$$RSC_SERVER_ACTION_0});var s=r(39201),i=r(67778)},11997:e=>{"use strict";e.exports=require("punycode")},15691:(e,t,r)=>{"use strict";r.d(t,{St:()=>l,W5:()=>a,aV:()=>n,gg:()=>o,gx:()=>c,j7:()=>d,nz:()=>i,zX:()=>u});var s=r(4995),i=function(e){return e.Created="created",e.Deleted="deleted",e.Online="online",e.Offline="offline",e}({});async function a(e){let t=(0,s.A)(),{data:r,error:i}=await t.from("posts").insert(e);if(i)throw i;return r}async function o(e,t){let r=(0,s.A)(),{data:i,error:a}=await r.from("posts").update(t).eq("uuid",e);if(a)throw a;return i}async function n(e){let t=(0,s.A)(),{data:r,error:i}=await t.from("posts").select("*").eq("uuid",e).limit(1).single();if(!i)return r}async function l(e,t){let r=(0,s.A)(),{data:i,error:a}=await r.from("posts").select("*").eq("slug",e).eq("locale",t).limit(1).single();if(!a)return i}async function u(e=1,t=50){let r=(0,s.A)(),{data:i,error:a}=await r.from("posts").select("*").order("created_at",{ascending:!1}).range((e-1)*t,e*t-1);return a?[]:i}async function d(e,t=1,r=50){let i=(0,s.A)(),{data:a,error:o}=await i.from("posts").select("*").eq("locale",e).eq("status","online").order("created_at",{ascending:!1}).range((t-1)*r,t*r-1);return o?[]:a}async function c(){let e=(0,s.A)(),{data:t,error:r}=await e.from("posts").select("count",{count:"exact"});if(!r)return t[0].count}},19121:e=>{"use strict";e.exports=require("next/dist/server/app-render/action-async-storage.external.js")},27910:e=>{"use strict";e.exports=require("stream")},29294:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-async-storage.external.js")},33873:e=>{"use strict";e.exports=require("path")},34631:e=>{"use strict";e.exports=require("tls")},41944:(e,t,r)=>{Promise.resolve().then(r.bind(r,1647)),Promise.resolve().then(r.bind(r,70439)),Promise.resolve().then(r.bind(r,21218)),Promise.resolve().then(r.t.bind(r,10050,23))},51672:(e,t,r)=>{Promise.resolve().then(r.bind(r,78085)),Promise.resolve().then(r.bind(r,80081)),Promise.resolve().then(r.bind(r,61716)),Promise.resolve().then(r.t.bind(r,59316,23))},55511:e=>{"use strict";e.exports=require("crypto")},55591:e=>{"use strict";e.exports=require("https")},57975:e=>{"use strict";e.exports=require("node:util")},63033:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-unit-async-storage.external.js")},67778:(e,t,r)=>{"use strict";r.r(t),r.d(t,{$$RSC_SERVER_ACTION_0:()=>c,default:()=>p});var s=r(34411),i=r(72784);r(97468);var a=r(15691),o=r(25253),n=r(92132),l=r(85651),u=r(4961),d=r(60736);let c=async function(e,t){let{user:r,post:s}=t;if(!r||!s||!s.uuid)throw Error("invalid params");let i=e.get("title"),o=e.get("slug"),n=e.get("locale"),l=e.get("status"),d=e.get("description"),c=e.get("cover_url"),p=e.get("author_name"),m=e.get("author_avatar_url"),f=e.get("content");if(!i||!i.trim()||!o||!o.trim()||!n||!n.trim())throw Error("invalid form data");let h=await (0,a.St)(o,n);if(h&&h.uuid!==s.uuid)throw Error("post with same slug already exists");let x={updated_at:(0,u.iq)(),status:l,title:i,slug:o,locale:n,description:d,cover_url:c,author_name:p,author_avatar_url:m,content:f};try{return await (0,a.gg)(s.uuid,x),{status:"success",message:"Post updated",redirect_url:"/admin/posts"}}catch(e){throw Error(e.message)}};async function p({params:e}){let{uuid:t}=await e,r=await (0,d.ug)();if(!r||!r.uuid)return(0,s.jsx)(n.A,{message:"no auth"});let u=await (0,a.aV)(t);if(!u)return(0,s.jsx)(n.A,{message:"post not found"});let p={title:"Edit Post",crumb:{items:[{title:"Posts",url:"/admin/posts"},{title:"Edit Post",is_active:!0}]},fields:[{name:"title",title:"Title",type:"text",placeholder:"Post Title",validation:{required:!0}},{name:"slug",title:"Slug",type:"text",placeholder:"what-is-shipany",validation:{required:!0},tip:"post slug should be unique, visit like: /blog/what-is-shipany"},{name:"locale",title:"Locale",type:"select",options:o.IB.map(e=>({title:o.L$[e],value:e})),value:"en",validation:{required:!0}},{name:"status",title:"Status",type:"select",options:Object.values(a.nz).map(e=>({title:e,value:e})),value:a.nz.Created},{name:"description",title:"Description",type:"textarea",placeholder:"Post Description"},{name:"cover_url",title:"Cover URL",type:"url",placeholder:"Post Cover Image URL"},{name:"author_name",title:"Author Name",type:"text",placeholder:"Author Name"},{name:"author_avatar_url",title:"Author Avatar URL",type:"url",placeholder:"Author Avatar Image URL"},{name:"content",title:"Content",type:"editor",placeholder:"Post Content"}],data:u,passby:{user:r,post:u},submit:{button:{title:"Submit"},handler:(0,i.A)(c,"60fa488dfd6057e54aa495ae74d6031511879a7a67",null)}};return(0,s.jsx)(l.A,{...p})}},74075:e=>{"use strict";e.exports=require("zlib")},77598:e=>{"use strict";e.exports=require("node:crypto")},79428:e=>{"use strict";e.exports=require("buffer")},79551:e=>{"use strict";e.exports=require("url")},81630:e=>{"use strict";e.exports=require("http")},85651:(e,t,r)=>{"use strict";r.d(t,{A:()=>n});var s=r(34411),i=r(1647),a=r(27419),o=r(18586);function n({...e}){return(0,s.jsxs)(s.Fragment,{children:[(0,s.jsx)(a.A,{crumb:e.crumb}),(0,s.jsxs)("div",{className:"w-full px-4 md:px-8 py-8",children:[(0,s.jsx)("h1",{className:"text-2xl font-medium mb-8",children:e.title}),(0,s.jsx)(o.Zp,{className:"overflow-x-auto px-6",children:(0,s.jsx)(i.default,{fields:e.fields,data:e.data,passby:e.passby,submit:e.submit,loading:e.loading})})]})]})}},91645:e=>{"use strict";e.exports=require("net")},94735:e=>{"use strict";e.exports=require("events")},98786:(e,t,r)=>{"use strict";r.r(t),r.d(t,{GlobalError:()=>o.a,__next_app__:()=>c,pages:()=>d,routeModule:()=>p,tree:()=>u});var s=r(43533),i=r(91514),a=r(17220),o=r.n(a),n=r(62931),l={};for(let e in n)0>["default","tree","pages","GlobalError","__next_app__","routeModule"].indexOf(e)&&(l[e]=()=>n[e]);r.d(t,l);let u={children:["",{children:["[locale]",{children:["(admin)",{children:["admin",{children:["posts",{children:["[uuid]",{children:["edit",{children:["__PAGE__",{},{page:[()=>Promise.resolve().then(r.bind(r,67778)),"/Users/<USER>/My-Project/fluxkrea_test/app/[locale]/(admin)/admin/posts/[uuid]/edit/page.tsx"]}]},{}]},{}]},{}]},{}]},{layout:[()=>Promise.resolve().then(r.bind(r,58770)),"/Users/<USER>/My-Project/fluxkrea_test/app/[locale]/(admin)/layout.tsx"]}]},{layout:[()=>Promise.resolve().then(r.bind(r,83581)),"/Users/<USER>/My-Project/fluxkrea_test/app/[locale]/layout.tsx"]}]},{"not-found":[()=>Promise.resolve().then(r.t.bind(r,53488,23)),"next/dist/client/components/not-found-error"],forbidden:[()=>Promise.resolve().then(r.t.bind(r,99017,23)),"next/dist/client/components/forbidden-error"],unauthorized:[()=>Promise.resolve().then(r.t.bind(r,90934,23)),"next/dist/client/components/unauthorized-error"]}]}.children,d=["/Users/<USER>/My-Project/fluxkrea_test/app/[locale]/(admin)/admin/posts/[uuid]/edit/page.tsx"],c={require:r,loadChunk:()=>Promise.resolve()},p=new s.AppPageRouteModule({definition:{kind:i.RouteKind.APP_PAGE,page:"/[locale]/(admin)/admin/posts/[uuid]/edit/page",pathname:"/[locale]/admin/posts/[uuid]/edit",bundlePath:"",filename:"",appPaths:[]},userland:{loaderTree:u}})}};var t=require("../../../../../../../webpack-runtime.js");t.C(e);var r=e=>t(t.s=e),s=t.X(0,[5673,7711,8734,5327,4457,7416,4626,2041,2303,4829,3748,2869,3412,1518,4524,2794,7862,3351,995],()=>r(98786));module.exports=s})();