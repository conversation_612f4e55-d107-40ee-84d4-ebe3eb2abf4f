(()=>{var e={};e.id=4867,e.ids=[4867],e.modules={3295:e=>{"use strict";e.exports=require("next/dist/server/app-render/after-task-async-storage.external.js")},3764:(e,t,r)=>{"use strict";r.d(t,{p:()=>i});var s=r(2569);r(29068);var a=r(75673);function i({className:e,type:t,...r}){return(0,s.jsx)("input",{type:t,"data-slot":"input",className:(0,a.cn)("file:text-foreground placeholder:text-muted-foreground selection:bg-primary selection:text-primary-foreground dark:bg-input/30 border-input flex h-9 w-full min-w-0 rounded-md border bg-transparent px-3 py-1 text-base shadow-xs transition-[color,box-shadow] outline-none file:inline-flex file:h-7 file:border-0 file:bg-transparent file:text-sm file:font-medium disabled:pointer-events-none disabled:cursor-not-allowed disabled:opacity-50 md:text-sm","focus-visible:border-ring focus-visible:ring-ring/50 focus-visible:ring-[3px]","aria-invalid:ring-destructive/20 dark:aria-invalid:ring-destructive/40 aria-invalid:border-destructive",e),...r})}},4573:e=>{"use strict";e.exports=require("node:buffer")},10846:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},11997:e=>{"use strict";e.exports=require("punycode")},19121:e=>{"use strict";e.exports=require("next/dist/server/app-render/action-async-storage.external.js")},20822:(e,t,r)=>{"use strict";r.d(t,{J:()=>n});var s=r(2569);r(29068);var a=r(64463),i=r(75673);function n({className:e,...t}){return(0,s.jsx)(a.b,{"data-slot":"label",className:(0,i.cn)("flex items-center gap-2 text-sm leading-none font-medium select-none group-data-[disabled=true]:pointer-events-none group-data-[disabled=true]:opacity-50 peer-disabled:cursor-not-allowed peer-disabled:opacity-50",e),...t})}},26820:(e,t,r)=>{"use strict";r.r(t),r.d(t,{"00880263a376180560adeff1fdd2511a7543726d4a":()=>s.T});var s=r(39201)},27910:e=>{"use strict";e.exports=require("stream")},28432:(e,t,r)=>{"use strict";r.d(t,{j2:()=>m,Y9:()=>u});var s=r(62364),a=r(36157),i=r(72648),n=r(39201),o=r(4961),l=r(29452),d=r(60736);let c=[];c.push((0,a.A)({id:"google-one-tap",name:"google-one-tap",credentials:{credential:{type:"text"}},async authorize(e,t){let r=e.credential,s=await fetch("https://oauth2.googleapis.com/tokeninfo?id_token="+r);if(!s.ok)return console.log("Failed to verify token"),null;let a=await s.json();if(!a)return console.log("invalid payload from token"),null;let{email:i,sub:n,given_name:o,family_name:l,email_verified:d,picture:c}=a;return i?{id:n,name:[o,l].join(" "),email:i,image:c,emailVerified:d?new Date:null}:(console.log("invalid email in payload"),null)}})),process.env.AUTH_GOOGLE_ID&&process.env.AUTH_GOOGLE_SECRET&&c.push((0,i.A)({clientId:process.env.AUTH_GOOGLE_ID,clientSecret:process.env.AUTH_GOOGLE_SECRET,authorization:{params:{prompt:"consent",access_type:"offline",response_type:"code"}}})),c.map(e=>{if("function"!=typeof e)return{id:e.id,name:e.name};{let t=e();return{id:t.id,name:t.name}}}).filter(e=>"google-one-tap"!==e.id);let{handlers:u,signIn:p,signOut:f,auth:m}=(0,s.Ay)({providers:c,pages:{signIn:"/auth/signin"},callbacks:{signIn:async({user:e,account:t,profile:r,email:s,credentials:a})=>!0,redirect:async({url:e,baseUrl:t})=>e.startsWith("/")?`${t}${e}`:new URL(e).origin===t?e:t,session:async({session:e,token:t,user:r})=>(t&&t.user&&t.user&&(e.user=t.user),e),async jwt({token:e,user:t,account:r}){try{if(t&&t.email&&r){let s={uuid:(0,l.YJ)(),email:t.email,nickname:t.name||"",avatar_url:t.image||"",signin_type:r.type,signin_provider:r.provider,signin_openid:r.providerAccountId,created_at:(0,o.iq)(),signin_ip:await (0,n.T)()};try{let t=await (0,d.$c)(s);e.user={uuid:t.uuid,email:t.email,nickname:t.nickname,avatar_url:t.avatar_url,created_at:t.created_at}}catch(e){console.error("save user failed:",e)}}return e}catch(t){return console.error("jwt callback error:",t),e}}}})},29294:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-async-storage.external.js")},32355:(e,t,r)=>{Promise.resolve().then(r.bind(r,69533))},33873:e=>{"use strict";e.exports=require("path")},34631:e=>{"use strict";e.exports=require("tls")},36578:(e,t,r)=>{"use strict";r.r(t),r.d(t,{GlobalError:()=>n.a,__next_app__:()=>u,pages:()=>c,routeModule:()=>p,tree:()=>d});var s=r(43533),a=r(91514),i=r(17220),n=r.n(i),o=r(62931),l={};for(let e in o)0>["default","tree","pages","GlobalError","__next_app__","routeModule"].indexOf(e)&&(l[e]=()=>o[e]);r.d(t,l);let d={children:["",{children:["[locale]",{children:["auth",{children:["signin",{children:["__PAGE__",{},{page:[()=>Promise.resolve().then(r.bind(r,52808)),"/Users/<USER>/My-Project/fluxkrea_test/app/[locale]/auth/signin/page.tsx"]}]},{}]},{}]},{layout:[()=>Promise.resolve().then(r.bind(r,83581)),"/Users/<USER>/My-Project/fluxkrea_test/app/[locale]/layout.tsx"]}]},{"not-found":[()=>Promise.resolve().then(r.t.bind(r,53488,23)),"next/dist/client/components/not-found-error"],forbidden:[()=>Promise.resolve().then(r.t.bind(r,99017,23)),"next/dist/client/components/forbidden-error"],unauthorized:[()=>Promise.resolve().then(r.t.bind(r,90934,23)),"next/dist/client/components/unauthorized-error"]}]}.children,c=["/Users/<USER>/My-Project/fluxkrea_test/app/[locale]/auth/signin/page.tsx"],u={require:r,loadChunk:()=>Promise.resolve()},p=new s.AppPageRouteModule({definition:{kind:a.RouteKind.APP_PAGE,page:"/[locale]/auth/signin/page",pathname:"/[locale]/auth/signin",bundlePath:"",filename:"",appPaths:[]},userland:{loaderTree:d}})},36699:(e,t,r)=>{"use strict";r.d(t,{default:()=>p});var s=r(2569),a=r(29068),i=r(68064),n=r(44401),o=r(71569),l=r(92622);r(3764),r(20822);var d=r(75673),c=r(54487),u=r(73054);function p({className:e,...t}){let r=(0,u.c3)(),[p,f]=a.useState(!1),[m,x]=a.useState(!1),g=async()=>{f(!0);try{await (0,c.Jv)("google")}catch(e){console.error("Google登录失败:",e),f(!1)}};return(0,s.jsxs)("div",{className:(0,d.cn)("flex flex-col gap-6",e),...t,children:[(0,s.jsxs)(i.Zp,{children:[(0,s.jsxs)(i.aR,{className:"text-center",children:[(0,s.jsx)(i.ZB,{className:"text-xl",children:r("sign_modal.sign_in_title")}),(0,s.jsx)(i.BT,{children:r("sign_modal.sign_in_description")})]}),(0,s.jsx)(i.Wu,{children:(0,s.jsxs)("div",{className:"grid gap-6",children:[(0,s.jsxs)("div",{className:"flex flex-col gap-4",children:[(0,s.jsx)(l.$,{variant:"outline",className:"w-full",onClick:g,disabled:p||m,children:p?(0,s.jsxs)(s.Fragment,{children:[(0,s.jsx)(o.A,{className:"w-4 h-4 animate-spin mr-2"}),r("sign_modal.google_signing_in")]}):(0,s.jsxs)(s.Fragment,{children:[(0,s.jsx)(n.UKz,{className:"w-4 h-4 mr-2"}),r("sign_modal.google_sign_in")]})}),!1]}),!1]})})]}),(0,s.jsxs)("div",{className:"text-balance text-center text-xs text-muted-foreground [&_a]:underline [&_a]:underline-offset-4 hover:[&_a]:text-primary  ",children:["By clicking continue, you agree to our"," ",(0,s.jsx)("a",{href:"/terms-of-service",target:"_blank",children:"Terms of Service"})," ","and"," ",(0,s.jsx)("a",{href:"/privacy-policy",target:"_blank",children:"Privacy Policy"}),"."]})]})}},39201:(e,t,r)=>{"use strict";r.d(t,{T:()=>i});var s=r(72784);r(97468);var a=r(62225);async function i(){let e=await (0,a.b3)();return e.get("cf-connecting-ip")||e.get("x-real-ip")||(e.get("x-forwarded-for")||"127.0.0.1").split(",")[0]}(0,r(78564).D)([i]),(0,s.A)(i,"00880263a376180560adeff1fdd2511a7543726d4a",null)},46427:(e,t,r)=>{Promise.resolve().then(r.bind(r,36699))},52808:(e,t,r)=>{"use strict";r.r(t),r.d(t,{default:()=>o});var s=r(34411),a=r(69533),i=r(28432),n=r(79502);async function o({searchParams:e}){let{callbackUrl:t}=await e;return await (0,i.j2)()?(0,n.redirect)(t||"/"):(0,s.jsx)("div",{className:"flex min-h-svh flex-col items-center justify-center gap-6 bg-muted p-6 md:p-10",children:(0,s.jsxs)("div",{className:"flex w-full max-w-sm flex-col gap-6",children:[(0,s.jsxs)("a",{href:"/",className:"flex items-center gap-2 self-center font-medium",children:[(0,s.jsx)("div",{className:"flex h-6 w-6 items-center justify-center rounded-md border text-primary-foreground",children:(0,s.jsx)("img",{src:"/logo.png",alt:"logo",className:"size-4"})}),"KREA FLUX"]}),(0,s.jsx)(a.default,{})]})})}},55511:e=>{"use strict";e.exports=require("crypto")},55591:e=>{"use strict";e.exports=require("https")},57975:e=>{"use strict";e.exports=require("node:util")},60736:(e,t,r)=>{"use strict";r.d(t,{$c:()=>d,TG:()=>c,qo:()=>p,ug:()=>f});var s=r(87830),a=r(35050),i=r(28432),n=r(4961),o=r(78054),l=r(62225);async function d(e){try{let t=await (0,a.Gs)(e.email);return t?(e.id=t.id,e.uuid=t.uuid,e.created_at=t.created_at):(await (0,a.HW)(e),await (0,s.d_)({user_uuid:e.uuid||"",trans_type:s.H3.NewUser,credits:s.rN.NewUserGet,expired_at:(0,n.MI)()})),e}catch(e){throw console.log("save user failed: ",e),e}}async function c(){let e="",t=await u();if(t&&t.startsWith("sk-"))return await (0,o.zz)(t)||"";let r=await (0,i.j2)();return r&&r.user&&r.user.uuid&&(e=r.user.uuid),e}async function u(){let e=(await (0,l.b3)()).get("Authorization");return e?e.replace("Bearer ",""):""}async function p(){let e="",t=await (0,i.j2)();return t&&t.user&&t.user.email&&(e=t.user.email),e}async function f(){let e=await c();if(e)return await (0,a.pX)(e)}},63033:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-unit-async-storage.external.js")},64463:(e,t,r)=>{"use strict";r.d(t,{b:()=>o});var s=r(29068),a=r(85137),i=r(2569),n=s.forwardRef((e,t)=>(0,i.jsx)(a.sG.label,{...e,ref:t,onMouseDown:t=>{t.target.closest("button, input, select, textarea")||(e.onMouseDown?.(t),!t.defaultPrevented&&t.detail>1&&t.preventDefault())}}));n.displayName="Label";var o=n},68064:(e,t,r)=>{"use strict";r.d(t,{BT:()=>l,Wu:()=>c,X9:()=>d,ZB:()=>o,Zp:()=>i,aR:()=>n,wL:()=>u});var s=r(2569);r(29068);var a=r(75673);function i({className:e,...t}){return(0,s.jsx)("div",{"data-slot":"card",className:(0,a.cn)("bg-card text-card-foreground flex flex-col gap-6 rounded-2xl border border-border/50 p-6 shadow-lg backdrop-blur-sm transition-all duration-200 hover:shadow-xl hover:scale-[1.01]",e),...t})}function n({className:e,...t}){return(0,s.jsx)("div",{"data-slot":"card-header",className:(0,a.cn)("@container/card-header grid auto-rows-min grid-rows-[auto_auto] items-start gap-2 has-data-[slot=card-action]:grid-cols-[1fr_auto] [.border-b]:pb-6",e),...t})}function o({className:e,...t}){return(0,s.jsx)("div",{"data-slot":"card-title",className:(0,a.cn)("leading-none font-semibold",e),...t})}function l({className:e,...t}){return(0,s.jsx)("div",{"data-slot":"card-description",className:(0,a.cn)("text-muted-foreground text-sm",e),...t})}function d({className:e,...t}){return(0,s.jsx)("div",{"data-slot":"card-action",className:(0,a.cn)("col-start-2 row-span-2 row-start-1 self-start justify-self-end",e),...t})}function c({className:e,...t}){return(0,s.jsx)("div",{"data-slot":"card-content",className:(0,a.cn)("px-6",e),...t})}function u({className:e,...t}){return(0,s.jsx)("div",{"data-slot":"card-footer",className:(0,a.cn)("flex items-center px-6 [.border-t]:pt-6",e),...t})}},69533:(e,t,r)=>{"use strict";r.d(t,{default:()=>s});let s=(0,r(29037).registerClientReference)(function(){throw Error("Attempted to call the default export of \"/Users/<USER>/My-Project/fluxkrea_test/components/sign/form.tsx\" from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"/Users/<USER>/My-Project/fluxkrea_test/components/sign/form.tsx","default")},74075:e=>{"use strict";e.exports=require("zlib")},77598:e=>{"use strict";e.exports=require("node:crypto")},78054:(e,t,r)=>{"use strict";r.d(t,{N1:()=>i,ai:()=>a,ox:()=>n,zz:()=>o});var s=r(4995),a=function(e){return e.Created="created",e.Deleted="deleted",e}({});async function i(e){let t=(0,s.A)(),{data:r,error:a}=await t.from("apikeys").insert(e);if(a)throw a;return r}async function n(e,t=1,r=50){let a=(t-1)*r,i=(0,s.A)(),{data:o,error:l}=await i.from("apikeys").select("*").eq("user_uuid",e).neq("status","deleted").order("created_at",{ascending:!1}).range(a,a+r-1);if(!l)return o}async function o(e){let t=(0,s.A)(),{data:r,error:a}=await t.from("apikeys").select("user_uuid").eq("api_key",e).eq("status","created").limit(1).single();if(!a)return r?.user_uuid}},79428:e=>{"use strict";e.exports=require("buffer")},79551:e=>{"use strict";e.exports=require("url")},81630:e=>{"use strict";e.exports=require("http")},91645:e=>{"use strict";e.exports=require("net")},94735:e=>{"use strict";e.exports=require("events")}};var t=require("../../../../webpack-runtime.js");t.C(e);var r=e=>t(t.s=e),s=t.X(0,[5673,7711,8734,5327,4457,7416,4626,2041,4524,2794],()=>r(36578));module.exports=s})();