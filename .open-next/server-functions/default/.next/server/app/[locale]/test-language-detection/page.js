(()=>{var e={};e.id=7557,e.ids=[7557],e.modules={3295:e=>{"use strict";e.exports=require("next/dist/server/app-render/after-task-async-storage.external.js")},10846:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},16982:(e,r,s)=>{"use strict";s.r(r),s.d(r,{GlobalError:()=>i.a,__next_app__:()=>u,pages:()=>c,routeModule:()=>x,tree:()=>o});var t=s(43533),n=s(91514),a=s(17220),i=s.n(a),d=s(62931),l={};for(let e in d)0>["default","tree","pages","GlobalError","__next_app__","routeModule"].indexOf(e)&&(l[e]=()=>d[e]);s.d(r,l);let o={children:["",{children:["[locale]",{children:["test-language-detection",{children:["__PAGE__",{},{page:[()=>Promise.resolve().then(s.bind(s,77006)),"/Users/<USER>/My-Project/fluxkrea_test/app/[locale]/test-language-detection/page.tsx"]}]},{}]},{layout:[()=>Promise.resolve().then(s.bind(s,83581)),"/Users/<USER>/My-Project/fluxkrea_test/app/[locale]/layout.tsx"]}]},{"not-found":[()=>Promise.resolve().then(s.t.bind(s,53488,23)),"next/dist/client/components/not-found-error"],forbidden:[()=>Promise.resolve().then(s.t.bind(s,99017,23)),"next/dist/client/components/forbidden-error"],unauthorized:[()=>Promise.resolve().then(s.t.bind(s,90934,23)),"next/dist/client/components/unauthorized-error"]}]}.children,c=["/Users/<USER>/My-Project/fluxkrea_test/app/[locale]/test-language-detection/page.tsx"],u={require:s,loadChunk:()=>Promise.resolve()},x=new t.AppPageRouteModule({definition:{kind:n.RouteKind.APP_PAGE,page:"/[locale]/test-language-detection/page",pathname:"/[locale]/test-language-detection",bundlePath:"",filename:"",appPaths:[]},userland:{loaderTree:o}})},19121:e=>{"use strict";e.exports=require("next/dist/server/app-render/action-async-storage.external.js")},21637:(e,r,s)=>{"use strict";s.d(r,{E:()=>l});var t=s(2569);s(29068);var n=s(70166),a=s(75958),i=s(75673);let d=(0,a.F)("inline-flex items-center justify-center rounded-md border px-2 py-0.5 text-xs font-medium w-fit whitespace-nowrap shrink-0 [&>svg]:size-3 gap-1 [&>svg]:pointer-events-none focus-visible:border-ring focus-visible:ring-ring/50 focus-visible:ring-[3px] aria-invalid:ring-destructive/20 dark:aria-invalid:ring-destructive/40 aria-invalid:border-destructive transition-[color,box-shadow] overflow-hidden",{variants:{variant:{default:"border-transparent bg-primary text-primary-foreground [a&]:hover:bg-primary/90",secondary:"border-transparent bg-secondary text-secondary-foreground [a&]:hover:bg-secondary/90",destructive:"border-transparent bg-destructive text-white [a&]:hover:bg-destructive/90 focus-visible:ring-destructive/20 dark:focus-visible:ring-destructive/40 dark:bg-destructive/60",outline:"text-foreground [a&]:hover:bg-accent [a&]:hover:text-accent-foreground"}},defaultVariants:{variant:"default"}});function l({className:e,variant:r,asChild:s=!1,...a}){let l=s?n.DX:"span";return(0,t.jsx)(l,{"data-slot":"badge",className:(0,i.cn)(d({variant:r}),e),...a})}},29294:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-async-storage.external.js")},33873:e=>{"use strict";e.exports=require("path")},50321:(e,r,s)=>{Promise.resolve().then(s.bind(s,84048))},60049:(e,r,s)=>{Promise.resolve().then(s.bind(s,77006))},63033:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-unit-async-storage.external.js")},68064:(e,r,s)=>{"use strict";s.d(r,{BT:()=>l,Wu:()=>c,X9:()=>o,ZB:()=>d,Zp:()=>a,aR:()=>i,wL:()=>u});var t=s(2569);s(29068);var n=s(75673);function a({className:e,...r}){return(0,t.jsx)("div",{"data-slot":"card",className:(0,n.cn)("bg-card text-card-foreground flex flex-col gap-6 rounded-2xl border border-border/50 p-6 shadow-lg backdrop-blur-sm transition-all duration-200 hover:shadow-xl hover:scale-[1.01]",e),...r})}function i({className:e,...r}){return(0,t.jsx)("div",{"data-slot":"card-header",className:(0,n.cn)("@container/card-header grid auto-rows-min grid-rows-[auto_auto] items-start gap-2 has-data-[slot=card-action]:grid-cols-[1fr_auto] [.border-b]:pb-6",e),...r})}function d({className:e,...r}){return(0,t.jsx)("div",{"data-slot":"card-title",className:(0,n.cn)("leading-none font-semibold",e),...r})}function l({className:e,...r}){return(0,t.jsx)("div",{"data-slot":"card-description",className:(0,n.cn)("text-muted-foreground text-sm",e),...r})}function o({className:e,...r}){return(0,t.jsx)("div",{"data-slot":"card-action",className:(0,n.cn)("col-start-2 row-span-2 row-start-1 self-start justify-self-end",e),...r})}function c({className:e,...r}){return(0,t.jsx)("div",{"data-slot":"card-content",className:(0,n.cn)("px-6",e),...r})}function u({className:e,...r}){return(0,t.jsx)("div",{"data-slot":"card-footer",className:(0,n.cn)("flex items-center px-6 [.border-t]:pt-6",e),...r})}},77006:(e,r,s)=>{"use strict";s.r(r),s.d(r,{default:()=>t});let t=(0,s(29037).registerClientReference)(function(){throw Error("Attempted to call the default export of \"/Users/<USER>/My-Project/fluxkrea_test/app/[locale]/test-language-detection/page.tsx\" from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"/Users/<USER>/My-Project/fluxkrea_test/app/[locale]/test-language-detection/page.tsx","default")},84048:(e,r,s)=>{"use strict";s.r(r),s.d(r,{default:()=>x});var t=s(2569),n=s(29068),a=s(14059),i=s(92622),d=s(68064),l=s(21637),o=s(18898),c=s(70080),u=s(63232);function x(){let e=(0,a.useParams)().locale,[r,s]=(0,n.useState)(null),[x,p]=(0,n.useState)(null),h=()=>{let r=(0,o.k3)(e),t=(0,c.DQ)();s(r),p(t)};return(0,t.jsxs)("div",{className:"container mx-auto p-6 space-y-6",children:[(0,t.jsxs)("div",{className:"text-center",children:[(0,t.jsx)("h1",{className:"text-3xl font-bold mb-2",children:"语言检测功能测试"}),(0,t.jsxs)("p",{className:"text-muted-foreground",children:["当前语言: ",(0,t.jsx)(l.E,{variant:"outline",children:e})]})]}),(0,t.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-2 gap-6",children:[(0,t.jsxs)(d.Zp,{children:[(0,t.jsxs)(d.aR,{children:[(0,t.jsx)(d.ZB,{children:"浏览器语言信息"}),(0,t.jsx)(d.BT,{children:"检测到的浏览器语言设置"})]}),(0,t.jsxs)(d.Wu,{className:"space-y-2",children:[(0,t.jsxs)("div",{children:[(0,t.jsx)("strong",{children:"navigator.language:"})," ",(0,o.xd)()||"未检测到"]}),(0,t.jsxs)("div",{children:[(0,t.jsx)("strong",{children:"支持的浏览器语言:"})," ",(0,o.BZ)()||"无"]}),(0,t.jsxs)("div",{children:[(0,t.jsx)("strong",{children:"建议切换到:"})," ",(0,o.aj)(e)||"无建议"]})]})]}),(0,t.jsxs)(d.Zp,{children:[(0,t.jsxs)(d.aR,{children:[(0,t.jsx)(d.ZB,{children:"用户偏好信息"}),(0,t.jsx)(d.BT,{children:"保存的用户语言偏好"})]}),(0,t.jsx)(d.Wu,{children:x?.preference?(0,t.jsxs)("div",{className:"space-y-2",children:[(0,t.jsxs)("div",{children:[(0,t.jsx)("strong",{children:"选择的语言:"})," ",x.preference.selectedLanguage]}),(0,t.jsxs)("div",{children:[(0,t.jsx)("strong",{children:"检测到的语言:"})," ",x.preference.detectedLanguage]}),(0,t.jsxs)("div",{children:[(0,t.jsx)("strong",{children:"是否拒绝:"})," ",x.preference.declined?"是":"否"]}),(0,t.jsxs)("div",{children:[(0,t.jsx)("strong",{children:"时间戳:"})," ",new Date(1e3*x.preference.timestamp).toLocaleString()]})]}):(0,t.jsx)("p",{className:"text-muted-foreground",children:"暂无保存的偏好"})})]})]}),r&&(0,t.jsxs)(d.Zp,{children:[(0,t.jsxs)(d.aR,{children:[(0,t.jsx)(d.ZB,{children:"详细调试信息"}),(0,t.jsx)(d.BT,{children:"完整的语言检测信息"})]}),(0,t.jsx)(d.Wu,{children:(0,t.jsx)("pre",{className:"bg-muted p-4 rounded-lg text-sm overflow-auto",children:JSON.stringify(r,null,2)})})]}),(0,t.jsxs)("div",{className:"flex flex-wrap gap-4 justify-center",children:[(0,t.jsx)(i.$,{onClick:h,variant:"outline",children:"刷新调试信息"}),(0,t.jsx)(i.$,{onClick:()=>{(0,c.A0)(),(0,c.or)(),h(),alert("所有语言偏好已清除！刷新页面以重新测试。")},variant:"destructive",children:"清除所有偏好"}),(0,t.jsx)(i.$,{onClick:()=>window.location.reload(),variant:"secondary",children:"刷新页面"})]}),(0,t.jsxs)(d.Zp,{children:[(0,t.jsx)(d.aR,{children:(0,t.jsx)(d.ZB,{children:"测试说明"})}),(0,t.jsxs)(d.Wu,{className:"space-y-4",children:[(0,t.jsxs)("div",{children:[(0,t.jsx)("h4",{className:"font-semibold mb-2",children:"如何测试语言检测功能："}),(0,t.jsxs)("ol",{className:"list-decimal list-inside space-y-1 text-sm",children:[(0,t.jsx)("li",{children:"确保浏览器语言设置与当前页面语言不同"}),(0,t.jsx)("li",{children:"清除所有偏好设置"}),(0,t.jsx)("li",{children:"刷新页面，应该会看到语言切换弹框"}),(0,t.jsx)("li",{children:'选择"切换"或"保持"来测试不同的用户选择'}),(0,t.jsx)("li",{children:"再次刷新页面，验证不会重复显示弹框"})]})]}),(0,t.jsxs)("div",{children:[(0,t.jsx)("h4",{className:"font-semibold mb-2",children:"浏览器语言设置方法："}),(0,t.jsxs)("ul",{className:"list-disc list-inside space-y-1 text-sm",children:[(0,t.jsxs)("li",{children:[(0,t.jsx)("strong",{children:"Chrome:"})," 设置 → 高级 → 语言"]}),(0,t.jsxs)("li",{children:[(0,t.jsx)("strong",{children:"Firefox:"})," 首选项 → 常规 → 语言"]}),(0,t.jsxs)("li",{children:[(0,t.jsx)("strong",{children:"Safari:"})," 偏好设置 → 高级 → 语言"]})]})]})]})]}),(0,t.jsx)(u.G,{})]})}}};var r=require("../../../webpack-runtime.js");r.C(e);var s=e=>r(r.s=e),t=r.X(0,[5673,7711,8734,4457,7416,4626,2794],()=>s(16982));module.exports=t})();