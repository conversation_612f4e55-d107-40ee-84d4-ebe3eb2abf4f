(()=>{var e={};e.id=6179,e.ids=[6179],e.modules={2140:(e,r,t)=>{"use strict";t.d(r,{default:()=>g});var a=t(2569),n=t(62135),s=t(67412),i=t(29068),o=t(41673),l=t(54035),d=t(75673);let c=i.forwardRef(({className:e,...r},t)=>(0,a.jsx)(o.bL,{className:(0,d.cn)("grid gap-2",e),...r,ref:t}));c.displayName=o.bL.displayName;let u=i.forwardRef(({className:e,...r},t)=>(0,a.jsx)(o.q7,{ref:t,className:(0,d.cn)("aspect-square h-4 w-4 rounded-full border border-primary text-primary ring-offset-background focus:outline-hidden focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:cursor-not-allowed disabled:opacity-50",e),...r,children:(0,a.jsx)(o.C1,{className:"flex items-center justify-center",children:(0,a.jsx)(l.A,{className:"h-2.5 w-2.5 fill-current text-current"})})}));u.displayName=o.q7.displayName;var p=t(21637),m=t(92622),f=t(92227),x=t(20822),h=t(13399),v=t(39365),b=t(6637);function g({pricing:e}){if(e.disabled)return null;let{user:r,setShowSignModal:t}=(0,b.U)(),[o,l]=(0,i.useState)(e.groups?.[0]?.name||"yearly"),[d,g]=(0,i.useState)(!1),[y,j]=(0,i.useState)(null),w=async e=>{try{if(!r){t(!0);return}let a={product_id:e.product_id,product_name:e.product_name,credits:e.credits,interval:e.interval,amount:e.amount,currency:e.currency,valid_months:e.valid_months};g(!0),j(e.product_id);let n=await fetch("/api/checkout",{method:"POST",headers:{"Content-Type":"application/json"},body:JSON.stringify(a)});if(401===n.status){g(!1),j(null),t(!0);return}let{code:s,message:i,data:o}=await n.json();if(0!==s){v.oR.error(i);return}let{public_key:l,session_id:d}=o,c=await (0,h.c)(l);if(!c){v.oR.error("checkout failed");return}let u=await c.redirectToCheckout({sessionId:d});u.error&&v.oR.error(u.error.message)}catch(e){console.log("checkout failed: ",e),v.oR.error("checkout failed")}finally{g(!1),j(null)}};return(0,a.jsx)("section",{id:e.name,className:"py-16",children:(0,a.jsxs)("div",{className:"container",children:[(0,a.jsxs)("div",{className:"mx-auto mb-12 text-center",children:[(0,a.jsx)("h2",{className:"mb-4 text-4xl font-semibold lg:text-5xl",children:e.title}),(0,a.jsx)("p",{className:"text-muted-foreground lg:text-lg",children:e.description})]}),(0,a.jsxs)("div",{className:"w-full flex flex-col items-center gap-2",children:[e.groups&&e.groups.length>0&&(0,a.jsx)("div",{className:"flex h-12 mb-12 items-center rounded-md bg-muted p-1 text-lg",children:(0,a.jsx)(c,{value:o,className:`h-full grid-cols-${e.groups.length}`,onValueChange:e=>{l(e)},children:e.groups.map((e,r)=>(0,a.jsxs)("div",{className:'h-full rounded-md transition-all border-2 border-transparent has-[button[data-state="checked"]]:border-primary has-[button[data-state="checked"]]:bg-transparent',children:[(0,a.jsx)(u,{value:e.name||"",id:e.name,className:"peer sr-only"}),(0,a.jsxs)(x.J,{htmlFor:e.name,className:"flex h-full cursor-pointer items-center justify-center px-7 font-semibold text-muted-foreground peer-data-[state=checked]:text-primary",children:[e.title,e.label&&(0,a.jsx)(p.E,{variant:"outline",className:"border-primary bg-primary px-1.5 ml-1 text-primary-foreground",children:e.label})]})]},r))})}),(0,a.jsx)("div",{className:`w-full mt-0 grid gap-6 md:grid-cols-${e.items?.filter(e=>!e.group||e.group===o)?.length}`,children:e.items?.map((e,r)=>e.group&&e.group!==o?null:a.jsx("div",{className:`rounded-lg p-6 ${e.is_featured?"border-primary border-2 bg-card text-card-foreground":"border-muted border"}`,children:a.jsxs("div",{className:"flex h-full flex-col justify-between gap-5",children:[a.jsxs("div",{children:[a.jsxs("div",{className:"flex items-center gap-2 mb-4",children:[e.title&&a.jsx("h3",{className:"text-xl font-semibold",children:e.title}),a.jsx("div",{className:"flex-1"}),e.label&&a.jsx(p.E,{variant:"outline",className:"border-primary bg-primary px-1.5 text-primary-foreground",children:e.label})]}),a.jsxs("div",{className:"flex items-end gap-2 mb-4",children:[e.original_price&&a.jsx("span",{className:"text-xl text-muted-foreground font-semibold line-through",children:e.original_price}),e.price&&a.jsx("span",{className:"text-5xl font-semibold",children:e.price}),e.unit&&a.jsx("span",{className:"block font-semibold",dangerouslySetInnerHTML:{__html:e.unit}})]}),e.description&&a.jsx("p",{className:"text-muted-foreground",children:e.description}),e.features_title&&a.jsx("p",{className:"mb-3 mt-6 font-semibold",children:e.features_title}),e.features&&a.jsx("ul",{className:"flex flex-col gap-3",children:e.features.map((e,r)=>a.jsxs("li",{className:"flex gap-2",children:[a.jsx(n.A,{className:"mt-1 size-4 shrink-0"}),e]},`feature-${r}`))})]}),a.jsxs("div",{className:"flex flex-col gap-2",children:[e.button&&a.jsxs(m.$,{className:"w-full flex items-center justify-center gap-2 font-semibold",disabled:d,onClick:()=>{!d&&w(e)},children:[(!d||d&&y!==e.product_id)&&a.jsx("p",{children:e.button.title}),d&&y===e.product_id&&a.jsx("p",{children:e.button.title}),d&&y===e.product_id&&a.jsx(s.A,{className:"mr-2 h-4 w-4 animate-spin"}),e.button.icon&&a.jsx(f.default,{name:e.button.icon,className:"size-4"})]}),e.tip&&a.jsx("p",{className:"text-muted-foreground text-sm mt-2",children:e.tip})]})]})},r))})]})]})})}},3295:e=>{"use strict";e.exports=require("next/dist/server/app-render/after-task-async-storage.external.js")},10846:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},13399:(e,r,t)=>{"use strict";t.d(r,{c:()=>v});var a,n="https://js.stripe.com",s="".concat(n,"/v3"),i=/^https:\/\/js\.stripe\.com\/v3\/?(\?.*)?$/,o=/^https:\/\/js\.stripe\.com\/(v3|[a-z]+)\/stripe\.js(\?.*)?$/,l=function(){for(var e=document.querySelectorAll('script[src^="'.concat(n,'"]')),r=0;r<e.length;r++){var t,a=e[r];if(t=a.src,i.test(t)||o.test(t))return a}return null},d=function(e){var r=e&&!e.advancedFraudSignals?"?advancedFraudSignals=false":"",t=document.createElement("script");t.src="".concat(s).concat(r);var a=document.head||document.body;if(!a)throw Error("Expected document.body not to be null. Stripe.js requires a <body> element.");return a.appendChild(t),t},c=function(e,r){e&&e._registerWrapper&&e._registerWrapper({name:"stripe-js",version:"5.10.0",startTime:r})},u=null,p=null,m=null,f=function(e,r,t){if(null===e)return null;var a,n=r[0].match(/^pk_test/),s=3===(a=e.version)?"v3":a;n&&"v3"!==s&&console.warn("Stripe.js@".concat(s," was loaded on the page, but @stripe/stripe-js@").concat("5.10.0"," expected Stripe.js@").concat("v3",". This may result in unexpected behavior. For more information, see https://docs.stripe.com/sdks/stripejs-versioning"));var i=e.apply(void 0,r);return c(i,t),i},x=!1,h=function(){return a?a:a=(null!==u?u:(u=new Promise(function(e,r){if("undefined"==typeof window||"undefined"==typeof document){e(null);return}if(window.Stripe,window.Stripe){e(window.Stripe);return}try{var t,a=l();a?a&&null!==m&&null!==p&&(a.removeEventListener("load",m),a.removeEventListener("error",p),null===(t=a.parentNode)||void 0===t||t.removeChild(a),a=d(null)):a=d(null),m=function(){window.Stripe?e(window.Stripe):r(Error("Stripe.js not available"))},p=function(e){r(Error("Failed to load Stripe.js",{cause:e}))},a.addEventListener("load",m),a.addEventListener("error",p)}catch(e){r(e);return}})).catch(function(e){return u=null,Promise.reject(e)})).catch(function(e){return a=null,Promise.reject(e)})};Promise.resolve().then(function(){return h()}).catch(function(e){x||console.warn(e)});var v=function(){for(var e=arguments.length,r=Array(e),t=0;t<e;t++)r[t]=arguments[t];x=!0;var a=Date.now();return h().then(function(e){return f(e,r,a)})}},19121:e=>{"use strict";e.exports=require("next/dist/server/app-render/action-async-storage.external.js")},20822:(e,r,t)=>{"use strict";t.d(r,{J:()=>i});var a=t(2569);t(29068);var n=t(64463),s=t(75673);function i({className:e,...r}){return(0,a.jsx)(n.b,{"data-slot":"label",className:(0,s.cn)("flex items-center gap-2 text-sm leading-none font-medium select-none group-data-[disabled=true]:pointer-events-none group-data-[disabled=true]:opacity-50 peer-disabled:cursor-not-allowed peer-disabled:opacity-50",e),...r})}},21637:(e,r,t)=>{"use strict";t.d(r,{E:()=>l});var a=t(2569);t(29068);var n=t(70166),s=t(75958),i=t(75673);let o=(0,s.F)("inline-flex items-center justify-center rounded-md border px-2 py-0.5 text-xs font-medium w-fit whitespace-nowrap shrink-0 [&>svg]:size-3 gap-1 [&>svg]:pointer-events-none focus-visible:border-ring focus-visible:ring-ring/50 focus-visible:ring-[3px] aria-invalid:ring-destructive/20 dark:aria-invalid:ring-destructive/40 aria-invalid:border-destructive transition-[color,box-shadow] overflow-hidden",{variants:{variant:{default:"border-transparent bg-primary text-primary-foreground [a&]:hover:bg-primary/90",secondary:"border-transparent bg-secondary text-secondary-foreground [a&]:hover:bg-secondary/90",destructive:"border-transparent bg-destructive text-white [a&]:hover:bg-destructive/90 focus-visible:ring-destructive/20 dark:focus-visible:ring-destructive/40 dark:bg-destructive/60",outline:"text-foreground [a&]:hover:bg-accent [a&]:hover:text-accent-foreground"}},defaultVariants:{variant:"default"}});function l({className:e,variant:r,asChild:t=!1,...s}){let l=t?n.DX:"span";return(0,a.jsx)(l,{"data-slot":"badge",className:(0,i.cn)(o({variant:r}),e),...s})}},26318:(e,r,t)=>{"use strict";t.r(r),t.d(r,{GlobalError:()=>i.a,__next_app__:()=>u,pages:()=>c,routeModule:()=>p,tree:()=>d});var a=t(43533),n=t(91514),s=t(17220),i=t.n(s),o=t(62931),l={};for(let e in o)0>["default","tree","pages","GlobalError","__next_app__","routeModule"].indexOf(e)&&(l[e]=()=>o[e]);t.d(r,l);let d={children:["",{children:["[locale]",{children:["(default)",{children:["pricing",{children:["__PAGE__",{},{page:[()=>Promise.resolve().then(t.bind(t,54760)),"/Users/<USER>/My-Project/fluxkrea_test/app/[locale]/(default)/pricing/page.tsx"]}]},{}]},{layout:[()=>Promise.resolve().then(t.bind(t,17890)),"/Users/<USER>/My-Project/fluxkrea_test/app/[locale]/(default)/layout.tsx"]}]},{layout:[()=>Promise.resolve().then(t.bind(t,83581)),"/Users/<USER>/My-Project/fluxkrea_test/app/[locale]/layout.tsx"]}]},{"not-found":[()=>Promise.resolve().then(t.t.bind(t,53488,23)),"next/dist/client/components/not-found-error"],forbidden:[()=>Promise.resolve().then(t.t.bind(t,99017,23)),"next/dist/client/components/forbidden-error"],unauthorized:[()=>Promise.resolve().then(t.t.bind(t,90934,23)),"next/dist/client/components/unauthorized-error"]}]}.children,c=["/Users/<USER>/My-Project/fluxkrea_test/app/[locale]/(default)/pricing/page.tsx"],u={require:t,loadChunk:()=>Promise.resolve()},p=new a.AppPageRouteModule({definition:{kind:n.RouteKind.APP_PAGE,page:"/[locale]/(default)/pricing/page",pathname:"/[locale]/pricing",bundlePath:"",filename:"",appPaths:[]},userland:{loaderTree:d}})},29294:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-async-storage.external.js")},33873:e=>{"use strict";e.exports=require("path")},34117:(e,r,t)=>{Promise.resolve().then(t.bind(t,2140))},40197:(e,r,t)=>{Promise.resolve().then(t.bind(t,45565))},41673:(e,r,t)=>{"use strict";t.d(r,{C1:()=>T,bL:()=>D,q7:()=>F});var a=t(29068),n=t(79208),s=t(58028),i=t(4002),o=t(85137),l=t(25481),d=t(83465),c=t(66505),u=t(42509),p=t(61781),m=t(50703),f=t(2569),x="Radio",[h,v]=(0,i.A)(x),[b,g]=h(x),y=a.forwardRef((e,r)=>{let{__scopeRadio:t,name:i,checked:l=!1,required:d,disabled:c,value:u="on",onCheck:p,form:m,...x}=e,[h,v]=a.useState(null),g=(0,s.s)(r,e=>v(e)),y=a.useRef(!1),j=!h||m||!!h.closest("form");return(0,f.jsxs)(b,{scope:t,checked:l,disabled:c,children:[(0,f.jsx)(o.sG.button,{type:"button",role:"radio","aria-checked":l,"data-state":N(l),"data-disabled":c?"":void 0,disabled:c,value:u,...x,ref:g,onClick:(0,n.m)(e.onClick,e=>{l||p?.(),j&&(y.current=e.isPropagationStopped(),y.current||e.stopPropagation())})}),j&&(0,f.jsx)(k,{control:h,bubbles:!y.current,name:i,value:u,checked:l,required:d,disabled:c,form:m,style:{transform:"translateX(-100%)"}})]})});y.displayName=x;var j="RadioIndicator",w=a.forwardRef((e,r)=>{let{__scopeRadio:t,forceMount:a,...n}=e,s=g(j,t);return(0,f.jsx)(m.C,{present:a||s.checked,children:(0,f.jsx)(o.sG.span,{"data-state":N(s.checked),"data-disabled":s.disabled?"":void 0,...n,ref:r})})});w.displayName=j;var k=a.forwardRef(({__scopeRadio:e,control:r,checked:t,bubbles:n=!0,...i},l)=>{let d=a.useRef(null),c=(0,s.s)(d,l),m=(0,p.Z)(t),x=(0,u.X)(r);return a.useEffect(()=>{let e=d.current;if(!e)return;let r=Object.getOwnPropertyDescriptor(window.HTMLInputElement.prototype,"checked").set;if(m!==t&&r){let a=new Event("click",{bubbles:n});r.call(e,t),e.dispatchEvent(a)}},[m,t,n]),(0,f.jsx)(o.sG.input,{type:"radio","aria-hidden":!0,defaultChecked:t,...i,tabIndex:-1,ref:c,style:{...i.style,...x,position:"absolute",pointerEvents:"none",opacity:0,margin:0}})});function N(e){return e?"checked":"unchecked"}k.displayName="RadioBubbleInput";var _=["ArrowUp","ArrowDown","ArrowLeft","ArrowRight"],P="RadioGroup",[R,E]=(0,i.A)(P,[l.RG,v]),C=(0,l.RG)(),S=v(),[A,q]=R(P),L=a.forwardRef((e,r)=>{let{__scopeRadioGroup:t,name:a,defaultValue:n,value:s,required:i=!1,disabled:u=!1,orientation:p,dir:m,loop:x=!0,onValueChange:h,...v}=e,b=C(t),g=(0,c.jH)(m),[y,j]=(0,d.i)({prop:s,defaultProp:n??null,onChange:h,caller:P});return(0,f.jsx)(A,{scope:t,name:a,required:i,disabled:u,value:y,onValueChange:j,children:(0,f.jsx)(l.bL,{asChild:!0,...b,orientation:p,dir:g,loop:x,children:(0,f.jsx)(o.sG.div,{role:"radiogroup","aria-required":i,"aria-orientation":p,"data-disabled":u?"":void 0,dir:g,...v,ref:r})})})});L.displayName=P;var M="RadioGroupItem",G=a.forwardRef((e,r)=>{let{__scopeRadioGroup:t,disabled:i,...o}=e,d=q(M,t),c=d.disabled||i,u=C(t),p=S(t),m=a.useRef(null),x=(0,s.s)(r,m),h=d.value===o.value,v=a.useRef(!1);return a.useEffect(()=>{let e=e=>{_.includes(e.key)&&(v.current=!0)},r=()=>v.current=!1;return document.addEventListener("keydown",e),document.addEventListener("keyup",r),()=>{document.removeEventListener("keydown",e),document.removeEventListener("keyup",r)}},[]),(0,f.jsx)(l.q7,{asChild:!0,...u,focusable:!c,active:h,children:(0,f.jsx)(y,{disabled:c,required:d.required,checked:h,...p,...o,name:d.name,ref:x,onCheck:()=>d.onValueChange(o.value),onKeyDown:(0,n.m)(e=>{"Enter"===e.key&&e.preventDefault()}),onFocus:(0,n.m)(o.onFocus,()=>{v.current&&m.current?.click()})})})});G.displayName=M;var I=a.forwardRef((e,r)=>{let{__scopeRadioGroup:t,...a}=e,n=S(t);return(0,f.jsx)(w,{...n,...a,ref:r})});I.displayName="RadioGroupIndicator";var D=L,F=G,T=I},45565:(e,r,t)=>{"use strict";t.d(r,{default:()=>a});let a=(0,t(29037).registerClientReference)(function(){throw Error("Attempted to call the default export of \"/Users/<USER>/My-Project/fluxkrea_test/components/blocks/pricing/index.tsx\" from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"/Users/<USER>/My-Project/fluxkrea_test/components/blocks/pricing/index.tsx","default")},54035:(e,r,t)=>{"use strict";t.d(r,{A:()=>a});let a=(0,t(32381).A)("Circle",[["circle",{cx:"12",cy:"12",r:"10",key:"1mglay"}]])},54760:(e,r,t)=>{"use strict";t.r(r),t.d(r,{default:()=>i});var a=t(34411),n=t(45565),s=t(64476);async function i({params:e}){let{locale:r}=await e,t=await (0,s.eI)(r);return(0,a.jsx)(a.Fragment,{children:t.pricing&&(0,a.jsx)(n.default,{pricing:t.pricing})})}},63033:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-unit-async-storage.external.js")},64463:(e,r,t)=>{"use strict";t.d(r,{b:()=>o});var a=t(29068),n=t(85137),s=t(2569),i=a.forwardRef((e,r)=>(0,s.jsx)(n.sG.label,{...e,ref:r,onMouseDown:r=>{r.target.closest("button, input, select, textarea")||(e.onMouseDown?.(r),!r.defaultPrevented&&r.detail>1&&r.preventDefault())}}));i.displayName="Label";var o=i},67412:(e,r,t)=>{"use strict";t.d(r,{A:()=>a});let a=(0,t(32381).A)("Loader",[["path",{d:"M12 2v4",key:"3427ic"}],["path",{d:"m16.2 7.8 2.9-2.9",key:"r700ao"}],["path",{d:"M18 12h4",key:"wj9ykh"}],["path",{d:"m16.2 16.2 2.9 2.9",key:"1bxg5t"}],["path",{d:"M12 18v4",key:"jadmvz"}],["path",{d:"m4.9 19.1 2.9-2.9",key:"bwix9q"}],["path",{d:"M2 12h4",key:"j09sii"}],["path",{d:"m4.9 4.9 2.9 2.9",key:"giyufr"}]])}};var r=require("../../../../webpack-runtime.js");r.C(e);var t=e=>r(r.s=e),a=r.X(0,[5673,7711,8734,4457,7416,4626,2303,4829,3162,2794,7862,4452],()=>t(26318));module.exports=a})();