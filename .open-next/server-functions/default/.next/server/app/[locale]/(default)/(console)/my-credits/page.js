"use strict";(()=>{var e={};e.id=5259,e.ids=[5259],e.modules={3295:e=>{e.exports=require("next/dist/server/app-render/after-task-async-storage.external.js")},4573:e=>{e.exports=require("node:buffer")},10846:e=>{e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},11997:e=>{e.exports=require("punycode")},19121:e=>{e.exports=require("next/dist/server/app-render/action-async-storage.external.js")},25120:(e,r,t)=>{t.r(r),t.d(r,{default:()=>c});var s=t(34411),a=t(92132),o=t(88456),i=t(53928),n=t(68791),l=t(87830),d=t(60736),u=t(22740),p=t.n(u);async function c(){let e=await (0,n.A)(),r=await (0,d.TG)();if(!r)return(0,s.jsx)(a.A,{message:"no auth"});let t=await (0,i._3)(r,1,100),u=await (0,l.mP)(r),c={title:e("my_credits.title"),tip:{title:e("my_credits.left_tip",{left_credits:u?.left_credits||0})},toolbar:{items:[{title:e("my_credits.recharge"),url:"/pricing",target:"_blank",icon:"RiBankCardLine"}]},columns:[{title:e("my_credits.table.trans_no"),name:"trans_no"},{title:e("my_credits.table.trans_type"),name:"trans_type"},{title:e("my_credits.table.credits"),name:"credits"},{title:e("my_credits.table.updated_at"),name:"created_at",callback:e=>p()(e.created_at).format("YYYY-MM-DD HH:mm:ss")}],data:t,empty_message:e("my_credits.no_credits")};return(0,s.jsx)(o.A,{...c})}},27910:e=>{e.exports=require("stream")},29294:e=>{e.exports=require("next/dist/server/app-render/work-async-storage.external.js")},33873:e=>{e.exports=require("path")},34631:e=>{e.exports=require("tls")},39234:(e,r,t)=>{t.r(r),t.d(r,{GlobalError:()=>i.a,__next_app__:()=>p,pages:()=>u,routeModule:()=>c,tree:()=>d});var s=t(43533),a=t(91514),o=t(17220),i=t.n(o),n=t(62931),l={};for(let e in n)0>["default","tree","pages","GlobalError","__next_app__","routeModule"].indexOf(e)&&(l[e]=()=>n[e]);t.d(r,l);let d={children:["",{children:["[locale]",{children:["(default)",{children:["(console)",{children:["my-credits",{children:["__PAGE__",{},{page:[()=>Promise.resolve().then(t.bind(t,25120)),"/Users/<USER>/My-Project/fluxkrea_test/app/[locale]/(default)/(console)/my-credits/page.tsx"]}]},{}]},{layout:[()=>Promise.resolve().then(t.bind(t,65690)),"/Users/<USER>/My-Project/fluxkrea_test/app/[locale]/(default)/(console)/layout.tsx"]}]},{layout:[()=>Promise.resolve().then(t.bind(t,17890)),"/Users/<USER>/My-Project/fluxkrea_test/app/[locale]/(default)/layout.tsx"]}]},{layout:[()=>Promise.resolve().then(t.bind(t,83581)),"/Users/<USER>/My-Project/fluxkrea_test/app/[locale]/layout.tsx"]}]},{"not-found":[()=>Promise.resolve().then(t.t.bind(t,53488,23)),"next/dist/client/components/not-found-error"],forbidden:[()=>Promise.resolve().then(t.t.bind(t,99017,23)),"next/dist/client/components/forbidden-error"],unauthorized:[()=>Promise.resolve().then(t.t.bind(t,90934,23)),"next/dist/client/components/unauthorized-error"]}]}.children,u=["/Users/<USER>/My-Project/fluxkrea_test/app/[locale]/(default)/(console)/my-credits/page.tsx"],p={require:t,loadChunk:()=>Promise.resolve()},c=new s.AppPageRouteModule({definition:{kind:a.RouteKind.APP_PAGE,page:"/[locale]/(default)/(console)/my-credits/page",pathname:"/[locale]/my-credits",bundlePath:"",filename:"",appPaths:[]},userland:{loaderTree:d}})},55511:e=>{e.exports=require("crypto")},55591:e=>{e.exports=require("https")},57975:e=>{e.exports=require("node:util")},63033:e=>{e.exports=require("next/dist/server/app-render/work-unit-async-storage.external.js")},74075:e=>{e.exports=require("zlib")},77598:e=>{e.exports=require("node:crypto")},79428:e=>{e.exports=require("buffer")},79551:e=>{e.exports=require("url")},81630:e=>{e.exports=require("http")},91645:e=>{e.exports=require("net")},92132:(e,r,t)=>{t.d(r,{A:()=>a});var s=t(34411);function a({message:e}){return(0,s.jsx)("div",{className:"flex flex-col items-center justify-center w-full h-[50vh]",children:(0,s.jsx)("p",{children:e})})}},94735:e=>{e.exports=require("events")}};var r=require("../../../../../webpack-runtime.js");r.C(e);var t=e=>r(r.s=e),s=r.X(0,[5673,7711,8734,5327,4457,7416,4626,2041,2303,4829,3162,2490,4524,2794,7862,4452,5378],()=>t(39234));module.exports=s})();