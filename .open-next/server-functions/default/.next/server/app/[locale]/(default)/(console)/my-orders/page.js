"use strict";(()=>{var e={};e.id=6914,e.ids=[6914],e.modules={3295:e=>{e.exports=require("next/dist/server/app-render/after-task-async-storage.external.js")},4573:e=>{e.exports=require("node:buffer")},10846:e=>{e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},11997:e=>{e.exports=require("punycode")},19121:e=>{e.exports=require("next/dist/server/app-render/action-async-storage.external.js")},27910:e=>{e.exports=require("stream")},29294:e=>{e.exports=require("next/dist/server/app-render/work-async-storage.external.js")},33873:e=>{e.exports=require("path")},34397:(e,r,t)=>{t.r(r),t.d(r,{default:()=>u});var o=t(34411),a=t(40109),s=t(60736),n=t(88456),l=t(68791),i=t(22740),d=t.n(i),p=t(79502);async function u(){let e=await (0,l.A)(),r=await (0,s.TG)(),t=await (0,s.qo)();r||(0,p.redirect)(`/auth/signin?callbackUrl=${encodeURIComponent("http://localhost:3000/my-orders")}`);let i=await (0,a.BJ)(r);i&&0!==i.length||(i=await (0,a.PG)(t));let u=[{name:"order_no",title:e("my_orders.table.order_no")},{name:"paid_email",title:e("my_orders.table.email")},{name:"product_name",title:e("my_orders.table.product_name")},{name:"amount",title:e("my_orders.table.amount"),callback:e=>`${"CNY"===e.currency.toUpperCase()?"\xa5":"$"} ${e.amount/100}`},{name:"paid_at",title:e("my_orders.table.paid_at"),callback:e=>d()(e.paid_at).format("YYYY-MM-DD HH:mm:ss")}],c={title:e("my_orders.title"),columns:u,data:i,empty_message:e("my_orders.no_orders")};return(0,o.jsx)(n.A,{...c})}},34631:e=>{e.exports=require("tls")},44294:(e,r,t)=>{t.r(r),t.d(r,{GlobalError:()=>n.a,__next_app__:()=>u,pages:()=>p,routeModule:()=>c,tree:()=>d});var o=t(43533),a=t(91514),s=t(17220),n=t.n(s),l=t(62931),i={};for(let e in l)0>["default","tree","pages","GlobalError","__next_app__","routeModule"].indexOf(e)&&(i[e]=()=>l[e]);t.d(r,i);let d={children:["",{children:["[locale]",{children:["(default)",{children:["(console)",{children:["my-orders",{children:["__PAGE__",{},{page:[()=>Promise.resolve().then(t.bind(t,34397)),"/Users/<USER>/My-Project/fluxkrea_test/app/[locale]/(default)/(console)/my-orders/page.tsx"]}]},{}]},{layout:[()=>Promise.resolve().then(t.bind(t,65690)),"/Users/<USER>/My-Project/fluxkrea_test/app/[locale]/(default)/(console)/layout.tsx"]}]},{layout:[()=>Promise.resolve().then(t.bind(t,17890)),"/Users/<USER>/My-Project/fluxkrea_test/app/[locale]/(default)/layout.tsx"]}]},{layout:[()=>Promise.resolve().then(t.bind(t,83581)),"/Users/<USER>/My-Project/fluxkrea_test/app/[locale]/layout.tsx"]}]},{"not-found":[()=>Promise.resolve().then(t.t.bind(t,53488,23)),"next/dist/client/components/not-found-error"],forbidden:[()=>Promise.resolve().then(t.t.bind(t,99017,23)),"next/dist/client/components/forbidden-error"],unauthorized:[()=>Promise.resolve().then(t.t.bind(t,90934,23)),"next/dist/client/components/unauthorized-error"]}]}.children,p=["/Users/<USER>/My-Project/fluxkrea_test/app/[locale]/(default)/(console)/my-orders/page.tsx"],u={require:t,loadChunk:()=>Promise.resolve()},c=new o.AppPageRouteModule({definition:{kind:a.RouteKind.APP_PAGE,page:"/[locale]/(default)/(console)/my-orders/page",pathname:"/[locale]/my-orders",bundlePath:"",filename:"",appPaths:[]},userland:{loaderTree:d}})},55511:e=>{e.exports=require("crypto")},55591:e=>{e.exports=require("https")},57975:e=>{e.exports=require("node:util")},63033:e=>{e.exports=require("next/dist/server/app-render/work-unit-async-storage.external.js")},74075:e=>{e.exports=require("zlib")},77598:e=>{e.exports=require("node:crypto")},79428:e=>{e.exports=require("buffer")},79551:e=>{e.exports=require("url")},81630:e=>{e.exports=require("http")},91645:e=>{e.exports=require("net")},94735:e=>{e.exports=require("events")}};var r=require("../../../../../webpack-runtime.js");r.C(e);var t=e=>r(r.s=e),o=r.X(0,[5673,7711,8734,5327,4457,7416,4626,2041,2303,4829,3162,2490,4524,2794,7862,4452,5378],()=>t(44294));module.exports=o})();