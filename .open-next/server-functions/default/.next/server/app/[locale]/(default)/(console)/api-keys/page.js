"use strict";(()=>{var e={};e.id=8653,e.ids=[8653],e.modules={3295:e=>{e.exports=require("next/dist/server/app-render/after-task-async-storage.external.js")},4573:e=>{e.exports=require("node:buffer")},10846:e=>{e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},11997:e=>{e.exports=require("punycode")},19121:e=>{e.exports=require("next/dist/server/app-render/action-async-storage.external.js")},26214:(e,r,t)=>{t.r(r),t.d(r,{default:()=>d});var s=t(34411),a=t(92132),o=t(88456),i=t(68791),l=t(78054),n=t(60736),p=t(22740),u=t.n(p);async function d(){let e=await (0,i.A)(),r=await (0,n.TG)();if(!r)return(0,s.jsx)(a.A,{message:"no auth"});let t=await (0,l.ox)(r),p={title:e("api_keys.title"),tip:{title:e("api_keys.tip")},toolbar:{items:[{title:e("api_keys.create_api_key"),url:"/api-keys/create",icon:"RiAddLine"}]},columns:[{title:e("api_keys.table.name"),name:"title"},{title:e("api_keys.table.key"),name:"api_key",type:"copy",callback:e=>e.api_key.slice(0,4)+"..."+e.api_key.slice(-4)},{title:e("api_keys.table.created_at"),name:"created_at",callback:e=>u()(e.created_at).fromNow()}],data:t,empty_message:e("api_keys.no_api_keys")};return(0,s.jsx)(o.A,{...p})}},27910:e=>{e.exports=require("stream")},29294:e=>{e.exports=require("next/dist/server/app-render/work-async-storage.external.js")},33873:e=>{e.exports=require("path")},34631:e=>{e.exports=require("tls")},55511:e=>{e.exports=require("crypto")},55591:e=>{e.exports=require("https")},57975:e=>{e.exports=require("node:util")},59154:(e,r,t)=>{t.r(r),t.d(r,{GlobalError:()=>i.a,__next_app__:()=>d,pages:()=>u,routeModule:()=>c,tree:()=>p});var s=t(43533),a=t(91514),o=t(17220),i=t.n(o),l=t(62931),n={};for(let e in l)0>["default","tree","pages","GlobalError","__next_app__","routeModule"].indexOf(e)&&(n[e]=()=>l[e]);t.d(r,n);let p={children:["",{children:["[locale]",{children:["(default)",{children:["(console)",{children:["api-keys",{children:["__PAGE__",{},{page:[()=>Promise.resolve().then(t.bind(t,26214)),"/Users/<USER>/My-Project/fluxkrea_test/app/[locale]/(default)/(console)/api-keys/page.tsx"]}]},{}]},{layout:[()=>Promise.resolve().then(t.bind(t,65690)),"/Users/<USER>/My-Project/fluxkrea_test/app/[locale]/(default)/(console)/layout.tsx"]}]},{layout:[()=>Promise.resolve().then(t.bind(t,17890)),"/Users/<USER>/My-Project/fluxkrea_test/app/[locale]/(default)/layout.tsx"]}]},{layout:[()=>Promise.resolve().then(t.bind(t,83581)),"/Users/<USER>/My-Project/fluxkrea_test/app/[locale]/layout.tsx"]}]},{"not-found":[()=>Promise.resolve().then(t.t.bind(t,53488,23)),"next/dist/client/components/not-found-error"],forbidden:[()=>Promise.resolve().then(t.t.bind(t,99017,23)),"next/dist/client/components/forbidden-error"],unauthorized:[()=>Promise.resolve().then(t.t.bind(t,90934,23)),"next/dist/client/components/unauthorized-error"]}]}.children,u=["/Users/<USER>/My-Project/fluxkrea_test/app/[locale]/(default)/(console)/api-keys/page.tsx"],d={require:t,loadChunk:()=>Promise.resolve()},c=new s.AppPageRouteModule({definition:{kind:a.RouteKind.APP_PAGE,page:"/[locale]/(default)/(console)/api-keys/page",pathname:"/[locale]/api-keys",bundlePath:"",filename:"",appPaths:[]},userland:{loaderTree:p}})},63033:e=>{e.exports=require("next/dist/server/app-render/work-unit-async-storage.external.js")},74075:e=>{e.exports=require("zlib")},77598:e=>{e.exports=require("node:crypto")},79428:e=>{e.exports=require("buffer")},79551:e=>{e.exports=require("url")},81630:e=>{e.exports=require("http")},91645:e=>{e.exports=require("net")},92132:(e,r,t)=>{t.d(r,{A:()=>a});var s=t(34411);function a({message:e}){return(0,s.jsx)("div",{className:"flex flex-col items-center justify-center w-full h-[50vh]",children:(0,s.jsx)("p",{children:e})})}},94735:e=>{e.exports=require("events")}};var r=require("../../../../../webpack-runtime.js");r.C(e);var t=e=>r(r.s=e),s=r.X(0,[5673,7711,8734,5327,4457,7416,4626,2041,2303,4829,3162,2490,4524,2794,7862,4452,5378],()=>t(59154));module.exports=s})();