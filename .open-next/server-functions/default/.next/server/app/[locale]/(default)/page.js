"use strict";(()=>{var e={};e.id=4598,e.ids=[4598],e.modules={3295:e=>{e.exports=require("next/dist/server/app-render/after-task-async-storage.external.js")},10846:e=>{e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},19121:e=>{e.exports=require("next/dist/server/app-render/action-async-storage.external.js")},29294:e=>{e.exports=require("next/dist/server/app-render/work-async-storage.external.js")},33873:e=>{e.exports=require("path")},54522:(e,t,r)=>{r.r(t),r.d(t,{GlobalError:()=>n.a,__next_app__:()=>p,pages:()=>c,routeModule:()=>u,tree:()=>d});var a=r(43533),s=r(91514),o=r(17220),n=r.n(o),i=r(62931),l={};for(let e in i)0>["default","tree","pages","GlobalError","__next_app__","routeModule"].indexOf(e)&&(l[e]=()=>i[e]);r.d(t,l);let d={children:["",{children:["[locale]",{children:["(default)",{children:["__PAGE__",{},{page:[()=>Promise.resolve().then(r.bind(r,82137)),"/Users/<USER>/My-Project/fluxkrea_test/app/[locale]/(default)/page.tsx"]}]},{layout:[()=>Promise.resolve().then(r.bind(r,17890)),"/Users/<USER>/My-Project/fluxkrea_test/app/[locale]/(default)/layout.tsx"]}]},{layout:[()=>Promise.resolve().then(r.bind(r,83581)),"/Users/<USER>/My-Project/fluxkrea_test/app/[locale]/layout.tsx"]}]},{"not-found":[()=>Promise.resolve().then(r.t.bind(r,53488,23)),"next/dist/client/components/not-found-error"],forbidden:[()=>Promise.resolve().then(r.t.bind(r,99017,23)),"next/dist/client/components/forbidden-error"],unauthorized:[()=>Promise.resolve().then(r.t.bind(r,90934,23)),"next/dist/client/components/unauthorized-error"]}]}.children,c=["/Users/<USER>/My-Project/fluxkrea_test/app/[locale]/(default)/page.tsx"],p={require:r,loadChunk:()=>Promise.resolve()},u=new a.AppPageRouteModule({definition:{kind:s.RouteKind.APP_PAGE,page:"/[locale]/(default)/page",pathname:"/[locale]",bundlePath:"",filename:"",appPaths:[]},userland:{loaderTree:d}})},63033:e=>{e.exports=require("next/dist/server/app-render/work-unit-async-storage.external.js")},82137:(e,t,r)=>{r.r(t),r.d(t,{default:()=>y,generateMetadata:()=>j});var a=r(34411),s=r(79115),o=r(23312),n=r(33425),i=r(68059),l=r(75525),d=r(97594),c=r(2013),p=r(4648),u=r(23463),x=r(45565),f=r(60854),m=r(95826),h=r(64476),g=r(41443);async function j({params:e}){let{locale:t}=await e,r=await (0,h.JE)(t),a="http://localhost:3000/";return"en"!==t&&(a=`http://localhost:3000/${t}/`),{title:{template:"%s",default:r.metadata.title||""},description:r.metadata.description||"",keywords:r.metadata.keywords||"",alternates:{canonical:a}}}async function y({params:e}){let{locale:t}=await e,r=await (0,h.JE)(t),j="http://localhost:3000",y="en"===t?`${j}`:`${j}/${t}`,b={title:r.hero?.title||"FLUX2 - Advanced Image to Image AI Platform",description:r.hero?.description?.replace(/<br\s*\/?>/gi," ")||r.feature?.description||"Revolutionary image to image AI technology that transforms, enhances, and converts images with precision. Professional AI image transformation tools.",image:`${j}/imgs/features/ai_dashboard_interface.png`,url:y,schemaType:"SoftwareApplication",applicationCategory:"WebApplication",operatingSystem:"Web Browser, iOS, Android",datePublished:"2024-01-01T00:00:00Z",dateModified:new Date().toISOString(),breadcrumb:[]};return(0,a.jsxs)(a.Fragment,{children:[(0,a.jsx)(g.Ay,{...b}),r.hero&&(0,a.jsx)(u.A,{hero:r.hero}),(0,a.jsx)(s.WorkspaceLayout,{}),r.branding&&(0,a.jsx)(o.A,{section:r.branding}),r.introduce&&(0,a.jsx)(d.A,{section:r.introduce}),r.benefit&&(0,a.jsx)(c.default,{section:r.benefit}),r.usage&&(0,a.jsx)(p.A,{section:r.usage}),r.feature&&(0,a.jsx)(l.A,{section:r.feature}),r.showcase&&(0,a.jsx)(f.Ay,{section:r.showcase}),r.stats&&(0,a.jsx)(m.A,{section:r.stats}),r.pricing&&(0,a.jsx)(x.default,{pricing:r.pricing}),r.faq&&(0,a.jsx)(i.A,{section:r.faq}),r.cta&&(0,a.jsx)(n.A,{section:r.cta})]})}}};var t=require("../../../webpack-runtime.js");t.C(e);var r=e=>t(t.s=e),a=t.X(0,[5673,7711,8734,4457,7416,4626,2303,4829,3162,3748,9426,3499,9034,2794,7862,4452,1826,9206,1261,6183],()=>r(54522));module.exports=a})();