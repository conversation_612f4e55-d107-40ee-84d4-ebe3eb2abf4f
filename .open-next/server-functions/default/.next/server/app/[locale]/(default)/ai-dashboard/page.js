"use strict";(()=>{var e={};e.id=3492,e.ids=[3492],e.modules={3295:e=>{e.exports=require("next/dist/server/app-render/after-task-async-storage.external.js")},10846:e=>{e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},19121:e=>{e.exports=require("next/dist/server/app-render/action-async-storage.external.js")},24850:(e,r,t)=>{t.r(r),t.d(r,{GlobalError:()=>n.a,__next_app__:()=>p,pages:()=>c,routeModule:()=>u,tree:()=>l});var a=t(43533),s=t(91514),o=t(17220),n=t.n(o),i=t(62931),d={};for(let e in i)0>["default","tree","pages","GlobalError","__next_app__","routeModule"].indexOf(e)&&(d[e]=()=>i[e]);t.d(r,d);let l={children:["",{children:["[locale]",{children:["(default)",{children:["ai-dashboard",{children:["__PAGE__",{},{page:[()=>Promise.resolve().then(t.bind(t,99483)),"/Users/<USER>/My-Project/fluxkrea_test/app/[locale]/(default)/ai-dashboard/page.tsx"]}]},{}]},{layout:[()=>Promise.resolve().then(t.bind(t,17890)),"/Users/<USER>/My-Project/fluxkrea_test/app/[locale]/(default)/layout.tsx"]}]},{layout:[()=>Promise.resolve().then(t.bind(t,83581)),"/Users/<USER>/My-Project/fluxkrea_test/app/[locale]/layout.tsx"]}]},{"not-found":[()=>Promise.resolve().then(t.t.bind(t,53488,23)),"next/dist/client/components/not-found-error"],forbidden:[()=>Promise.resolve().then(t.t.bind(t,99017,23)),"next/dist/client/components/forbidden-error"],unauthorized:[()=>Promise.resolve().then(t.t.bind(t,90934,23)),"next/dist/client/components/unauthorized-error"]}]}.children,c=["/Users/<USER>/My-Project/fluxkrea_test/app/[locale]/(default)/ai-dashboard/page.tsx"],p={require:t,loadChunk:()=>Promise.resolve()},u=new a.AppPageRouteModule({definition:{kind:s.RouteKind.APP_PAGE,page:"/[locale]/(default)/ai-dashboard/page",pathname:"/[locale]/ai-dashboard",bundlePath:"",filename:"",appPaths:[]},userland:{loaderTree:l}})},29294:e=>{e.exports=require("next/dist/server/app-render/work-async-storage.external.js")},33873:e=>{e.exports=require("path")},63033:e=>{e.exports=require("next/dist/server/app-render/work-unit-async-storage.external.js")},99483:(e,r,t)=>{t.r(r),t.d(r,{default:()=>m,generateMetadata:()=>j});var a=t(34411),s=t(79115),o=t(23312),n=t(33425),i=t(68059),d=t(75525),l=t(97594),c=t(2013),p=t(4648),u=t(23463),h=t(45565),x=t(60854),f=t(95826),b=t(64476),g=t(41443);async function j({params:e}){let{locale:r}=await e,t="http://localhost:3000/ai-dashboard";return"en"!==r&&(t=`http://localhost:3000/${r}/ai-dashboard`),{alternates:{canonical:t}}}async function m({params:e}){let{locale:r}=await e,t=await (0,b.JE)(r),j="http://localhost:3000",m="en"===r?`${j}/ai-dashboard`:`${j}/${r}/ai-dashboard`,_=[{name:t.header?.brand?.title||"FLUX2",url:"/ai-dashboard"}],y={title:t.hero?.title||"FLUX2",description:t.hero?.description?.replace(/<br\s*\/?>/gi," ")||t.feature?.description||"AI creative platform",image:`${j}/imgs/features/ai_dashboard_interface.png`,url:m,schemaType:"SoftwareApplication",applicationCategory:"WebApplication",operatingSystem:"Web Browser, iOS, Android",datePublished:"2024-01-01T00:00:00Z",dateModified:new Date().toISOString(),breadcrumb:_};return(0,a.jsxs)(a.Fragment,{children:[(0,a.jsx)(g.Ay,{...y}),t.hero&&(0,a.jsx)(u.A,{hero:t.hero}),(0,a.jsx)(s.WorkspaceLayout,{}),t.branding&&(0,a.jsx)(o.A,{section:t.branding}),t.introduce&&(0,a.jsx)(l.A,{section:t.introduce}),t.benefit&&(0,a.jsx)(c.default,{section:t.benefit}),t.usage&&(0,a.jsx)(p.A,{section:t.usage}),t.feature&&(0,a.jsx)(d.A,{section:t.feature}),t.showcase&&(0,a.jsx)(x.Ay,{section:t.showcase}),t.stats&&(0,a.jsx)(f.A,{section:t.stats}),t.pricing&&(0,a.jsx)(h.default,{pricing:t.pricing}),t.faq&&(0,a.jsx)(i.A,{section:t.faq}),t.cta&&(0,a.jsx)(n.A,{section:t.cta})]})}}};var r=require("../../../../webpack-runtime.js");r.C(e);var t=e=>r(r.s=e),a=r.X(0,[5673,7711,8734,4457,7416,4626,2303,4829,3162,3748,9426,3499,9034,2794,7862,4452,1826,9206,1261,6183],()=>t(24850));module.exports=a})();