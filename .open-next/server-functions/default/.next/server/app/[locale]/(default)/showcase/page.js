(()=>{var e={};e.id=6294,e.ids=[6294],e.modules={3295:e=>{"use strict";e.exports=require("next/dist/server/app-render/after-task-async-storage.external.js")},10846:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},16674:(e,t,r)=>{Promise.resolve().then(r.bind(r,83093)),Promise.resolve().then(r.bind(r,38246)),Promise.resolve().then(r.t.bind(r,10050,23)),Promise.resolve().then(r.t.bind(r,76881,23))},18586:(e,t,r)=>{"use strict";r.d(t,{BT:()=>i,Wu:()=>c,X9:()=>d,ZB:()=>l,Zp:()=>a,aR:()=>n,wL:()=>u});var s=r(34411);r(37582);var o=r(25039);function a({className:e,...t}){return(0,s.jsx)("div",{"data-slot":"card",className:(0,o.cn)("bg-card text-card-foreground flex flex-col gap-6 rounded-2xl border border-border/50 p-6 shadow-lg backdrop-blur-sm transition-all duration-200 hover:shadow-xl hover:scale-[1.01]",e),...t})}function n({className:e,...t}){return(0,s.jsx)("div",{"data-slot":"card-header",className:(0,o.cn)("@container/card-header grid auto-rows-min grid-rows-[auto_auto] items-start gap-2 has-data-[slot=card-action]:grid-cols-[1fr_auto] [.border-b]:pb-6",e),...t})}function l({className:e,...t}){return(0,s.jsx)("div",{"data-slot":"card-title",className:(0,o.cn)("leading-none font-semibold",e),...t})}function i({className:e,...t}){return(0,s.jsx)("div",{"data-slot":"card-description",className:(0,o.cn)("text-muted-foreground text-sm",e),...t})}function d({className:e,...t}){return(0,s.jsx)("div",{"data-slot":"card-action",className:(0,o.cn)("col-start-2 row-span-2 row-start-1 self-start justify-self-end",e),...t})}function c({className:e,...t}){return(0,s.jsx)("div",{"data-slot":"card-content",className:(0,o.cn)("px-6",e),...t})}function u({className:e,...t}){return(0,s.jsx)("div",{"data-slot":"card-footer",className:(0,o.cn)("flex items-center px-6 [.border-t]:pt-6",e),...t})}},19121:e=>{"use strict";e.exports=require("next/dist/server/app-render/action-async-storage.external.js")},29294:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-async-storage.external.js")},33873:e=>{"use strict";e.exports=require("path")},34826:(e,t,r)=>{Promise.resolve().then(r.bind(r,73699)),Promise.resolve().then(r.bind(r,27084)),Promise.resolve().then(r.t.bind(r,59316,23)),Promise.resolve().then(r.t.bind(r,14127,23))},38246:(e,t,r)=>{"use strict";r.d(t,{default:()=>s});let s=(0,r(29037).registerClientReference)(function(){throw Error("Attempted to call the default export of \"/Users/<USER>/My-Project/fluxkrea_test/components/blocks/showcase/prompt-showcase.tsx\" from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"/Users/<USER>/My-Project/fluxkrea_test/components/blocks/showcase/prompt-showcase.tsx","default")},44857:(e,t,r)=>{"use strict";r.r(t),r.d(t,{default:()=>n});var s=r(34411),o=r(60854),a=r(64476);async function n({params:e}){let{locale:t}=await e,r=await (0,a.DN)(t);return(0,s.jsx)(s.Fragment,{children:r.showcase&&(0,s.jsx)(o.Ay,{section:r.showcase})})}},60854:(e,t,r)=>{"use strict";r.d(t,{Ay:()=>i});var s=r(34411),o=r(18586),a=r(77094),n=r(10050),l=r.n(n);function i({section:e}){return e.disabled?null:(0,s.jsxs)("section",{className:"container py-16",children:[(0,s.jsxs)("div",{className:"mx-auto mb-12 text-center",children:[(0,s.jsx)("h2",{className:"mb-6 text-pretty text-3xl font-bold lg:text-4xl",children:e.title}),(0,s.jsx)("p",{className:"mb-4 max-w-xl text-muted-foreground lg:max-w-none lg:text-lg",children:e.description})]}),(0,s.jsx)("div",{className:"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8",children:e.items?.map((e,t)=>s.jsx(l(),{href:e.url||"",target:e.target,children:s.jsx(o.Zp,{className:"overflow-hidden transition-all hover:shadow-lg dark:hover:shadow-primary/10 p-0",children:s.jsxs(o.Wu,{className:"p-0",children:[s.jsx("div",{className:"relative aspect-[16/10] w-full overflow-hidden",children:s.jsx(a.default,{src:e.image?.src||"",alt:e.image?.alt||e.title||"",fill:!0,className:"object-cover rounded-t-lg transition-transform duration-300 hover:scale-110"})}),s.jsxs("div",{className:"p-6",children:[s.jsx("h3",{className:"text-xl font-semibold mb-2 line-clamp-1",children:e.title}),s.jsx("p",{className:"text-sm text-muted-foreground line-clamp-3",children:e.description})]})]})})},t))})]})}r(38246),r(83093)},63033:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-unit-async-storage.external.js")},83093:(e,t,r)=>{"use strict";r.d(t,{default:()=>s});let s=(0,r(29037).registerClientReference)(function(){throw Error("Attempted to call the default export of \"/Users/<USER>/My-Project/fluxkrea_test/components/blocks/showcase/comparison-showcase.tsx\" from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"/Users/<USER>/My-Project/fluxkrea_test/components/blocks/showcase/comparison-showcase.tsx","default")},84498:(e,t,r)=>{"use strict";r.r(t),r.d(t,{GlobalError:()=>n.a,__next_app__:()=>u,pages:()=>c,routeModule:()=>p,tree:()=>d});var s=r(43533),o=r(91514),a=r(17220),n=r.n(a),l=r(62931),i={};for(let e in l)0>["default","tree","pages","GlobalError","__next_app__","routeModule"].indexOf(e)&&(i[e]=()=>l[e]);r.d(t,i);let d={children:["",{children:["[locale]",{children:["(default)",{children:["showcase",{children:["__PAGE__",{},{page:[()=>Promise.resolve().then(r.bind(r,44857)),"/Users/<USER>/My-Project/fluxkrea_test/app/[locale]/(default)/showcase/page.tsx"]}]},{}]},{layout:[()=>Promise.resolve().then(r.bind(r,17890)),"/Users/<USER>/My-Project/fluxkrea_test/app/[locale]/(default)/layout.tsx"]}]},{layout:[()=>Promise.resolve().then(r.bind(r,83581)),"/Users/<USER>/My-Project/fluxkrea_test/app/[locale]/layout.tsx"]}]},{"not-found":[()=>Promise.resolve().then(r.t.bind(r,53488,23)),"next/dist/client/components/not-found-error"],forbidden:[()=>Promise.resolve().then(r.t.bind(r,99017,23)),"next/dist/client/components/forbidden-error"],unauthorized:[()=>Promise.resolve().then(r.t.bind(r,90934,23)),"next/dist/client/components/unauthorized-error"]}]}.children,c=["/Users/<USER>/My-Project/fluxkrea_test/app/[locale]/(default)/showcase/page.tsx"],u={require:r,loadChunk:()=>Promise.resolve()},p=new s.AppPageRouteModule({definition:{kind:o.RouteKind.APP_PAGE,page:"/[locale]/(default)/showcase/page",pathname:"/[locale]/showcase",bundlePath:"",filename:"",appPaths:[]},userland:{loaderTree:d}})}};var t=require("../../../../webpack-runtime.js");t.C(e);var r=e=>t(t.s=e),s=t.X(0,[5673,7711,8734,4457,7416,4626,2303,4829,3162,3748,9426,2794,7862,4452,1826],()=>r(84498));module.exports=s})();