(()=>{var e={};e.id=2314,e.ids=[2314],e.modules={3295:e=>{"use strict";e.exports=require("next/dist/server/app-render/after-task-async-storage.external.js")},10846:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},18230:(e,t,r)=>{"use strict";r.d(t,{A:()=>s});let s=(0,r(32381).A)("Copy",[["rect",{width:"14",height:"14",x:"8",y:"8",rx:"2",ry:"2",key:"17jyea"}],["path",{d:"M4 16c-1.1 0-2-.9-2-2V4c0-1.1.9-2 2-2h10c1.1 0 2 .9 2 2",key:"zix9uf"}]])},19121:e=>{"use strict";e.exports=require("next/dist/server/app-render/action-async-storage.external.js")},19405:(e,t,r)=>{"use strict";r.d(t,{A:()=>s});let s=(0,r(32381).A)("ArrowRight",[["path",{d:"M5 12h14",key:"1ays0h"}],["path",{d:"m12 5 7 7-7 7",key:"xquz4c"}]])},29294:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-async-storage.external.js")},32149:(e,t,r)=>{Promise.resolve().then(r.bind(r,43197))},33873:e=>{"use strict";e.exports=require("path")},43197:(e,t,r)=>{"use strict";r.d(t,{default:()=>s});let s=(0,r(29037).registerClientReference)(function(){throw Error("Attempted to call the default export of \"/Users/<USER>/My-Project/fluxkrea_test/components/blocks/showcase/demo-page.tsx\" from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"/Users/<USER>/My-Project/fluxkrea_test/components/blocks/showcase/demo-page.tsx","default")},45717:(e,t,r)=>{Promise.resolve().then(r.bind(r,46448))},46448:(e,t,r)=>{"use strict";r.d(t,{default:()=>o});var s=r(2569);r(68064),r(13748),r(59316);var a=r(27084),i=r(73699);function o(){return(0,s.jsxs)("div",{className:"min-h-screen bg-background",children:[(0,s.jsx)("div",{className:"container py-16",children:(0,s.jsxs)("div",{className:"text-center mb-16",children:[(0,s.jsx)("h1",{className:"text-4xl font-bold mb-4 bg-gradient-to-r from-primary to-accent bg-clip-text text-transparent",children:"Showcase 组件演示"}),(0,s.jsx)("p",{className:"text-lg text-muted-foreground max-w-2xl mx-auto",children:"展示 PromptShowcase 和 ComparisonShowcase 两个组件的实际效果"})]})}),(0,s.jsx)(a.default,{section:{title:"FLUX & KREA AI 创作 Prompt 精选",description:"精心调试的高质量 Prompt，助您在 FLUX 和 KREA 平台创作出令人惊艳的 AI 作品",items:[{title:"超现实主义人像",description:"创造具有梦幻色彩和超现实元素的人物肖像",prompt:"Surreal portrait of a person with flowing liquid hair that transforms into cosmic nebula, ethereal lighting, dreamlike atmosphere, hyperrealistic details, vibrant colors blending into abstract patterns, professional photography style, 8K ultra-detailed",image:{src:"/imgs/showcases/flux-krea-showcase-2-1.webp",alt:"超现实主义人像作品"},category:"人像创作",tags:["超现实","梦幻","宇宙","液体"]},{title:"科幻场景设计",prompt:"Futuristic sci-fi environment with holographic interfaces, neon-lit corridors, advanced technology panels, atmospheric fog, dramatic lighting with blue and purple tones, cyberpunk aesthetic, highly detailed architecture, cinematic composition",image:{src:"/imgs/showcases/flux-krea-showcase-2-2.webp",alt:"科幻场景设计作品"}},{title:"抽象艺术创作",description:"充满创意的抽象艺术风格图像",prompt:"Abstract digital art with flowing geometric shapes, gradient colors transitioning from warm to cool tones, dynamic composition with depth and movement, modern artistic style, high contrast, professional digital artwork, 4K resolution",image:{src:"/imgs/showcases/flux-krea-showcase-2-3.webp",alt:"抽象艺术创作作品"},category:"抽象艺术",tags:["抽象","几何","渐变","动态"]},{title:"自然风光摄影",description:"壮观的自然景观和风光摄影效果",prompt:"Breathtaking landscape photography of mountain peaks during golden hour, dramatic clouds, perfect lighting, ultra-wide angle shot, professional nature photography, vivid colors, high dynamic range, crystal clear details, award-winning composition",image:{src:"/imgs/showcases/flux-krea-showcase-1-1.webp",alt:"自然风光摄影作品"},category:"风光摄影",tags:["山峰","黄金时刻","自然","广角"]},{title:"时尚人物摄影",description:"高端时尚杂志风格的人物摄影",prompt:"High-fashion portrait photography, model wearing avant-garde clothing, studio lighting setup, clean background, professional makeup and styling, editorial magazine quality, sharp focus, luxury aesthetic, commercial photography style",image:{src:"/imgs/showcases/flux-krea-showcase-1-2.webp",alt:"时尚人物摄影作品"},category:"时尚摄影",tags:["时尚","杂志","工作室","高端"]},{title:"建筑空间设计",description:"现代建筑和室内空间的设计概念",prompt:"Modern architectural interior design, minimalist aesthetic, natural lighting through large windows, clean lines and geometric forms, neutral color palette, high-end materials, professional architectural photography, ultra-detailed",image:{src:"/imgs/showcases/flux-krea-showcase-1-3.webp",alt:"建筑空间设计作品"},category:"建筑设计",tags:["现代","极简","室内","几何"]}]}}),(0,s.jsx)("div",{className:"container py-8",children:(0,s.jsx)("div",{className:"border-t border-border/50"})}),(0,s.jsx)(i.default,{section:{title:"FLUX & KREA AI 图像处理前后对比",description:"展示 FLUX 和 KREA AI 强大的图像处理能力，从普通照片到专业级作品的华丽转变",items:[{title:"艺术风格转换效果",description:"将普通图像转换为具有艺术感的创作作品",prompt:"Transform this image with artistic enhancement: apply cinematic lighting, enhance colors and contrast, add artistic depth and atmosphere, maintain natural proportions while creating a more visually striking and professional appearance. Style: photorealistic with artistic flair, high detail, dramatic lighting.",beforeImage:{src:"/imgs/showcases/flux-krea-showcase-3-1-1.jpg",alt:"AI处理前的原始图像"},afterImage:{src:"/imgs/showcases/flux-krea-showcase-3-1-2.jpg",alt:"AI艺术风格转换后的图像"},category:"艺术转换",tags:["风格转换","艺术增强","色彩优化","专业级"]}]}}),(0,s.jsx)("div",{className:"container py-16",children:(0,s.jsx)("div",{className:"text-center",children:(0,s.jsxs)("div",{className:"bg-muted/50 rounded-lg p-8 max-w-4xl mx-auto",children:[(0,s.jsx)("h3",{className:"text-xl font-semibold mb-4",children:"组件特性说明"}),(0,s.jsxs)("div",{className:"grid md:grid-cols-2 gap-6 text-sm text-muted-foreground",children:[(0,s.jsxs)("div",{children:[(0,s.jsx)("h4",{className:"font-medium text-foreground mb-2",children:"PromptShowcase"}),(0,s.jsxs)("ul",{className:"space-y-1 text-left",children:[(0,s.jsx)("li",{children:"• 支持 Prompt 一键复制"}),(0,s.jsx)("li",{children:"• 响应式网格布局"}),(0,s.jsx)("li",{children:"• 分类标签和多标签支持"}),(0,s.jsx)("li",{children:"• 复制状态反馈"})]})]}),(0,s.jsxs)("div",{children:[(0,s.jsx)("h4",{className:"font-medium text-foreground mb-2",children:"ComparisonShowcase"}),(0,s.jsxs)("ul",{className:"space-y-1 text-left",children:[(0,s.jsx)("li",{children:"• 前后图片对比展示"}),(0,s.jsx)("li",{children:"• 移动端垂直布局适配"}),(0,s.jsx)("li",{children:"• 图片悬停缩放效果"}),(0,s.jsx)("li",{children:"• Prompt 展示和复制"})]})]})]})]})})})]})}},47349:(e,t,r)=>{"use strict";r.r(t),r.d(t,{default:()=>i,metadata:()=>o});var s=r(34411),a=r(43197);function i(){return(0,s.jsx)(a.default,{})}let o={title:"Showcase 组件演示 | FLUX KREA",description:"展示 PromptShowcase 和 ComparisonShowcase 组件的实际效果"}},63033:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-unit-async-storage.external.js")},70538:(e,t,r)=>{"use strict";r.r(t),r.d(t,{GlobalError:()=>o.a,__next_app__:()=>p,pages:()=>d,routeModule:()=>h,tree:()=>c});var s=r(43533),a=r(91514),i=r(17220),o=r.n(i),n=r(62931),l={};for(let e in n)0>["default","tree","pages","GlobalError","__next_app__","routeModule"].indexOf(e)&&(l[e]=()=>n[e]);r.d(t,l);let c={children:["",{children:["[locale]",{children:["(default)",{children:["showcase-demo",{children:["__PAGE__",{},{page:[()=>Promise.resolve().then(r.bind(r,47349)),"/Users/<USER>/My-Project/fluxkrea_test/app/[locale]/(default)/showcase-demo/page.tsx"]}]},{}]},{layout:[()=>Promise.resolve().then(r.bind(r,17890)),"/Users/<USER>/My-Project/fluxkrea_test/app/[locale]/(default)/layout.tsx"]}]},{layout:[()=>Promise.resolve().then(r.bind(r,83581)),"/Users/<USER>/My-Project/fluxkrea_test/app/[locale]/layout.tsx"]}]},{"not-found":[()=>Promise.resolve().then(r.t.bind(r,53488,23)),"next/dist/client/components/not-found-error"],forbidden:[()=>Promise.resolve().then(r.t.bind(r,99017,23)),"next/dist/client/components/forbidden-error"],unauthorized:[()=>Promise.resolve().then(r.t.bind(r,90934,23)),"next/dist/client/components/unauthorized-error"]}]}.children,d=["/Users/<USER>/My-Project/fluxkrea_test/app/[locale]/(default)/showcase-demo/page.tsx"],p={require:r,loadChunk:()=>Promise.resolve()},h=new s.AppPageRouteModule({definition:{kind:a.RouteKind.APP_PAGE,page:"/[locale]/(default)/showcase-demo/page",pathname:"/[locale]/showcase-demo",bundlePath:"",filename:"",appPaths:[]},userland:{loaderTree:c}})}};var t=require("../../../../webpack-runtime.js");t.C(e);var r=e=>t(t.s=e),s=t.X(0,[5673,7711,8734,4457,7416,4626,2303,4829,3162,3748,2794,7862,4452,1826],()=>r(70538));module.exports=s})();