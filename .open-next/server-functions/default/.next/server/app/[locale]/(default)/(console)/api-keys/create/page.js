(()=>{var e={};e.id=4106,e.ids=[4106],e.modules={3295:e=>{"use strict";e.exports=require("next/dist/server/app-render/after-task-async-storage.external.js")},3764:(e,t,r)=>{"use strict";r.d(t,{p:()=>s});var i=r(2569);r(29068);var a=r(75673);function s({className:e,type:t,...r}){return(0,i.jsx)("input",{type:t,"data-slot":"input",className:(0,a.cn)("file:text-foreground placeholder:text-muted-foreground selection:bg-primary selection:text-primary-foreground dark:bg-input/30 border-input flex h-9 w-full min-w-0 rounded-md border bg-transparent px-3 py-1 text-base shadow-xs transition-[color,box-shadow] outline-none file:inline-flex file:h-7 file:border-0 file:bg-transparent file:text-sm file:font-medium disabled:pointer-events-none disabled:cursor-not-allowed disabled:opacity-50 md:text-sm","focus-visible:border-ring focus-visible:ring-ring/50 focus-visible:ring-[3px]","aria-invalid:ring-destructive/20 dark:aria-invalid:ring-destructive/40 aria-invalid:border-destructive",e),...r})}},4573:e=>{"use strict";e.exports=require("node:buffer")},6120:(e,t,r)=>{Promise.resolve().then(r.bind(r,78085)),Promise.resolve().then(r.bind(r,92227)),Promise.resolve().then(r.bind(r,80081)),Promise.resolve().then(r.bind(r,20518)),Promise.resolve().then(r.t.bind(r,59316,23))},10050:(e,t,r)=>{let{createProxy:i}=r(26474);e.exports=i("/Users/<USER>/My-Project/fluxkrea_test/node_modules/.pnpm/next@15.2.3_@babel+core@7.28.0_@opentelemetry+api@1.9.0_react-dom@18.3.1_react@18.3.1__react@18.3.1/node_modules/next/dist/client/app-dir/link.js")},10846:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},11997:e=>{"use strict";e.exports=require("punycode")},19121:e=>{"use strict";e.exports=require("next/dist/server/app-render/action-async-storage.external.js")},19272:(e,t,r)=>{Promise.resolve().then(r.bind(r,1647)),Promise.resolve().then(r.bind(r,57595)),Promise.resolve().then(r.bind(r,70439)),Promise.resolve().then(r.bind(r,37032)),Promise.resolve().then(r.t.bind(r,10050,23))},20392:(e,t,r)=>{"use strict";r.d(t,{DX:()=>n});var i=r(37582);function a(e,t){if("function"==typeof e)return e(t);null!=e&&(e.current=t)}var s=r(34411),n=function(e){let t=function(e){let t=i.forwardRef((e,t)=>{let{children:r,...s}=e;if(i.isValidElement(r)){var n;let e,o;let l=(n=r,(o=(e=Object.getOwnPropertyDescriptor(n.props,"ref")?.get)&&"isReactWarning"in e&&e.isReactWarning)?n.ref:(o=(e=Object.getOwnPropertyDescriptor(n,"ref")?.get)&&"isReactWarning"in e&&e.isReactWarning)?n.props.ref:n.props.ref||n.ref),c=function(e,t){let r={...t};for(let i in t){let a=e[i],s=t[i];/^on[A-Z]/.test(i)?a&&s?r[i]=(...e)=>{let t=s(...e);return a(...e),t}:a&&(r[i]=a):"style"===i?r[i]={...a,...s}:"className"===i&&(r[i]=[a,s].filter(Boolean).join(" "))}return{...e,...r}}(s,r.props);return r.type!==i.Fragment&&(c.ref=t?function(...e){return t=>{let r=!1,i=e.map(e=>{let i=a(e,t);return r||"function"!=typeof i||(r=!0),i});if(r)return()=>{for(let t=0;t<i.length;t++){let r=i[t];"function"==typeof r?r():a(e[t],null)}}}}(t,l):l),i.cloneElement(r,c)}return i.Children.count(r)>1?i.Children.only(null):null});return t.displayName=`${e}.SlotClone`,t}(e),r=i.forwardRef((e,r)=>{let{children:a,...n}=e,o=i.Children.toArray(a),c=o.find(l);if(c){let e=c.props.children,a=o.map(t=>t!==c?t:i.Children.count(e)>1?i.Children.only(null):i.isValidElement(e)?e.props.children:null);return(0,s.jsx)(t,{...n,ref:r,children:i.isValidElement(e)?i.cloneElement(e,void 0,a):null})}return(0,s.jsx)(t,{...n,ref:r,children:a})});return r.displayName=`${e}.Slot`,r}("Slot"),o=Symbol("radix.slottable");function l(e){return i.isValidElement(e)&&"function"==typeof e.type&&"__radixId"in e.type&&e.type.__radixId===o}},26826:(e,t,r)=>{"use strict";r.d(t,{A:()=>l});var i=r(37582);let a=e=>e.replace(/([a-z0-9])([A-Z])/g,"$1-$2").toLowerCase(),s=(...e)=>e.filter((e,t,r)=>!!e&&r.indexOf(e)===t).join(" ");var n={xmlns:"http://www.w3.org/2000/svg",width:24,height:24,viewBox:"0 0 24 24",fill:"none",stroke:"currentColor",strokeWidth:2,strokeLinecap:"round",strokeLinejoin:"round"};let o=(0,i.forwardRef)(({color:e="currentColor",size:t=24,strokeWidth:r=2,absoluteStrokeWidth:a,className:o="",children:l,iconNode:c,...d},u)=>(0,i.createElement)("svg",{ref:u,...n,width:t,height:t,stroke:e,strokeWidth:a?24*Number(r)/Number(t):r,className:s("lucide",o),...d},[...c.map(([e,t])=>(0,i.createElement)(e,t)),...Array.isArray(l)?l:[l]])),l=(e,t)=>{let r=(0,i.forwardRef)(({className:r,...n},l)=>(0,i.createElement)(o,{ref:l,iconNode:t,className:s(`lucide-${a(e)}`,r),...n}));return r.displayName=`${e}`,r}},27910:e=>{"use strict";e.exports=require("stream")},28432:(e,t,r)=>{"use strict";r.d(t,{j2:()=>f,Y9:()=>u});var i=r(62364),a=r(36157),s=r(72648),n=r(39201),o=r(4961),l=r(29452),c=r(60736);let d=[];d.push((0,a.A)({id:"google-one-tap",name:"google-one-tap",credentials:{credential:{type:"text"}},async authorize(e,t){let r=e.credential,i=await fetch("https://oauth2.googleapis.com/tokeninfo?id_token="+r);if(!i.ok)return console.log("Failed to verify token"),null;let a=await i.json();if(!a)return console.log("invalid payload from token"),null;let{email:s,sub:n,given_name:o,family_name:l,email_verified:c,picture:d}=a;return s?{id:n,name:[o,l].join(" "),email:s,image:d,emailVerified:c?new Date:null}:(console.log("invalid email in payload"),null)}})),process.env.AUTH_GOOGLE_ID&&process.env.AUTH_GOOGLE_SECRET&&d.push((0,s.A)({clientId:process.env.AUTH_GOOGLE_ID,clientSecret:process.env.AUTH_GOOGLE_SECRET,authorization:{params:{prompt:"consent",access_type:"offline",response_type:"code"}}})),d.map(e=>{if("function"!=typeof e)return{id:e.id,name:e.name};{let t=e();return{id:t.id,name:t.name}}}).filter(e=>"google-one-tap"!==e.id);let{handlers:u,signIn:p,signOut:m,auth:f}=(0,i.Ay)({providers:d,pages:{signIn:"/auth/signin"},callbacks:{signIn:async({user:e,account:t,profile:r,email:i,credentials:a})=>!0,redirect:async({url:e,baseUrl:t})=>e.startsWith("/")?`${t}${e}`:new URL(e).origin===t?e:t,session:async({session:e,token:t,user:r})=>(t&&t.user&&t.user&&(e.user=t.user),e),async jwt({token:e,user:t,account:r}){try{if(t&&t.email&&r){let i={uuid:(0,l.YJ)(),email:t.email,nickname:t.name||"",avatar_url:t.image||"",signin_type:r.type,signin_provider:r.provider,signin_openid:r.providerAccountId,created_at:(0,o.iq)(),signin_ip:await (0,n.T)()};try{let t=await (0,c.$c)(i);e.user={uuid:t.uuid,email:t.email,nickname:t.nickname,avatar_url:t.avatar_url,created_at:t.created_at}}catch(e){console.error("save user failed:",e)}}return e}catch(t){return console.error("jwt callback error:",t),e}}}})},29294:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-async-storage.external.js")},31547:(e,t,r)=>{Promise.resolve().then(r.bind(r,35211)),Promise.resolve().then(r.bind(r,20518))},33873:e=>{"use strict";e.exports=require("path")},34414:(e,t,r)=>{"use strict";r.d(t,{A:()=>o});var i=r(34411),a=r(92172),s=r(57595),n=r(78933);function o({items:e}){return(0,i.jsx)("div",{className:"flex space-x-4 mb-8",children:e?.map((e,t)=>e.url?i.jsx(n.N_,{href:e.url,children:i.jsxs(a.$,{variant:e.variant,size:"sm",className:e.className,children:[e.icon&&i.jsx(s.default,{name:e.icon,className:"w-4 h-4 mr-1"}),e.title]})},t):i.jsxs(a.$,{variant:e.variant,size:"sm",className:e.className,children:[e.icon&&i.jsx(s.default,{name:e.icon,className:"w-4 h-4 mr-1"}),e.title]},t))})}},34631:e=>{"use strict";e.exports=require("tls")},35211:(e,t,r)=>{"use strict";r.d(t,{default:()=>c});var i=r(2569),a=r(92227),s=r(37967),n=r(92622),o=r(75673),l=r(14059);function c({className:e,items:t,...r}){let c=(0,l.usePathname)();return console.log(c),(0,i.jsx)("nav",{className:(0,o.cn)("flex space-x-2 lg:flex-col lg:space-x-0 lg:space-y-1",e),...r,children:t.map((e,t)=>(0,i.jsxs)(s.N_,{href:e.url,className:(0,o.cn)((0,n.r)({variant:"ghost"}),e.is_active||c.includes(e.url)?"bg-muted/50 text-primary hover:bg-muted hover:text-primary":"hover:bg-transparent hover:underline","justify-start"),children:[e.icon&&(0,i.jsx)(a.default,{name:e.icon,className:"w-4 h-4"}),e.title]},t))})}},36393:(e,t,r)=>{"use strict";r.d(t,{b:()=>c});var i=r(29068),a=r(85137),s=r(2569),n="horizontal",o=["horizontal","vertical"],l=i.forwardRef((e,t)=>{var r;let{decorative:i,orientation:l=n,...c}=e,d=(r=l,o.includes(r))?l:n;return(0,s.jsx)(a.sG.div,{"data-orientation":d,...i?{role:"none"}:{"aria-orientation":"vertical"===d?d:void 0,role:"separator"},...c,ref:t})});l.displayName="Separator";var c=l},38e3:(e,t,r)=>{"use strict";r.d(t,{F:()=>n});var i=r(1832);let a=e=>"boolean"==typeof e?`${e}`:0===e?"0":e,s=i.$,n=(e,t)=>r=>{var i;if((null==t?void 0:t.variants)==null)return s(e,null==r?void 0:r.class,null==r?void 0:r.className);let{variants:n,defaultVariants:o}=t,l=Object.keys(n).map(e=>{let t=null==r?void 0:r[e],i=null==o?void 0:o[e];if(null===t)return null;let s=a(t)||a(i);return n[e][s]}),c=r&&Object.entries(r).reduce((e,t)=>{let[r,i]=t;return void 0===i||(e[r]=i),e},{});return s(e,l,null==t?void 0:null===(i=t.compoundVariants)||void 0===i?void 0:i.reduce((e,t)=>{let{class:r,className:i,...a}=t;return Object.entries(a).every(e=>{let[t,r]=e;return Array.isArray(r)?r.includes({...o,...c}[t]):({...o,...c})[t]===r})?[...e,r,i]:e},[]),null==r?void 0:r.class,null==r?void 0:r.className)}},39201:(e,t,r)=>{"use strict";r.d(t,{T:()=>s});var i=r(72784);r(97468);var a=r(62225);async function s(){let e=await (0,a.b3)();return e.get("cf-connecting-ip")||e.get("x-real-ip")||(e.get("x-forwarded-for")||"127.0.0.1").split(",")[0]}(0,r(78564).D)([s]),(0,i.A)(s,"00880263a376180560adeff1fdd2511a7543726d4a",null)},39838:(e,t,r)=>{"use strict";r.r(t),r.d(t,{GlobalError:()=>n.a,__next_app__:()=>u,pages:()=>d,routeModule:()=>p,tree:()=>c});var i=r(43533),a=r(91514),s=r(17220),n=r.n(s),o=r(62931),l={};for(let e in o)0>["default","tree","pages","GlobalError","__next_app__","routeModule"].indexOf(e)&&(l[e]=()=>o[e]);r.d(t,l);let c={children:["",{children:["[locale]",{children:["(default)",{children:["(console)",{children:["api-keys",{children:["create",{children:["__PAGE__",{},{page:[()=>Promise.resolve().then(r.bind(r,71803)),"/Users/<USER>/My-Project/fluxkrea_test/app/[locale]/(default)/(console)/api-keys/create/page.tsx"]}]},{}]},{}]},{layout:[()=>Promise.resolve().then(r.bind(r,65690)),"/Users/<USER>/My-Project/fluxkrea_test/app/[locale]/(default)/(console)/layout.tsx"]}]},{layout:[()=>Promise.resolve().then(r.bind(r,17890)),"/Users/<USER>/My-Project/fluxkrea_test/app/[locale]/(default)/layout.tsx"]}]},{layout:[()=>Promise.resolve().then(r.bind(r,83581)),"/Users/<USER>/My-Project/fluxkrea_test/app/[locale]/layout.tsx"]}]},{"not-found":[()=>Promise.resolve().then(r.t.bind(r,53488,23)),"next/dist/client/components/not-found-error"],forbidden:[()=>Promise.resolve().then(r.t.bind(r,99017,23)),"next/dist/client/components/forbidden-error"],unauthorized:[()=>Promise.resolve().then(r.t.bind(r,90934,23)),"next/dist/client/components/unauthorized-error"]}]}.children,d=["/Users/<USER>/My-Project/fluxkrea_test/app/[locale]/(default)/(console)/api-keys/create/page.tsx"],u={require:r,loadChunk:()=>Promise.resolve()},p=new i.AppPageRouteModule({definition:{kind:a.RouteKind.APP_PAGE,page:"/[locale]/(default)/(console)/api-keys/create/page",pathname:"/[locale]/api-keys/create",bundlePath:"",filename:"",appPaths:[]},userland:{loaderTree:c}})},55511:e=>{"use strict";e.exports=require("crypto")},55591:e=>{"use strict";e.exports=require("https")},55977:(e,t,r)=>{"use strict";r.d(t,{default:()=>i});let i=(0,r(29037).registerClientReference)(function(){throw Error("Attempted to call the default export of \"/Users/<USER>/My-Project/fluxkrea_test/components/console/sidebar/nav.tsx\" from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"/Users/<USER>/My-Project/fluxkrea_test/components/console/sidebar/nav.tsx","default")},57975:e=>{"use strict";e.exports=require("node:util")},60736:(e,t,r)=>{"use strict";r.d(t,{$c:()=>c,TG:()=>d,qo:()=>p,ug:()=>m});var i=r(87830),a=r(35050),s=r(28432),n=r(4961),o=r(78054),l=r(62225);async function c(e){try{let t=await (0,a.Gs)(e.email);return t?(e.id=t.id,e.uuid=t.uuid,e.created_at=t.created_at):(await (0,a.HW)(e),await (0,i.d_)({user_uuid:e.uuid||"",trans_type:i.H3.NewUser,credits:i.rN.NewUserGet,expired_at:(0,n.MI)()})),e}catch(e){throw console.log("save user failed: ",e),e}}async function d(){let e="",t=await u();if(t&&t.startsWith("sk-"))return await (0,o.zz)(t)||"";let r=await (0,s.j2)();return r&&r.user&&r.user.uuid&&(e=r.user.uuid),e}async function u(){let e=(await (0,l.b3)()).get("Authorization");return e?e.replace("Bearer ",""):""}async function p(){let e="",t=await (0,s.j2)();return t&&t.user&&t.user.email&&(e=t.user.email),e}async function m(){let e=await d();if(e)return await (0,a.pX)(e)}},63033:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-unit-async-storage.external.js")},65690:(e,t,r)=>{"use strict";r.r(t),r.d(t,{default:()=>c});var i=r(34411),a=r(55977);async function s({children:e,sidebar:t}){return(0,i.jsx)("div",{className:"container md:max-w-7xl py-8 mx-auto",children:(0,i.jsx)("div",{className:"w-full space-y-6 p-4 pb-16 block",children:(0,i.jsxs)("div",{className:"flex flex-col space-y-8 lg:flex-row lg:space-x-12 lg:space-y-0",children:[t?.nav?.items&&(0,i.jsx)("aside",{className:"md:min-w-40 flex-shrink-0",children:(0,i.jsx)(a.default,{items:t.nav?.items})}),(0,i.jsx)("div",{className:"flex-1 lg:max-w-full",children:e})]})})})}var n=r(68791),o=r(60736),l=r(79502);async function c({children:e}){let t=await (0,o.ug)();t&&t.email||(0,l.redirect)("/auth/signin");let r=await (0,n.A)(),a={nav:{items:[{title:r("user.my_orders"),url:"/my-orders",icon:"RiOrderPlayLine",is_active:!1},{title:r("my_credits.title"),url:"/my-credits",icon:"RiBankCardLine",is_active:!1},{title:r("my_invites.title"),url:"/my-invites",icon:"RiMoneyCnyCircleFill",is_active:!1},{title:r("api_keys.title"),url:"/api-keys",icon:"RiKey2Line",is_active:!1}]}};return(0,i.jsx)(s,{sidebar:a,children:e})}},65891:(e,t,r)=>{Promise.resolve().then(r.bind(r,55977)),Promise.resolve().then(r.bind(r,37032))},68791:(e,t,r)=>{"use strict";r.d(t,{A:()=>o});var i=r(37582),a=r(93134),s=r(21972),n=(0,i.cache)(function(e,t){return function({_cache:e=(0,s.d)(),_formatters:t=(0,s.b)(e),getMessageFallback:r=s.f,messages:i,namespace:a,onError:n=s.g,...o}){return function({messages:e,namespace:t,...r},i){return e=e["!"],t=(0,s.r)(t,"!"),(0,s.e)({...r,messages:e,namespace:t})}({...o,onError:n,cache:e,formatters:t,getMessageFallback:r,messages:{"!":i},namespace:a?`!.${a}`:"!"},"!")}({...e,namespace:t})}),o=(0,i.cache)(async function(e){let t,r;return"string"==typeof e?t=e:e&&(r=e.locale,t=e.namespace),n(await (0,a.A)(r),t)})},70439:(e,t,r)=>{"use strict";r.d(t,{Separator:()=>i});let i=(0,r(29037).registerClientReference)(function(){throw Error("Attempted to call Separator() from the server but Separator is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"/Users/<USER>/My-Project/fluxkrea_test/components/ui/separator.tsx","Separator")},71803:(e,t,r)=>{"use strict";r.r(t),r.d(t,{$$RSC_SERVER_ACTION_0:()=>h,default:()=>x});var i=r(34411),a=r(72784);r(97468);var s=r(78054),n=r(92132),o=r(98194),l=r(1647),c=r(70439),d=r(34414);function u({...e}){return(0,i.jsxs)("div",{className:"space-y-6",children:[e.crumb?.items&&(0,i.jsx)(o.A,{items:e.crumb.items}),(0,i.jsxs)("div",{children:[(0,i.jsx)("h3",{className:"text-lg font-medium",children:e.title}),(0,i.jsx)("p",{className:"text-sm text-muted-foreground",children:e.description})]}),e.tip&&(0,i.jsx)("p",{className:"text-sm text-muted-foreground",children:e.tip.description||e.tip.title}),e.toolbar&&(0,i.jsx)(d.A,{items:e.toolbar.items}),(0,i.jsx)(c.Separator,{}),(0,i.jsx)(l.default,{...e})]})}var p=r(4961),m=r(29452),f=r(68791),v=r(60736);let h=async function(e,t){let{user_uuid:r}=t;if(!r)throw Error("no auth");let i=e.get("title");if(!i||!i.trim())throw Error("invalid params");let a={user_uuid:r,api_key:`sk-${(0,m.f1)(32)}`,title:i,created_at:(0,p.iq)(),status:s.ai.Created};try{return await (0,s.N1)(a),{status:"success",message:"apikey created",redirect_url:"/api-keys"}}catch(e){throw console.error(e),Error("create api key failed: "+e.message)}};async function x(){let e=await (0,f.A)(),t=await (0,v.TG)();if(!t)return(0,i.jsx)(n.A,{message:"no auth"});let r={title:e("api_keys.create_api_key"),crumb:{items:[{title:e("api_keys.title"),url:"/api-keys"},{title:e("api_keys.create_api_key"),is_active:!0}]},fields:[{title:e("api_keys.form.name"),name:"title",type:"text",placeholder:e("api_keys.form.name_placeholder"),validation:{required:!0}}],passby:{user_uuid:t},submit:{button:{title:e("api_keys.form.submit"),icon:"RiCheckLine"},handler:(0,a.A)(h,"604ce8d80bdd01aed835850ccf14913b8e23190012",null)}};return(0,i.jsx)(u,{...r})}},74075:e=>{"use strict";e.exports=require("zlib")},77598:e=>{"use strict";e.exports=require("node:crypto")},78054:(e,t,r)=>{"use strict";r.d(t,{N1:()=>s,ai:()=>a,ox:()=>n,zz:()=>o});var i=r(4995),a=function(e){return e.Created="created",e.Deleted="deleted",e}({});async function s(e){let t=(0,i.A)(),{data:r,error:a}=await t.from("apikeys").insert(e);if(a)throw a;return r}async function n(e,t=1,r=50){let a=(t-1)*r,s=(0,i.A)(),{data:o,error:l}=await s.from("apikeys").select("*").eq("user_uuid",e).neq("status","deleted").order("created_at",{ascending:!1}).range(a,a+r-1);if(!l)return o}async function o(e){let t=(0,i.A)(),{data:r,error:a}=await t.from("apikeys").select("user_uuid").eq("api_key",e).eq("status","created").limit(1).single();if(!a)return r?.user_uuid}},79428:e=>{"use strict";e.exports=require("buffer")},79551:e=>{"use strict";e.exports=require("url")},80081:(e,t,r)=>{"use strict";r.d(t,{Separator:()=>n});var i=r(2569);r(29068);var a=r(36393),s=r(75673);function n({className:e,orientation:t="horizontal",decorative:r=!0,...n}){return(0,i.jsx)(a.b,{"data-slot":"separator-root",decorative:r,orientation:t,className:(0,s.cn)("bg-border shrink-0 data-[orientation=horizontal]:h-px data-[orientation=horizontal]:w-full data-[orientation=vertical]:h-full data-[orientation=vertical]:w-px",e),...n})}},81630:e=>{"use strict";e.exports=require("http")},91357:(e,t,r)=>{"use strict";r.d(t,{A:()=>i});let i=(0,r(26826).A)("ChevronRight",[["path",{d:"m9 18 6-6-6-6",key:"mthhwq"}]])},91645:e=>{"use strict";e.exports=require("net")},92132:(e,t,r)=>{"use strict";r.d(t,{A:()=>a});var i=r(34411);function a({message:e}){return(0,i.jsx)("div",{className:"flex flex-col items-center justify-center w-full h-[50vh]",children:(0,i.jsx)("p",{children:e})})}},92172:(e,t,r)=>{"use strict";r.d(t,{$:()=>l});var i=r(34411);r(37582);var a=r(20392),s=r(38e3),n=r(25039);let o=(0,s.F)("inline-flex items-center justify-center gap-2 whitespace-nowrap rounded-lg text-sm font-medium transition-all duration-200 disabled:pointer-events-none disabled:opacity-50 [&_svg]:pointer-events-none [&_svg:not([class*='size-'])]:size-4 shrink-0 [&_svg]:shrink-0 outline-none focus-visible:border-ring focus-visible:ring-ring/50 focus-visible:ring-[3px] aria-invalid:ring-destructive/20 dark:aria-invalid:ring-destructive/40 aria-invalid:border-destructive",{variants:{variant:{default:"bg-gradient-to-r from-primary to-accent text-primary-foreground shadow-md hover:shadow-lg hover:scale-[1.02] active:scale-[0.98]",destructive:"bg-destructive text-destructive-foreground shadow-md hover:bg-destructive/90 hover:shadow-lg hover:scale-[1.02] active:scale-[0.98] focus-visible:ring-destructive/20 dark:focus-visible:ring-destructive/40",outline:"border-2 border-primary/20 bg-background/50 backdrop-blur-sm shadow-sm hover:bg-accent/50 hover:text-accent-foreground hover:border-primary/40 hover:shadow-md hover:scale-[1.02] active:scale-[0.98] dark:bg-input/30 dark:border-input dark:hover:bg-input/50",secondary:"bg-gradient-to-r from-secondary to-muted text-secondary-foreground shadow-sm hover:shadow-md hover:scale-[1.02] active:scale-[0.98]",ghost:"hover:bg-accent/50 hover:text-accent-foreground hover:scale-[1.02] active:scale-[0.98] dark:hover:bg-accent/30",link:"text-primary underline-offset-4 hover:underline hover:text-accent"},size:{default:"h-10 px-6 py-2 has-[>svg]:px-4",sm:"h-8 rounded-lg gap-1.5 px-4 has-[>svg]:px-3",lg:"h-12 rounded-lg px-8 has-[>svg]:px-6 text-base",icon:"size-10 rounded-full"}},defaultVariants:{variant:"default",size:"default"}});function l({className:e,variant:t,size:r,asChild:s=!1,...l}){let c=s?a.DX:"button";return(0,i.jsx)(c,{"data-slot":"button",className:(0,n.cn)(o({variant:t,size:r,className:e})),...l})}},92276:(e,t,r)=>{"use strict";r.r(t),r.d(t,{"00880263a376180560adeff1fdd2511a7543726d4a":()=>i.T,"604ce8d80bdd01aed835850ccf14913b8e23190012":()=>a.$$RSC_SERVER_ACTION_0});var i=r(39201),a=r(71803)},94735:e=>{"use strict";e.exports=require("events")},98194:(e,t,r)=>{"use strict";r.d(t,{A:()=>o});var i=r(34411),a=r(91357),s=r(10050),n=r.n(s);function o({items:e}){return(0,i.jsx)("nav",{className:"flex items-center text-sm text-muted-foreground",children:e.map((e,t)=>{let r=e.is_active;return(0,i.jsxs)("div",{className:"flex items-center",children:[(0,i.jsx)(n(),{href:e.url||"",className:`hover:text-foreground transition-colors ${r?"text-primary font-medium hover:text-primary":""}`,children:e.title}),!r&&(0,i.jsx)(a.A,{className:"h-4 w-4 mx-2 text-muted-foreground/40"})]},t)})})}}};var t=require("../../../../../../webpack-runtime.js");t.C(e);var r=e=>t(t.s=e),i=t.X(0,[5673,7711,8734,5327,4457,7416,4626,2041,2303,4829,3162,3412,1518,4524,2794,7862,4452,995],()=>r(39838));module.exports=i})();