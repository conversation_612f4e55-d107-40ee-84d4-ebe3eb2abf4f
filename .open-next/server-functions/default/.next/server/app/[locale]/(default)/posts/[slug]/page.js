(()=>{var e={};e.id=9482,e.ids=[9482],e.modules={3295:e=>{"use strict";e.exports=require("next/dist/server/app-render/after-task-async-storage.external.js")},4995:(e,t,r)=>{"use strict";r.d(t,{A:()=>n});var s=r(5327);function n(){let e=process.env.SUPABASE_URL||"",t=process.env.SUPABASE_ANON_KEY||"";if(process.env.SUPABASE_SERVICE_ROLE_KEY&&(t=process.env.SUPABASE_SERVICE_ROLE_KEY),!e||!t)throw Error("Supabase URL or key is not set");return(0,s.UU)(e,t)}},10846:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},11997:e=>{"use strict";e.exports=require("punycode")},15691:(e,t,r)=>{"use strict";r.d(t,{St:()=>l,W5:()=>o,aV:()=>a,gg:()=>i,gx:()=>p,j7:()=>c,nz:()=>n,zX:()=>u});var s=r(4995),n=function(e){return e.Created="created",e.Deleted="deleted",e.Online="online",e.Offline="offline",e}({});async function o(e){let t=(0,s.A)(),{data:r,error:n}=await t.from("posts").insert(e);if(n)throw n;return r}async function i(e,t){let r=(0,s.A)(),{data:n,error:o}=await r.from("posts").update(t).eq("uuid",e);if(o)throw o;return n}async function a(e){let t=(0,s.A)(),{data:r,error:n}=await t.from("posts").select("*").eq("uuid",e).limit(1).single();if(!n)return r}async function l(e,t){let r=(0,s.A)(),{data:n,error:o}=await r.from("posts").select("*").eq("slug",e).eq("locale",t).limit(1).single();if(!o)return n}async function u(e=1,t=50){let r=(0,s.A)(),{data:n,error:o}=await r.from("posts").select("*").order("created_at",{ascending:!1}).range((e-1)*t,e*t-1);return o?[]:n}async function c(e,t=1,r=50){let n=(0,s.A)(),{data:o,error:i}=await n.from("posts").select("*").eq("locale",e).eq("status","online").order("created_at",{ascending:!1}).range((t-1)*r,t*r-1);return i?[]:o}async function p(){let e=(0,s.A)(),{data:t,error:r}=await e.from("posts").select("count",{count:"exact"});if(!r)return t[0].count}},19121:e=>{"use strict";e.exports=require("next/dist/server/app-render/action-async-storage.external.js")},27910:e=>{"use strict";e.exports=require("stream")},29274:(e,t,r)=>{"use strict";r.r(t),r.d(t,{GlobalError:()=>i.a,__next_app__:()=>p,pages:()=>c,routeModule:()=>d,tree:()=>u});var s=r(43533),n=r(91514),o=r(17220),i=r.n(o),a=r(62931),l={};for(let e in a)0>["default","tree","pages","GlobalError","__next_app__","routeModule"].indexOf(e)&&(l[e]=()=>a[e]);r.d(t,l);let u={children:["",{children:["[locale]",{children:["(default)",{children:["posts",{children:["[slug]",{children:["__PAGE__",{},{page:[()=>Promise.resolve().then(r.bind(r,55781)),"/Users/<USER>/My-Project/fluxkrea_test/app/[locale]/(default)/posts/[slug]/page.tsx"]}]},{}]},{}]},{layout:[()=>Promise.resolve().then(r.bind(r,17890)),"/Users/<USER>/My-Project/fluxkrea_test/app/[locale]/(default)/layout.tsx"]}]},{layout:[()=>Promise.resolve().then(r.bind(r,83581)),"/Users/<USER>/My-Project/fluxkrea_test/app/[locale]/layout.tsx"]}]},{"not-found":[()=>Promise.resolve().then(r.t.bind(r,53488,23)),"next/dist/client/components/not-found-error"],forbidden:[()=>Promise.resolve().then(r.t.bind(r,99017,23)),"next/dist/client/components/forbidden-error"],unauthorized:[()=>Promise.resolve().then(r.t.bind(r,90934,23)),"next/dist/client/components/unauthorized-error"]}]}.children,c=["/Users/<USER>/My-Project/fluxkrea_test/app/[locale]/(default)/posts/[slug]/page.tsx"],p={require:r,loadChunk:()=>Promise.resolve()},d=new s.AppPageRouteModule({definition:{kind:n.RouteKind.APP_PAGE,page:"/[locale]/(default)/posts/[slug]/page",pathname:"/[locale]/posts/[slug]",bundlePath:"",filename:"",appPaths:[]},userland:{loaderTree:u}})},29294:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-async-storage.external.js")},33873:e=>{"use strict";e.exports=require("path")},34631:e=>{"use strict";e.exports=require("tls")},45590:()=>{},51517:(e,t,r)=>{Promise.resolve().then(r.bind(r,61413))},52367:()=>{},55511:e=>{"use strict";e.exports=require("crypto")},55591:e=>{"use strict";e.exports=require("https")},55781:(e,t,r)=>{"use strict";r.r(t),r.d(t,{default:()=>l,generateMetadata:()=>a});var s=r(34411),n=r(15691),o=r(61413),i=r(92132);async function a({params:e}){let{locale:t,slug:r}=await e,s=await (0,n.St)(r,t),o=`http://localhost:3000/posts/${r}`;return"en"!==t&&(o=`http://localhost:3000/${t}/posts/${r}`),{title:s?.title,description:s?.description,alternates:{canonical:o}}}async function l({params:e}){let{locale:t,slug:r}=await e,a=await (0,n.St)(r,t);return a&&a.status===n.nz.Online?(0,s.jsx)(o.default,{post:a}):(0,s.jsx)(i.A,{message:"Post not found"})}},63033:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-unit-async-storage.external.js")},74075:e=>{"use strict";e.exports=require("zlib")},79133:(e,t,r)=>{Promise.resolve().then(r.bind(r,62090))},79428:e=>{"use strict";e.exports=require("buffer")},79551:e=>{"use strict";e.exports=require("url")},81630:e=>{"use strict";e.exports=require("http")},91645:e=>{"use strict";e.exports=require("net")},92132:(e,t,r)=>{"use strict";r.d(t,{A:()=>n});var s=r(34411);function n({message:e}){return(0,s.jsx)("div",{className:"flex flex-col items-center justify-center w-full h-[50vh]",children:(0,s.jsx)("p",{children:e})})}},94735:e=>{"use strict";e.exports=require("events")}};var t=require("../../../../../webpack-runtime.js");t.C(e);var r=e=>t(t.s=e),s=t.X(0,[5673,7711,8734,5327,4457,7416,4626,2303,4829,3162,2794,7862,4452,4100],()=>r(29274));module.exports=s})();