(()=>{var e={};e.id=1689,e.ids=[1689],e.modules={3295:e=>{"use strict";e.exports=require("next/dist/server/app-render/after-task-async-storage.external.js")},10846:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},18586:(e,r,t)=>{"use strict";t.d(r,{BT:()=>l,Wu:()=>c,X9:()=>d,ZB:()=>i,Zp:()=>o,aR:()=>a,wL:()=>u});var n=t(34411);t(37582);var s=t(25039);function o({className:e,...r}){return(0,n.jsx)("div",{"data-slot":"card",className:(0,s.cn)("bg-card text-card-foreground flex flex-col gap-6 rounded-2xl border border-border/50 p-6 shadow-lg backdrop-blur-sm transition-all duration-200 hover:shadow-xl hover:scale-[1.01]",e),...r})}function a({className:e,...r}){return(0,n.jsx)("div",{"data-slot":"card-header",className:(0,s.cn)("@container/card-header grid auto-rows-min grid-rows-[auto_auto] items-start gap-2 has-data-[slot=card-action]:grid-cols-[1fr_auto] [.border-b]:pb-6",e),...r})}function i({className:e,...r}){return(0,n.jsx)("div",{"data-slot":"card-title",className:(0,s.cn)("leading-none font-semibold",e),...r})}function l({className:e,...r}){return(0,n.jsx)("div",{"data-slot":"card-description",className:(0,s.cn)("text-muted-foreground text-sm",e),...r})}function d({className:e,...r}){return(0,n.jsx)("div",{"data-slot":"card-action",className:(0,s.cn)("col-start-2 row-span-2 row-start-1 self-start justify-self-end",e),...r})}function c({className:e,...r}){return(0,n.jsx)("div",{"data-slot":"card-content",className:(0,s.cn)("px-6",e),...r})}function u({className:e,...r}){return(0,n.jsx)("div",{"data-slot":"card-footer",className:(0,s.cn)("flex items-center px-6 [.border-t]:pt-6",e),...r})}},19121:e=>{"use strict";e.exports=require("next/dist/server/app-render/action-async-storage.external.js")},20392:(e,r,t)=>{"use strict";t.d(r,{DX:()=>a});var n=t(37582);function s(e,r){if("function"==typeof e)return e(r);null!=e&&(e.current=r)}var o=t(34411),a=function(e){let r=function(e){let r=n.forwardRef((e,r)=>{let{children:t,...o}=e;if(n.isValidElement(t)){var a;let e,i;let l=(a=t,(i=(e=Object.getOwnPropertyDescriptor(a.props,"ref")?.get)&&"isReactWarning"in e&&e.isReactWarning)?a.ref:(i=(e=Object.getOwnPropertyDescriptor(a,"ref")?.get)&&"isReactWarning"in e&&e.isReactWarning)?a.props.ref:a.props.ref||a.ref),d=function(e,r){let t={...r};for(let n in r){let s=e[n],o=r[n];/^on[A-Z]/.test(n)?s&&o?t[n]=(...e)=>{let r=o(...e);return s(...e),r}:s&&(t[n]=s):"style"===n?t[n]={...s,...o}:"className"===n&&(t[n]=[s,o].filter(Boolean).join(" "))}return{...e,...t}}(o,t.props);return t.type!==n.Fragment&&(d.ref=r?function(...e){return r=>{let t=!1,n=e.map(e=>{let n=s(e,r);return t||"function"!=typeof n||(t=!0),n});if(t)return()=>{for(let r=0;r<n.length;r++){let t=n[r];"function"==typeof t?t():s(e[r],null)}}}}(r,l):l),n.cloneElement(t,d)}return n.Children.count(t)>1?n.Children.only(null):null});return r.displayName=`${e}.SlotClone`,r}(e),t=n.forwardRef((e,t)=>{let{children:s,...a}=e,i=n.Children.toArray(s),d=i.find(l);if(d){let e=d.props.children,s=i.map(r=>r!==d?r:n.Children.count(e)>1?n.Children.only(null):n.isValidElement(e)?e.props.children:null);return(0,o.jsx)(r,{...a,ref:t,children:n.isValidElement(e)?n.cloneElement(e,void 0,s):null})}return(0,o.jsx)(r,{...a,ref:t,children:s})});return t.displayName=`${e}.Slot`,t}("Slot"),i=Symbol("radix.slottable");function l(e){return n.isValidElement(e)&&"function"==typeof e.type&&"__radixId"in e.type&&e.type.__radixId===i}},29294:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-async-storage.external.js")},30711:(e,r,t)=>{Promise.resolve().then(t.bind(t,92227)),Promise.resolve().then(t.bind(t,20518))},33873:e=>{"use strict";e.exports=require("path")},36287:(e,r,t)=>{Promise.resolve().then(t.bind(t,57595)),Promise.resolve().then(t.bind(t,37032))},38e3:(e,r,t)=>{"use strict";t.d(r,{F:()=>a});var n=t(1832);let s=e=>"boolean"==typeof e?`${e}`:0===e?"0":e,o=n.$,a=(e,r)=>t=>{var n;if((null==r?void 0:r.variants)==null)return o(e,null==t?void 0:t.class,null==t?void 0:t.className);let{variants:a,defaultVariants:i}=r,l=Object.keys(a).map(e=>{let r=null==t?void 0:t[e],n=null==i?void 0:i[e];if(null===r)return null;let o=s(r)||s(n);return a[e][o]}),d=t&&Object.entries(t).reduce((e,r)=>{let[t,n]=r;return void 0===n||(e[t]=n),e},{});return o(e,l,null==r?void 0:null===(n=r.compoundVariants)||void 0===n?void 0:n.reduce((e,r)=>{let{class:t,className:n,...s}=r;return Object.entries(s).every(e=>{let[r,t]=e;return Array.isArray(t)?t.includes({...i,...d}[r]):({...i,...d})[r]===t})?[...e,t,n]:e},[]),null==t?void 0:t.class,null==t?void 0:t.className)}},58243:(e,r,t)=>{"use strict";t.d(r,{E:()=>l});var n=t(34411);t(37582);var s=t(20392),o=t(38e3),a=t(25039);let i=(0,o.F)("inline-flex items-center justify-center rounded-md border px-2 py-0.5 text-xs font-medium w-fit whitespace-nowrap shrink-0 [&>svg]:size-3 gap-1 [&>svg]:pointer-events-none focus-visible:border-ring focus-visible:ring-ring/50 focus-visible:ring-[3px] aria-invalid:ring-destructive/20 dark:aria-invalid:ring-destructive/40 aria-invalid:border-destructive transition-[color,box-shadow] overflow-hidden",{variants:{variant:{default:"border-transparent bg-primary text-primary-foreground [a&]:hover:bg-primary/90",secondary:"border-transparent bg-secondary text-secondary-foreground [a&]:hover:bg-secondary/90",destructive:"border-transparent bg-destructive text-white [a&]:hover:bg-destructive/90 focus-visible:ring-destructive/20 dark:focus-visible:ring-destructive/40 dark:bg-destructive/60",outline:"text-foreground [a&]:hover:bg-accent [a&]:hover:text-accent-foreground"}},defaultVariants:{variant:"default"}});function l({className:e,variant:r,asChild:t=!1,...o}){let l=t?s.DX:"span";return(0,n.jsx)(l,{"data-slot":"badge",className:(0,a.cn)(i({variant:r}),e),...o})}},63033:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-unit-async-storage.external.js")},65634:(e,r,t)=>{"use strict";t.r(r),t.d(r,{GlobalError:()=>a.a,__next_app__:()=>u,pages:()=>c,routeModule:()=>p,tree:()=>d});var n=t(43533),s=t(91514),o=t(17220),a=t.n(o),i=t(62931),l={};for(let e in i)0>["default","tree","pages","GlobalError","__next_app__","routeModule"].indexOf(e)&&(l[e]=()=>i[e]);t.d(r,l);let d={children:["",{children:["[locale]",{children:["(default)",{children:["components-demo",{children:["__PAGE__",{},{page:[()=>Promise.resolve().then(t.bind(t,74445)),"/Users/<USER>/My-Project/fluxkrea_test/app/[locale]/(default)/components-demo/page.tsx"]}]},{}]},{layout:[()=>Promise.resolve().then(t.bind(t,17890)),"/Users/<USER>/My-Project/fluxkrea_test/app/[locale]/(default)/layout.tsx"]}]},{layout:[()=>Promise.resolve().then(t.bind(t,83581)),"/Users/<USER>/My-Project/fluxkrea_test/app/[locale]/layout.tsx"]}]},{"not-found":[()=>Promise.resolve().then(t.t.bind(t,53488,23)),"next/dist/client/components/not-found-error"],forbidden:[()=>Promise.resolve().then(t.t.bind(t,99017,23)),"next/dist/client/components/forbidden-error"],unauthorized:[()=>Promise.resolve().then(t.t.bind(t,90934,23)),"next/dist/client/components/unauthorized-error"]}]}.children,c=["/Users/<USER>/My-Project/fluxkrea_test/app/[locale]/(default)/components-demo/page.tsx"],u={require:t,loadChunk:()=>Promise.resolve()},p=new n.AppPageRouteModule({definition:{kind:s.RouteKind.APP_PAGE,page:"/[locale]/(default)/components-demo/page",pathname:"/[locale]/components-demo",bundlePath:"",filename:"",appPaths:[]},userland:{loaderTree:d}})},74445:(e,r,t)=>{"use strict";t.r(r),t.d(r,{default:()=>d});var n=t(34411),s=t(92172),o=t(18586),a=t(58243),i=t(78933),l=t(57595);function d(){return(0,n.jsxs)("div",{className:"container mx-auto py-16 px-4",children:[(0,n.jsxs)("div",{className:"text-center mb-12",children:[(0,n.jsx)(a.E,{variant:"outline",className:"mb-4",children:"Components Demo"}),(0,n.jsx)("h1",{className:"text-4xl font-bold mb-4",children:"ShipAny Block Components Showcase"}),(0,n.jsx)("p",{className:"text-xl text-muted-foreground max-w-3xl mx-auto",children:"Explore all the available block components in the ShipAny template. Each category contains interactive examples with different configurations and use cases."})]}),(0,n.jsx)("div",{className:"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6",children:[{title:"Layout Components",description:"Header, Footer, Navigation and Breadcrumb components",href:"/components-demo/layout",icon:"RiLayoutLine",components:["Header","Footer","Crumb"],color:"bg-blue-50 border-blue-200 text-blue-800"},{title:"Content Display",description:"Hero sections, features, showcases and content blocks",href:"/components-demo/content",icon:"RiArticleLine",components:["Hero","Feature","Feature1","Feature2","Feature3","Showcase","Showcase1","Blog","Testimonial","Stats","Branding"],color:"bg-green-50 border-green-200 text-green-800"},{title:"Interactive Components",description:"CTAs, FAQs, Pricing tables, Forms and Tables",href:"/components-demo/interactive",icon:"RiCursorLine",components:["CTA","FAQ","Pricing","Form","Table"],color:"bg-purple-50 border-purple-200 text-purple-800"},{title:"Data Visualization",description:"Data cards and charts for displaying metrics",href:"/components-demo/data",icon:"RiBarChartLine",components:["Data-cards","Data-charts"],color:"bg-orange-50 border-orange-200 text-orange-800"},{title:"Editors",description:"Rich text and Markdown editors",href:"/components-demo/editors",icon:"RiEditLine",components:["Editor","MDEditor"],color:"bg-red-50 border-red-200 text-red-800"},{title:"Utility Components",description:"Toolbars, empty states and other utilities",href:"/components-demo/tools",icon:"RiToolsLine",components:["Toolbar","Empty"],color:"bg-gray-50 border-gray-200 text-gray-800"},{title:"Blog Detail",description:"Detailed blog post view with content rendering",href:"/components-demo/blog-detail",icon:"RiFileTextLine",components:["Blog-detail"],color:"bg-indigo-50 border-indigo-200 text-indigo-800"}].map((e,r)=>(0,n.jsxs)(o.Zp,{className:"hover:shadow-lg transition-shadow duration-300",children:[(0,n.jsxs)(o.aR,{children:[(0,n.jsxs)("div",{className:"flex items-center gap-3 mb-2",children:[(0,n.jsx)("div",{className:"p-2 rounded-lg bg-primary/10",children:(0,n.jsx)(l.default,{name:e.icon,className:"w-6 h-6 text-primary"})}),(0,n.jsx)(o.ZB,{className:"text-xl",children:e.title})]}),(0,n.jsx)(o.BT,{className:"text-base",children:e.description})]}),(0,n.jsxs)(o.Wu,{children:[(0,n.jsxs)("div",{className:"mb-4",children:[(0,n.jsxs)("p",{className:"text-sm font-medium text-muted-foreground mb-2",children:["Components (",e.components.length,"):"]}),(0,n.jsx)("div",{className:"flex flex-wrap gap-1",children:e.components.map((r,t)=>(0,n.jsx)(a.E,{variant:"secondary",className:`text-xs ${e.color}`,children:r},t))})]}),(0,n.jsx)(i.N_,{href:e.href,children:(0,n.jsxs)(s.$,{className:"w-full",variant:"outline",children:["View Examples",(0,n.jsx)(l.default,{name:"RiArrowRightLine",className:"w-4 h-4 ml-2"})]})})]})]},r))}),(0,n.jsx)("div",{className:"mt-16 text-center",children:(0,n.jsxs)(o.Zp,{className:"max-w-2xl mx-auto",children:[(0,n.jsx)(o.aR,{children:(0,n.jsxs)(o.ZB,{className:"flex items-center justify-center gap-2",children:[(0,n.jsx)(l.default,{name:"RiInformationLine",className:"w-5 h-5"}),"About These Components"]})}),(0,n.jsxs)(o.Wu,{className:"text-left space-y-3",children:[(0,n.jsx)("p",{className:"text-muted-foreground",children:"These block components are the building blocks of the ShipAny template. Each component is designed to be:"}),(0,n.jsxs)("ul",{className:"list-disc list-inside space-y-1 text-muted-foreground",children:[(0,n.jsxs)("li",{children:[(0,n.jsx)("strong",{children:"Reusable:"})," Can be used across different pages and contexts"]}),(0,n.jsxs)("li",{children:[(0,n.jsx)("strong",{children:"Customizable:"})," Supports various props and configurations"]}),(0,n.jsxs)("li",{children:[(0,n.jsx)("strong",{children:"Responsive:"})," Works seamlessly across all device sizes"]}),(0,n.jsxs)("li",{children:[(0,n.jsx)("strong",{children:"Accessible:"})," Built with accessibility best practices"]}),(0,n.jsxs)("li",{children:[(0,n.jsx)("strong",{children:"Type-safe:"})," Full TypeScript support with proper interfaces"]})]}),(0,n.jsx)("div",{className:"pt-4 border-t",children:(0,n.jsxs)(i.N_,{href:"/",className:"inline-flex items-center text-primary hover:underline",children:[(0,n.jsx)(l.default,{name:"RiArrowLeftLine",className:"w-4 h-4 mr-1"}),"Back to Home"]})})]})]})})]})}},92172:(e,r,t)=>{"use strict";t.d(r,{$:()=>l});var n=t(34411);t(37582);var s=t(20392),o=t(38e3),a=t(25039);let i=(0,o.F)("inline-flex items-center justify-center gap-2 whitespace-nowrap rounded-lg text-sm font-medium transition-all duration-200 disabled:pointer-events-none disabled:opacity-50 [&_svg]:pointer-events-none [&_svg:not([class*='size-'])]:size-4 shrink-0 [&_svg]:shrink-0 outline-none focus-visible:border-ring focus-visible:ring-ring/50 focus-visible:ring-[3px] aria-invalid:ring-destructive/20 dark:aria-invalid:ring-destructive/40 aria-invalid:border-destructive",{variants:{variant:{default:"bg-gradient-to-r from-primary to-accent text-primary-foreground shadow-md hover:shadow-lg hover:scale-[1.02] active:scale-[0.98]",destructive:"bg-destructive text-destructive-foreground shadow-md hover:bg-destructive/90 hover:shadow-lg hover:scale-[1.02] active:scale-[0.98] focus-visible:ring-destructive/20 dark:focus-visible:ring-destructive/40",outline:"border-2 border-primary/20 bg-background/50 backdrop-blur-sm shadow-sm hover:bg-accent/50 hover:text-accent-foreground hover:border-primary/40 hover:shadow-md hover:scale-[1.02] active:scale-[0.98] dark:bg-input/30 dark:border-input dark:hover:bg-input/50",secondary:"bg-gradient-to-r from-secondary to-muted text-secondary-foreground shadow-sm hover:shadow-md hover:scale-[1.02] active:scale-[0.98]",ghost:"hover:bg-accent/50 hover:text-accent-foreground hover:scale-[1.02] active:scale-[0.98] dark:hover:bg-accent/30",link:"text-primary underline-offset-4 hover:underline hover:text-accent"},size:{default:"h-10 px-6 py-2 has-[>svg]:px-4",sm:"h-8 rounded-lg gap-1.5 px-4 has-[>svg]:px-3",lg:"h-12 rounded-lg px-8 has-[>svg]:px-6 text-base",icon:"size-10 rounded-full"}},defaultVariants:{variant:"default",size:"default"}});function l({className:e,variant:r,size:t,asChild:o=!1,...l}){let d=o?s.DX:"button";return(0,n.jsx)(d,{"data-slot":"button",className:(0,a.cn)(i({variant:r,size:t,className:e})),...l})}}};var r=require("../../../../webpack-runtime.js");r.C(e);var t=e=>r(r.s=e),n=r.X(0,[5673,7711,8734,4457,7416,4626,2303,4829,3162,2794,7862,4452],()=>t(65634));module.exports=n})();