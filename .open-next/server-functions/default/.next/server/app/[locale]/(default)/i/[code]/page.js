(()=>{var e={};e.id=9240,e.ids=[9240],e.modules={3295:e=>{"use strict";e.exports=require("next/dist/server/app-render/after-task-async-storage.external.js")},10846:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},19121:e=>{"use strict";e.exports=require("next/dist/server/app-render/action-async-storage.external.js")},20465:(e,r,t)=>{"use strict";t.r(r),t.d(r,{default:()=>a});var s=t(2569);t(99293),t(67534),t(21307),t(29068);var o=t(14059);function a(){return(0,o.useParams)().code,(0,s.jsx)("div",{className:"w-screen h-screen flex items-center justify-center",children:"loading..."})}},26330:(e,r,t)=>{Promise.resolve().then(t.bind(t,20465))},29294:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-async-storage.external.js")},33873:e=>{"use strict";e.exports=require("path")},33943:(e,r,t)=>{"use strict";t.r(r),t.d(r,{default:()=>s});let s=(0,t(29037).registerClientReference)(function(){throw Error("Attempted to call the default export of \"/Users/<USER>/My-Project/fluxkrea_test/app/[locale]/(default)/i/[code]/page.tsx\" from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"/Users/<USER>/My-Project/fluxkrea_test/app/[locale]/(default)/i/[code]/page.tsx","default")},63033:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-unit-async-storage.external.js")},73186:(e,r,t)=>{"use strict";t.r(r),t.d(r,{GlobalError:()=>n.a,__next_app__:()=>c,pages:()=>u,routeModule:()=>p,tree:()=>d});var s=t(43533),o=t(91514),a=t(17220),n=t.n(a),i=t(62931),l={};for(let e in i)0>["default","tree","pages","GlobalError","__next_app__","routeModule"].indexOf(e)&&(l[e]=()=>i[e]);t.d(r,l);let d={children:["",{children:["[locale]",{children:["(default)",{children:["i",{children:["[code]",{children:["__PAGE__",{},{page:[()=>Promise.resolve().then(t.bind(t,33943)),"/Users/<USER>/My-Project/fluxkrea_test/app/[locale]/(default)/i/[code]/page.tsx"]}]},{}]},{}]},{layout:[()=>Promise.resolve().then(t.bind(t,17890)),"/Users/<USER>/My-Project/fluxkrea_test/app/[locale]/(default)/layout.tsx"]}]},{layout:[()=>Promise.resolve().then(t.bind(t,83581)),"/Users/<USER>/My-Project/fluxkrea_test/app/[locale]/layout.tsx"]}]},{"not-found":[()=>Promise.resolve().then(t.t.bind(t,53488,23)),"next/dist/client/components/not-found-error"],forbidden:[()=>Promise.resolve().then(t.t.bind(t,99017,23)),"next/dist/client/components/forbidden-error"],unauthorized:[()=>Promise.resolve().then(t.t.bind(t,90934,23)),"next/dist/client/components/unauthorized-error"]}]}.children,u=["/Users/<USER>/My-Project/fluxkrea_test/app/[locale]/(default)/i/[code]/page.tsx"],c={require:t,loadChunk:()=>Promise.resolve()},p=new s.AppPageRouteModule({definition:{kind:o.RouteKind.APP_PAGE,page:"/[locale]/(default)/i/[code]/page",pathname:"/[locale]/i/[code]",bundlePath:"",filename:"",appPaths:[]},userland:{loaderTree:d}})},79474:(e,r,t)=>{Promise.resolve().then(t.bind(t,33943))}};var r=require("../../../../../webpack-runtime.js");r.C(e);var t=e=>r(r.s=e),s=r.X(0,[5673,7711,8734,4457,7416,4626,2303,4829,3162,2794,7862,4452],()=>t(73186));module.exports=s})();