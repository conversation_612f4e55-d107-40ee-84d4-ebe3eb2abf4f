(()=>{var e={};e.id=3951,e.ids=[3951],e.modules={3295:e=>{"use strict";e.exports=require("next/dist/server/app-render/after-task-async-storage.external.js")},3764:(e,t,r)=>{"use strict";r.d(t,{p:()=>n});var a=r(2569);r(29068);var s=r(75673);function n({className:e,type:t,...r}){return(0,a.jsx)("input",{type:t,"data-slot":"input",className:(0,s.cn)("file:text-foreground placeholder:text-muted-foreground selection:bg-primary selection:text-primary-foreground dark:bg-input/30 border-input flex h-9 w-full min-w-0 rounded-md border bg-transparent px-3 py-1 text-base shadow-xs transition-[color,box-shadow] outline-none file:inline-flex file:h-7 file:border-0 file:bg-transparent file:text-sm file:font-medium disabled:pointer-events-none disabled:cursor-not-allowed disabled:opacity-50 md:text-sm","focus-visible:border-ring focus-visible:ring-ring/50 focus-visible:ring-[3px]","aria-invalid:ring-destructive/20 dark:aria-invalid:ring-destructive/40 aria-invalid:border-destructive",e),...r})}},4573:e=>{"use strict";e.exports=require("node:buffer")},8303:(e,t,r)=>{"use strict";r.d(t,{default:()=>a});let a=(0,r(29037).registerClientReference)(function(){throw Error("Attempted to call the default export of \"/Users/<USER>/My-Project/fluxkrea_test/components/invite/index.tsx\" from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"/Users/<USER>/My-Project/fluxkrea_test/components/invite/index.tsx","default")},10050:(e,t,r)=>{let{createProxy:a}=r(26474);e.exports=a("/Users/<USER>/My-Project/fluxkrea_test/node_modules/.pnpm/next@15.2.3_@babel+core@7.28.0_@opentelemetry+api@1.9.0_react-dom@18.3.1_react@18.3.1__react@18.3.1/node_modules/next/dist/client/app-dir/link.js")},10846:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},11997:e=>{"use strict";e.exports=require("punycode")},14066:(e,t,r)=>{"use strict";r.d(t,{default:()=>i});var a=r(2569),s=r(53467),n=r(39365);function i({text:e,children:t}){return(0,a.jsx)(s.CopyToClipboard,{text:e,onCopy:()=>n.oR.success("Copied"),children:(0,a.jsx)("div",{className:"cursor-pointer",children:t})})}},15062:(e,t,r)=>{"use strict";r.r(t),r.d(t,{default:()=>y});var a=r(34411),s=r(67695);function n(e){return(0,s.k5)({tag:"svg",attr:{viewBox:"0 0 24 24",fill:"currentColor"},child:[{tag:"path",attr:{d:"M19.3034 5.33716C17.9344 4.71103 16.4805 4.2547 14.9629 4C14.7719 4.32899 14.5596 4.77471 14.411 5.12492C12.7969 4.89144 11.1944 4.89144 9.60255 5.12492C9.45397 4.77471 9.2311 4.32899 9.05068 4C7.52251 4.2547 6.06861 4.71103 4.70915 5.33716C1.96053 9.39111 1.21766 13.3495 1.5891 17.2549C3.41443 18.5815 5.17612 19.388 6.90701 19.9187C7.33151 19.3456 7.71356 18.73 8.04255 18.0827C7.41641 17.8492 6.82211 17.5627 6.24904 17.2231C6.39762 17.117 6.5462 17.0003 6.68416 16.8835C10.1438 18.4648 13.8911 18.4648 17.3082 16.8835C17.4568 17.0003 17.5948 17.117 17.7434 17.2231C17.1703 17.5627 16.576 17.8492 15.9499 18.0827C16.2789 18.73 16.6609 19.3456 17.0854 19.9187C18.8152 19.388 20.5875 18.5815 22.4033 17.2549C22.8596 12.7341 21.6806 8.80747 19.3034 5.33716ZM8.5201 14.8459C7.48007 14.8459 6.63107 13.9014 6.63107 12.7447C6.63107 11.5879 7.45884 10.6434 8.5201 10.6434C9.57071 10.6434 10.4303 11.5879 10.4091 12.7447C10.4091 13.9014 9.57071 14.8459 8.5201 14.8459ZM15.4936 14.8459C14.4535 14.8459 13.6034 13.9014 13.6034 12.7447C13.6034 11.5879 14.4323 10.6434 15.4936 10.6434C16.5442 10.6434 17.4038 11.5879 17.3825 12.7447C17.3825 13.9014 16.5548 14.8459 15.4936 14.8459Z"},child:[]}]})(e)}function i(e){return(0,s.k5)({tag:"svg",attr:{viewBox:"0 0 24 24",fill:"currentColor"},child:[{tag:"path",attr:{d:"M12 2C17.5228 2 22 6.47715 22 12C22 13.6169 21.6162 15.1442 20.9348 16.4958C20.8633 16.2175 20.7307 15.9523 20.5374 15.7206L20.4142 15.5858L19 14.1716L17.5858 15.5858L17.469 15.713C16.8069 16.4988 16.8458 17.6743 17.5858 18.4142C18.014 18.8424 18.588 19.0358 19.148 18.9946C17.3323 20.8487 14.8006 22 12 22C6.47715 22 2 17.5228 2 12C2 6.47715 6.47715 2 12 2ZM12 15C10.6199 15 9.37036 15.5592 8.46564 16.4633L8.30009 16.6368L9.24506 17.4961C10.035 17.1825 10.982 17 12 17C12.9049 17 13.7537 17.1442 14.4859 17.3965L14.7549 17.4961L15.6999 16.6368C14.7853 15.6312 13.4664 15 12 15ZM8.5 10C7.67157 10 7 10.6716 7 11.5C7 12.3284 7.67157 13 8.5 13C9.32843 13 10 12.3284 10 11.5C10 10.6716 9.32843 10 8.5 10ZM15.5 10C14.6716 10 14 10.6716 14 11.5C14 12.3284 14.6716 13 15.5 13C16.3284 13 17 12.3284 17 11.5C17 10.6716 16.3284 10 15.5 10Z"},child:[]}]})(e)}var o=r(80310),l=r(40109),c=r(60736),d=r(8303),u=r(10050),p=r.n(u),m=r(15585),f=r(35050),v=r(68791),x=r(22740),b=r.n(x),h=r(79502);async function y(){let e=await (0,v.A)(),t=await (0,c.TG)(),r=await (0,c.qo)(),s="http://localhost:3000/my-invites";t||(0,h.redirect)(`/auth/signin?callbackUrl=${encodeURIComponent(s)}`);let u=await (0,f.pX)(t);u||(0,h.redirect)(`/auth/signin?callbackUrl=${encodeURIComponent(s)}`);let x=await (0,l.BJ)(t);if(x&&0!==x.length||(x=await (0,l.PG)(r)),u.is_affiliate=!0,x&&0!==x.length){let t=!1;for(let e of x)if("premium"===e.product_id){t=!0;break}if(!t&&!u.is_affiliate)return(0,a.jsxs)("div",{className:"text-center flex flex-col items-center justify-center h-full py-16 gap-4",children:[(0,a.jsx)(i,{className:"w-8 h-8"}),(0,a.jsx)("span",{children:e("my_invites.no_affiliates")}),(0,a.jsxs)(p(),{href:"https://discord.gg/HQNnrzjZQS",target:"_blank",className:"flex items-center gap-1 font-semibold text-sm text-primary border border-primary rounded-md px-4 py-2",children:[(0,a.jsx)(n,{className:"text-xl"}),"Discord"]})]})}else if(!u.is_affiliate)return(0,a.jsxs)("div",{className:"text-center flex flex-col items-center justify-center h-full py-16 gap-4",children:[(0,a.jsx)(i,{className:"w-8 h-8"}),(0,a.jsx)("span",{children:e("my_invites.no_orders")})]});let y=await (0,o.Ex)(t),g=await (0,o.A)(t),_=[{name:"created_at",title:e("my_invites.table.invite_time"),callback:e=>b()(e.created_at).format("YYYY-MM-DD HH:mm:ss")},{name:"user",title:e("my_invites.table.invite_user"),callback:e=>(0,a.jsxs)("div",{className:"flex items-center gap-2",children:[e?.user?.avatar_url&&(0,a.jsx)("img",{src:e.user?.avatar_url||"",className:"w-8 h-8 rounded-full"}),(0,a.jsx)("span",{children:e.user?.nickname})]})},{name:"status",title:e("my_invites.table.status"),callback:t=>"pending"===t.status?e("my_invites.table.pending"):e("my_invites.table.completed")},{name:"reward_amount",title:e("my_invites.table.reward_amount"),callback:e=>`$${e.reward_amount/100}`}],j={title:e("my_invites.title"),description:e("my_invites.description"),tip:{description:e("my_invites.my_invite_link")},columns:_,data:y,empty_message:e("my_invites.no_invites")};return(0,a.jsxs)("div",{className:"space-y-8",children:[(0,a.jsx)(d.default,{summary:g}),(0,a.jsx)(m.A,{...j})]})}},15585:(e,t,r)=>{"use strict";r.d(t,{A:()=>p});var a=r(34411),s=r(60050);function n({value:e,options:t,className:r}){return(0,a.jsx)("img",{src:e,alt:e,className:`w-10 h-10 rounded-full ${r}`})}var i=r(58243);function o({value:e,options:t,className:r}){return(0,a.jsx)(i.E,{variant:t?.variant??"secondary",className:r,children:e})}var l=r(22740),c=r.n(l);function d({value:e,options:t,className:r}){return(0,a.jsx)("div",{className:r,children:t?.format?c()(e).format(t?.format):c()(e).fromNow()})}var u=r(16976);function p({columns:e,data:t,emptyMessage:r}){return e||(e=[]),(0,a.jsxs)(s.Table,{className:"w-full",children:[(0,a.jsx)(s.TableHeader,{className:"",children:(0,a.jsx)(s.TableRow,{className:"rounded-md",children:e&&e.map((e,t)=>(0,a.jsx)(s.TableHead,{className:e.className,children:e.title},t))})}),(0,a.jsx)(s.TableBody,{children:t&&t.length>0?t.map((t,r)=>(0,a.jsx)(s.TableRow,{className:"h-16",children:e&&e.map((e,r)=>{let i=t[e.name],l=e.callback?e.callback(t):i,c=l;return"image"===e.type?c=(0,a.jsx)(n,{value:i,options:e.options,className:e.className}):"time"===e.type?c=(0,a.jsx)(d,{value:i,options:e.options,className:e.className}):"label"===e.type?c=(0,a.jsx)(o,{value:i,options:e.options,className:e.className}):"copy"===e.type&&i&&(c=(0,a.jsx)(u.default,{text:i,children:l})),(0,a.jsx)(s.TableCell,{className:e.className,children:c},r)})},r)):(0,a.jsx)(s.TableRow,{className:"",children:(0,a.jsx)(s.TableCell,{colSpan:e.length,children:(0,a.jsx)("div",{className:"flex w-full justify-center items-center py-8 text-muted-foreground",children:(0,a.jsx)("p",{children:r})})})})})]})}},16976:(e,t,r)=>{"use strict";r.d(t,{default:()=>a});let a=(0,r(29037).registerClientReference)(function(){throw Error("Attempted to call the default export of \"/Users/<USER>/My-Project/fluxkrea_test/components/blocks/table/copy.tsx\" from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"/Users/<USER>/My-Project/fluxkrea_test/components/blocks/table/copy.tsx","default")},19121:e=>{"use strict";e.exports=require("next/dist/server/app-render/action-async-storage.external.js")},21208:(e,t,r)=>{Promise.resolve().then(r.bind(r,16976)),Promise.resolve().then(r.bind(r,8303)),Promise.resolve().then(r.bind(r,60050)),Promise.resolve().then(r.bind(r,37032)),Promise.resolve().then(r.t.bind(r,10050,23))},21880:(e,t,r)=>{Promise.resolve().then(r.bind(r,14066)),Promise.resolve().then(r.bind(r,92896)),Promise.resolve().then(r.bind(r,72568)),Promise.resolve().then(r.bind(r,20518)),Promise.resolve().then(r.t.bind(r,59316,23))},26820:(e,t,r)=>{"use strict";r.r(t),r.d(t,{"00880263a376180560adeff1fdd2511a7543726d4a":()=>a.T});var a=r(39201)},27910:e=>{"use strict";e.exports=require("stream")},28432:(e,t,r)=>{"use strict";r.d(t,{j2:()=>f,Y9:()=>u});var a=r(62364),s=r(36157),n=r(72648),i=r(39201),o=r(4961),l=r(29452),c=r(60736);let d=[];d.push((0,s.A)({id:"google-one-tap",name:"google-one-tap",credentials:{credential:{type:"text"}},async authorize(e,t){let r=e.credential,a=await fetch("https://oauth2.googleapis.com/tokeninfo?id_token="+r);if(!a.ok)return console.log("Failed to verify token"),null;let s=await a.json();if(!s)return console.log("invalid payload from token"),null;let{email:n,sub:i,given_name:o,family_name:l,email_verified:c,picture:d}=s;return n?{id:i,name:[o,l].join(" "),email:n,image:d,emailVerified:c?new Date:null}:(console.log("invalid email in payload"),null)}})),process.env.AUTH_GOOGLE_ID&&process.env.AUTH_GOOGLE_SECRET&&d.push((0,n.A)({clientId:process.env.AUTH_GOOGLE_ID,clientSecret:process.env.AUTH_GOOGLE_SECRET,authorization:{params:{prompt:"consent",access_type:"offline",response_type:"code"}}})),d.map(e=>{if("function"!=typeof e)return{id:e.id,name:e.name};{let t=e();return{id:t.id,name:t.name}}}).filter(e=>"google-one-tap"!==e.id);let{handlers:u,signIn:p,signOut:m,auth:f}=(0,a.Ay)({providers:d,pages:{signIn:"/auth/signin"},callbacks:{signIn:async({user:e,account:t,profile:r,email:a,credentials:s})=>!0,redirect:async({url:e,baseUrl:t})=>e.startsWith("/")?`${t}${e}`:new URL(e).origin===t?e:t,session:async({session:e,token:t,user:r})=>(t&&t.user&&t.user&&(e.user=t.user),e),async jwt({token:e,user:t,account:r}){try{if(t&&t.email&&r){let a={uuid:(0,l.YJ)(),email:t.email,nickname:t.name||"",avatar_url:t.image||"",signin_type:r.type,signin_provider:r.provider,signin_openid:r.providerAccountId,created_at:(0,o.iq)(),signin_ip:await (0,i.T)()};try{let t=await (0,c.$c)(a);e.user={uuid:t.uuid,email:t.email,nickname:t.nickname,avatar_url:t.avatar_url,created_at:t.created_at}}catch(e){console.error("save user failed:",e)}}return e}catch(t){return console.error("jwt callback error:",t),e}}}})},29294:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-async-storage.external.js")},31547:(e,t,r)=>{Promise.resolve().then(r.bind(r,35211)),Promise.resolve().then(r.bind(r,20518))},33873:e=>{"use strict";e.exports=require("path")},34631:e=>{"use strict";e.exports=require("tls")},35211:(e,t,r)=>{"use strict";r.d(t,{default:()=>c});var a=r(2569),s=r(92227),n=r(37967),i=r(92622),o=r(75673),l=r(14059);function c({className:e,items:t,...r}){let c=(0,l.usePathname)();return console.log(c),(0,a.jsx)("nav",{className:(0,o.cn)("flex space-x-2 lg:flex-col lg:space-x-0 lg:space-y-1",e),...r,children:t.map((e,t)=>(0,a.jsxs)(n.N_,{href:e.url,className:(0,o.cn)((0,i.r)({variant:"ghost"}),e.is_active||c.includes(e.url)?"bg-muted/50 text-primary hover:bg-muted hover:text-primary":"hover:bg-transparent hover:underline","justify-start"),children:[e.icon&&(0,a.jsx)(s.default,{name:e.icon,className:"w-4 h-4"}),e.title]},t))})}},39201:(e,t,r)=>{"use strict";r.d(t,{T:()=>n});var a=r(72784);r(97468);var s=r(62225);async function n(){let e=await (0,s.b3)();return e.get("cf-connecting-ip")||e.get("x-real-ip")||(e.get("x-forwarded-for")||"127.0.0.1").split(",")[0]}(0,r(78564).D)([n]),(0,a.A)(n,"00880263a376180560adeff1fdd2511a7543726d4a",null)},55511:e=>{"use strict";e.exports=require("crypto")},55591:e=>{"use strict";e.exports=require("https")},55977:(e,t,r)=>{"use strict";r.d(t,{default:()=>a});let a=(0,r(29037).registerClientReference)(function(){throw Error("Attempted to call the default export of \"/Users/<USER>/My-Project/fluxkrea_test/components/console/sidebar/nav.tsx\" from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"/Users/<USER>/My-Project/fluxkrea_test/components/console/sidebar/nav.tsx","default")},57975:e=>{"use strict";e.exports=require("node:util")},58243:(e,t,r)=>{"use strict";r.d(t,{E:()=>l});var a=r(34411);r(37582);var s=r(20392),n=r(38e3),i=r(25039);let o=(0,n.F)("inline-flex items-center justify-center rounded-md border px-2 py-0.5 text-xs font-medium w-fit whitespace-nowrap shrink-0 [&>svg]:size-3 gap-1 [&>svg]:pointer-events-none focus-visible:border-ring focus-visible:ring-ring/50 focus-visible:ring-[3px] aria-invalid:ring-destructive/20 dark:aria-invalid:ring-destructive/40 aria-invalid:border-destructive transition-[color,box-shadow] overflow-hidden",{variants:{variant:{default:"border-transparent bg-primary text-primary-foreground [a&]:hover:bg-primary/90",secondary:"border-transparent bg-secondary text-secondary-foreground [a&]:hover:bg-secondary/90",destructive:"border-transparent bg-destructive text-white [a&]:hover:bg-destructive/90 focus-visible:ring-destructive/20 dark:focus-visible:ring-destructive/40 dark:bg-destructive/60",outline:"text-foreground [a&]:hover:bg-accent [a&]:hover:text-accent-foreground"}},defaultVariants:{variant:"default"}});function l({className:e,variant:t,asChild:r=!1,...n}){let l=r?s.DX:"span";return(0,a.jsx)(l,{"data-slot":"badge",className:(0,i.cn)(o({variant:t}),e),...n})}},60050:(e,t,r)=>{"use strict";r.d(t,{Table:()=>s,TableBody:()=>i,TableCell:()=>c,TableHead:()=>o,TableHeader:()=>n,TableRow:()=>l});var a=r(29037);let s=(0,a.registerClientReference)(function(){throw Error("Attempted to call Table() from the server but Table is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"/Users/<USER>/My-Project/fluxkrea_test/components/ui/table.tsx","Table"),n=(0,a.registerClientReference)(function(){throw Error("Attempted to call TableHeader() from the server but TableHeader is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"/Users/<USER>/My-Project/fluxkrea_test/components/ui/table.tsx","TableHeader"),i=(0,a.registerClientReference)(function(){throw Error("Attempted to call TableBody() from the server but TableBody is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"/Users/<USER>/My-Project/fluxkrea_test/components/ui/table.tsx","TableBody");(0,a.registerClientReference)(function(){throw Error("Attempted to call TableFooter() from the server but TableFooter is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"/Users/<USER>/My-Project/fluxkrea_test/components/ui/table.tsx","TableFooter");let o=(0,a.registerClientReference)(function(){throw Error("Attempted to call TableHead() from the server but TableHead is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"/Users/<USER>/My-Project/fluxkrea_test/components/ui/table.tsx","TableHead"),l=(0,a.registerClientReference)(function(){throw Error("Attempted to call TableRow() from the server but TableRow is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"/Users/<USER>/My-Project/fluxkrea_test/components/ui/table.tsx","TableRow"),c=(0,a.registerClientReference)(function(){throw Error("Attempted to call TableCell() from the server but TableCell is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"/Users/<USER>/My-Project/fluxkrea_test/components/ui/table.tsx","TableCell");(0,a.registerClientReference)(function(){throw Error("Attempted to call TableCaption() from the server but TableCaption is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"/Users/<USER>/My-Project/fluxkrea_test/components/ui/table.tsx","TableCaption")},60736:(e,t,r)=>{"use strict";r.d(t,{$c:()=>c,TG:()=>d,qo:()=>p,ug:()=>m});var a=r(87830),s=r(35050),n=r(28432),i=r(4961),o=r(78054),l=r(62225);async function c(e){try{let t=await (0,s.Gs)(e.email);return t?(e.id=t.id,e.uuid=t.uuid,e.created_at=t.created_at):(await (0,s.HW)(e),await (0,a.d_)({user_uuid:e.uuid||"",trans_type:a.H3.NewUser,credits:a.rN.NewUserGet,expired_at:(0,i.MI)()})),e}catch(e){throw console.log("save user failed: ",e),e}}async function d(){let e="",t=await u();if(t&&t.startsWith("sk-"))return await (0,o.zz)(t)||"";let r=await (0,n.j2)();return r&&r.user&&r.user.uuid&&(e=r.user.uuid),e}async function u(){let e=(await (0,l.b3)()).get("Authorization");return e?e.replace("Bearer ",""):""}async function p(){let e="",t=await (0,n.j2)();return t&&t.user&&t.user.email&&(e=t.user.email),e}async function m(){let e=await d();if(e)return await (0,s.pX)(e)}},63033:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-unit-async-storage.external.js")},65690:(e,t,r)=>{"use strict";r.r(t),r.d(t,{default:()=>c});var a=r(34411),s=r(55977);async function n({children:e,sidebar:t}){return(0,a.jsx)("div",{className:"container md:max-w-7xl py-8 mx-auto",children:(0,a.jsx)("div",{className:"w-full space-y-6 p-4 pb-16 block",children:(0,a.jsxs)("div",{className:"flex flex-col space-y-8 lg:flex-row lg:space-x-12 lg:space-y-0",children:[t?.nav?.items&&(0,a.jsx)("aside",{className:"md:min-w-40 flex-shrink-0",children:(0,a.jsx)(s.default,{items:t.nav?.items})}),(0,a.jsx)("div",{className:"flex-1 lg:max-w-full",children:e})]})})})}var i=r(68791),o=r(60736),l=r(79502);async function c({children:e}){let t=await (0,o.ug)();t&&t.email||(0,l.redirect)("/auth/signin");let r=await (0,i.A)(),s={nav:{items:[{title:r("user.my_orders"),url:"/my-orders",icon:"RiOrderPlayLine",is_active:!1},{title:r("my_credits.title"),url:"/my-credits",icon:"RiBankCardLine",is_active:!1},{title:r("my_invites.title"),url:"/my-invites",icon:"RiMoneyCnyCircleFill",is_active:!1},{title:r("api_keys.title"),url:"/api-keys",icon:"RiKey2Line",is_active:!1}]}};return(0,a.jsx)(n,{sidebar:s,children:e})}},65891:(e,t,r)=>{Promise.resolve().then(r.bind(r,55977)),Promise.resolve().then(r.bind(r,37032))},67695:(e,t,r)=>{"use strict";r.d(t,{k5:()=>d});var a=r(37582),s={color:void 0,size:void 0,className:void 0,style:void 0,attr:void 0},n=a.createContext&&a.createContext(s),i=["attr","size","title"];function o(){return(o=Object.assign?Object.assign.bind():function(e){for(var t=1;t<arguments.length;t++){var r=arguments[t];for(var a in r)Object.prototype.hasOwnProperty.call(r,a)&&(e[a]=r[a])}return e}).apply(this,arguments)}function l(e,t){var r=Object.keys(e);if(Object.getOwnPropertySymbols){var a=Object.getOwnPropertySymbols(e);t&&(a=a.filter(function(t){return Object.getOwnPropertyDescriptor(e,t).enumerable})),r.push.apply(r,a)}return r}function c(e){for(var t=1;t<arguments.length;t++){var r=null!=arguments[t]?arguments[t]:{};t%2?l(Object(r),!0).forEach(function(t){var a,s,n;a=e,s=t,n=r[t],(s=function(e){var t=function(e,t){if("object"!=typeof e||!e)return e;var r=e[Symbol.toPrimitive];if(void 0!==r){var a=r.call(e,t||"default");if("object"!=typeof a)return a;throw TypeError("@@toPrimitive must return a primitive value.")}return("string"===t?String:Number)(e)}(e,"string");return"symbol"==typeof t?t:t+""}(s))in a?Object.defineProperty(a,s,{value:n,enumerable:!0,configurable:!0,writable:!0}):a[s]=n}):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(r)):l(Object(r)).forEach(function(t){Object.defineProperty(e,t,Object.getOwnPropertyDescriptor(r,t))})}return e}function d(e){return t=>a.createElement(u,o({attr:c({},e.attr)},t),function e(t){return t&&t.map((t,r)=>a.createElement(t.tag,c({key:r},t.attr),e(t.child)))}(e.child))}function u(e){var t=t=>{var r,{attr:s,size:n,title:l}=e,d=function(e,t){if(null==e)return{};var r,a,s=function(e,t){if(null==e)return{};var r={};for(var a in e)if(Object.prototype.hasOwnProperty.call(e,a)){if(t.indexOf(a)>=0)continue;r[a]=e[a]}return r}(e,t);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(e);for(a=0;a<n.length;a++)r=n[a],!(t.indexOf(r)>=0)&&Object.prototype.propertyIsEnumerable.call(e,r)&&(s[r]=e[r])}return s}(e,i),u=n||t.size||"1em";return t.className&&(r=t.className),e.className&&(r=(r?r+" ":"")+e.className),a.createElement("svg",o({stroke:"currentColor",fill:"currentColor",strokeWidth:"0"},t.attr,s,d,{className:r,style:c(c({color:e.color||t.color},t.style),e.style),height:u,width:u,xmlns:"http://www.w3.org/2000/svg"}),l&&a.createElement("title",null,l),e.children)};return void 0!==n?a.createElement(n.Consumer,null,e=>t(e)):t(s)}},68064:(e,t,r)=>{"use strict";r.d(t,{BT:()=>l,Wu:()=>d,X9:()=>c,ZB:()=>o,Zp:()=>n,aR:()=>i,wL:()=>u});var a=r(2569);r(29068);var s=r(75673);function n({className:e,...t}){return(0,a.jsx)("div",{"data-slot":"card",className:(0,s.cn)("bg-card text-card-foreground flex flex-col gap-6 rounded-2xl border border-border/50 p-6 shadow-lg backdrop-blur-sm transition-all duration-200 hover:shadow-xl hover:scale-[1.01]",e),...t})}function i({className:e,...t}){return(0,a.jsx)("div",{"data-slot":"card-header",className:(0,s.cn)("@container/card-header grid auto-rows-min grid-rows-[auto_auto] items-start gap-2 has-data-[slot=card-action]:grid-cols-[1fr_auto] [.border-b]:pb-6",e),...t})}function o({className:e,...t}){return(0,a.jsx)("div",{"data-slot":"card-title",className:(0,s.cn)("leading-none font-semibold",e),...t})}function l({className:e,...t}){return(0,a.jsx)("div",{"data-slot":"card-description",className:(0,s.cn)("text-muted-foreground text-sm",e),...t})}function c({className:e,...t}){return(0,a.jsx)("div",{"data-slot":"card-action",className:(0,s.cn)("col-start-2 row-span-2 row-start-1 self-start justify-self-end",e),...t})}function d({className:e,...t}){return(0,a.jsx)("div",{"data-slot":"card-content",className:(0,s.cn)("px-6",e),...t})}function u({className:e,...t}){return(0,a.jsx)("div",{"data-slot":"card-footer",className:(0,s.cn)("flex items-center px-6 [.border-t]:pt-6",e),...t})}},68791:(e,t,r)=>{"use strict";r.d(t,{A:()=>o});var a=r(37582),s=r(93134),n=r(21972),i=(0,a.cache)(function(e,t){return function({_cache:e=(0,n.d)(),_formatters:t=(0,n.b)(e),getMessageFallback:r=n.f,messages:a,namespace:s,onError:i=n.g,...o}){return function({messages:e,namespace:t,...r},a){return e=e["!"],t=(0,n.r)(t,"!"),(0,n.e)({...r,messages:e,namespace:t})}({...o,onError:i,cache:e,formatters:t,getMessageFallback:r,messages:{"!":a},namespace:s?`!.${s}`:"!"},"!")}({...e,namespace:t})}),o=(0,a.cache)(async function(e){let t,r;return"string"==typeof e?t=e:e&&(r=e.locale,t=e.namespace),i(await (0,s.A)(r),t)})},72568:(e,t,r)=>{"use strict";r.d(t,{Table:()=>n,TableBody:()=>o,TableCell:()=>d,TableHead:()=>c,TableHeader:()=>i,TableRow:()=>l});var a=r(2569);r(29068);var s=r(75673);function n({className:e,...t}){return(0,a.jsx)("div",{"data-slot":"table-container",className:"relative w-full overflow-x-auto",children:(0,a.jsx)("table",{"data-slot":"table",className:(0,s.cn)("w-full caption-bottom text-sm",e),...t})})}function i({className:e,...t}){return(0,a.jsx)("thead",{"data-slot":"table-header",className:(0,s.cn)("[&_tr]:border-b",e),...t})}function o({className:e,...t}){return(0,a.jsx)("tbody",{"data-slot":"table-body",className:(0,s.cn)("[&_tr:last-child]:border-0",e),...t})}function l({className:e,...t}){return(0,a.jsx)("tr",{"data-slot":"table-row",className:(0,s.cn)("hover:bg-muted/50 data-[state=selected]:bg-muted border-b transition-colors",e),...t})}function c({className:e,...t}){return(0,a.jsx)("th",{"data-slot":"table-head",className:(0,s.cn)("text-foreground h-10 px-2 text-left align-middle font-medium whitespace-nowrap [&:has([role=checkbox])]:pr-0 [&>[role=checkbox]]:translate-y-[2px]",e),...t})}function d({className:e,...t}){return(0,a.jsx)("td",{"data-slot":"table-cell",className:(0,s.cn)("p-2 align-middle whitespace-nowrap [&:has([role=checkbox])]:pr-0 [&>[role=checkbox]]:translate-y-[2px]",e),...t})}},73874:(e,t,r)=>{"use strict";r.r(t),r.d(t,{GlobalError:()=>i.a,__next_app__:()=>u,pages:()=>d,routeModule:()=>p,tree:()=>c});var a=r(43533),s=r(91514),n=r(17220),i=r.n(n),o=r(62931),l={};for(let e in o)0>["default","tree","pages","GlobalError","__next_app__","routeModule"].indexOf(e)&&(l[e]=()=>o[e]);r.d(t,l);let c={children:["",{children:["[locale]",{children:["(default)",{children:["(console)",{children:["my-invites",{children:["__PAGE__",{},{page:[()=>Promise.resolve().then(r.bind(r,15062)),"/Users/<USER>/My-Project/fluxkrea_test/app/[locale]/(default)/(console)/my-invites/page.tsx"]}]},{}]},{layout:[()=>Promise.resolve().then(r.bind(r,65690)),"/Users/<USER>/My-Project/fluxkrea_test/app/[locale]/(default)/(console)/layout.tsx"]}]},{layout:[()=>Promise.resolve().then(r.bind(r,17890)),"/Users/<USER>/My-Project/fluxkrea_test/app/[locale]/(default)/layout.tsx"]}]},{layout:[()=>Promise.resolve().then(r.bind(r,83581)),"/Users/<USER>/My-Project/fluxkrea_test/app/[locale]/layout.tsx"]}]},{"not-found":[()=>Promise.resolve().then(r.t.bind(r,53488,23)),"next/dist/client/components/not-found-error"],forbidden:[()=>Promise.resolve().then(r.t.bind(r,99017,23)),"next/dist/client/components/forbidden-error"],unauthorized:[()=>Promise.resolve().then(r.t.bind(r,90934,23)),"next/dist/client/components/unauthorized-error"]}]}.children,d=["/Users/<USER>/My-Project/fluxkrea_test/app/[locale]/(default)/(console)/my-invites/page.tsx"],u={require:r,loadChunk:()=>Promise.resolve()},p=new a.AppPageRouteModule({definition:{kind:s.RouteKind.APP_PAGE,page:"/[locale]/(default)/(console)/my-invites/page",pathname:"/[locale]/my-invites",bundlePath:"",filename:"",appPaths:[]},userland:{loaderTree:c}})},74075:e=>{"use strict";e.exports=require("zlib")},77598:e=>{"use strict";e.exports=require("node:crypto")},78054:(e,t,r)=>{"use strict";r.d(t,{N1:()=>n,ai:()=>s,ox:()=>i,zz:()=>o});var a=r(4995),s=function(e){return e.Created="created",e.Deleted="deleted",e}({});async function n(e){let t=(0,a.A)(),{data:r,error:s}=await t.from("apikeys").insert(e);if(s)throw s;return r}async function i(e,t=1,r=50){let s=(t-1)*r,n=(0,a.A)(),{data:o,error:l}=await n.from("apikeys").select("*").eq("user_uuid",e).neq("status","deleted").order("created_at",{ascending:!1}).range(s,s+r-1);if(!l)return o}async function o(e){let t=(0,a.A)(),{data:r,error:s}=await t.from("apikeys").select("user_uuid").eq("api_key",e).eq("status","created").limit(1).single();if(!s)return r?.user_uuid}},79428:e=>{"use strict";e.exports=require("buffer")},79551:e=>{"use strict";e.exports=require("url")},80310:(e,t,r)=>{"use strict";r.d(t,{A:()=>o,Ex:()=>i,N$:()=>l,Rt:()=>n});var a=r(4995),s=r(35050);async function n(e){let t=(0,a.A)(),{data:r,error:s}=await t.from("affiliates").insert({user_uuid:e.user_uuid,invited_by:e.invited_by,created_at:e.created_at,status:e.status,paid_order_no:e.paid_order_no,paid_amount:e.paid_amount,reward_percent:e.reward_percent,reward_amount:e.reward_amount});if(s)throw s;return r}async function i(e,t=1,r=50){let n=(0,a.A)(),{data:o,error:l}=await n.from("affiliates").select("*").eq("invited_by",e).order("created_at",{ascending:!1}).range((t-1)*r,t*r);if(l)return console.error("Error fetching user invites:",l),[];if(!o||0===o.length)return;let c=Array.from(new Set(o.map(e=>e.user_uuid))),d=await (0,s.QZ)(c);return o.map(e=>{let t=d.find(t=>t.uuid===e.user_uuid);return{...e,user:t}})}async function o(e){let t=(0,a.A)(),{data:r,error:s}=await t.from("affiliates").select("*").eq("invited_by",e),n={total_invited:0,total_paid:0,total_reward:0};if(s)return n;let i=new Set,o=new Set;return r.forEach(e=>{i.add(e.user_uuid),e.paid_amount>0&&(o.add(e.user_uuid),n.total_reward+=e.reward_amount)}),n.total_invited=i.size,n.total_paid=o.size,n}async function l(e){let t=(0,a.A)(),{data:r,error:s}=await t.from("affiliates").select("*").eq("paid_order_no",e).single();if(!s)return r}},81630:e=>{"use strict";e.exports=require("http")},91645:e=>{"use strict";e.exports=require("net")},92896:(e,t,r)=>{"use strict";r.d(t,{default:()=>v});var a=r(2569),s=r(68064),n=r(29068),i=r(92622),o=r(53467),l=r(92227),c=r(97946),d=r(3764),u=r(73054);function p({open:e,setOpen:t,username:r,initInviteCode:s,updateInviteCode:o,loading:l}){let p=(0,u.c3)(),[m,f]=(0,n.useState)(s);return(0,a.jsx)(c.lG,{open:e,onOpenChange:t,children:(0,a.jsxs)(c.Cf,{className:"sm:max-w-[425px]",children:[(0,a.jsxs)(c.c7,{children:[(0,a.jsx)(c.L3,{children:p("my_invites.update_invite_code")}),(0,a.jsx)(c.rr,{children:p("my_invites.update_invite_code_tip")})]}),(0,a.jsx)("div",{className:"grid gap-4 py-4",children:(0,a.jsx)("div",{className:"grid grid-cols-1 items-center gap-4",children:(0,a.jsx)(d.p,{placeholder:`${r}`,value:m,onChange:e=>f(e.target.value),className:"w-full"})})}),(0,a.jsx)(c.Es,{children:(0,a.jsx)(i.$,{onClick:()=>o(m),disabled:l,children:p("my_invites.update_invite_button")})})]})})}var m=r(39365),f=r(6637);function v({summary:e}){let t=(0,u.c3)(),[r,c]=(0,n.useState)(!1),{user:d,setUser:v}=(0,f.U)(),[x,b]=(0,n.useState)(!1),h=async function(e){try{if(!(e=e.trim())){m.oR.error("invite code is required");return}b(!0);let t={invite_code:e},r=await fetch("/api/update-invite-code",{method:"POST",headers:{"Content-Type":"application/json"},body:JSON.stringify(t)});if(!r.ok)throw Error("update invite code faild with status "+r.status);let{code:a,message:s,data:n}=await r.json();if(0!==a){m.oR.error(s);return}v(n),m.oR.success("set invite code success"),c(!1)}catch(e){console.log("update invite code failed",e),m.oR.error("set invite code failed")}finally{b(!1)}};return(0,a.jsxs)("div",{className:"flex flex-wrap gap-6",children:[(0,a.jsxs)(s.Zp,{className:"flex-1 p-6",children:[(0,a.jsx)("h2",{className:"text-sm text-gray-500 mb-4",children:t("my_invites.invite_code")}),d&&d.uuid&&(0,a.jsxs)("div",{className:"flex items-center justify-between mb-4",children:[(0,a.jsx)(p,{open:r,setOpen:c,username:d.nickname,initInviteCode:d.invite_code,updateInviteCode:h,loading:x}),(0,a.jsxs)("div",{className:"flex items-center gap-2",children:[(0,a.jsx)("span",{className:"text-3xl font-bold",children:d.invite_code||"NOT SET"}),(0,a.jsx)(l.default,{name:"RiEditLine",className:"text-primary text-xl cursor-pointer",onClick:()=>c(!0)})]}),d.invite_code&&(0,a.jsx)(o.CopyToClipboard,{text:`http://localhost:3000/i/${d?.invite_code}`,onCopy:()=>m.oR.success("copied"),children:(0,a.jsx)(i.$,{size:"sm",children:t("my_invites.copy_invite_link")})})]}),(0,a.jsx)("p",{className:"text-sm text-gray-500",children:t("my_invites.invite_tip")})]}),(0,a.jsxs)(s.Zp,{className:"flex-1 p-6",children:[(0,a.jsx)("div",{className:"flex justify-between items-end mb-8",children:(0,a.jsxs)("div",{children:[(0,a.jsx)("h2",{className:"text-sm text-gray-500 mb-4",children:t("my_invites.invite_balance")}),(0,a.jsxs)("p",{className:"text-4xl font-bold",children:["$",e.total_reward/100]})]})}),(0,a.jsxs)("div",{className:"grid grid-cols-4 gap-4",children:[(0,a.jsxs)("div",{children:[(0,a.jsx)("p",{className:"text-2xl font-bold",children:e.total_invited}),(0,a.jsx)("p",{className:"text-sm text-gray-500",children:t("my_invites.total_invite_count")})]}),(0,a.jsxs)("div",{children:[(0,a.jsx)("p",{className:"text-2xl font-bold",children:e.total_paid}),(0,a.jsx)("p",{className:"text-sm text-gray-500",children:t("my_invites.total_paid_count")})]}),(0,a.jsxs)("div",{children:[(0,a.jsxs)("p",{className:"text-2xl font-bold",children:["$",e.total_reward/100]}),(0,a.jsx)("p",{className:"text-sm text-gray-500",children:t("my_invites.total_award_amount")})]})]})]})]})}},94735:e=>{"use strict";e.exports=require("events")}};var t=require("../../../../../webpack-runtime.js");t.C(e);var r=e=>t(t.s=e),a=t.X(0,[5673,7711,8734,5327,4457,7416,4626,2041,2303,4829,3162,2490,4524,2794,7862,4452],()=>r(73874));module.exports=a})();