"use strict";exports.id=5989,exports.ids=[5989],exports.modules={45989:e=>{e.exports=JSON.parse('{"workspace":{"title":"AI Workspace","subtitle":"Unleash Unlimited Creativity","fullscreen":"Full Screen","start_create":"Start to create","choose_model":"Choose a model and start your creation"},"generator":{"start":"Start","generating":"Generating...","model_selector":"Select Model","prompt_input":"Enter your prompt","prompt_placeholder":"Describe what you want to create...","options_config":"Configuration Options"},"cost":{"estimated":"Estimated cost","credits":"credits","consumed":"Consumed {amount} credits","not_enough":"Not enough credits, need {shortfall} credits","can_afford":"You have sufficient credits"},"status":{"success":"Generation completed","failed":"Failed to generate","pending":"Processing...","running":"Generating...","progress":"Progress: {percent}%"},"actions":{"view":"View","download":"Download","retry":"Retry","cancel":"Cancel","close":"Close"},"models":{"loading":"Loading...","error":"Failed to load","no_models":"No models available","select_model":"Choose a model","model_selector":"AI Model","model_info":"Model Information"},"results":{"text_result":"Generated Text","image_result":"Generated Image {index}","video_result":"Generated Video","no_result":"No results yet","result_ready":"Generation Result","text_description":"The result of text generation will be displayed here, supporting copy and export","image_description":"The result of image generation will be displayed here, supporting preview and download","video_description":"The result of video generation will be displayed here, supporting play and download"},"errors":{"generation_failed":"Generation failed: {detail}","network_error":"Network error, please try again","invalid_input":"Invalid input, please check your prompt","model_unavailable":"Selected model is currently unavailable","insufficient_credits":"Insufficient credits, please recharge"},"tabs":{"text":"TEXT LLM","image":"IMAGE","video":"VIDEO","audio":"AUDIO"},"toolbar":{"minimize":"Minimize","maximize":"Maximize","exit_fullscreen":"Exit Fullscreen","settings":"Settings"},"credits":{"current_balance":"Current Balance","insufficient":"Insufficient Credits","recharge":"Recharge","usage_info":"Usage Information"},"options":{"image_upload":"Upload Image","reference_image":"Reference Image","first_frame":"First Frame Image","uploading":"Uploading image...","drag_drop":"Click to select or drag image here","drop_to_upload":"Release to upload","file_detected":"Image file detected, release to upload","supported_formats":"Supports JPG, PNG, GIF, WebP formats, max 10MB","max_tokens":"Max Output Length","temperature":"Creativity (0-1)","variants":"Number of Images","image_size":"Image Size","square":"Square","landscape":"Landscape","portrait":"Portrait","1_image":"1 Image","2_images":"2 Images","generate_1":"Generate 1 image","generate_2":"Generate 2 images","square_ratio":"Square (1:1)","landscape_ratio":"Landscape (16:9)","portrait_ratio":"Portrait (9:16)"}}')}};