"use strict";exports.id=9513,exports.ids=[9513],exports.modules={68064:(e,t,a)=>{a.d(t,{BT:()=>o,Wu:()=>c,X9:()=>i,ZB:()=>d,Zp:()=>n,aR:()=>l,wL:()=>u});var r=a(2569);a(29068);var s=a(75673);function n({className:e,...t}){return(0,r.jsx)("div",{"data-slot":"card",className:(0,s.cn)("bg-card text-card-foreground flex flex-col gap-6 rounded-2xl border border-border/50 p-6 shadow-lg backdrop-blur-sm transition-all duration-200 hover:shadow-xl hover:scale-[1.01]",e),...t})}function l({className:e,...t}){return(0,r.jsx)("div",{"data-slot":"card-header",className:(0,s.cn)("@container/card-header grid auto-rows-min grid-rows-[auto_auto] items-start gap-2 has-data-[slot=card-action]:grid-cols-[1fr_auto] [.border-b]:pb-6",e),...t})}function d({className:e,...t}){return(0,r.jsx)("div",{"data-slot":"card-title",className:(0,s.cn)("leading-none font-semibold",e),...t})}function o({className:e,...t}){return(0,r.jsx)("div",{"data-slot":"card-description",className:(0,s.cn)("text-muted-foreground text-sm",e),...t})}function i({className:e,...t}){return(0,r.jsx)("div",{"data-slot":"card-action",className:(0,s.cn)("col-start-2 row-span-2 row-start-1 self-start justify-self-end",e),...t})}function c({className:e,...t}){return(0,r.jsx)("div",{"data-slot":"card-content",className:(0,s.cn)("px-6",e),...t})}function u({className:e,...t}){return(0,r.jsx)("div",{"data-slot":"card-footer",className:(0,s.cn)("flex items-center px-6 [.border-t]:pt-6",e),...t})}},79513:(e,t,a)=>{a.d(t,{default:()=>C});var r=a(2569),s=a(29068),n=a(32840),l=a(80859),d=a(73068),o=a(28992),i=a(71765),c=a(68064),u=a(79929),x=a(26738),h=a(67442),m=a(75673);let f={light:"",dark:".dark"},p=s.createContext(null);function g({id:e,className:t,children:a,config:n,...l}){let d=s.useId(),o=`chart-${e||d.replace(/:/g,"")}`;return(0,r.jsx)(p.Provider,{value:{config:n},children:(0,r.jsxs)("div",{"data-slot":"chart","data-chart":o,className:(0,m.cn)("[&_.recharts-cartesian-axis-tick_text]:fill-muted-foreground [&_.recharts-cartesian-grid_line[stroke='#ccc']]:stroke-border/50 [&_.recharts-curve.recharts-tooltip-cursor]:stroke-border [&_.recharts-polar-grid_[stroke='#ccc']]:stroke-border [&_.recharts-radial-bar-background-sector]:fill-muted [&_.recharts-rectangle.recharts-tooltip-cursor]:fill-muted [&_.recharts-reference-line_[stroke='#ccc']]:stroke-border flex aspect-video justify-center text-xs [&_.recharts-dot[stroke='#fff']]:stroke-transparent [&_.recharts-layer]:outline-hidden [&_.recharts-sector]:outline-hidden [&_.recharts-sector[stroke='#fff']]:stroke-transparent [&_.recharts-surface]:outline-hidden",t),...l,children:[(0,r.jsx)(v,{id:o,config:n}),(0,r.jsx)(u.u,{children:a})]})})}let v=({id:e,config:t})=>{let a=Object.entries(t).filter(([,e])=>e.theme||e.color);return a.length?(0,r.jsx)("style",{dangerouslySetInnerHTML:{__html:Object.entries(f).map(([t,r])=>`
${r} [data-chart=${e}] {
${a.map(([e,a])=>{let r=a.theme?.[t]||a.color;return r?`  --color-${e}: ${r};`:null}).join("\n")}
}
`).join("\n")}}):null},b=x.m;function j({active:e,payload:t,className:a,indicator:n="dot",hideLabel:l=!1,hideIndicator:d=!1,label:o,labelFormatter:i,labelClassName:c,formatter:u,color:x,nameKey:h,labelKey:f}){let{config:g}=function(){let e=s.useContext(p);if(!e)throw Error("useChart must be used within a <ChartContainer />");return e}(),v=s.useMemo(()=>{if(l||!t?.length)return null;let[e]=t,a=`${f||e?.dataKey||e?.name||"value"}`,s=y(g,e,a),n=f||"string"!=typeof o?s?.label:g[o]?.label||o;return i?(0,r.jsx)("div",{className:(0,m.cn)("font-medium",c),children:i(n,t)}):n?(0,r.jsx)("div",{className:(0,m.cn)("font-medium",c),children:n}):null},[o,i,t,l,c,g,f]);if(!e||!t?.length)return null;let b=1===t.length&&"dot"!==n;return(0,r.jsxs)("div",{className:(0,m.cn)("border-border/50 bg-background grid min-w-[8rem] items-start gap-1.5 rounded-lg border px-2.5 py-1.5 text-xs shadow-xl",a),children:[b?null:v,(0,r.jsx)("div",{className:"grid gap-1.5",children:t.map((e,t)=>{let a=`${h||e.name||e.dataKey||"value"}`,s=y(g,e,a),l=x||e.payload.fill||e.color;return(0,r.jsx)("div",{className:(0,m.cn)("[&>svg]:text-muted-foreground flex w-full flex-wrap items-stretch gap-2 [&>svg]:h-2.5 [&>svg]:w-2.5","dot"===n&&"items-center"),children:u&&e?.value!==void 0&&e.name?u(e.value,e.name,e,t,e.payload):(0,r.jsxs)(r.Fragment,{children:[s?.icon?(0,r.jsx)(s.icon,{}):!d&&(0,r.jsx)("div",{className:(0,m.cn)("shrink-0 rounded-[2px] border-(--color-border) bg-(--color-bg)",{"h-2.5 w-2.5":"dot"===n,"w-1":"line"===n,"w-0 border-[1.5px] border-dashed bg-transparent":"dashed"===n,"my-0.5":b&&"dashed"===n}),style:{"--color-bg":l,"--color-border":l}}),(0,r.jsxs)("div",{className:(0,m.cn)("flex flex-1 justify-between leading-none",b?"items-end":"items-center"),children:[(0,r.jsxs)("div",{className:"grid gap-1.5",children:[b?v:null,(0,r.jsx)("span",{className:"text-muted-foreground",children:s?.label||e.name})]}),e.value&&(0,r.jsx)("span",{className:"text-foreground font-mono font-medium tabular-nums",children:e.value.toLocaleString()})]})]})},e.dataKey)})})]})}function y(e,t,a){if("object"!=typeof t||null===t)return;let r="payload"in t&&"object"==typeof t.payload&&null!==t.payload?t.payload:void 0,s=a;return a in t&&"string"==typeof t[a]?s=t[a]:r&&a in r&&"string"==typeof r[a]&&(s=r[a]),s in e?e[s]:e[a]}h.s;var w=a(14078),k=a(85497);let N=(0,a(75958).F)("inline-flex items-center justify-center gap-2 rounded-md text-sm font-medium hover:bg-muted hover:text-muted-foreground disabled:pointer-events-none disabled:opacity-50 data-[state=on]:bg-accent data-[state=on]:text-accent-foreground [&_svg]:pointer-events-none [&_svg:not([class*='size-'])]:size-4 [&_svg]:shrink-0 focus-visible:border-ring focus-visible:ring-ring/50 focus-visible:ring-[3px] outline-none transition-[color,box-shadow] aria-invalid:ring-destructive/20 dark:aria-invalid:ring-destructive/40 aria-invalid:border-destructive whitespace-nowrap",{variants:{variant:{default:"bg-transparent",outline:"border border-input bg-transparent shadow-xs hover:bg-accent hover:text-accent-foreground"},size:{default:"h-9 px-2 min-w-9",sm:"h-8 px-1.5 min-w-8",lg:"h-10 px-2.5 min-w-10"}},defaultVariants:{variant:"default",size:"default"}}),_=s.createContext({size:"default",variant:"default"});function L({className:e,variant:t,size:a,children:s,...n}){return(0,r.jsx)(k.bL,{"data-slot":"toggle-group","data-variant":t,"data-size":a,className:(0,m.cn)("group/toggle-group flex w-fit items-center rounded-md data-[variant=outline]:shadow-xs",e),...n,children:(0,r.jsx)(_.Provider,{value:{variant:t,size:a},children:s})})}function z({className:e,children:t,variant:a,size:n,...l}){let d=s.useContext(_);return(0,r.jsx)(k.q7,{"data-slot":"toggle-group-item","data-variant":d.variant||a,"data-size":d.size||n,className:(0,m.cn)(N({variant:d.variant||a,size:d.size||n}),"min-w-0 flex-1 shrink-0 rounded-none shadow-none first:rounded-l-md last:rounded-r-md focus:z-10 focus-visible:z-10 data-[variant=outline]:border-l-0 data-[variant=outline]:first:border-l",e),...l,children:t})}function C({data:e,fields:t,title:a,description:u,defaultTimeRange:x="90d"}){let h=(0,i.a)(),[m,f]=s.useState(x);s.useEffect(()=>{h&&f("7d")},[h]);let p=t.reduce((e,t)=>(e[t.key]={label:t.label,color:t.color||"var(--primary)"},e),{}),v=e.filter(t=>{let a=new Date(t.date),r=new Date(e[e.length-1].date),s=90;"30d"===m?s=30:"7d"===m&&(s=7);let n=new Date(r);return n.setDate(n.getDate()-s),a>=n});return(0,r.jsxs)(c.Zp,{className:"@container/card",children:[(0,r.jsxs)(c.aR,{children:[(0,r.jsx)(c.ZB,{children:a}),(0,r.jsxs)(c.BT,{children:[(0,r.jsx)("span",{className:"hidden @[540px]/card:block",children:u}),(0,r.jsx)("span",{className:"@[540px]/card:hidden",children:u})]}),(0,r.jsxs)(c.X9,{children:[(0,r.jsxs)(L,{type:"single",value:m,onValueChange:e=>f(e),variant:"outline",className:"hidden *:data-[slot=toggle-group-item]:!px-4 @[767px]/card:flex",children:[(0,r.jsx)(z,{value:"90d",children:"Last 3 months"}),(0,r.jsx)(z,{value:"30d",children:"Last 30 days"}),(0,r.jsx)(z,{value:"7d",children:"Last 7 days"})]}),(0,r.jsxs)(w.l6,{value:m,onValueChange:e=>f(e),children:[(0,r.jsx)(w.bq,{className:"flex w-40 **:data-[slot=select-value]:block **:data-[slot=select-value]:truncate @[767px]/card:hidden",size:"sm","aria-label":"Select a value",children:(0,r.jsx)(w.yv,{placeholder:"Last 3 months"})}),(0,r.jsxs)(w.gC,{className:"rounded-xl",children:[(0,r.jsx)(w.eb,{value:"90d",className:"rounded-lg",children:"Last 3 months"}),(0,r.jsx)(w.eb,{value:"30d",className:"rounded-lg",children:"Last 30 days"}),(0,r.jsx)(w.eb,{value:"7d",className:"rounded-lg",children:"Last 7 days"})]})]})]})]}),(0,r.jsx)(c.Wu,{className:"px-2 pt-4 sm:px-6 sm:pt-6",children:(0,r.jsx)(g,{config:p,className:"aspect-auto h-[250px] w-full",children:(0,r.jsxs)(n.Q,{data:v,children:[(0,r.jsx)("defs",{children:t.map(e=>(0,r.jsxs)("linearGradient",{id:`fill-${e.key}`,x1:"0",y1:"0",x2:"0",y2:"1",children:[(0,r.jsx)("stop",{offset:"5%",stopColor:e.color||"var(--primary)",stopOpacity:1}),(0,r.jsx)("stop",{offset:"95%",stopColor:e.color||"var(--primary)",stopOpacity:.1})]},`fill-${e.key}`))}),(0,r.jsx)(l.d,{vertical:!1}),(0,r.jsx)(d.W,{dataKey:"date",tickLine:!1,axisLine:!1,tickMargin:8,minTickGap:32,tickFormatter:e=>new Date(e).toLocaleDateString("en-US",{month:"short",day:"numeric"})}),(0,r.jsx)(b,{cursor:!1,defaultIndex:h?-1:10,content:(0,r.jsx)(j,{labelFormatter:e=>new Date(e).toLocaleDateString("en-US",{month:"short",day:"numeric"}),indicator:"dot"})}),t.map(e=>(0,r.jsx)(o.G,{dataKey:e.key,type:"natural",fill:`url(#fill-${e.key})`,stroke:e.color||"var(--primary)",stackId:"a"},e.key))]})})})]})}}};