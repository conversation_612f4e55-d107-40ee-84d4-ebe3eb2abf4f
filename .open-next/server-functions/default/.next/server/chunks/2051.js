"use strict";exports.id=2051,exports.ids=[2051],exports.modules={12206:(e,t,r)=>{let a;r.d(t,{kY:()=>o,z:()=>l});var s,n,i,o,l={};r.r(l),r.d(l,{BRAND:()=>eN,DIRTY:()=>k,EMPTY_PATH:()=>_,INVALID:()=>x,NEVER:()=>th,OK:()=>T,ParseStatus:()=>w,Schema:()=>N,ZodAny:()=>en,ZodArray:()=>eu,ZodBigInt:()=>Q,ZodBoolean:()=>ee,ZodBranded:()=>eR,ZodCatch:()=>ej,ZodDate:()=>et,ZodDefault:()=>eA,ZodDiscriminatedUnion:()=>em,ZodEffects:()=>eS,ZodEnum:()=>ek,ZodError:()=>m,ZodFirstPartyTypeKind:()=>o,ZodFunction:()=>e_,ZodIntersection:()=>eh,ZodIssueCode:()=>d,ZodLazy:()=>eb,ZodLiteral:()=>ew,ZodMap:()=>ey,ZodNaN:()=>eC,ZodNativeEnum:()=>eT,ZodNever:()=>eo,ZodNull:()=>es,ZodNullable:()=>eE,ZodNumber:()=>X,ZodObject:()=>ec,ZodOptional:()=>ez,ZodParsedType:()=>u,ZodPipeline:()=>eO,ZodPromise:()=>eI,ZodReadonly:()=>eP,ZodRecord:()=>eg,ZodSchema:()=>N,ZodSet:()=>ev,ZodString:()=>H,ZodSymbol:()=>er,ZodTransformer:()=>eS,ZodTuple:()=>ef,ZodType:()=>N,ZodUndefined:()=>ea,ZodUnion:()=>ed,ZodUnknown:()=>ei,ZodVoid:()=>el,addIssueToContext:()=>b,any:()=>eW,array:()=>eQ,bigint:()=>eU,boolean:()=>eV,coerce:()=>tm,custom:()=>e$,date:()=>eB,datetimeRegex:()=>G,defaultErrorMap:()=>h,discriminatedUnion:()=>e9,effect:()=>tn,enum:()=>tr,function:()=>e7,getErrorMap:()=>y,getParsedType:()=>c,instanceof:()=>eD,intersection:()=>e2,isAborted:()=>I,isAsync:()=>E,isDirty:()=>S,isValid:()=>z,late:()=>eZ,lazy:()=>te,literal:()=>tt,makeIssue:()=>v,map:()=>e5,nan:()=>eF,nativeEnum:()=>ta,never:()=>eH,null:()=>eK,nullable:()=>to,number:()=>eL,object:()=>e0,objectUtil:()=>n,oboolean:()=>tp,onumber:()=>td,optional:()=>ti,ostring:()=>tc,pipeline:()=>tu,preprocess:()=>tl,promise:()=>ts,quotelessJson:()=>p,record:()=>e3,set:()=>e8,setErrorMap:()=>g,strictObject:()=>e1,string:()=>eq,symbol:()=>eJ,transformer:()=>tn,tuple:()=>e6,undefined:()=>eY,union:()=>e4,unknown:()=>eG,util:()=>s,void:()=>eX}),function(e){e.assertEqual=e=>{},e.assertIs=function(e){},e.assertNever=function(e){throw Error()},e.arrayToEnum=e=>{let t={};for(let r of e)t[r]=r;return t},e.getValidEnumValues=t=>{let r=e.objectKeys(t).filter(e=>"number"!=typeof t[t[e]]),a={};for(let e of r)a[e]=t[e];return e.objectValues(a)},e.objectValues=t=>e.objectKeys(t).map(function(e){return t[e]}),e.objectKeys="function"==typeof Object.keys?e=>Object.keys(e):e=>{let t=[];for(let r in e)Object.prototype.hasOwnProperty.call(e,r)&&t.push(r);return t},e.find=(e,t)=>{for(let r of e)if(t(r))return r},e.isInteger="function"==typeof Number.isInteger?e=>Number.isInteger(e):e=>"number"==typeof e&&Number.isFinite(e)&&Math.floor(e)===e,e.joinValues=function(e,t=" | "){return e.map(e=>"string"==typeof e?`'${e}'`:e).join(t)},e.jsonStringifyReplacer=(e,t)=>"bigint"==typeof t?t.toString():t}(s||(s={})),(n||(n={})).mergeShapes=(e,t)=>({...e,...t});let u=s.arrayToEnum(["string","nan","number","integer","float","boolean","date","bigint","symbol","function","undefined","null","array","object","unknown","promise","void","never","map","set"]),c=e=>{switch(typeof e){case"undefined":return u.undefined;case"string":return u.string;case"number":return Number.isNaN(e)?u.nan:u.number;case"boolean":return u.boolean;case"function":return u.function;case"bigint":return u.bigint;case"symbol":return u.symbol;case"object":if(Array.isArray(e))return u.array;if(null===e)return u.null;if(e.then&&"function"==typeof e.then&&e.catch&&"function"==typeof e.catch)return u.promise;if("undefined"!=typeof Map&&e instanceof Map)return u.map;if("undefined"!=typeof Set&&e instanceof Set)return u.set;if("undefined"!=typeof Date&&e instanceof Date)return u.date;return u.object;default:return u.unknown}},d=s.arrayToEnum(["invalid_type","invalid_literal","custom","invalid_union","invalid_union_discriminator","invalid_enum_value","unrecognized_keys","invalid_arguments","invalid_return_type","invalid_date","invalid_string","too_small","too_big","invalid_intersection_types","not_multiple_of","not_finite"]),p=e=>JSON.stringify(e,null,2).replace(/"([^"]+)":/g,"$1:");class m extends Error{get errors(){return this.issues}constructor(e){super(),this.issues=[],this.addIssue=e=>{this.issues=[...this.issues,e]},this.addIssues=(e=[])=>{this.issues=[...this.issues,...e]};let t=new.target.prototype;Object.setPrototypeOf?Object.setPrototypeOf(this,t):this.__proto__=t,this.name="ZodError",this.issues=e}format(e){let t=e||function(e){return e.message},r={_errors:[]},a=e=>{for(let s of e.issues)if("invalid_union"===s.code)s.unionErrors.map(a);else if("invalid_return_type"===s.code)a(s.returnTypeError);else if("invalid_arguments"===s.code)a(s.argumentsError);else if(0===s.path.length)r._errors.push(t(s));else{let e=r,a=0;for(;a<s.path.length;){let r=s.path[a];a===s.path.length-1?(e[r]=e[r]||{_errors:[]},e[r]._errors.push(t(s))):e[r]=e[r]||{_errors:[]},e=e[r],a++}}};return a(this),r}static assert(e){if(!(e instanceof m))throw Error(`Not a ZodError: ${e}`)}toString(){return this.message}get message(){return JSON.stringify(this.issues,s.jsonStringifyReplacer,2)}get isEmpty(){return 0===this.issues.length}flatten(e=e=>e.message){let t={},r=[];for(let a of this.issues)a.path.length>0?(t[a.path[0]]=t[a.path[0]]||[],t[a.path[0]].push(e(a))):r.push(e(a));return{formErrors:r,fieldErrors:t}}get formErrors(){return this.flatten()}}m.create=e=>new m(e);let h=(e,t)=>{let r;switch(e.code){case d.invalid_type:r=e.received===u.undefined?"Required":`Expected ${e.expected}, received ${e.received}`;break;case d.invalid_literal:r=`Invalid literal value, expected ${JSON.stringify(e.expected,s.jsonStringifyReplacer)}`;break;case d.unrecognized_keys:r=`Unrecognized key(s) in object: ${s.joinValues(e.keys,", ")}`;break;case d.invalid_union:r="Invalid input";break;case d.invalid_union_discriminator:r=`Invalid discriminator value. Expected ${s.joinValues(e.options)}`;break;case d.invalid_enum_value:r=`Invalid enum value. Expected ${s.joinValues(e.options)}, received '${e.received}'`;break;case d.invalid_arguments:r="Invalid function arguments";break;case d.invalid_return_type:r="Invalid function return type";break;case d.invalid_date:r="Invalid date";break;case d.invalid_string:"object"==typeof e.validation?"includes"in e.validation?(r=`Invalid input: must include "${e.validation.includes}"`,"number"==typeof e.validation.position&&(r=`${r} at one or more positions greater than or equal to ${e.validation.position}`)):"startsWith"in e.validation?r=`Invalid input: must start with "${e.validation.startsWith}"`:"endsWith"in e.validation?r=`Invalid input: must end with "${e.validation.endsWith}"`:s.assertNever(e.validation):r="regex"!==e.validation?`Invalid ${e.validation}`:"Invalid";break;case d.too_small:r="array"===e.type?`Array must contain ${e.exact?"exactly":e.inclusive?"at least":"more than"} ${e.minimum} element(s)`:"string"===e.type?`String must contain ${e.exact?"exactly":e.inclusive?"at least":"over"} ${e.minimum} character(s)`:"number"===e.type?`Number must be ${e.exact?"exactly equal to ":e.inclusive?"greater than or equal to ":"greater than "}${e.minimum}`:"date"===e.type?`Date must be ${e.exact?"exactly equal to ":e.inclusive?"greater than or equal to ":"greater than "}${new Date(Number(e.minimum))}`:"Invalid input";break;case d.too_big:r="array"===e.type?`Array must contain ${e.exact?"exactly":e.inclusive?"at most":"less than"} ${e.maximum} element(s)`:"string"===e.type?`String must contain ${e.exact?"exactly":e.inclusive?"at most":"under"} ${e.maximum} character(s)`:"number"===e.type?`Number must be ${e.exact?"exactly":e.inclusive?"less than or equal to":"less than"} ${e.maximum}`:"bigint"===e.type?`BigInt must be ${e.exact?"exactly":e.inclusive?"less than or equal to":"less than"} ${e.maximum}`:"date"===e.type?`Date must be ${e.exact?"exactly":e.inclusive?"smaller than or equal to":"smaller than"} ${new Date(Number(e.maximum))}`:"Invalid input";break;case d.custom:r="Invalid input";break;case d.invalid_intersection_types:r="Intersection results could not be merged";break;case d.not_multiple_of:r=`Number must be a multiple of ${e.multipleOf}`;break;case d.not_finite:r="Number must be finite";break;default:r=t.defaultError,s.assertNever(e)}return{message:r}},f=h;function g(e){f=e}function y(){return f}let v=e=>{let{data:t,path:r,errorMaps:a,issueData:s}=e,n=[...r,...s.path||[]],i={...s,path:n};if(void 0!==s.message)return{...s,path:n,message:s.message};let o="";for(let e of a.filter(e=>!!e).slice().reverse())o=e(i,{data:t,defaultError:o}).message;return{...s,path:n,message:o}},_=[];function b(e,t){let r=f,a=v({issueData:t,data:e.data,path:e.path,errorMaps:[e.common.contextualErrorMap,e.schemaErrorMap,r,r===h?void 0:h].filter(e=>!!e)});e.common.issues.push(a)}class w{constructor(){this.value="valid"}dirty(){"valid"===this.value&&(this.value="dirty")}abort(){"aborted"!==this.value&&(this.value="aborted")}static mergeArray(e,t){let r=[];for(let a of t){if("aborted"===a.status)return x;"dirty"===a.status&&e.dirty(),r.push(a.value)}return{status:e.value,value:r}}static async mergeObjectAsync(e,t){let r=[];for(let e of t){let t=await e.key,a=await e.value;r.push({key:t,value:a})}return w.mergeObjectSync(e,r)}static mergeObjectSync(e,t){let r={};for(let a of t){let{key:t,value:s}=a;if("aborted"===t.status||"aborted"===s.status)return x;"dirty"===t.status&&e.dirty(),"dirty"===s.status&&e.dirty(),"__proto__"!==t.value&&(void 0!==s.value||a.alwaysSet)&&(r[t.value]=s.value)}return{status:e.value,value:r}}}let x=Object.freeze({status:"aborted"}),k=e=>({status:"dirty",value:e}),T=e=>({status:"valid",value:e}),I=e=>"aborted"===e.status,S=e=>"dirty"===e.status,z=e=>"valid"===e.status,E=e=>"undefined"!=typeof Promise&&e instanceof Promise;!function(e){e.errToObj=e=>"string"==typeof e?{message:e}:e||{},e.toString=e=>"string"==typeof e?e:e?.message}(i||(i={}));class A{constructor(e,t,r,a){this._cachedPath=[],this.parent=e,this.data=t,this._path=r,this._key=a}get path(){return this._cachedPath.length||(Array.isArray(this._key)?this._cachedPath.push(...this._path,...this._key):this._cachedPath.push(...this._path,this._key)),this._cachedPath}}let j=(e,t)=>{if(z(t))return{success:!0,data:t.value};if(!e.common.issues.length)throw Error("Validation failed but no issues detected.");return{success:!1,get error(){if(this._error)return this._error;let t=new m(e.common.issues);return this._error=t,this._error}}};function C(e){if(!e)return{};let{errorMap:t,invalid_type_error:r,required_error:a,description:s}=e;if(t&&(r||a))throw Error('Can\'t use "invalid_type_error" or "required_error" in conjunction with custom error map.');return t?{errorMap:t,description:s}:{errorMap:(t,s)=>{let{message:n}=e;return"invalid_enum_value"===t.code?{message:n??s.defaultError}:void 0===s.data?{message:n??a??s.defaultError}:"invalid_type"!==t.code?{message:s.defaultError}:{message:n??r??s.defaultError}},description:s}}class N{get description(){return this._def.description}_getType(e){return c(e.data)}_getOrReturnCtx(e,t){return t||{common:e.parent.common,data:e.data,parsedType:c(e.data),schemaErrorMap:this._def.errorMap,path:e.path,parent:e.parent}}_processInputParams(e){return{status:new w,ctx:{common:e.parent.common,data:e.data,parsedType:c(e.data),schemaErrorMap:this._def.errorMap,path:e.path,parent:e.parent}}}_parseSync(e){let t=this._parse(e);if(E(t))throw Error("Synchronous parse encountered promise.");return t}_parseAsync(e){return Promise.resolve(this._parse(e))}parse(e,t){let r=this.safeParse(e,t);if(r.success)return r.data;throw r.error}safeParse(e,t){let r={common:{issues:[],async:t?.async??!1,contextualErrorMap:t?.errorMap},path:t?.path||[],schemaErrorMap:this._def.errorMap,parent:null,data:e,parsedType:c(e)},a=this._parseSync({data:e,path:r.path,parent:r});return j(r,a)}"~validate"(e){let t={common:{issues:[],async:!!this["~standard"].async},path:[],schemaErrorMap:this._def.errorMap,parent:null,data:e,parsedType:c(e)};if(!this["~standard"].async)try{let r=this._parseSync({data:e,path:[],parent:t});return z(r)?{value:r.value}:{issues:t.common.issues}}catch(e){e?.message?.toLowerCase()?.includes("encountered")&&(this["~standard"].async=!0),t.common={issues:[],async:!0}}return this._parseAsync({data:e,path:[],parent:t}).then(e=>z(e)?{value:e.value}:{issues:t.common.issues})}async parseAsync(e,t){let r=await this.safeParseAsync(e,t);if(r.success)return r.data;throw r.error}async safeParseAsync(e,t){let r={common:{issues:[],contextualErrorMap:t?.errorMap,async:!0},path:t?.path||[],schemaErrorMap:this._def.errorMap,parent:null,data:e,parsedType:c(e)},a=this._parse({data:e,path:r.path,parent:r});return j(r,await (E(a)?a:Promise.resolve(a)))}refine(e,t){let r=e=>"string"==typeof t||void 0===t?{message:t}:"function"==typeof t?t(e):t;return this._refinement((t,a)=>{let s=e(t),n=()=>a.addIssue({code:d.custom,...r(t)});return"undefined"!=typeof Promise&&s instanceof Promise?s.then(e=>!!e||(n(),!1)):!!s||(n(),!1)})}refinement(e,t){return this._refinement((r,a)=>!!e(r)||(a.addIssue("function"==typeof t?t(r,a):t),!1))}_refinement(e){return new eS({schema:this,typeName:o.ZodEffects,effect:{type:"refinement",refinement:e}})}superRefine(e){return this._refinement(e)}constructor(e){this.spa=this.safeParseAsync,this._def=e,this.parse=this.parse.bind(this),this.safeParse=this.safeParse.bind(this),this.parseAsync=this.parseAsync.bind(this),this.safeParseAsync=this.safeParseAsync.bind(this),this.spa=this.spa.bind(this),this.refine=this.refine.bind(this),this.refinement=this.refinement.bind(this),this.superRefine=this.superRefine.bind(this),this.optional=this.optional.bind(this),this.nullable=this.nullable.bind(this),this.nullish=this.nullish.bind(this),this.array=this.array.bind(this),this.promise=this.promise.bind(this),this.or=this.or.bind(this),this.and=this.and.bind(this),this.transform=this.transform.bind(this),this.brand=this.brand.bind(this),this.default=this.default.bind(this),this.catch=this.catch.bind(this),this.describe=this.describe.bind(this),this.pipe=this.pipe.bind(this),this.readonly=this.readonly.bind(this),this.isNullable=this.isNullable.bind(this),this.isOptional=this.isOptional.bind(this),this["~standard"]={version:1,vendor:"zod",validate:e=>this["~validate"](e)}}optional(){return ez.create(this,this._def)}nullable(){return eE.create(this,this._def)}nullish(){return this.nullable().optional()}array(){return eu.create(this)}promise(){return eI.create(this,this._def)}or(e){return ed.create([this,e],this._def)}and(e){return eh.create(this,e,this._def)}transform(e){return new eS({...C(this._def),schema:this,typeName:o.ZodEffects,effect:{type:"transform",transform:e}})}default(e){return new eA({...C(this._def),innerType:this,defaultValue:"function"==typeof e?e:()=>e,typeName:o.ZodDefault})}brand(){return new eR({typeName:o.ZodBranded,type:this,...C(this._def)})}catch(e){return new ej({...C(this._def),innerType:this,catchValue:"function"==typeof e?e:()=>e,typeName:o.ZodCatch})}describe(e){return new this.constructor({...this._def,description:e})}pipe(e){return eO.create(this,e)}readonly(){return eP.create(this)}isOptional(){return this.safeParse(void 0).success}isNullable(){return this.safeParse(null).success}}let R=/^c[^\s-]{8,}$/i,O=/^[0-9a-z]+$/,P=/^[0-9A-HJKMNP-TV-Z]{26}$/i,M=/^[0-9a-fA-F]{8}\b-[0-9a-fA-F]{4}\b-[0-9a-fA-F]{4}\b-[0-9a-fA-F]{4}\b-[0-9a-fA-F]{12}$/i,$=/^[a-z0-9_-]{21}$/i,Z=/^[A-Za-z0-9-_]+\.[A-Za-z0-9-_]+\.[A-Za-z0-9-_]*$/,D=/^[-+]?P(?!$)(?:(?:[-+]?\d+Y)|(?:[-+]?\d+[.,]\d+Y$))?(?:(?:[-+]?\d+M)|(?:[-+]?\d+[.,]\d+M$))?(?:(?:[-+]?\d+W)|(?:[-+]?\d+[.,]\d+W$))?(?:(?:[-+]?\d+D)|(?:[-+]?\d+[.,]\d+D$))?(?:T(?=[\d+-])(?:(?:[-+]?\d+H)|(?:[-+]?\d+[.,]\d+H$))?(?:(?:[-+]?\d+M)|(?:[-+]?\d+[.,]\d+M$))?(?:[-+]?\d+(?:[.,]\d+)?S)?)??$/,q=/^(?!\.)(?!.*\.\.)([A-Z0-9_'+\-\.]*)[A-Z0-9_+-]@([A-Z0-9][A-Z0-9\-]*\.)+[A-Z]{2,}$/i,L=/^(?:(?:25[0-5]|2[0-4][0-9]|1[0-9][0-9]|[1-9][0-9]|[0-9])\.){3}(?:25[0-5]|2[0-4][0-9]|1[0-9][0-9]|[1-9][0-9]|[0-9])$/,F=/^(?:(?:25[0-5]|2[0-4][0-9]|1[0-9][0-9]|[1-9][0-9]|[0-9])\.){3}(?:25[0-5]|2[0-4][0-9]|1[0-9][0-9]|[1-9][0-9]|[0-9])\/(3[0-2]|[12]?[0-9])$/,U=/^(([0-9a-fA-F]{1,4}:){7,7}[0-9a-fA-F]{1,4}|([0-9a-fA-F]{1,4}:){1,7}:|([0-9a-fA-F]{1,4}:){1,6}:[0-9a-fA-F]{1,4}|([0-9a-fA-F]{1,4}:){1,5}(:[0-9a-fA-F]{1,4}){1,2}|([0-9a-fA-F]{1,4}:){1,4}(:[0-9a-fA-F]{1,4}){1,3}|([0-9a-fA-F]{1,4}:){1,3}(:[0-9a-fA-F]{1,4}){1,4}|([0-9a-fA-F]{1,4}:){1,2}(:[0-9a-fA-F]{1,4}){1,5}|[0-9a-fA-F]{1,4}:((:[0-9a-fA-F]{1,4}){1,6})|:((:[0-9a-fA-F]{1,4}){1,7}|:)|fe80:(:[0-9a-fA-F]{0,4}){0,4}%[0-9a-zA-Z]{1,}|::(ffff(:0{1,4}){0,1}:){0,1}((25[0-5]|(2[0-4]|1{0,1}[0-9]){0,1}[0-9])\.){3,3}(25[0-5]|(2[0-4]|1{0,1}[0-9]){0,1}[0-9])|([0-9a-fA-F]{1,4}:){1,4}:((25[0-5]|(2[0-4]|1{0,1}[0-9]){0,1}[0-9])\.){3,3}(25[0-5]|(2[0-4]|1{0,1}[0-9]){0,1}[0-9]))$/,V=/^(([0-9a-fA-F]{1,4}:){7,7}[0-9a-fA-F]{1,4}|([0-9a-fA-F]{1,4}:){1,7}:|([0-9a-fA-F]{1,4}:){1,6}:[0-9a-fA-F]{1,4}|([0-9a-fA-F]{1,4}:){1,5}(:[0-9a-fA-F]{1,4}){1,2}|([0-9a-fA-F]{1,4}:){1,4}(:[0-9a-fA-F]{1,4}){1,3}|([0-9a-fA-F]{1,4}:){1,3}(:[0-9a-fA-F]{1,4}){1,4}|([0-9a-fA-F]{1,4}:){1,2}(:[0-9a-fA-F]{1,4}){1,5}|[0-9a-fA-F]{1,4}:((:[0-9a-fA-F]{1,4}){1,6})|:((:[0-9a-fA-F]{1,4}){1,7}|:)|fe80:(:[0-9a-fA-F]{0,4}){0,4}%[0-9a-zA-Z]{1,}|::(ffff(:0{1,4}){0,1}:){0,1}((25[0-5]|(2[0-4]|1{0,1}[0-9]){0,1}[0-9])\.){3,3}(25[0-5]|(2[0-4]|1{0,1}[0-9]){0,1}[0-9])|([0-9a-fA-F]{1,4}:){1,4}:((25[0-5]|(2[0-4]|1{0,1}[0-9]){0,1}[0-9])\.){3,3}(25[0-5]|(2[0-4]|1{0,1}[0-9]){0,1}[0-9]))\/(12[0-8]|1[01][0-9]|[1-9]?[0-9])$/,B=/^([0-9a-zA-Z+/]{4})*(([0-9a-zA-Z+/]{2}==)|([0-9a-zA-Z+/]{3}=))?$/,J=/^([0-9a-zA-Z-_]{4})*(([0-9a-zA-Z-_]{2}(==)?)|([0-9a-zA-Z-_]{3}(=)?))?$/,Y="((\\d\\d[2468][048]|\\d\\d[13579][26]|\\d\\d0[48]|[02468][048]00|[13579][26]00)-02-29|\\d{4}-((0[13578]|1[02])-(0[1-9]|[12]\\d|3[01])|(0[469]|11)-(0[1-9]|[12]\\d|30)|(02)-(0[1-9]|1\\d|2[0-8])))",K=RegExp(`^${Y}$`);function W(e){let t="[0-5]\\d";e.precision?t=`${t}\\.\\d{${e.precision}}`:null==e.precision&&(t=`${t}(\\.\\d+)?`);let r=e.precision?"+":"?";return`([01]\\d|2[0-3]):[0-5]\\d(:${t})${r}`}function G(e){let t=`${Y}T${W(e)}`,r=[];return r.push(e.local?"Z?":"Z"),e.offset&&r.push("([+-]\\d{2}:?\\d{2})"),t=`${t}(${r.join("|")})`,RegExp(`^${t}$`)}class H extends N{_parse(e){var t,r,n,i;let o;if(this._def.coerce&&(e.data=String(e.data)),this._getType(e)!==u.string){let t=this._getOrReturnCtx(e);return b(t,{code:d.invalid_type,expected:u.string,received:t.parsedType}),x}let l=new w;for(let u of this._def.checks)if("min"===u.kind)e.data.length<u.value&&(b(o=this._getOrReturnCtx(e,o),{code:d.too_small,minimum:u.value,type:"string",inclusive:!0,exact:!1,message:u.message}),l.dirty());else if("max"===u.kind)e.data.length>u.value&&(b(o=this._getOrReturnCtx(e,o),{code:d.too_big,maximum:u.value,type:"string",inclusive:!0,exact:!1,message:u.message}),l.dirty());else if("length"===u.kind){let t=e.data.length>u.value,r=e.data.length<u.value;(t||r)&&(o=this._getOrReturnCtx(e,o),t?b(o,{code:d.too_big,maximum:u.value,type:"string",inclusive:!0,exact:!0,message:u.message}):r&&b(o,{code:d.too_small,minimum:u.value,type:"string",inclusive:!0,exact:!0,message:u.message}),l.dirty())}else if("email"===u.kind)q.test(e.data)||(b(o=this._getOrReturnCtx(e,o),{validation:"email",code:d.invalid_string,message:u.message}),l.dirty());else if("emoji"===u.kind)a||(a=RegExp("^(\\p{Extended_Pictographic}|\\p{Emoji_Component})+$","u")),a.test(e.data)||(b(o=this._getOrReturnCtx(e,o),{validation:"emoji",code:d.invalid_string,message:u.message}),l.dirty());else if("uuid"===u.kind)M.test(e.data)||(b(o=this._getOrReturnCtx(e,o),{validation:"uuid",code:d.invalid_string,message:u.message}),l.dirty());else if("nanoid"===u.kind)$.test(e.data)||(b(o=this._getOrReturnCtx(e,o),{validation:"nanoid",code:d.invalid_string,message:u.message}),l.dirty());else if("cuid"===u.kind)R.test(e.data)||(b(o=this._getOrReturnCtx(e,o),{validation:"cuid",code:d.invalid_string,message:u.message}),l.dirty());else if("cuid2"===u.kind)O.test(e.data)||(b(o=this._getOrReturnCtx(e,o),{validation:"cuid2",code:d.invalid_string,message:u.message}),l.dirty());else if("ulid"===u.kind)P.test(e.data)||(b(o=this._getOrReturnCtx(e,o),{validation:"ulid",code:d.invalid_string,message:u.message}),l.dirty());else if("url"===u.kind)try{new URL(e.data)}catch{b(o=this._getOrReturnCtx(e,o),{validation:"url",code:d.invalid_string,message:u.message}),l.dirty()}else"regex"===u.kind?(u.regex.lastIndex=0,u.regex.test(e.data)||(b(o=this._getOrReturnCtx(e,o),{validation:"regex",code:d.invalid_string,message:u.message}),l.dirty())):"trim"===u.kind?e.data=e.data.trim():"includes"===u.kind?e.data.includes(u.value,u.position)||(b(o=this._getOrReturnCtx(e,o),{code:d.invalid_string,validation:{includes:u.value,position:u.position},message:u.message}),l.dirty()):"toLowerCase"===u.kind?e.data=e.data.toLowerCase():"toUpperCase"===u.kind?e.data=e.data.toUpperCase():"startsWith"===u.kind?e.data.startsWith(u.value)||(b(o=this._getOrReturnCtx(e,o),{code:d.invalid_string,validation:{startsWith:u.value},message:u.message}),l.dirty()):"endsWith"===u.kind?e.data.endsWith(u.value)||(b(o=this._getOrReturnCtx(e,o),{code:d.invalid_string,validation:{endsWith:u.value},message:u.message}),l.dirty()):"datetime"===u.kind?G(u).test(e.data)||(b(o=this._getOrReturnCtx(e,o),{code:d.invalid_string,validation:"datetime",message:u.message}),l.dirty()):"date"===u.kind?K.test(e.data)||(b(o=this._getOrReturnCtx(e,o),{code:d.invalid_string,validation:"date",message:u.message}),l.dirty()):"time"===u.kind?RegExp(`^${W(u)}$`).test(e.data)||(b(o=this._getOrReturnCtx(e,o),{code:d.invalid_string,validation:"time",message:u.message}),l.dirty()):"duration"===u.kind?D.test(e.data)||(b(o=this._getOrReturnCtx(e,o),{validation:"duration",code:d.invalid_string,message:u.message}),l.dirty()):"ip"===u.kind?(t=e.data,!(("v4"===(r=u.version)||!r)&&L.test(t)||("v6"===r||!r)&&U.test(t))&&(b(o=this._getOrReturnCtx(e,o),{validation:"ip",code:d.invalid_string,message:u.message}),l.dirty())):"jwt"===u.kind?!function(e,t){if(!Z.test(e))return!1;try{let[r]=e.split("."),a=r.replace(/-/g,"+").replace(/_/g,"/").padEnd(r.length+(4-r.length%4)%4,"="),s=JSON.parse(atob(a));if("object"!=typeof s||null===s||"typ"in s&&s?.typ!=="JWT"||!s.alg||t&&s.alg!==t)return!1;return!0}catch{return!1}}(e.data,u.alg)&&(b(o=this._getOrReturnCtx(e,o),{validation:"jwt",code:d.invalid_string,message:u.message}),l.dirty()):"cidr"===u.kind?(n=e.data,!(("v4"===(i=u.version)||!i)&&F.test(n)||("v6"===i||!i)&&V.test(n))&&(b(o=this._getOrReturnCtx(e,o),{validation:"cidr",code:d.invalid_string,message:u.message}),l.dirty())):"base64"===u.kind?B.test(e.data)||(b(o=this._getOrReturnCtx(e,o),{validation:"base64",code:d.invalid_string,message:u.message}),l.dirty()):"base64url"===u.kind?J.test(e.data)||(b(o=this._getOrReturnCtx(e,o),{validation:"base64url",code:d.invalid_string,message:u.message}),l.dirty()):s.assertNever(u);return{status:l.value,value:e.data}}_regex(e,t,r){return this.refinement(t=>e.test(t),{validation:t,code:d.invalid_string,...i.errToObj(r)})}_addCheck(e){return new H({...this._def,checks:[...this._def.checks,e]})}email(e){return this._addCheck({kind:"email",...i.errToObj(e)})}url(e){return this._addCheck({kind:"url",...i.errToObj(e)})}emoji(e){return this._addCheck({kind:"emoji",...i.errToObj(e)})}uuid(e){return this._addCheck({kind:"uuid",...i.errToObj(e)})}nanoid(e){return this._addCheck({kind:"nanoid",...i.errToObj(e)})}cuid(e){return this._addCheck({kind:"cuid",...i.errToObj(e)})}cuid2(e){return this._addCheck({kind:"cuid2",...i.errToObj(e)})}ulid(e){return this._addCheck({kind:"ulid",...i.errToObj(e)})}base64(e){return this._addCheck({kind:"base64",...i.errToObj(e)})}base64url(e){return this._addCheck({kind:"base64url",...i.errToObj(e)})}jwt(e){return this._addCheck({kind:"jwt",...i.errToObj(e)})}ip(e){return this._addCheck({kind:"ip",...i.errToObj(e)})}cidr(e){return this._addCheck({kind:"cidr",...i.errToObj(e)})}datetime(e){return"string"==typeof e?this._addCheck({kind:"datetime",precision:null,offset:!1,local:!1,message:e}):this._addCheck({kind:"datetime",precision:void 0===e?.precision?null:e?.precision,offset:e?.offset??!1,local:e?.local??!1,...i.errToObj(e?.message)})}date(e){return this._addCheck({kind:"date",message:e})}time(e){return"string"==typeof e?this._addCheck({kind:"time",precision:null,message:e}):this._addCheck({kind:"time",precision:void 0===e?.precision?null:e?.precision,...i.errToObj(e?.message)})}duration(e){return this._addCheck({kind:"duration",...i.errToObj(e)})}regex(e,t){return this._addCheck({kind:"regex",regex:e,...i.errToObj(t)})}includes(e,t){return this._addCheck({kind:"includes",value:e,position:t?.position,...i.errToObj(t?.message)})}startsWith(e,t){return this._addCheck({kind:"startsWith",value:e,...i.errToObj(t)})}endsWith(e,t){return this._addCheck({kind:"endsWith",value:e,...i.errToObj(t)})}min(e,t){return this._addCheck({kind:"min",value:e,...i.errToObj(t)})}max(e,t){return this._addCheck({kind:"max",value:e,...i.errToObj(t)})}length(e,t){return this._addCheck({kind:"length",value:e,...i.errToObj(t)})}nonempty(e){return this.min(1,i.errToObj(e))}trim(){return new H({...this._def,checks:[...this._def.checks,{kind:"trim"}]})}toLowerCase(){return new H({...this._def,checks:[...this._def.checks,{kind:"toLowerCase"}]})}toUpperCase(){return new H({...this._def,checks:[...this._def.checks,{kind:"toUpperCase"}]})}get isDatetime(){return!!this._def.checks.find(e=>"datetime"===e.kind)}get isDate(){return!!this._def.checks.find(e=>"date"===e.kind)}get isTime(){return!!this._def.checks.find(e=>"time"===e.kind)}get isDuration(){return!!this._def.checks.find(e=>"duration"===e.kind)}get isEmail(){return!!this._def.checks.find(e=>"email"===e.kind)}get isURL(){return!!this._def.checks.find(e=>"url"===e.kind)}get isEmoji(){return!!this._def.checks.find(e=>"emoji"===e.kind)}get isUUID(){return!!this._def.checks.find(e=>"uuid"===e.kind)}get isNANOID(){return!!this._def.checks.find(e=>"nanoid"===e.kind)}get isCUID(){return!!this._def.checks.find(e=>"cuid"===e.kind)}get isCUID2(){return!!this._def.checks.find(e=>"cuid2"===e.kind)}get isULID(){return!!this._def.checks.find(e=>"ulid"===e.kind)}get isIP(){return!!this._def.checks.find(e=>"ip"===e.kind)}get isCIDR(){return!!this._def.checks.find(e=>"cidr"===e.kind)}get isBase64(){return!!this._def.checks.find(e=>"base64"===e.kind)}get isBase64url(){return!!this._def.checks.find(e=>"base64url"===e.kind)}get minLength(){let e=null;for(let t of this._def.checks)"min"===t.kind&&(null===e||t.value>e)&&(e=t.value);return e}get maxLength(){let e=null;for(let t of this._def.checks)"max"===t.kind&&(null===e||t.value<e)&&(e=t.value);return e}}H.create=e=>new H({checks:[],typeName:o.ZodString,coerce:e?.coerce??!1,...C(e)});class X extends N{constructor(){super(...arguments),this.min=this.gte,this.max=this.lte,this.step=this.multipleOf}_parse(e){let t;if(this._def.coerce&&(e.data=Number(e.data)),this._getType(e)!==u.number){let t=this._getOrReturnCtx(e);return b(t,{code:d.invalid_type,expected:u.number,received:t.parsedType}),x}let r=new w;for(let a of this._def.checks)"int"===a.kind?s.isInteger(e.data)||(b(t=this._getOrReturnCtx(e,t),{code:d.invalid_type,expected:"integer",received:"float",message:a.message}),r.dirty()):"min"===a.kind?(a.inclusive?e.data<a.value:e.data<=a.value)&&(b(t=this._getOrReturnCtx(e,t),{code:d.too_small,minimum:a.value,type:"number",inclusive:a.inclusive,exact:!1,message:a.message}),r.dirty()):"max"===a.kind?(a.inclusive?e.data>a.value:e.data>=a.value)&&(b(t=this._getOrReturnCtx(e,t),{code:d.too_big,maximum:a.value,type:"number",inclusive:a.inclusive,exact:!1,message:a.message}),r.dirty()):"multipleOf"===a.kind?0!==function(e,t){let r=(e.toString().split(".")[1]||"").length,a=(t.toString().split(".")[1]||"").length,s=r>a?r:a;return Number.parseInt(e.toFixed(s).replace(".",""))%Number.parseInt(t.toFixed(s).replace(".",""))/10**s}(e.data,a.value)&&(b(t=this._getOrReturnCtx(e,t),{code:d.not_multiple_of,multipleOf:a.value,message:a.message}),r.dirty()):"finite"===a.kind?Number.isFinite(e.data)||(b(t=this._getOrReturnCtx(e,t),{code:d.not_finite,message:a.message}),r.dirty()):s.assertNever(a);return{status:r.value,value:e.data}}gte(e,t){return this.setLimit("min",e,!0,i.toString(t))}gt(e,t){return this.setLimit("min",e,!1,i.toString(t))}lte(e,t){return this.setLimit("max",e,!0,i.toString(t))}lt(e,t){return this.setLimit("max",e,!1,i.toString(t))}setLimit(e,t,r,a){return new X({...this._def,checks:[...this._def.checks,{kind:e,value:t,inclusive:r,message:i.toString(a)}]})}_addCheck(e){return new X({...this._def,checks:[...this._def.checks,e]})}int(e){return this._addCheck({kind:"int",message:i.toString(e)})}positive(e){return this._addCheck({kind:"min",value:0,inclusive:!1,message:i.toString(e)})}negative(e){return this._addCheck({kind:"max",value:0,inclusive:!1,message:i.toString(e)})}nonpositive(e){return this._addCheck({kind:"max",value:0,inclusive:!0,message:i.toString(e)})}nonnegative(e){return this._addCheck({kind:"min",value:0,inclusive:!0,message:i.toString(e)})}multipleOf(e,t){return this._addCheck({kind:"multipleOf",value:e,message:i.toString(t)})}finite(e){return this._addCheck({kind:"finite",message:i.toString(e)})}safe(e){return this._addCheck({kind:"min",inclusive:!0,value:Number.MIN_SAFE_INTEGER,message:i.toString(e)})._addCheck({kind:"max",inclusive:!0,value:Number.MAX_SAFE_INTEGER,message:i.toString(e)})}get minValue(){let e=null;for(let t of this._def.checks)"min"===t.kind&&(null===e||t.value>e)&&(e=t.value);return e}get maxValue(){let e=null;for(let t of this._def.checks)"max"===t.kind&&(null===e||t.value<e)&&(e=t.value);return e}get isInt(){return!!this._def.checks.find(e=>"int"===e.kind||"multipleOf"===e.kind&&s.isInteger(e.value))}get isFinite(){let e=null,t=null;for(let r of this._def.checks){if("finite"===r.kind||"int"===r.kind||"multipleOf"===r.kind)return!0;"min"===r.kind?(null===t||r.value>t)&&(t=r.value):"max"===r.kind&&(null===e||r.value<e)&&(e=r.value)}return Number.isFinite(t)&&Number.isFinite(e)}}X.create=e=>new X({checks:[],typeName:o.ZodNumber,coerce:e?.coerce||!1,...C(e)});class Q extends N{constructor(){super(...arguments),this.min=this.gte,this.max=this.lte}_parse(e){let t;if(this._def.coerce)try{e.data=BigInt(e.data)}catch{return this._getInvalidInput(e)}if(this._getType(e)!==u.bigint)return this._getInvalidInput(e);let r=new w;for(let a of this._def.checks)"min"===a.kind?(a.inclusive?e.data<a.value:e.data<=a.value)&&(b(t=this._getOrReturnCtx(e,t),{code:d.too_small,type:"bigint",minimum:a.value,inclusive:a.inclusive,message:a.message}),r.dirty()):"max"===a.kind?(a.inclusive?e.data>a.value:e.data>=a.value)&&(b(t=this._getOrReturnCtx(e,t),{code:d.too_big,type:"bigint",maximum:a.value,inclusive:a.inclusive,message:a.message}),r.dirty()):"multipleOf"===a.kind?e.data%a.value!==BigInt(0)&&(b(t=this._getOrReturnCtx(e,t),{code:d.not_multiple_of,multipleOf:a.value,message:a.message}),r.dirty()):s.assertNever(a);return{status:r.value,value:e.data}}_getInvalidInput(e){let t=this._getOrReturnCtx(e);return b(t,{code:d.invalid_type,expected:u.bigint,received:t.parsedType}),x}gte(e,t){return this.setLimit("min",e,!0,i.toString(t))}gt(e,t){return this.setLimit("min",e,!1,i.toString(t))}lte(e,t){return this.setLimit("max",e,!0,i.toString(t))}lt(e,t){return this.setLimit("max",e,!1,i.toString(t))}setLimit(e,t,r,a){return new Q({...this._def,checks:[...this._def.checks,{kind:e,value:t,inclusive:r,message:i.toString(a)}]})}_addCheck(e){return new Q({...this._def,checks:[...this._def.checks,e]})}positive(e){return this._addCheck({kind:"min",value:BigInt(0),inclusive:!1,message:i.toString(e)})}negative(e){return this._addCheck({kind:"max",value:BigInt(0),inclusive:!1,message:i.toString(e)})}nonpositive(e){return this._addCheck({kind:"max",value:BigInt(0),inclusive:!0,message:i.toString(e)})}nonnegative(e){return this._addCheck({kind:"min",value:BigInt(0),inclusive:!0,message:i.toString(e)})}multipleOf(e,t){return this._addCheck({kind:"multipleOf",value:e,message:i.toString(t)})}get minValue(){let e=null;for(let t of this._def.checks)"min"===t.kind&&(null===e||t.value>e)&&(e=t.value);return e}get maxValue(){let e=null;for(let t of this._def.checks)"max"===t.kind&&(null===e||t.value<e)&&(e=t.value);return e}}Q.create=e=>new Q({checks:[],typeName:o.ZodBigInt,coerce:e?.coerce??!1,...C(e)});class ee extends N{_parse(e){if(this._def.coerce&&(e.data=!!e.data),this._getType(e)!==u.boolean){let t=this._getOrReturnCtx(e);return b(t,{code:d.invalid_type,expected:u.boolean,received:t.parsedType}),x}return T(e.data)}}ee.create=e=>new ee({typeName:o.ZodBoolean,coerce:e?.coerce||!1,...C(e)});class et extends N{_parse(e){let t;if(this._def.coerce&&(e.data=new Date(e.data)),this._getType(e)!==u.date){let t=this._getOrReturnCtx(e);return b(t,{code:d.invalid_type,expected:u.date,received:t.parsedType}),x}if(Number.isNaN(e.data.getTime()))return b(this._getOrReturnCtx(e),{code:d.invalid_date}),x;let r=new w;for(let a of this._def.checks)"min"===a.kind?e.data.getTime()<a.value&&(b(t=this._getOrReturnCtx(e,t),{code:d.too_small,message:a.message,inclusive:!0,exact:!1,minimum:a.value,type:"date"}),r.dirty()):"max"===a.kind?e.data.getTime()>a.value&&(b(t=this._getOrReturnCtx(e,t),{code:d.too_big,message:a.message,inclusive:!0,exact:!1,maximum:a.value,type:"date"}),r.dirty()):s.assertNever(a);return{status:r.value,value:new Date(e.data.getTime())}}_addCheck(e){return new et({...this._def,checks:[...this._def.checks,e]})}min(e,t){return this._addCheck({kind:"min",value:e.getTime(),message:i.toString(t)})}max(e,t){return this._addCheck({kind:"max",value:e.getTime(),message:i.toString(t)})}get minDate(){let e=null;for(let t of this._def.checks)"min"===t.kind&&(null===e||t.value>e)&&(e=t.value);return null!=e?new Date(e):null}get maxDate(){let e=null;for(let t of this._def.checks)"max"===t.kind&&(null===e||t.value<e)&&(e=t.value);return null!=e?new Date(e):null}}et.create=e=>new et({checks:[],coerce:e?.coerce||!1,typeName:o.ZodDate,...C(e)});class er extends N{_parse(e){if(this._getType(e)!==u.symbol){let t=this._getOrReturnCtx(e);return b(t,{code:d.invalid_type,expected:u.symbol,received:t.parsedType}),x}return T(e.data)}}er.create=e=>new er({typeName:o.ZodSymbol,...C(e)});class ea extends N{_parse(e){if(this._getType(e)!==u.undefined){let t=this._getOrReturnCtx(e);return b(t,{code:d.invalid_type,expected:u.undefined,received:t.parsedType}),x}return T(e.data)}}ea.create=e=>new ea({typeName:o.ZodUndefined,...C(e)});class es extends N{_parse(e){if(this._getType(e)!==u.null){let t=this._getOrReturnCtx(e);return b(t,{code:d.invalid_type,expected:u.null,received:t.parsedType}),x}return T(e.data)}}es.create=e=>new es({typeName:o.ZodNull,...C(e)});class en extends N{constructor(){super(...arguments),this._any=!0}_parse(e){return T(e.data)}}en.create=e=>new en({typeName:o.ZodAny,...C(e)});class ei extends N{constructor(){super(...arguments),this._unknown=!0}_parse(e){return T(e.data)}}ei.create=e=>new ei({typeName:o.ZodUnknown,...C(e)});class eo extends N{_parse(e){let t=this._getOrReturnCtx(e);return b(t,{code:d.invalid_type,expected:u.never,received:t.parsedType}),x}}eo.create=e=>new eo({typeName:o.ZodNever,...C(e)});class el extends N{_parse(e){if(this._getType(e)!==u.undefined){let t=this._getOrReturnCtx(e);return b(t,{code:d.invalid_type,expected:u.void,received:t.parsedType}),x}return T(e.data)}}el.create=e=>new el({typeName:o.ZodVoid,...C(e)});class eu extends N{_parse(e){let{ctx:t,status:r}=this._processInputParams(e),a=this._def;if(t.parsedType!==u.array)return b(t,{code:d.invalid_type,expected:u.array,received:t.parsedType}),x;if(null!==a.exactLength){let e=t.data.length>a.exactLength.value,s=t.data.length<a.exactLength.value;(e||s)&&(b(t,{code:e?d.too_big:d.too_small,minimum:s?a.exactLength.value:void 0,maximum:e?a.exactLength.value:void 0,type:"array",inclusive:!0,exact:!0,message:a.exactLength.message}),r.dirty())}if(null!==a.minLength&&t.data.length<a.minLength.value&&(b(t,{code:d.too_small,minimum:a.minLength.value,type:"array",inclusive:!0,exact:!1,message:a.minLength.message}),r.dirty()),null!==a.maxLength&&t.data.length>a.maxLength.value&&(b(t,{code:d.too_big,maximum:a.maxLength.value,type:"array",inclusive:!0,exact:!1,message:a.maxLength.message}),r.dirty()),t.common.async)return Promise.all([...t.data].map((e,r)=>a.type._parseAsync(new A(t,e,t.path,r)))).then(e=>w.mergeArray(r,e));let s=[...t.data].map((e,r)=>a.type._parseSync(new A(t,e,t.path,r)));return w.mergeArray(r,s)}get element(){return this._def.type}min(e,t){return new eu({...this._def,minLength:{value:e,message:i.toString(t)}})}max(e,t){return new eu({...this._def,maxLength:{value:e,message:i.toString(t)}})}length(e,t){return new eu({...this._def,exactLength:{value:e,message:i.toString(t)}})}nonempty(e){return this.min(1,e)}}eu.create=(e,t)=>new eu({type:e,minLength:null,maxLength:null,exactLength:null,typeName:o.ZodArray,...C(t)});class ec extends N{constructor(){super(...arguments),this._cached=null,this.nonstrict=this.passthrough,this.augment=this.extend}_getCached(){if(null!==this._cached)return this._cached;let e=this._def.shape(),t=s.objectKeys(e);return this._cached={shape:e,keys:t},this._cached}_parse(e){if(this._getType(e)!==u.object){let t=this._getOrReturnCtx(e);return b(t,{code:d.invalid_type,expected:u.object,received:t.parsedType}),x}let{status:t,ctx:r}=this._processInputParams(e),{shape:a,keys:s}=this._getCached(),n=[];if(!(this._def.catchall instanceof eo&&"strip"===this._def.unknownKeys))for(let e in r.data)s.includes(e)||n.push(e);let i=[];for(let e of s){let t=a[e],s=r.data[e];i.push({key:{status:"valid",value:e},value:t._parse(new A(r,s,r.path,e)),alwaysSet:e in r.data})}if(this._def.catchall instanceof eo){let e=this._def.unknownKeys;if("passthrough"===e)for(let e of n)i.push({key:{status:"valid",value:e},value:{status:"valid",value:r.data[e]}});else if("strict"===e)n.length>0&&(b(r,{code:d.unrecognized_keys,keys:n}),t.dirty());else if("strip"===e);else throw Error("Internal ZodObject error: invalid unknownKeys value.")}else{let e=this._def.catchall;for(let t of n){let a=r.data[t];i.push({key:{status:"valid",value:t},value:e._parse(new A(r,a,r.path,t)),alwaysSet:t in r.data})}}return r.common.async?Promise.resolve().then(async()=>{let e=[];for(let t of i){let r=await t.key,a=await t.value;e.push({key:r,value:a,alwaysSet:t.alwaysSet})}return e}).then(e=>w.mergeObjectSync(t,e)):w.mergeObjectSync(t,i)}get shape(){return this._def.shape()}strict(e){return i.errToObj,new ec({...this._def,unknownKeys:"strict",...void 0!==e?{errorMap:(t,r)=>{let a=this._def.errorMap?.(t,r).message??r.defaultError;return"unrecognized_keys"===t.code?{message:i.errToObj(e).message??a}:{message:a}}}:{}})}strip(){return new ec({...this._def,unknownKeys:"strip"})}passthrough(){return new ec({...this._def,unknownKeys:"passthrough"})}extend(e){return new ec({...this._def,shape:()=>({...this._def.shape(),...e})})}merge(e){return new ec({unknownKeys:e._def.unknownKeys,catchall:e._def.catchall,shape:()=>({...this._def.shape(),...e._def.shape()}),typeName:o.ZodObject})}setKey(e,t){return this.augment({[e]:t})}catchall(e){return new ec({...this._def,catchall:e})}pick(e){let t={};for(let r of s.objectKeys(e))e[r]&&this.shape[r]&&(t[r]=this.shape[r]);return new ec({...this._def,shape:()=>t})}omit(e){let t={};for(let r of s.objectKeys(this.shape))e[r]||(t[r]=this.shape[r]);return new ec({...this._def,shape:()=>t})}deepPartial(){return function e(t){if(t instanceof ec){let r={};for(let a in t.shape){let s=t.shape[a];r[a]=ez.create(e(s))}return new ec({...t._def,shape:()=>r})}if(t instanceof eu)return new eu({...t._def,type:e(t.element)});if(t instanceof ez)return ez.create(e(t.unwrap()));if(t instanceof eE)return eE.create(e(t.unwrap()));if(t instanceof ef)return ef.create(t.items.map(t=>e(t)));else return t}(this)}partial(e){let t={};for(let r of s.objectKeys(this.shape)){let a=this.shape[r];e&&!e[r]?t[r]=a:t[r]=a.optional()}return new ec({...this._def,shape:()=>t})}required(e){let t={};for(let r of s.objectKeys(this.shape))if(e&&!e[r])t[r]=this.shape[r];else{let e=this.shape[r];for(;e instanceof ez;)e=e._def.innerType;t[r]=e}return new ec({...this._def,shape:()=>t})}keyof(){return ex(s.objectKeys(this.shape))}}ec.create=(e,t)=>new ec({shape:()=>e,unknownKeys:"strip",catchall:eo.create(),typeName:o.ZodObject,...C(t)}),ec.strictCreate=(e,t)=>new ec({shape:()=>e,unknownKeys:"strict",catchall:eo.create(),typeName:o.ZodObject,...C(t)}),ec.lazycreate=(e,t)=>new ec({shape:e,unknownKeys:"strip",catchall:eo.create(),typeName:o.ZodObject,...C(t)});class ed extends N{_parse(e){let{ctx:t}=this._processInputParams(e),r=this._def.options;if(t.common.async)return Promise.all(r.map(async e=>{let r={...t,common:{...t.common,issues:[]},parent:null};return{result:await e._parseAsync({data:t.data,path:t.path,parent:r}),ctx:r}})).then(function(e){for(let t of e)if("valid"===t.result.status)return t.result;for(let r of e)if("dirty"===r.result.status)return t.common.issues.push(...r.ctx.common.issues),r.result;let r=e.map(e=>new m(e.ctx.common.issues));return b(t,{code:d.invalid_union,unionErrors:r}),x});{let e;let a=[];for(let s of r){let r={...t,common:{...t.common,issues:[]},parent:null},n=s._parseSync({data:t.data,path:t.path,parent:r});if("valid"===n.status)return n;"dirty"!==n.status||e||(e={result:n,ctx:r}),r.common.issues.length&&a.push(r.common.issues)}if(e)return t.common.issues.push(...e.ctx.common.issues),e.result;let s=a.map(e=>new m(e));return b(t,{code:d.invalid_union,unionErrors:s}),x}}get options(){return this._def.options}}ed.create=(e,t)=>new ed({options:e,typeName:o.ZodUnion,...C(t)});let ep=e=>{if(e instanceof eb)return ep(e.schema);if(e instanceof eS)return ep(e.innerType());if(e instanceof ew)return[e.value];if(e instanceof ek)return e.options;if(e instanceof eT)return s.objectValues(e.enum);else if(e instanceof eA)return ep(e._def.innerType);else if(e instanceof ea)return[void 0];else if(e instanceof es)return[null];else if(e instanceof ez)return[void 0,...ep(e.unwrap())];else if(e instanceof eE)return[null,...ep(e.unwrap())];else if(e instanceof eR)return ep(e.unwrap());else if(e instanceof eP)return ep(e.unwrap());else if(e instanceof ej)return ep(e._def.innerType);else return[]};class em extends N{_parse(e){let{ctx:t}=this._processInputParams(e);if(t.parsedType!==u.object)return b(t,{code:d.invalid_type,expected:u.object,received:t.parsedType}),x;let r=this.discriminator,a=t.data[r],s=this.optionsMap.get(a);return s?t.common.async?s._parseAsync({data:t.data,path:t.path,parent:t}):s._parseSync({data:t.data,path:t.path,parent:t}):(b(t,{code:d.invalid_union_discriminator,options:Array.from(this.optionsMap.keys()),path:[r]}),x)}get discriminator(){return this._def.discriminator}get options(){return this._def.options}get optionsMap(){return this._def.optionsMap}static create(e,t,r){let a=new Map;for(let r of t){let t=ep(r.shape[e]);if(!t.length)throw Error(`A discriminator value for key \`${e}\` could not be extracted from all schema options`);for(let s of t){if(a.has(s))throw Error(`Discriminator property ${String(e)} has duplicate value ${String(s)}`);a.set(s,r)}}return new em({typeName:o.ZodDiscriminatedUnion,discriminator:e,options:t,optionsMap:a,...C(r)})}}class eh extends N{_parse(e){let{status:t,ctx:r}=this._processInputParams(e),a=(e,a)=>{if(I(e)||I(a))return x;let n=function e(t,r){let a=c(t),n=c(r);if(t===r)return{valid:!0,data:t};if(a===u.object&&n===u.object){let a=s.objectKeys(r),n=s.objectKeys(t).filter(e=>-1!==a.indexOf(e)),i={...t,...r};for(let a of n){let s=e(t[a],r[a]);if(!s.valid)return{valid:!1};i[a]=s.data}return{valid:!0,data:i}}if(a===u.array&&n===u.array){if(t.length!==r.length)return{valid:!1};let a=[];for(let s=0;s<t.length;s++){let n=e(t[s],r[s]);if(!n.valid)return{valid:!1};a.push(n.data)}return{valid:!0,data:a}}if(a===u.date&&n===u.date&&+t==+r)return{valid:!0,data:t};return{valid:!1}}(e.value,a.value);return n.valid?((S(e)||S(a))&&t.dirty(),{status:t.value,value:n.data}):(b(r,{code:d.invalid_intersection_types}),x)};return r.common.async?Promise.all([this._def.left._parseAsync({data:r.data,path:r.path,parent:r}),this._def.right._parseAsync({data:r.data,path:r.path,parent:r})]).then(([e,t])=>a(e,t)):a(this._def.left._parseSync({data:r.data,path:r.path,parent:r}),this._def.right._parseSync({data:r.data,path:r.path,parent:r}))}}eh.create=(e,t,r)=>new eh({left:e,right:t,typeName:o.ZodIntersection,...C(r)});class ef extends N{_parse(e){let{status:t,ctx:r}=this._processInputParams(e);if(r.parsedType!==u.array)return b(r,{code:d.invalid_type,expected:u.array,received:r.parsedType}),x;if(r.data.length<this._def.items.length)return b(r,{code:d.too_small,minimum:this._def.items.length,inclusive:!0,exact:!1,type:"array"}),x;!this._def.rest&&r.data.length>this._def.items.length&&(b(r,{code:d.too_big,maximum:this._def.items.length,inclusive:!0,exact:!1,type:"array"}),t.dirty());let a=[...r.data].map((e,t)=>{let a=this._def.items[t]||this._def.rest;return a?a._parse(new A(r,e,r.path,t)):null}).filter(e=>!!e);return r.common.async?Promise.all(a).then(e=>w.mergeArray(t,e)):w.mergeArray(t,a)}get items(){return this._def.items}rest(e){return new ef({...this._def,rest:e})}}ef.create=(e,t)=>{if(!Array.isArray(e))throw Error("You must pass an array of schemas to z.tuple([ ... ])");return new ef({items:e,typeName:o.ZodTuple,rest:null,...C(t)})};class eg extends N{get keySchema(){return this._def.keyType}get valueSchema(){return this._def.valueType}_parse(e){let{status:t,ctx:r}=this._processInputParams(e);if(r.parsedType!==u.object)return b(r,{code:d.invalid_type,expected:u.object,received:r.parsedType}),x;let a=[],s=this._def.keyType,n=this._def.valueType;for(let e in r.data)a.push({key:s._parse(new A(r,e,r.path,e)),value:n._parse(new A(r,r.data[e],r.path,e)),alwaysSet:e in r.data});return r.common.async?w.mergeObjectAsync(t,a):w.mergeObjectSync(t,a)}get element(){return this._def.valueType}static create(e,t,r){return new eg(t instanceof N?{keyType:e,valueType:t,typeName:o.ZodRecord,...C(r)}:{keyType:H.create(),valueType:e,typeName:o.ZodRecord,...C(t)})}}class ey extends N{get keySchema(){return this._def.keyType}get valueSchema(){return this._def.valueType}_parse(e){let{status:t,ctx:r}=this._processInputParams(e);if(r.parsedType!==u.map)return b(r,{code:d.invalid_type,expected:u.map,received:r.parsedType}),x;let a=this._def.keyType,s=this._def.valueType,n=[...r.data.entries()].map(([e,t],n)=>({key:a._parse(new A(r,e,r.path,[n,"key"])),value:s._parse(new A(r,t,r.path,[n,"value"]))}));if(r.common.async){let e=new Map;return Promise.resolve().then(async()=>{for(let r of n){let a=await r.key,s=await r.value;if("aborted"===a.status||"aborted"===s.status)return x;("dirty"===a.status||"dirty"===s.status)&&t.dirty(),e.set(a.value,s.value)}return{status:t.value,value:e}})}{let e=new Map;for(let r of n){let a=r.key,s=r.value;if("aborted"===a.status||"aborted"===s.status)return x;("dirty"===a.status||"dirty"===s.status)&&t.dirty(),e.set(a.value,s.value)}return{status:t.value,value:e}}}}ey.create=(e,t,r)=>new ey({valueType:t,keyType:e,typeName:o.ZodMap,...C(r)});class ev extends N{_parse(e){let{status:t,ctx:r}=this._processInputParams(e);if(r.parsedType!==u.set)return b(r,{code:d.invalid_type,expected:u.set,received:r.parsedType}),x;let a=this._def;null!==a.minSize&&r.data.size<a.minSize.value&&(b(r,{code:d.too_small,minimum:a.minSize.value,type:"set",inclusive:!0,exact:!1,message:a.minSize.message}),t.dirty()),null!==a.maxSize&&r.data.size>a.maxSize.value&&(b(r,{code:d.too_big,maximum:a.maxSize.value,type:"set",inclusive:!0,exact:!1,message:a.maxSize.message}),t.dirty());let s=this._def.valueType;function n(e){let r=new Set;for(let a of e){if("aborted"===a.status)return x;"dirty"===a.status&&t.dirty(),r.add(a.value)}return{status:t.value,value:r}}let i=[...r.data.values()].map((e,t)=>s._parse(new A(r,e,r.path,t)));return r.common.async?Promise.all(i).then(e=>n(e)):n(i)}min(e,t){return new ev({...this._def,minSize:{value:e,message:i.toString(t)}})}max(e,t){return new ev({...this._def,maxSize:{value:e,message:i.toString(t)}})}size(e,t){return this.min(e,t).max(e,t)}nonempty(e){return this.min(1,e)}}ev.create=(e,t)=>new ev({valueType:e,minSize:null,maxSize:null,typeName:o.ZodSet,...C(t)});class e_ extends N{constructor(){super(...arguments),this.validate=this.implement}_parse(e){let{ctx:t}=this._processInputParams(e);if(t.parsedType!==u.function)return b(t,{code:d.invalid_type,expected:u.function,received:t.parsedType}),x;function r(e,r){return v({data:e,path:t.path,errorMaps:[t.common.contextualErrorMap,t.schemaErrorMap,f,h].filter(e=>!!e),issueData:{code:d.invalid_arguments,argumentsError:r}})}function a(e,r){return v({data:e,path:t.path,errorMaps:[t.common.contextualErrorMap,t.schemaErrorMap,f,h].filter(e=>!!e),issueData:{code:d.invalid_return_type,returnTypeError:r}})}let s={errorMap:t.common.contextualErrorMap},n=t.data;if(this._def.returns instanceof eI){let e=this;return T(async function(...t){let i=new m([]),o=await e._def.args.parseAsync(t,s).catch(e=>{throw i.addIssue(r(t,e)),i}),l=await Reflect.apply(n,this,o);return await e._def.returns._def.type.parseAsync(l,s).catch(e=>{throw i.addIssue(a(l,e)),i})})}{let e=this;return T(function(...t){let i=e._def.args.safeParse(t,s);if(!i.success)throw new m([r(t,i.error)]);let o=Reflect.apply(n,this,i.data),l=e._def.returns.safeParse(o,s);if(!l.success)throw new m([a(o,l.error)]);return l.data})}}parameters(){return this._def.args}returnType(){return this._def.returns}args(...e){return new e_({...this._def,args:ef.create(e).rest(ei.create())})}returns(e){return new e_({...this._def,returns:e})}implement(e){return this.parse(e)}strictImplement(e){return this.parse(e)}static create(e,t,r){return new e_({args:e||ef.create([]).rest(ei.create()),returns:t||ei.create(),typeName:o.ZodFunction,...C(r)})}}class eb extends N{get schema(){return this._def.getter()}_parse(e){let{ctx:t}=this._processInputParams(e);return this._def.getter()._parse({data:t.data,path:t.path,parent:t})}}eb.create=(e,t)=>new eb({getter:e,typeName:o.ZodLazy,...C(t)});class ew extends N{_parse(e){if(e.data!==this._def.value){let t=this._getOrReturnCtx(e);return b(t,{received:t.data,code:d.invalid_literal,expected:this._def.value}),x}return{status:"valid",value:e.data}}get value(){return this._def.value}}function ex(e,t){return new ek({values:e,typeName:o.ZodEnum,...C(t)})}ew.create=(e,t)=>new ew({value:e,typeName:o.ZodLiteral,...C(t)});class ek extends N{_parse(e){if("string"!=typeof e.data){let t=this._getOrReturnCtx(e),r=this._def.values;return b(t,{expected:s.joinValues(r),received:t.parsedType,code:d.invalid_type}),x}if(this._cache||(this._cache=new Set(this._def.values)),!this._cache.has(e.data)){let t=this._getOrReturnCtx(e),r=this._def.values;return b(t,{received:t.data,code:d.invalid_enum_value,options:r}),x}return T(e.data)}get options(){return this._def.values}get enum(){let e={};for(let t of this._def.values)e[t]=t;return e}get Values(){let e={};for(let t of this._def.values)e[t]=t;return e}get Enum(){let e={};for(let t of this._def.values)e[t]=t;return e}extract(e,t=this._def){return ek.create(e,{...this._def,...t})}exclude(e,t=this._def){return ek.create(this.options.filter(t=>!e.includes(t)),{...this._def,...t})}}ek.create=ex;class eT extends N{_parse(e){let t=s.getValidEnumValues(this._def.values),r=this._getOrReturnCtx(e);if(r.parsedType!==u.string&&r.parsedType!==u.number){let e=s.objectValues(t);return b(r,{expected:s.joinValues(e),received:r.parsedType,code:d.invalid_type}),x}if(this._cache||(this._cache=new Set(s.getValidEnumValues(this._def.values))),!this._cache.has(e.data)){let e=s.objectValues(t);return b(r,{received:r.data,code:d.invalid_enum_value,options:e}),x}return T(e.data)}get enum(){return this._def.values}}eT.create=(e,t)=>new eT({values:e,typeName:o.ZodNativeEnum,...C(t)});class eI extends N{unwrap(){return this._def.type}_parse(e){let{ctx:t}=this._processInputParams(e);return t.parsedType!==u.promise&&!1===t.common.async?(b(t,{code:d.invalid_type,expected:u.promise,received:t.parsedType}),x):T((t.parsedType===u.promise?t.data:Promise.resolve(t.data)).then(e=>this._def.type.parseAsync(e,{path:t.path,errorMap:t.common.contextualErrorMap})))}}eI.create=(e,t)=>new eI({type:e,typeName:o.ZodPromise,...C(t)});class eS extends N{innerType(){return this._def.schema}sourceType(){return this._def.schema._def.typeName===o.ZodEffects?this._def.schema.sourceType():this._def.schema}_parse(e){let{status:t,ctx:r}=this._processInputParams(e),a=this._def.effect||null,n={addIssue:e=>{b(r,e),e.fatal?t.abort():t.dirty()},get path(){return r.path}};if(n.addIssue=n.addIssue.bind(n),"preprocess"===a.type){let e=a.transform(r.data,n);if(r.common.async)return Promise.resolve(e).then(async e=>{if("aborted"===t.value)return x;let a=await this._def.schema._parseAsync({data:e,path:r.path,parent:r});return"aborted"===a.status?x:"dirty"===a.status||"dirty"===t.value?k(a.value):a});{if("aborted"===t.value)return x;let a=this._def.schema._parseSync({data:e,path:r.path,parent:r});return"aborted"===a.status?x:"dirty"===a.status||"dirty"===t.value?k(a.value):a}}if("refinement"===a.type){let e=e=>{let t=a.refinement(e,n);if(r.common.async)return Promise.resolve(t);if(t instanceof Promise)throw Error("Async refinement encountered during synchronous parse operation. Use .parseAsync instead.");return e};if(!1!==r.common.async)return this._def.schema._parseAsync({data:r.data,path:r.path,parent:r}).then(r=>"aborted"===r.status?x:("dirty"===r.status&&t.dirty(),e(r.value).then(()=>({status:t.value,value:r.value}))));{let a=this._def.schema._parseSync({data:r.data,path:r.path,parent:r});return"aborted"===a.status?x:("dirty"===a.status&&t.dirty(),e(a.value),{status:t.value,value:a.value})}}if("transform"===a.type){if(!1!==r.common.async)return this._def.schema._parseAsync({data:r.data,path:r.path,parent:r}).then(e=>z(e)?Promise.resolve(a.transform(e.value,n)).then(e=>({status:t.value,value:e})):x);{let e=this._def.schema._parseSync({data:r.data,path:r.path,parent:r});if(!z(e))return x;let s=a.transform(e.value,n);if(s instanceof Promise)throw Error("Asynchronous transform encountered during synchronous parse operation. Use .parseAsync instead.");return{status:t.value,value:s}}}s.assertNever(a)}}eS.create=(e,t,r)=>new eS({schema:e,typeName:o.ZodEffects,effect:t,...C(r)}),eS.createWithPreprocess=(e,t,r)=>new eS({schema:t,effect:{type:"preprocess",transform:e},typeName:o.ZodEffects,...C(r)});class ez extends N{_parse(e){return this._getType(e)===u.undefined?T(void 0):this._def.innerType._parse(e)}unwrap(){return this._def.innerType}}ez.create=(e,t)=>new ez({innerType:e,typeName:o.ZodOptional,...C(t)});class eE extends N{_parse(e){return this._getType(e)===u.null?T(null):this._def.innerType._parse(e)}unwrap(){return this._def.innerType}}eE.create=(e,t)=>new eE({innerType:e,typeName:o.ZodNullable,...C(t)});class eA extends N{_parse(e){let{ctx:t}=this._processInputParams(e),r=t.data;return t.parsedType===u.undefined&&(r=this._def.defaultValue()),this._def.innerType._parse({data:r,path:t.path,parent:t})}removeDefault(){return this._def.innerType}}eA.create=(e,t)=>new eA({innerType:e,typeName:o.ZodDefault,defaultValue:"function"==typeof t.default?t.default:()=>t.default,...C(t)});class ej extends N{_parse(e){let{ctx:t}=this._processInputParams(e),r={...t,common:{...t.common,issues:[]}},a=this._def.innerType._parse({data:r.data,path:r.path,parent:{...r}});return E(a)?a.then(e=>({status:"valid",value:"valid"===e.status?e.value:this._def.catchValue({get error(){return new m(r.common.issues)},input:r.data})})):{status:"valid",value:"valid"===a.status?a.value:this._def.catchValue({get error(){return new m(r.common.issues)},input:r.data})}}removeCatch(){return this._def.innerType}}ej.create=(e,t)=>new ej({innerType:e,typeName:o.ZodCatch,catchValue:"function"==typeof t.catch?t.catch:()=>t.catch,...C(t)});class eC extends N{_parse(e){if(this._getType(e)!==u.nan){let t=this._getOrReturnCtx(e);return b(t,{code:d.invalid_type,expected:u.nan,received:t.parsedType}),x}return{status:"valid",value:e.data}}}eC.create=e=>new eC({typeName:o.ZodNaN,...C(e)});let eN=Symbol("zod_brand");class eR extends N{_parse(e){let{ctx:t}=this._processInputParams(e),r=t.data;return this._def.type._parse({data:r,path:t.path,parent:t})}unwrap(){return this._def.type}}class eO extends N{_parse(e){let{status:t,ctx:r}=this._processInputParams(e);if(r.common.async)return(async()=>{let e=await this._def.in._parseAsync({data:r.data,path:r.path,parent:r});return"aborted"===e.status?x:"dirty"===e.status?(t.dirty(),k(e.value)):this._def.out._parseAsync({data:e.value,path:r.path,parent:r})})();{let e=this._def.in._parseSync({data:r.data,path:r.path,parent:r});return"aborted"===e.status?x:"dirty"===e.status?(t.dirty(),{status:"dirty",value:e.value}):this._def.out._parseSync({data:e.value,path:r.path,parent:r})}}static create(e,t){return new eO({in:e,out:t,typeName:o.ZodPipeline})}}class eP extends N{_parse(e){let t=this._def.innerType._parse(e),r=e=>(z(e)&&(e.value=Object.freeze(e.value)),e);return E(t)?t.then(e=>r(e)):r(t)}unwrap(){return this._def.innerType}}function eM(e,t){let r="function"==typeof e?e(t):"string"==typeof e?{message:e}:e;return"string"==typeof r?{message:r}:r}function e$(e,t={},r){return e?en.create().superRefine((a,s)=>{let n=e(a);if(n instanceof Promise)return n.then(e=>{if(!e){let e=eM(t,a),n=e.fatal??r??!0;s.addIssue({code:"custom",...e,fatal:n})}});if(!n){let e=eM(t,a),n=e.fatal??r??!0;s.addIssue({code:"custom",...e,fatal:n})}}):en.create()}eP.create=(e,t)=>new eP({innerType:e,typeName:o.ZodReadonly,...C(t)});let eZ={object:ec.lazycreate};!function(e){e.ZodString="ZodString",e.ZodNumber="ZodNumber",e.ZodNaN="ZodNaN",e.ZodBigInt="ZodBigInt",e.ZodBoolean="ZodBoolean",e.ZodDate="ZodDate",e.ZodSymbol="ZodSymbol",e.ZodUndefined="ZodUndefined",e.ZodNull="ZodNull",e.ZodAny="ZodAny",e.ZodUnknown="ZodUnknown",e.ZodNever="ZodNever",e.ZodVoid="ZodVoid",e.ZodArray="ZodArray",e.ZodObject="ZodObject",e.ZodUnion="ZodUnion",e.ZodDiscriminatedUnion="ZodDiscriminatedUnion",e.ZodIntersection="ZodIntersection",e.ZodTuple="ZodTuple",e.ZodRecord="ZodRecord",e.ZodMap="ZodMap",e.ZodSet="ZodSet",e.ZodFunction="ZodFunction",e.ZodLazy="ZodLazy",e.ZodLiteral="ZodLiteral",e.ZodEnum="ZodEnum",e.ZodEffects="ZodEffects",e.ZodNativeEnum="ZodNativeEnum",e.ZodOptional="ZodOptional",e.ZodNullable="ZodNullable",e.ZodDefault="ZodDefault",e.ZodCatch="ZodCatch",e.ZodPromise="ZodPromise",e.ZodBranded="ZodBranded",e.ZodPipeline="ZodPipeline",e.ZodReadonly="ZodReadonly"}(o||(o={}));let eD=(e,t={message:`Input not instance of ${e.name}`})=>e$(t=>t instanceof e,t),eq=H.create,eL=X.create,eF=eC.create,eU=Q.create,eV=ee.create,eB=et.create,eJ=er.create,eY=ea.create,eK=es.create,eW=en.create,eG=ei.create,eH=eo.create,eX=el.create,eQ=eu.create,e0=ec.create,e1=ec.strictCreate,e4=ed.create,e9=em.create,e2=eh.create,e6=ef.create,e3=eg.create,e5=ey.create,e8=ev.create,e7=e_.create,te=eb.create,tt=ew.create,tr=ek.create,ta=eT.create,ts=eI.create,tn=eS.create,ti=ez.create,to=eE.create,tl=eS.createWithPreprocess,tu=eO.create,tc=()=>eq().optional(),td=()=>eL().optional(),tp=()=>eV().optional(),tm={string:e=>H.create({...e,coerce:!0}),number:e=>X.create({...e,coerce:!0}),boolean:e=>ee.create({...e,coerce:!0}),bigint:e=>Q.create({...e,coerce:!0}),date:e=>et.create({...e,coerce:!0})},th=x},41902:(e,t,r)=>{let a;r.d(t,{gr:()=>eX,Ol:()=>rP,Df:()=>rh,gM:()=>rR,ae:()=>rM});var s,n,i,o,l,u,c,d,p,m,h,f,g,y,v,_,b=r(49715),w=r(92229),x=r(42388),k=r(92451),T=r(12206);let I=Symbol("Let zodToJsonSchema decide on which parser to use"),S={name:void 0,$refStrategy:"root",basePath:["#"],effectStrategy:"input",pipeStrategy:"all",dateStrategy:"format:date-time",mapStrategy:"entries",removeAdditionalStrategy:"passthrough",allowedAdditionalProperties:!0,rejectedAdditionalProperties:!1,definitionPath:"definitions",target:"jsonSchema7",strictUnions:!1,definitions:{},errorMessages:!1,markdownDescription:!1,patternStrategy:"escape",applyRegexFlags:!1,emailStrategy:"format:email",base64Strategy:"contentEncoding:base64",nameStrategy:"ref",openAiAnyTypeName:"OpenAiAnyType"},z=e=>"string"==typeof e?{...S,name:e}:{...S,...e},E=e=>{let t=z(e),r=void 0!==t.name?[...t.basePath,t.definitionPath,t.name]:t.basePath;return{...t,flags:{hasReferencedOpenAiAnyType:!1},currentPath:r,propertyPath:void 0,seen:new Map(Object.entries(t.definitions).map(([e,r])=>[r._def,{def:r._def,path:[...t.basePath,t.definitionPath,e],jsonSchema:void 0}]))}},A=(e,t)=>{let r=0;for(;r<e.length&&r<t.length&&e[r]===t[r];r++);return[(e.length-r).toString(),...t.slice(r)].join("/")};function j(e){if("openAi"!==e.target)return{};let t=[...e.basePath,e.definitionPath,e.openAiAnyTypeName];return e.flags.hasReferencedOpenAiAnyType=!0,{$ref:"relative"===e.$refStrategy?A(t,e.currentPath):t.join("/")}}function C(e,t,r,a){a?.errorMessages&&r&&(e.errorMessage={...e.errorMessage,[t]:r})}function N(e,t,r,a,s){e[t]=r,C(e,t,a,s)}function R(e,t){return H(e.type._def,t)}let O=(e,t)=>H(e.innerType._def,t),P=(e,t)=>{let r={type:"integer",format:"unix-time"};if("openApi3"===t.target)return r;for(let a of e.checks)switch(a.kind){case"min":N(r,"minimum",a.value,a.message,t);break;case"max":N(r,"maximum",a.value,a.message,t)}return r},M=e=>(!("type"in e)||"string"!==e.type)&&"allOf"in e,$={cuid:/^[cC][^\s-]{8,}$/,cuid2:/^[0-9a-z]+$/,ulid:/^[0-9A-HJKMNP-TV-Z]{26}$/,email:/^(?!\.)(?!.*\.\.)([a-zA-Z0-9_'+\-\.]*)[a-zA-Z0-9_+-]@([a-zA-Z0-9][a-zA-Z0-9\-]*\.)+[a-zA-Z]{2,}$/,emoji:()=>(void 0===a&&(a=RegExp("^(\\p{Extended_Pictographic}|\\p{Emoji_Component})+$","u")),a),ipv4Cidr:/^(?:(?:25[0-5]|2[0-4][0-9]|1[0-9][0-9]|[1-9][0-9]|[0-9])\.){3}(?:25[0-5]|2[0-4][0-9]|1[0-9][0-9]|[1-9][0-9]|[0-9])\/(3[0-2]|[12]?[0-9])$/,ipv6Cidr:/^(([0-9a-fA-F]{1,4}:){7,7}[0-9a-fA-F]{1,4}|([0-9a-fA-F]{1,4}:){1,7}:|([0-9a-fA-F]{1,4}:){1,6}:[0-9a-fA-F]{1,4}|([0-9a-fA-F]{1,4}:){1,5}(:[0-9a-fA-F]{1,4}){1,2}|([0-9a-fA-F]{1,4}:){1,4}(:[0-9a-fA-F]{1,4}){1,3}|([0-9a-fA-F]{1,4}:){1,3}(:[0-9a-fA-F]{1,4}){1,4}|([0-9a-fA-F]{1,4}:){1,2}(:[0-9a-fA-F]{1,4}){1,5}|[0-9a-fA-F]{1,4}:((:[0-9a-fA-F]{1,4}){1,6})|:((:[0-9a-fA-F]{1,4}){1,7}|:)|fe80:(:[0-9a-fA-F]{0,4}){0,4}%[0-9a-zA-Z]{1,}|::(ffff(:0{1,4}){0,1}:){0,1}((25[0-5]|(2[0-4]|1{0,1}[0-9]){0,1}[0-9])\.){3,3}(25[0-5]|(2[0-4]|1{0,1}[0-9]){0,1}[0-9])|([0-9a-fA-F]{1,4}:){1,4}:((25[0-5]|(2[0-4]|1{0,1}[0-9]){0,1}[0-9])\.){3,3}(25[0-5]|(2[0-4]|1{0,1}[0-9]){0,1}[0-9]))\/(12[0-8]|1[01][0-9]|[1-9]?[0-9])$/,base64:/^([0-9a-zA-Z+/]{4})*(([0-9a-zA-Z+/]{2}==)|([0-9a-zA-Z+/]{3}=))?$/,base64url:/^([0-9a-zA-Z-_]{4})*(([0-9a-zA-Z-_]{2}(==)?)|([0-9a-zA-Z-_]{3}(=)?))?$/,nanoid:/^[a-zA-Z0-9_-]{21}$/,jwt:/^[A-Za-z0-9-_]+\.[A-Za-z0-9-_]+\.[A-Za-z0-9-_]*$/};function Z(e,t){let r={type:"string"};if(e.checks)for(let a of e.checks)switch(a.kind){case"min":N(r,"minLength","number"==typeof r.minLength?Math.max(r.minLength,a.value):a.value,a.message,t);break;case"max":N(r,"maxLength","number"==typeof r.maxLength?Math.min(r.maxLength,a.value):a.value,a.message,t);break;case"email":switch(t.emailStrategy){case"format:email":L(r,"email",a.message,t);break;case"format:idn-email":L(r,"idn-email",a.message,t);break;case"pattern:zod":F(r,$.email,a.message,t)}break;case"url":L(r,"uri",a.message,t);break;case"uuid":L(r,"uuid",a.message,t);break;case"regex":F(r,a.regex,a.message,t);break;case"cuid":F(r,$.cuid,a.message,t);break;case"cuid2":F(r,$.cuid2,a.message,t);break;case"startsWith":F(r,RegExp(`^${D(a.value,t)}`),a.message,t);break;case"endsWith":F(r,RegExp(`${D(a.value,t)}$`),a.message,t);break;case"datetime":L(r,"date-time",a.message,t);break;case"date":L(r,"date",a.message,t);break;case"time":L(r,"time",a.message,t);break;case"duration":L(r,"duration",a.message,t);break;case"length":N(r,"minLength","number"==typeof r.minLength?Math.max(r.minLength,a.value):a.value,a.message,t),N(r,"maxLength","number"==typeof r.maxLength?Math.min(r.maxLength,a.value):a.value,a.message,t);break;case"includes":F(r,RegExp(D(a.value,t)),a.message,t);break;case"ip":"v6"!==a.version&&L(r,"ipv4",a.message,t),"v4"!==a.version&&L(r,"ipv6",a.message,t);break;case"base64url":F(r,$.base64url,a.message,t);break;case"jwt":F(r,$.jwt,a.message,t);break;case"cidr":"v6"!==a.version&&F(r,$.ipv4Cidr,a.message,t),"v4"!==a.version&&F(r,$.ipv6Cidr,a.message,t);break;case"emoji":F(r,$.emoji(),a.message,t);break;case"ulid":F(r,$.ulid,a.message,t);break;case"base64":switch(t.base64Strategy){case"format:binary":L(r,"binary",a.message,t);break;case"contentEncoding:base64":N(r,"contentEncoding","base64",a.message,t);break;case"pattern:zod":F(r,$.base64,a.message,t)}break;case"nanoid":F(r,$.nanoid,a.message,t);case"toLowerCase":case"toUpperCase":case"trim":break;default:}return r}function D(e,t){return"escape"===t.patternStrategy?function(e){let t="";for(let r=0;r<e.length;r++)q.has(e[r])||(t+="\\"),t+=e[r];return t}(e):e}let q=new Set("ABCDEFGHIJKLMNOPQRSTUVXYZabcdefghijklmnopqrstuvxyz0123456789");function L(e,t,r,a){e.format||e.anyOf?.some(e=>e.format)?(e.anyOf||(e.anyOf=[]),e.format&&(e.anyOf.push({format:e.format,...e.errorMessage&&a.errorMessages&&{errorMessage:{format:e.errorMessage.format}}}),delete e.format,e.errorMessage&&(delete e.errorMessage.format,0===Object.keys(e.errorMessage).length&&delete e.errorMessage)),e.anyOf.push({format:t,...r&&a.errorMessages&&{errorMessage:{format:r}}})):N(e,"format",t,r,a)}function F(e,t,r,a){e.pattern||e.allOf?.some(e=>e.pattern)?(e.allOf||(e.allOf=[]),e.pattern&&(e.allOf.push({pattern:e.pattern,...e.errorMessage&&a.errorMessages&&{errorMessage:{pattern:e.errorMessage.pattern}}}),delete e.pattern,e.errorMessage&&(delete e.errorMessage.pattern,0===Object.keys(e.errorMessage).length&&delete e.errorMessage)),e.allOf.push({pattern:U(t,a),...r&&a.errorMessages&&{errorMessage:{pattern:r}}})):N(e,"pattern",U(t,a),r,a)}function U(e,t){if(!t.applyRegexFlags||!e.flags)return e.source;let r={i:e.flags.includes("i"),m:e.flags.includes("m"),s:e.flags.includes("s")},a=r.i?e.source.toLowerCase():e.source,s="",n=!1,i=!1,o=!1;for(let e=0;e<a.length;e++){if(n){s+=a[e],n=!1;continue}if(r.i){if(i){if(a[e].match(/[a-z]/)){o?(s+=a[e],s+=`${a[e-2]}-${a[e]}`.toUpperCase(),o=!1):"-"===a[e+1]&&a[e+2]?.match(/[a-z]/)?(s+=a[e],o=!0):s+=`${a[e]}${a[e].toUpperCase()}`;continue}}else if(a[e].match(/[a-z]/)){s+=`[${a[e]}${a[e].toUpperCase()}]`;continue}}if(r.m){if("^"===a[e]){s+=`(^|(?<=[\r
]))`;continue}if("$"===a[e]){s+=`($|(?=[\r
]))`;continue}}if(r.s&&"."===a[e]){s+=i?`${a[e]}\r
`:`[${a[e]}\r
]`;continue}s+=a[e],"\\"===a[e]?n=!0:i&&"]"===a[e]?i=!1:i||"["!==a[e]||(i=!0)}try{new RegExp(s)}catch{return console.warn(`Could not convert regex pattern at ${t.currentPath.join("/")} to a flag-independent form! Falling back to the flag-ignorant source`),e.source}return s}function V(e,t){if("openAi"===t.target&&console.warn("Warning: OpenAI may not support records in schemas! Try an array of key-value pairs instead."),"openApi3"===t.target&&e.keyType?._def.typeName===T.kY.ZodEnum)return{type:"object",required:e.keyType._def.values,properties:e.keyType._def.values.reduce((r,a)=>({...r,[a]:H(e.valueType._def,{...t,currentPath:[...t.currentPath,"properties",a]})??j(t)}),{}),additionalProperties:t.rejectedAdditionalProperties};let r={type:"object",additionalProperties:H(e.valueType._def,{...t,currentPath:[...t.currentPath,"additionalProperties"]})??t.allowedAdditionalProperties};if("openApi3"===t.target)return r;if(e.keyType?._def.typeName===T.kY.ZodString&&e.keyType._def.checks?.length){let{type:a,...s}=Z(e.keyType._def,t);return{...r,propertyNames:s}}if(e.keyType?._def.typeName===T.kY.ZodEnum)return{...r,propertyNames:{enum:e.keyType._def.values}};if(e.keyType?._def.typeName===T.kY.ZodBranded&&e.keyType._def.type._def.typeName===T.kY.ZodString&&e.keyType._def.type._def.checks?.length){let{type:a,...s}=R(e.keyType._def,t);return{...r,propertyNames:s}}return r}let B={ZodString:"string",ZodNumber:"number",ZodBigInt:"integer",ZodBoolean:"boolean",ZodNull:"null"},J=(e,t)=>{let r=(e.options instanceof Map?Array.from(e.options.values()):e.options).map((e,r)=>H(e._def,{...t,currentPath:[...t.currentPath,"anyOf",`${r}`]})).filter(e=>!!e&&(!t.strictUnions||"object"==typeof e&&Object.keys(e).length>0));return r.length?{anyOf:r}:void 0},Y=(e,t)=>{if(t.currentPath.toString()===t.propertyPath?.toString())return H(e.innerType._def,t);let r=H(e.innerType._def,{...t,currentPath:[...t.currentPath,"anyOf","1"]});return r?{anyOf:[{not:j(t)},r]}:j(t)},K=(e,t)=>{if("input"===t.pipeStrategy)return H(e.in._def,t);if("output"===t.pipeStrategy)return H(e.out._def,t);let r=H(e.in._def,{...t,currentPath:[...t.currentPath,"allOf","0"]}),a=H(e.out._def,{...t,currentPath:[...t.currentPath,"allOf",r?"1":"0"]});return{allOf:[r,a].filter(e=>void 0!==e)}},W=(e,t)=>H(e.innerType._def,t),G=(e,t,r)=>{switch(t){case T.kY.ZodString:return Z(e,r);case T.kY.ZodNumber:return function(e,t){let r={type:"number"};if(!e.checks)return r;for(let a of e.checks)switch(a.kind){case"int":r.type="integer",C(r,"type",a.message,t);break;case"min":"jsonSchema7"===t.target?a.inclusive?N(r,"minimum",a.value,a.message,t):N(r,"exclusiveMinimum",a.value,a.message,t):(a.inclusive||(r.exclusiveMinimum=!0),N(r,"minimum",a.value,a.message,t));break;case"max":"jsonSchema7"===t.target?a.inclusive?N(r,"maximum",a.value,a.message,t):N(r,"exclusiveMaximum",a.value,a.message,t):(a.inclusive||(r.exclusiveMaximum=!0),N(r,"maximum",a.value,a.message,t));break;case"multipleOf":N(r,"multipleOf",a.value,a.message,t)}return r}(e,r);case T.kY.ZodObject:return function(e,t){let r="openAi"===t.target,a={type:"object",properties:{}},s=[],n=e.shape();for(let e in n){let i=n[e];if(void 0===i||void 0===i._def)continue;let o=function(e){try{return e.isOptional()}catch{return!0}}(i);o&&r&&("ZodOptional"===i._def.typeName&&(i=i._def.innerType),i.isNullable()||(i=i.nullable()),o=!1);let l=H(i._def,{...t,currentPath:[...t.currentPath,"properties",e],propertyPath:[...t.currentPath,"properties",e]});void 0!==l&&(a.properties[e]=l,o||s.push(e))}s.length&&(a.required=s);let i=function(e,t){if("ZodNever"!==e.catchall._def.typeName)return H(e.catchall._def,{...t,currentPath:[...t.currentPath,"additionalProperties"]});switch(e.unknownKeys){case"passthrough":return t.allowedAdditionalProperties;case"strict":return t.rejectedAdditionalProperties;case"strip":return"strict"===t.removeAdditionalStrategy?t.allowedAdditionalProperties:t.rejectedAdditionalProperties}}(e,t);return void 0!==i&&(a.additionalProperties=i),a}(e,r);case T.kY.ZodBigInt:return function(e,t){let r={type:"integer",format:"int64"};if(!e.checks)return r;for(let a of e.checks)switch(a.kind){case"min":"jsonSchema7"===t.target?a.inclusive?N(r,"minimum",a.value,a.message,t):N(r,"exclusiveMinimum",a.value,a.message,t):(a.inclusive||(r.exclusiveMinimum=!0),N(r,"minimum",a.value,a.message,t));break;case"max":"jsonSchema7"===t.target?a.inclusive?N(r,"maximum",a.value,a.message,t):N(r,"exclusiveMaximum",a.value,a.message,t):(a.inclusive||(r.exclusiveMaximum=!0),N(r,"maximum",a.value,a.message,t));break;case"multipleOf":N(r,"multipleOf",a.value,a.message,t)}return r}(e,r);case T.kY.ZodBoolean:return{type:"boolean"};case T.kY.ZodDate:return function e(t,r,a){let s=a??r.dateStrategy;if(Array.isArray(s))return{anyOf:s.map((a,s)=>e(t,r,a))};switch(s){case"string":case"format:date-time":return{type:"string",format:"date-time"};case"format:date":return{type:"string",format:"date"};case"integer":return P(t,r)}}(e,r);case T.kY.ZodUndefined:return{not:j(r)};case T.kY.ZodNull:return"openApi3"===r.target?{enum:["null"],nullable:!0}:{type:"null"};case T.kY.ZodArray:return function(e,t){let r={type:"array"};return e.type?._def&&e.type?._def?.typeName!==T.kY.ZodAny&&(r.items=H(e.type._def,{...t,currentPath:[...t.currentPath,"items"]})),e.minLength&&N(r,"minItems",e.minLength.value,e.minLength.message,t),e.maxLength&&N(r,"maxItems",e.maxLength.value,e.maxLength.message,t),e.exactLength&&(N(r,"minItems",e.exactLength.value,e.exactLength.message,t),N(r,"maxItems",e.exactLength.value,e.exactLength.message,t)),r}(e,r);case T.kY.ZodUnion:case T.kY.ZodDiscriminatedUnion:return function(e,t){if("openApi3"===t.target)return J(e,t);let r=e.options instanceof Map?Array.from(e.options.values()):e.options;if(r.every(e=>e._def.typeName in B&&(!e._def.checks||!e._def.checks.length))){let e=r.reduce((e,t)=>{let r=B[t._def.typeName];return r&&!e.includes(r)?[...e,r]:e},[]);return{type:e.length>1?e:e[0]}}if(r.every(e=>"ZodLiteral"===e._def.typeName&&!e.description)){let e=r.reduce((e,t)=>{let r=typeof t._def.value;switch(r){case"string":case"number":case"boolean":return[...e,r];case"bigint":return[...e,"integer"];case"object":if(null===t._def.value)return[...e,"null"];default:return e}},[]);if(e.length===r.length){let t=e.filter((e,t,r)=>r.indexOf(e)===t);return{type:t.length>1?t:t[0],enum:r.reduce((e,t)=>e.includes(t._def.value)?e:[...e,t._def.value],[])}}}else if(r.every(e=>"ZodEnum"===e._def.typeName))return{type:"string",enum:r.reduce((e,t)=>[...e,...t._def.values.filter(t=>!e.includes(t))],[])};return J(e,t)}(e,r);case T.kY.ZodIntersection:return function(e,t){let r=[H(e.left._def,{...t,currentPath:[...t.currentPath,"allOf","0"]}),H(e.right._def,{...t,currentPath:[...t.currentPath,"allOf","1"]})].filter(e=>!!e),a="jsonSchema2019-09"===t.target?{unevaluatedProperties:!1}:void 0,s=[];return r.forEach(e=>{if(M(e))s.push(...e.allOf),void 0===e.unevaluatedProperties&&(a=void 0);else{let t=e;if("additionalProperties"in e&&!1===e.additionalProperties){let{additionalProperties:r,...a}=e;t=a}else a=void 0;s.push(t)}}),s.length?{allOf:s,...a}:void 0}(e,r);case T.kY.ZodTuple:return function(e,t){return e.rest?{type:"array",minItems:e.items.length,items:e.items.map((e,r)=>H(e._def,{...t,currentPath:[...t.currentPath,"items",`${r}`]})).reduce((e,t)=>void 0===t?e:[...e,t],[]),additionalItems:H(e.rest._def,{...t,currentPath:[...t.currentPath,"additionalItems"]})}:{type:"array",minItems:e.items.length,maxItems:e.items.length,items:e.items.map((e,r)=>H(e._def,{...t,currentPath:[...t.currentPath,"items",`${r}`]})).reduce((e,t)=>void 0===t?e:[...e,t],[])}}(e,r);case T.kY.ZodRecord:return V(e,r);case T.kY.ZodLiteral:return function(e,t){let r=typeof e.value;return"bigint"!==r&&"number"!==r&&"boolean"!==r&&"string"!==r?{type:Array.isArray(e.value)?"array":"object"}:"openApi3"===t.target?{type:"bigint"===r?"integer":r,enum:[e.value]}:{type:"bigint"===r?"integer":r,const:e.value}}(e,r);case T.kY.ZodEnum:return{type:"string",enum:Array.from(e.values)};case T.kY.ZodNativeEnum:return function(e){let t=e.values,r=Object.keys(e.values).filter(e=>"number"!=typeof t[t[e]]).map(e=>t[e]),a=Array.from(new Set(r.map(e=>typeof e)));return{type:1===a.length?"string"===a[0]?"string":"number":["string","number"],enum:r}}(e);case T.kY.ZodNullable:return function(e,t){if(["ZodString","ZodNumber","ZodBigInt","ZodBoolean","ZodNull"].includes(e.innerType._def.typeName)&&(!e.innerType._def.checks||!e.innerType._def.checks.length))return"openApi3"===t.target?{type:B[e.innerType._def.typeName],nullable:!0}:{type:[B[e.innerType._def.typeName],"null"]};if("openApi3"===t.target){let r=H(e.innerType._def,{...t,currentPath:[...t.currentPath]});return r&&"$ref"in r?{allOf:[r],nullable:!0}:r&&{...r,nullable:!0}}let r=H(e.innerType._def,{...t,currentPath:[...t.currentPath,"anyOf","0"]});return r&&{anyOf:[r,{type:"null"}]}}(e,r);case T.kY.ZodOptional:return Y(e,r);case T.kY.ZodMap:return function(e,t){return"record"===t.mapStrategy?V(e,t):{type:"array",maxItems:125,items:{type:"array",items:[H(e.keyType._def,{...t,currentPath:[...t.currentPath,"items","items","0"]})||j(t),H(e.valueType._def,{...t,currentPath:[...t.currentPath,"items","items","1"]})||j(t)],minItems:2,maxItems:2}}}(e,r);case T.kY.ZodSet:return function(e,t){let r={type:"array",uniqueItems:!0,items:H(e.valueType._def,{...t,currentPath:[...t.currentPath,"items"]})};return e.minSize&&N(r,"minItems",e.minSize.value,e.minSize.message,t),e.maxSize&&N(r,"maxItems",e.maxSize.value,e.maxSize.message,t),r}(e,r);case T.kY.ZodLazy:return()=>e.getter()._def;case T.kY.ZodPromise:return H(e.type._def,r);case T.kY.ZodNaN:case T.kY.ZodNever:return function(e){return"openAi"===e.target?void 0:{not:j({...e,currentPath:[...e.currentPath,"not"]})}}(r);case T.kY.ZodEffects:return function(e,t){return"input"===t.effectStrategy?H(e.schema._def,t):j(t)}(e,r);case T.kY.ZodAny:case T.kY.ZodUnknown:return j(r);case T.kY.ZodDefault:return function(e,t){return{...H(e.innerType._def,t),default:e.defaultValue()}}(e,r);case T.kY.ZodBranded:return R(e,r);case T.kY.ZodReadonly:return W(e,r);case T.kY.ZodCatch:return O(e,r);case T.kY.ZodPipeline:return K(e,r);case T.kY.ZodFunction:case T.kY.ZodVoid:case T.kY.ZodSymbol:default:return}};function H(e,t,r=!1){let a=t.seen.get(e);if(t.override){let s=t.override?.(e,t,a,r);if(s!==I)return s}if(a&&!r){let e=X(a,t);if(void 0!==e)return e}let s={def:e,path:t.currentPath,jsonSchema:void 0};t.seen.set(e,s);let n=G(e,e.typeName,t),i="function"==typeof n?H(n(),t):n;if(i&&Q(e,t,i),t.postProcess){let r=t.postProcess(i,e,t);return s.jsonSchema=i,r}return s.jsonSchema=i,i}let X=(e,t)=>{switch(t.$refStrategy){case"root":return{$ref:e.path.join("/")};case"relative":return{$ref:A(t.currentPath,e.path)};case"none":case"seen":if(e.path.length<t.currentPath.length&&e.path.every((e,r)=>t.currentPath[r]===e))return console.warn(`Recursive reference detected at ${t.currentPath.join("/")}! Defaulting to any`),j(t);return"seen"===t.$refStrategy?j(t):void 0}},Q=(e,t,r)=>(e.description&&(r.description=e.description,t.markdownDescription&&(r.markdownDescription=e.description)),r),ee=(e,t)=>{let r=E(t),a="object"==typeof t&&t.definitions?Object.entries(t.definitions).reduce((e,[t,a])=>({...e,[t]:H(a._def,{...r,currentPath:[...r.basePath,r.definitionPath,t]},!0)??j(r)}),{}):void 0,s="string"==typeof t?t:t?.nameStrategy==="title"?void 0:t?.name,n=H(e._def,void 0===s?r:{...r,currentPath:[...r.basePath,r.definitionPath,s]},!1)??j(r),i="object"==typeof t&&void 0!==t.name&&"title"===t.nameStrategy?t.name:void 0;void 0!==i&&(n.title=i),r.flags.hasReferencedOpenAiAnyType&&(a||(a={}),a[r.openAiAnyTypeName]||(a[r.openAiAnyTypeName]={type:["string","number","integer","boolean","array","null"],items:{$ref:"relative"===r.$refStrategy?"1":[...r.basePath,r.definitionPath,r.openAiAnyTypeName].join("/")}}));let o=void 0===s?a?{...n,[r.definitionPath]:a}:n:{$ref:[..."relative"===r.$refStrategy?[]:r.basePath,r.definitionPath,s].join("/"),[r.definitionPath]:{...a,[s]:n}};return"jsonSchema7"===r.target?o.$schema="http://json-schema.org/draft-07/schema#":("jsonSchema2019-09"===r.target||"openAi"===r.target)&&(o.$schema="https://json-schema.org/draft/2019-09/schema#"),"openAi"===r.target&&("anyOf"in o||"oneOf"in o||"allOf"in o||"type"in o&&Array.isArray(o.type))&&console.warn("Warning: OpenAI may not support schemas with unions as roots! Try wrapping it in an object property."),o};var et={code:"0",name:"text",parse:e=>{if("string"!=typeof e)throw Error('"text" parts expect a string value.');return{type:"text",value:e}}},er={code:"3",name:"error",parse:e=>{if("string"!=typeof e)throw Error('"error" parts expect a string value.');return{type:"error",value:e}}},ea={code:"4",name:"assistant_message",parse:e=>{if(null==e||"object"!=typeof e||!("id"in e)||!("role"in e)||!("content"in e)||"string"!=typeof e.id||"string"!=typeof e.role||"assistant"!==e.role||!Array.isArray(e.content)||!e.content.every(e=>null!=e&&"object"==typeof e&&"type"in e&&"text"===e.type&&"text"in e&&null!=e.text&&"object"==typeof e.text&&"value"in e.text&&"string"==typeof e.text.value))throw Error('"assistant_message" parts expect an object with an "id", "role", and "content" property.');return{type:"assistant_message",value:e}}},es={code:"5",name:"assistant_control_data",parse:e=>{if(null==e||"object"!=typeof e||!("threadId"in e)||!("messageId"in e)||"string"!=typeof e.threadId||"string"!=typeof e.messageId)throw Error('"assistant_control_data" parts expect an object with a "threadId" and "messageId" property.');return{type:"assistant_control_data",value:{threadId:e.threadId,messageId:e.messageId}}}},en={code:"6",name:"data_message",parse:e=>{if(null==e||"object"!=typeof e||!("role"in e)||!("data"in e)||"string"!=typeof e.role||"data"!==e.role)throw Error('"data_message" parts expect an object with a "role" and "data" property.');return{type:"data_message",value:e}}},ei=[et,er,ea,es,en],eo={[et.code]:et,[er.code]:er,[ea.code]:ea,[es.code]:es,[en.code]:en};et.name,et.code,er.name,er.code,ea.name,ea.code,es.name,es.code,en.name,en.code;var el=ei.map(e=>e.code);function eu(e){if(void 0===e)return{value:void 0,state:"undefined-input"};let t=(0,w.N8)({text:e});return t.success?{value:t.value,state:"successful-parse"}:(t=(0,w.N8)({text:function(e){let t=["ROOT"],r=-1,a=null;function s(e,s,n){switch(e){case'"':r=s,t.pop(),t.push(n),t.push("INSIDE_STRING");break;case"f":case"t":case"n":r=s,a=s,t.pop(),t.push(n),t.push("INSIDE_LITERAL");break;case"-":t.pop(),t.push(n),t.push("INSIDE_NUMBER");break;case"0":case"1":case"2":case"3":case"4":case"5":case"6":case"7":case"8":case"9":r=s,t.pop(),t.push(n),t.push("INSIDE_NUMBER");break;case"{":r=s,t.pop(),t.push(n),t.push("INSIDE_OBJECT_START");break;case"[":r=s,t.pop(),t.push(n),t.push("INSIDE_ARRAY_START")}}function n(e,a){switch(e){case",":t.pop(),t.push("INSIDE_OBJECT_AFTER_COMMA");break;case"}":r=a,t.pop()}}function i(e,a){switch(e){case",":t.pop(),t.push("INSIDE_ARRAY_AFTER_COMMA");break;case"]":r=a,t.pop()}}for(let o=0;o<e.length;o++){let l=e[o];switch(t[t.length-1]){case"ROOT":s(l,o,"FINISH");break;case"INSIDE_OBJECT_START":switch(l){case'"':t.pop(),t.push("INSIDE_OBJECT_KEY");break;case"}":r=o,t.pop()}break;case"INSIDE_OBJECT_AFTER_COMMA":'"'===l&&(t.pop(),t.push("INSIDE_OBJECT_KEY"));break;case"INSIDE_OBJECT_KEY":'"'===l&&(t.pop(),t.push("INSIDE_OBJECT_AFTER_KEY"));break;case"INSIDE_OBJECT_AFTER_KEY":":"===l&&(t.pop(),t.push("INSIDE_OBJECT_BEFORE_VALUE"));break;case"INSIDE_OBJECT_BEFORE_VALUE":s(l,o,"INSIDE_OBJECT_AFTER_VALUE");break;case"INSIDE_OBJECT_AFTER_VALUE":n(l,o);break;case"INSIDE_STRING":switch(l){case'"':t.pop(),r=o;break;case"\\":t.push("INSIDE_STRING_ESCAPE");break;default:r=o}break;case"INSIDE_ARRAY_START":"]"===l?(r=o,t.pop()):(r=o,s(l,o,"INSIDE_ARRAY_AFTER_VALUE"));break;case"INSIDE_ARRAY_AFTER_VALUE":switch(l){case",":t.pop(),t.push("INSIDE_ARRAY_AFTER_COMMA");break;case"]":r=o,t.pop();break;default:r=o}break;case"INSIDE_ARRAY_AFTER_COMMA":s(l,o,"INSIDE_ARRAY_AFTER_VALUE");break;case"INSIDE_STRING_ESCAPE":t.pop(),r=o;break;case"INSIDE_NUMBER":switch(l){case"0":case"1":case"2":case"3":case"4":case"5":case"6":case"7":case"8":case"9":r=o;break;case"e":case"E":case"-":case".":break;case",":t.pop(),"INSIDE_ARRAY_AFTER_VALUE"===t[t.length-1]&&i(l,o),"INSIDE_OBJECT_AFTER_VALUE"===t[t.length-1]&&n(l,o);break;case"}":t.pop(),"INSIDE_OBJECT_AFTER_VALUE"===t[t.length-1]&&n(l,o);break;case"]":t.pop(),"INSIDE_ARRAY_AFTER_VALUE"===t[t.length-1]&&i(l,o);break;default:t.pop()}break;case"INSIDE_LITERAL":{let s=e.substring(a,o+1);"false".startsWith(s)||"true".startsWith(s)||"null".startsWith(s)?r=o:(t.pop(),"INSIDE_OBJECT_AFTER_VALUE"===t[t.length-1]?n(l,o):"INSIDE_ARRAY_AFTER_VALUE"===t[t.length-1]&&i(l,o))}}}let o=e.slice(0,r+1);for(let r=t.length-1;r>=0;r--)switch(t[r]){case"INSIDE_STRING":o+='"';break;case"INSIDE_OBJECT_KEY":case"INSIDE_OBJECT_AFTER_KEY":case"INSIDE_OBJECT_AFTER_COMMA":case"INSIDE_OBJECT_START":case"INSIDE_OBJECT_BEFORE_VALUE":case"INSIDE_OBJECT_AFTER_VALUE":o+="}";break;case"INSIDE_ARRAY_START":case"INSIDE_ARRAY_AFTER_COMMA":case"INSIDE_ARRAY_AFTER_VALUE":o+="]";break;case"INSIDE_LITERAL":{let t=e.substring(a,e.length);"true".startsWith(t)?o+="true".slice(t.length):"false".startsWith(t)?o+="false".slice(t.length):"null".startsWith(t)&&(o+="null".slice(t.length))}}return o}(e)})).success?{value:t.value,state:"repaired-parse"}:{value:void 0,state:"failed-parse"}}var ec=[{code:"0",name:"text",parse:e=>{if("string"!=typeof e)throw Error('"text" parts expect a string value.');return{type:"text",value:e}}},{code:"2",name:"data",parse:e=>{if(!Array.isArray(e))throw Error('"data" parts expect an array value.');return{type:"data",value:e}}},{code:"3",name:"error",parse:e=>{if("string"!=typeof e)throw Error('"error" parts expect a string value.');return{type:"error",value:e}}},{code:"8",name:"message_annotations",parse:e=>{if(!Array.isArray(e))throw Error('"message_annotations" parts expect an array value.');return{type:"message_annotations",value:e}}},{code:"9",name:"tool_call",parse:e=>{if(null==e||"object"!=typeof e||!("toolCallId"in e)||"string"!=typeof e.toolCallId||!("toolName"in e)||"string"!=typeof e.toolName||!("args"in e)||"object"!=typeof e.args)throw Error('"tool_call" parts expect an object with a "toolCallId", "toolName", and "args" property.');return{type:"tool_call",value:e}}},{code:"a",name:"tool_result",parse:e=>{if(null==e||"object"!=typeof e||!("toolCallId"in e)||"string"!=typeof e.toolCallId||!("result"in e))throw Error('"tool_result" parts expect an object with a "toolCallId" and a "result" property.');return{type:"tool_result",value:e}}},{code:"b",name:"tool_call_streaming_start",parse:e=>{if(null==e||"object"!=typeof e||!("toolCallId"in e)||"string"!=typeof e.toolCallId||!("toolName"in e)||"string"!=typeof e.toolName)throw Error('"tool_call_streaming_start" parts expect an object with a "toolCallId" and "toolName" property.');return{type:"tool_call_streaming_start",value:e}}},{code:"c",name:"tool_call_delta",parse:e=>{if(null==e||"object"!=typeof e||!("toolCallId"in e)||"string"!=typeof e.toolCallId||!("argsTextDelta"in e)||"string"!=typeof e.argsTextDelta)throw Error('"tool_call_delta" parts expect an object with a "toolCallId" and "argsTextDelta" property.');return{type:"tool_call_delta",value:e}}},{code:"d",name:"finish_message",parse:e=>{if(null==e||"object"!=typeof e||!("finishReason"in e)||"string"!=typeof e.finishReason)throw Error('"finish_message" parts expect an object with a "finishReason" property.');let t={finishReason:e.finishReason};return"usage"in e&&null!=e.usage&&"object"==typeof e.usage&&"promptTokens"in e.usage&&"completionTokens"in e.usage&&(t.usage={promptTokens:"number"==typeof e.usage.promptTokens?e.usage.promptTokens:Number.NaN,completionTokens:"number"==typeof e.usage.completionTokens?e.usage.completionTokens:Number.NaN}),{type:"finish_message",value:t}}},{code:"e",name:"finish_step",parse:e=>{if(null==e||"object"!=typeof e||!("finishReason"in e)||"string"!=typeof e.finishReason)throw Error('"finish_step" parts expect an object with a "finishReason" property.');let t={finishReason:e.finishReason,isContinued:!1};return"usage"in e&&null!=e.usage&&"object"==typeof e.usage&&"promptTokens"in e.usage&&"completionTokens"in e.usage&&(t.usage={promptTokens:"number"==typeof e.usage.promptTokens?e.usage.promptTokens:Number.NaN,completionTokens:"number"==typeof e.usage.completionTokens?e.usage.completionTokens:Number.NaN}),"isContinued"in e&&"boolean"==typeof e.isContinued&&(t.isContinued=e.isContinued),{type:"finish_step",value:t}}},{code:"f",name:"start_step",parse:e=>{if(null==e||"object"!=typeof e||!("messageId"in e)||"string"!=typeof e.messageId)throw Error('"start_step" parts expect an object with an "id" property.');return{type:"start_step",value:{messageId:e.messageId}}}},{code:"g",name:"reasoning",parse:e=>{if("string"!=typeof e)throw Error('"reasoning" parts expect a string value.');return{type:"reasoning",value:e}}},{code:"h",name:"source",parse:e=>{if(null==e||"object"!=typeof e)throw Error('"source" parts expect a Source object.');return{type:"source",value:e}}},{code:"i",name:"redacted_reasoning",parse:e=>{if(null==e||"object"!=typeof e||!("data"in e)||"string"!=typeof e.data)throw Error('"redacted_reasoning" parts expect an object with a "data" property.');return{type:"redacted_reasoning",value:{data:e.data}}}},{code:"j",name:"reasoning_signature",parse:e=>{if(null==e||"object"!=typeof e||!("signature"in e)||"string"!=typeof e.signature)throw Error('"reasoning_signature" parts expect an object with a "signature" property.');return{type:"reasoning_signature",value:{signature:e.signature}}}},{code:"k",name:"file",parse:e=>{if(null==e||"object"!=typeof e||!("data"in e)||"string"!=typeof e.data||!("mimeType"in e)||"string"!=typeof e.mimeType)throw Error('"file" parts expect an object with a "data" and "mimeType" property.');return{type:"file",value:e}}}],ed=Object.fromEntries(ec.map(e=>[e.code,e]));Object.fromEntries(ec.map(e=>[e.name,e.code]));var ep=ec.map(e=>e.code),em=e=>{let t=e.indexOf(":");if(-1===t)throw Error("Failed to parse stream string. No separator found.");let r=e.slice(0,t);if(!ep.includes(r))throw Error(`Failed to parse stream string. Invalid code ${r}.`);let a=JSON.parse(e.slice(t+1));return ed[r].parse(a)};function eh(e,t){let r=ec.find(t=>t.name===e);if(!r)throw Error(`Invalid stream part type: ${e}`);return`${r.code}:${JSON.stringify(t)}
`}async function ef({stream:e,onTextPart:t,onReasoningPart:r,onReasoningSignaturePart:a,onRedactedReasoningPart:s,onSourcePart:n,onFilePart:i,onDataPart:o,onErrorPart:l,onToolCallStreamingStartPart:u,onToolCallDeltaPart:c,onToolCallPart:d,onToolResultPart:p,onMessageAnnotationsPart:m,onFinishMessagePart:h,onFinishStepPart:f,onStartStepPart:g}){let y=e.getReader(),v=new TextDecoder,_=[],b=0;for(;;){let{value:e}=await y.read();if(e&&(_.push(e),b+=e.length,10!==e[e.length-1]))continue;if(0===_.length)break;let w=function(e,t){let r=new Uint8Array(t),a=0;for(let t of e)r.set(t,a),a+=t.length;return e.length=0,r}(_,b);for(let{type:e,value:y}of(b=0,v.decode(w,{stream:!0}).split("\n").filter(e=>""!==e).map(em)))switch(e){case"text":await (null==t?void 0:t(y));break;case"reasoning":await (null==r?void 0:r(y));break;case"reasoning_signature":await (null==a?void 0:a(y));break;case"redacted_reasoning":await (null==s?void 0:s(y));break;case"file":await (null==i?void 0:i(y));break;case"source":await (null==n?void 0:n(y));break;case"data":await (null==o?void 0:o(y));break;case"error":await (null==l?void 0:l(y));break;case"message_annotations":await (null==m?void 0:m(y));break;case"tool_call_streaming_start":await (null==u?void 0:u(y));break;case"tool_call_delta":await (null==c?void 0:c(y));break;case"tool_call":await (null==d?void 0:d(y));break;case"tool_result":await (null==p?void 0:p(y));break;case"finish_message":await (null==h?void 0:h(y));break;case"finish_step":await (null==f?void 0:f(y));break;case"start_step":await (null==g?void 0:g(y));break;default:throw Error(`Unknown stream part type: ${e}`)}}}async function eg({stream:e,onTextPart:t}){let r=e.pipeThrough(new TextDecoderStream).getReader();for(;;){let{done:e,value:a}=await r.read();if(e)break;await t(a)}}var ey=Symbol.for("vercel.ai.schema");function ev(e){return"object"==typeof e&&null!==e&&ey in e&&!0===e[ey]&&"jsonSchema"in e&&"validate"in e?e:function(e,{validate:t}={}){return{[ey]:!0,_type:void 0,[w.eu]:!0,jsonSchema:e,validate:t}}(ee(e,{$refStrategy:"none",target:"jsonSchema7"}),{validate:t=>{let r=e.safeParse(t);return r.success?{success:!0,value:r.data}:{success:!1,error:r.error}}})}var e_=Object.defineProperty,eb=(e,t)=>{for(var r in t)e_(e,r,{get:t[r],enumerable:!0})};function ew(e,{contentType:t,dataStreamVersion:r}){let a=new Headers(null!=e?e:{});return a.has("Content-Type")||a.set("Content-Type",t),void 0!==r&&a.set("X-Vercel-AI-Data-Stream",r),a}function ex(e,{contentType:t,dataStreamVersion:r}){let a={};if(null!=e)for(let[t,r]of Object.entries(e))a[t]=r;return null==a["Content-Type"]&&(a["Content-Type"]=t),void 0!==r&&(a["X-Vercel-AI-Data-Stream"]=r),a}function ek({response:e,status:t,statusText:r,headers:a,stream:s}){e.writeHead(null!=t?t:200,r,a);let n=s.getReader();(async()=>{try{for(;;){let{done:t,value:r}=await n.read();if(t)break;e.write(r)}}catch(e){throw e}finally{e.end()}})()}var eT="AI_InvalidArgumentError",eI=`vercel.ai.error.${eT}`,eS=Symbol.for(eI),ez=class extends b.bD{constructor({parameter:e,value:t,message:r}){super({name:eT,message:`Invalid argument for parameter ${e}: ${r}`}),this[s]=!0,this.parameter=e,this.value=t}static isInstance(e){return b.bD.hasMarker(e,eI)}};s=eS;var eE="AI_RetryError",eA=`vercel.ai.error.${eE}`,ej=Symbol.for(eA),eC=class extends b.bD{constructor({message:e,reason:t,errors:r}){super({name:eE,message:e}),this[n]=!0,this.reason=t,this.errors=r,this.lastError=r[r.length-1]}static isInstance(e){return b.bD.hasMarker(e,eA)}};n=ej;var eN=({maxRetries:e=2,initialDelayInMs:t=2e3,backoffFactor:r=2}={})=>async a=>eR(a,{maxRetries:e,delayInMs:t,backoffFactor:r});async function eR(e,{maxRetries:t,delayInMs:r,backoffFactor:a},s=[]){try{return await e()}catch(l){if((0,w.zf)(l)||0===t)throw l;let n=(0,w.u1)(l),i=[...s,l],o=i.length;if(o>t)throw new eC({message:`Failed after ${o} attempts. Last error: ${n}`,reason:"maxRetriesExceeded",errors:i});if(l instanceof Error&&b.hL.isInstance(l)&&!0===l.isRetryable&&o<=t)return await (0,w.cb)(r),eR(e,{maxRetries:t,delayInMs:a*r,backoffFactor:a},i);if(1===o)throw l;throw new eC({message:`Failed after ${o} attempts with non-retryable error: '${n}'`,reason:"errorNotRetryable",errors:i})}}function eO({maxRetries:e}){if(null!=e){if(!Number.isInteger(e))throw new ez({parameter:"maxRetries",value:e,message:"maxRetries must be an integer"});if(e<0)throw new ez({parameter:"maxRetries",value:e,message:"maxRetries must be >= 0"})}let t=null!=e?e:2;return{maxRetries:t,retry:eN({maxRetries:t})}}function eP({operationId:e,telemetry:t}){return{"operation.name":`${e}${(null==t?void 0:t.functionId)!=null?` ${t.functionId}`:""}`,"resource.name":null==t?void 0:t.functionId,"ai.operationId":e,"ai.telemetry.functionId":null==t?void 0:t.functionId}}function eM({model:e,settings:t,telemetry:r,headers:a}){var s;return{"ai.model.provider":e.provider,"ai.model.id":e.modelId,...Object.entries(t).reduce((e,[t,r])=>(e[`ai.settings.${t}`]=r,e),{}),...Object.entries(null!=(s=null==r?void 0:r.metadata)?s:{}).reduce((e,[t,r])=>(e[`ai.telemetry.metadata.${t}`]=r,e),{}),...Object.entries(null!=a?a:{}).reduce((e,[t,r])=>(void 0!==r&&(e[`ai.request.headers.${t}`]=r),e),{})}}var e$={startSpan:()=>eZ,startActiveSpan:(e,t,r,a)=>"function"==typeof t?t(eZ):"function"==typeof r?r(eZ):"function"==typeof a?a(eZ):void 0},eZ={spanContext:()=>eD,setAttribute(){return this},setAttributes(){return this},addEvent(){return this},addLink(){return this},addLinks(){return this},setStatus(){return this},updateName(){return this},end(){return this},isRecording:()=>!1,recordException(){return this}},eD={traceId:"",spanId:"",traceFlags:0};function eq({isEnabled:e=!1,tracer:t}={}){return e?t||x.u.getTracer("ai"):e$}function eL({name:e,tracer:t,attributes:r,fn:a,endWhenDone:s=!0}){return t.startActiveSpan(e,{attributes:r},async e=>{try{let t=await a(e);return s&&e.end(),t}catch(t){try{t instanceof Error?(e.recordException({name:t.name,message:t.message,stack:t.stack}),e.setStatus({code:k.s.ERROR,message:t.message})):e.setStatus({code:k.s.ERROR})}finally{e.end()}throw t}})}function eF({telemetry:e,attributes:t}){return(null==e?void 0:e.isEnabled)!==!0?{}:Object.entries(t).reduce((t,[r,a])=>{if(void 0===a)return t;if("object"==typeof a&&"input"in a&&"function"==typeof a.input){if((null==e?void 0:e.recordInputs)===!1)return t;let s=a.input();return void 0===s?t:{...t,[r]:s}}if("object"==typeof a&&"output"in a&&"function"==typeof a.output){if((null==e?void 0:e.recordOutputs)===!1)return t;let s=a.output();return void 0===s?t:{...t,[r]:s}}return{...t,[r]:a}},{})}var eU="AI_NoImageGeneratedError",eV=`vercel.ai.error.${eU}`,eB=Symbol.for(eV),eJ=class extends b.bD{constructor({message:e="No image generated.",cause:t,responses:r}){super({name:eU,message:e,cause:t}),this[i]=!0,this.responses=r}static isInstance(e){return b.bD.hasMarker(e,eV)}};i=eB;var eY=class{constructor({data:e,mimeType:t}){let r=e instanceof Uint8Array;this.base64Data=r?void 0:e,this.uint8ArrayData=r?e:void 0,this.mimeType=t}get base64(){return null==this.base64Data&&(this.base64Data=(0,w.n_)(this.uint8ArrayData)),this.base64Data}get uint8Array(){return null==this.uint8ArrayData&&(this.uint8ArrayData=(0,w.Z9)(this.base64Data)),this.uint8ArrayData}},eK=class extends eY{constructor(e){super(e),this.type="file"}},eW=[{mimeType:"image/gif",bytesPrefix:[71,73,70],base64Prefix:"R0lG"},{mimeType:"image/png",bytesPrefix:[137,80,78,71],base64Prefix:"iVBORw"},{mimeType:"image/jpeg",bytesPrefix:[255,216],base64Prefix:"/9j/"},{mimeType:"image/webp",bytesPrefix:[82,73,70,70],base64Prefix:"UklGRg"},{mimeType:"image/bmp",bytesPrefix:[66,77],base64Prefix:"Qk"},{mimeType:"image/tiff",bytesPrefix:[73,73,42,0],base64Prefix:"SUkqAA"},{mimeType:"image/tiff",bytesPrefix:[77,77,0,42],base64Prefix:"TU0AKg"},{mimeType:"image/avif",bytesPrefix:[0,0,0,32,102,116,121,112,97,118,105,102],base64Prefix:"AAAAIGZ0eXBhdmlm"},{mimeType:"image/heic",bytesPrefix:[0,0,0,32,102,116,121,112,104,101,105,99],base64Prefix:"AAAAIGZ0eXBoZWlj"}],eG=e=>{let t="string"==typeof e?(0,w.Z9)(e):e,r=(127&t[6])<<21|(127&t[7])<<14|(127&t[8])<<7|127&t[9];return t.slice(r+10)};function eH({data:e,signatures:t}){let r="string"==typeof e&&e.startsWith("SUQz")||"string"!=typeof e&&e.length>10&&73===e[0]&&68===e[1]&&51===e[2]?eG(e):e;for(let e of t)if("string"==typeof r?r.startsWith(e.base64Prefix):r.length>=e.bytesPrefix.length&&e.bytesPrefix.every((e,t)=>r[t]===e))return e.mimeType}async function eX({model:e,prompt:t,n:r=1,size:a,aspectRatio:s,seed:n,providerOptions:i,maxRetries:o,abortSignal:l,headers:u}){var c;let{retry:d}=eO({maxRetries:o}),p=null!=(c=e.maxImagesPerCall)?c:1,m=Math.ceil(r/p),h=Array.from({length:m},(e,t)=>{if(t<m-1)return p;let a=r%p;return 0===a?p:a}),f=await Promise.all(h.map(async r=>d(()=>e.doGenerate({prompt:t,n:r,abortSignal:l,headers:u,size:a,aspectRatio:s,seed:n,providerOptions:null!=i?i:{}})))),g=[],y=[],v=[];for(let e of f)g.push(...e.images.map(e=>{var t;return new eY({data:e,mimeType:null!=(t=eH({data:e,signatures:eW}))?t:"image/png"})})),y.push(...e.warnings),v.push(e.response);if(!g.length)throw new eJ({responses:v});return new eQ({images:g,warnings:y,responses:v})}var eQ=class{constructor(e){this.images=e.images,this.warnings=e.warnings,this.responses=e.responses}get image(){return this.images[0]}},e0="AI_NoObjectGeneratedError",e1=`vercel.ai.error.${e0}`,e4=Symbol.for(e1),e9=class extends b.bD{constructor({message:e="No object generated.",cause:t,text:r,response:a,usage:s,finishReason:n}){super({name:e0,message:e,cause:t}),this[o]=!0,this.text=r,this.response=a,this.usage=s,this.finishReason=n}static isInstance(e){return b.bD.hasMarker(e,e1)}};o=e4;var e2="AI_DownloadError",e6=`vercel.ai.error.${e2}`,e3=Symbol.for(e6),e5=class extends b.bD{constructor({url:e,statusCode:t,statusText:r,cause:a,message:s=null==a?`Failed to download ${e}: ${t} ${r}`:`Failed to download ${e}: ${a}`}){super({name:e2,message:s,cause:a}),this[l]=!0,this.url=e,this.statusCode=t,this.statusText=r}static isInstance(e){return b.bD.hasMarker(e,e6)}};async function e8({url:e}){var t;let r=e.toString();try{let e=await fetch(r);if(!e.ok)throw new e5({url:r,statusCode:e.status,statusText:e.statusText});return{data:new Uint8Array(await e.arrayBuffer()),mimeType:null!=(t=e.headers.get("content-type"))?t:void 0}}catch(e){if(e5.isInstance(e))throw e;throw new e5({url:r,cause:e})}}l=e3;var e7="AI_InvalidDataContentError",te=`vercel.ai.error.${e7}`,tt=Symbol.for(te),tr=class extends b.bD{constructor({content:e,cause:t,message:r=`Invalid data content. Expected a base64 string, Uint8Array, ArrayBuffer, or Buffer, but got ${typeof e}.`}){super({name:e7,message:r,cause:t}),this[u]=!0,this.content=e}static isInstance(e){return b.bD.hasMarker(e,te)}};u=tt;var ta=T.z.union([T.z.string(),T.z.instanceof(Uint8Array),T.z.instanceof(ArrayBuffer),T.z.custom(e=>{var t,r;return null!=(r=null==(t=globalThis.Buffer)?void 0:t.isBuffer(e))&&r},{message:"Must be a Buffer"})]);function ts(e){return"string"==typeof e?e:e instanceof ArrayBuffer?(0,w.n_)(new Uint8Array(e)):(0,w.n_)(e)}function tn(e){if(e instanceof Uint8Array)return e;if("string"==typeof e)try{return(0,w.Z9)(e)}catch(t){throw new tr({message:"Invalid data content. Content string is not a base64-encoded media.",content:e,cause:t})}if(e instanceof ArrayBuffer)return new Uint8Array(e);throw new tr({content:e})}var ti="AI_InvalidMessageRoleError",to=`vercel.ai.error.${ti}`,tl=Symbol.for(to),tu=class extends b.bD{constructor({role:e,message:t=`Invalid message role: '${e}'. Must be one of: "system", "user", "assistant", "tool".`}){super({name:ti,message:t}),this[c]=!0,this.role=e}static isInstance(e){return b.bD.hasMarker(e,to)}};async function tc({prompt:e,modelSupportsImageUrls:t=!0,modelSupportsUrl:r=()=>!1,downloadImplementation:a=e8}){let s=await td(e.messages,a,t,r);return[...null!=e.system?[{role:"system",content:e.system}]:[],...e.messages.map(e=>(function(e,t){var r,a,s,n,i,o;let l=e.role;switch(l){case"system":return{role:"system",content:e.content,providerMetadata:null!=(r=e.providerOptions)?r:e.experimental_providerMetadata};case"user":if("string"==typeof e.content)return{role:"user",content:[{type:"text",text:e.content}],providerMetadata:null!=(a=e.providerOptions)?a:e.experimental_providerMetadata};return{role:"user",content:e.content.map(e=>(function(e,t){var r,a,s,n;let i,o,l;if("text"===e.type)return{type:"text",text:e.text,providerMetadata:null!=(r=e.providerOptions)?r:e.experimental_providerMetadata};let u=e.mimeType,c=e.type;switch(c){case"image":i=e.image;break;case"file":i=e.data;break;default:throw Error(`Unsupported part type: ${c}`)}try{o="string"==typeof i?new URL(i):i}catch(e){o=i}if(o instanceof URL){if("data:"===o.protocol){let{mimeType:e,base64Content:t}=function(e){try{let[t,r]=e.split(",");return{mimeType:t.split(";")[0].split(":")[1],base64Content:r}}catch(e){return{mimeType:void 0,base64Content:void 0}}}(o.toString());if(null==e||null==t)throw Error(`Invalid data URL format in part ${c}`);u=e,l=tn(t)}else{let e=t[o.toString()];e?(l=e.data,null!=u||(u=e.mimeType)):l=o}}else l=tn(o);switch(c){case"image":return l instanceof Uint8Array&&(u=null!=(a=eH({data:l,signatures:eW}))?a:u),{type:"image",image:l,mimeType:u,providerMetadata:null!=(s=e.providerOptions)?s:e.experimental_providerMetadata};case"file":if(null==u)throw Error("Mime type is missing for file part");return{type:"file",data:l instanceof Uint8Array?ts(l):l,filename:e.filename,mimeType:u,providerMetadata:null!=(n=e.providerOptions)?n:e.experimental_providerMetadata}}})(e,t)).filter(e=>"text"!==e.type||""!==e.text),providerMetadata:null!=(s=e.providerOptions)?s:e.experimental_providerMetadata};case"assistant":if("string"==typeof e.content)return{role:"assistant",content:[{type:"text",text:e.content}],providerMetadata:null!=(n=e.providerOptions)?n:e.experimental_providerMetadata};return{role:"assistant",content:e.content.filter(e=>"text"!==e.type||""!==e.text).map(e=>{var t;let r=null!=(t=e.providerOptions)?t:e.experimental_providerMetadata;switch(e.type){case"file":return{type:"file",data:e.data instanceof URL?e.data:ts(e.data),filename:e.filename,mimeType:e.mimeType,providerMetadata:r};case"reasoning":return{type:"reasoning",text:e.text,signature:e.signature,providerMetadata:r};case"redacted-reasoning":return{type:"redacted-reasoning",data:e.data,providerMetadata:r};case"text":return{type:"text",text:e.text,providerMetadata:r};case"tool-call":return{type:"tool-call",toolCallId:e.toolCallId,toolName:e.toolName,args:e.args,providerMetadata:r}}}),providerMetadata:null!=(i=e.providerOptions)?i:e.experimental_providerMetadata};case"tool":return{role:"tool",content:e.content.map(e=>{var t;return{type:"tool-result",toolCallId:e.toolCallId,toolName:e.toolName,result:e.result,content:e.experimental_content,isError:e.isError,providerMetadata:null!=(t=e.providerOptions)?t:e.experimental_providerMetadata}}),providerMetadata:null!=(o=e.providerOptions)?o:e.experimental_providerMetadata};default:throw new tu({role:l})}})(e,s))]}async function td(e,t,r,a){let s=e.filter(e=>"user"===e.role).map(e=>e.content).filter(e=>Array.isArray(e)).flat().filter(e=>"image"===e.type||"file"===e.type).filter(e=>"image"!==e.type||!0!==r).map(e=>"image"===e.type?e.image:e.data).map(e=>"string"==typeof e&&(e.startsWith("http:")||e.startsWith("https:"))?new URL(e):e).filter(e=>e instanceof URL).filter(e=>!a(e));return Object.fromEntries((await Promise.all(s.map(async e=>({url:e,data:await t({url:e})})))).map(({url:e,data:t})=>[e.toString(),t]))}function tp({maxTokens:e,temperature:t,topP:r,topK:a,presencePenalty:s,frequencyPenalty:n,stopSequences:i,seed:o}){if(null!=e){if(!Number.isInteger(e))throw new ez({parameter:"maxTokens",value:e,message:"maxTokens must be an integer"});if(e<1)throw new ez({parameter:"maxTokens",value:e,message:"maxTokens must be >= 1"})}if(null!=t&&"number"!=typeof t)throw new ez({parameter:"temperature",value:t,message:"temperature must be a number"});if(null!=r&&"number"!=typeof r)throw new ez({parameter:"topP",value:r,message:"topP must be a number"});if(null!=a&&"number"!=typeof a)throw new ez({parameter:"topK",value:a,message:"topK must be a number"});if(null!=s&&"number"!=typeof s)throw new ez({parameter:"presencePenalty",value:s,message:"presencePenalty must be a number"});if(null!=n&&"number"!=typeof n)throw new ez({parameter:"frequencyPenalty",value:n,message:"frequencyPenalty must be a number"});if(null!=o&&!Number.isInteger(o))throw new ez({parameter:"seed",value:o,message:"seed must be an integer"});return{maxTokens:e,temperature:null!=t?t:0,topP:r,topK:a,presencePenalty:s,frequencyPenalty:n,stopSequences:null!=i&&i.length>0?i:void 0,seed:o}}function tm(e){var t,r,a;let s=[];for(let n of e){let e;try{e=new URL(n.url)}catch(e){throw Error(`Invalid URL: ${n.url}`)}switch(e.protocol){case"http:":case"https:":if(null==(t=n.contentType)?void 0:t.startsWith("image/"))s.push({type:"image",image:e});else{if(!n.contentType)throw Error("If the attachment is not an image, it must specify a content type");s.push({type:"file",data:e,mimeType:n.contentType})}break;case"data:":{let e,t,i;try{[e,t]=n.url.split(","),i=e.split(";")[0].split(":")[1]}catch(e){throw Error(`Error processing data URL: ${n.url}`)}if(null==i||null==t)throw Error(`Invalid data URL format: ${n.url}`);if(null==(r=n.contentType)?void 0:r.startsWith("image/"))s.push({type:"image",image:tn(t)});else if(null==(a=n.contentType)?void 0:a.startsWith("text/"))s.push({type:"text",text:function(e){try{return new TextDecoder().decode(e)}catch(e){throw Error("Error decoding Uint8Array to text")}}(tn(t))});else{if(!n.contentType)throw Error("If the attachment is not an image or text, it must specify a content type");s.push({type:"file",data:t,mimeType:n.contentType})}break}default:throw Error(`Unsupported URL protocol: ${e.protocol}`)}}return s}c=tl;var th="AI_MessageConversionError",tf=`vercel.ai.error.${th}`,tg=Symbol.for(tf),ty=class extends b.bD{constructor({originalMessage:e,message:t}){super({name:th,message:t}),this[d]=!0,this.originalMessage=e}static isInstance(e){return b.bD.hasMarker(e,tf)}};d=tg;var tv=T.z.lazy(()=>T.z.union([T.z.null(),T.z.string(),T.z.number(),T.z.boolean(),T.z.record(T.z.string(),tv),T.z.array(tv)])),t_=T.z.record(T.z.string(),T.z.record(T.z.string(),tv)),tb=T.z.array(T.z.union([T.z.object({type:T.z.literal("text"),text:T.z.string()}),T.z.object({type:T.z.literal("image"),data:T.z.string(),mimeType:T.z.string().optional()})])),tw=T.z.object({type:T.z.literal("text"),text:T.z.string(),providerOptions:t_.optional(),experimental_providerMetadata:t_.optional()}),tx=T.z.object({type:T.z.literal("image"),image:T.z.union([ta,T.z.instanceof(URL)]),mimeType:T.z.string().optional(),providerOptions:t_.optional(),experimental_providerMetadata:t_.optional()}),tk=T.z.object({type:T.z.literal("file"),data:T.z.union([ta,T.z.instanceof(URL)]),filename:T.z.string().optional(),mimeType:T.z.string(),providerOptions:t_.optional(),experimental_providerMetadata:t_.optional()}),tT=T.z.object({type:T.z.literal("reasoning"),text:T.z.string(),providerOptions:t_.optional(),experimental_providerMetadata:t_.optional()}),tI=T.z.object({type:T.z.literal("redacted-reasoning"),data:T.z.string(),providerOptions:t_.optional(),experimental_providerMetadata:t_.optional()}),tS=T.z.object({type:T.z.literal("tool-call"),toolCallId:T.z.string(),toolName:T.z.string(),args:T.z.unknown(),providerOptions:t_.optional(),experimental_providerMetadata:t_.optional()}),tz=T.z.object({type:T.z.literal("tool-result"),toolCallId:T.z.string(),toolName:T.z.string(),result:T.z.unknown(),content:tb.optional(),isError:T.z.boolean().optional(),providerOptions:t_.optional(),experimental_providerMetadata:t_.optional()}),tE=T.z.object({role:T.z.literal("system"),content:T.z.string(),providerOptions:t_.optional(),experimental_providerMetadata:t_.optional()}),tA=T.z.object({role:T.z.literal("user"),content:T.z.union([T.z.string(),T.z.array(T.z.union([tw,tx,tk]))]),providerOptions:t_.optional(),experimental_providerMetadata:t_.optional()}),tj=T.z.object({role:T.z.literal("assistant"),content:T.z.union([T.z.string(),T.z.array(T.z.union([tw,tk,tT,tI,tS]))]),providerOptions:t_.optional(),experimental_providerMetadata:t_.optional()}),tC=T.z.object({role:T.z.literal("tool"),content:T.z.array(tz),providerOptions:t_.optional(),experimental_providerMetadata:t_.optional()}),tN=T.z.union([tE,tA,tj,tC]);function tR({prompt:e,tools:t}){if(null==e.prompt&&null==e.messages)throw new b.M3({prompt:e,message:"prompt or messages must be defined"});if(null!=e.prompt&&null!=e.messages)throw new b.M3({prompt:e,message:"prompt and messages cannot be defined at the same time"});if(null!=e.system&&"string"!=typeof e.system)throw new b.M3({prompt:e,message:"system must be a string"});if(null!=e.prompt){if("string"!=typeof e.prompt)throw new b.M3({prompt:e,message:"prompt must be a string"});return{type:"prompt",system:e.system,messages:[{role:"user",content:e.prompt}]}}if(null!=e.messages){let r="ui-messages"===function(e){if(!Array.isArray(e))throw new b.M3({prompt:e,message:`messages must be an array of CoreMessage or UIMessage
Received non-array value: ${JSON.stringify(e)}`,cause:e});if(0===e.length)return"messages";let t=e.map(tO);if(t.some(e=>"has-ui-specific-parts"===e))return"ui-messages";let r=t.findIndex(e=>"has-core-specific-parts"!==e&&"message"!==e);if(-1===r)return"messages";throw new b.M3({prompt:e,message:`messages must be an array of CoreMessage or UIMessage
Received message of type: "${t[r]}" at index ${r}
messages[${r}]: ${JSON.stringify(e[r])}`,cause:e})}(e.messages)?function(e,t){var r,a;let s=null!=(r=null==t?void 0:t.tools)?r:{},n=[];for(let t=0;t<e.length;t++){let r=e[t],i=t===e.length-1,{role:o,content:l,experimental_attachments:u}=r;switch(o){case"system":n.push({role:"system",content:l});break;case"user":if(null==r.parts)n.push({role:"user",content:u?[{type:"text",text:l},...tm(u)]:l});else{let e=r.parts.filter(e=>"text"===e.type).map(e=>({type:"text",text:e.text}));n.push({role:"user",content:u?[...e,...tm(u)]:e})}break;case"assistant":{if(null!=r.parts){let e=function(){let e=[];for(let t of o)switch(t.type){case"file":case"text":e.push(t);break;case"reasoning":for(let r of t.details)switch(r.type){case"text":e.push({type:"reasoning",text:r.text,signature:r.signature});break;case"redacted":e.push({type:"redacted-reasoning",data:r.data})}break;case"tool-invocation":e.push({type:"tool-call",toolCallId:t.toolInvocation.toolCallId,toolName:t.toolInvocation.toolName,args:t.toolInvocation.args});break;default:throw Error(`Unsupported part: ${t}`)}n.push({role:"assistant",content:e});let a=o.filter(e=>"tool-invocation"===e.type).map(e=>e.toolInvocation);a.length>0&&n.push({role:"tool",content:a.map(e=>{if(!("result"in e))throw new ty({originalMessage:r,message:"ToolInvocation must have a result: "+JSON.stringify(e)});let{toolCallId:t,toolName:a,result:n}=e,i=s[a];return(null==i?void 0:i.experimental_toToolResultContent)!=null?{type:"tool-result",toolCallId:t,toolName:a,result:i.experimental_toToolResultContent(n),experimental_content:i.experimental_toToolResultContent(n)}:{type:"tool-result",toolCallId:t,toolName:a,result:n}})}),o=[],i=!1,t++},t=0,i=!1,o=[];for(let s of r.parts)switch(s.type){case"text":i&&e(),o.push(s);break;case"file":case"reasoning":o.push(s);break;case"tool-invocation":(null!=(a=s.toolInvocation.step)?a:0)!==t&&e(),o.push(s),i=!0}e();break}let e=r.toolInvocations;if(null==e||0===e.length){n.push({role:"assistant",content:l});break}let t=e.reduce((e,t)=>{var r;return Math.max(e,null!=(r=t.step)?r:0)},0);for(let a=0;a<=t;a++){let t=e.filter(e=>{var t;return(null!=(t=e.step)?t:0)===a});0!==t.length&&(n.push({role:"assistant",content:[...i&&l&&0===a?[{type:"text",text:l}]:[],...t.map(({toolCallId:e,toolName:t,args:r})=>({type:"tool-call",toolCallId:e,toolName:t,args:r}))]}),n.push({role:"tool",content:t.map(e=>{if(!("result"in e))throw new ty({originalMessage:r,message:"ToolInvocation must have a result: "+JSON.stringify(e)});let{toolCallId:t,toolName:a,result:n}=e,i=s[a];return(null==i?void 0:i.experimental_toToolResultContent)!=null?{type:"tool-result",toolCallId:t,toolName:a,result:i.experimental_toToolResultContent(n),experimental_content:i.experimental_toToolResultContent(n)}:{type:"tool-result",toolCallId:t,toolName:a,result:n}})}))}l&&!i&&n.push({role:"assistant",content:l});break}case"data":break;default:throw new ty({originalMessage:r,message:`Unsupported role: ${o}`})}}return n}(e.messages,{tools:t}):e.messages;if(0===r.length)throw new b.M3({prompt:e,message:"messages must not be empty"});let a=(0,w.ZZ)({value:r,schema:T.z.array(tN)});if(!a.success)throw new b.M3({prompt:e,message:`message must be a CoreMessage or a UI message
Validation error: ${a.error.message}`,cause:a.error});return{type:"messages",messages:r,system:e.system}}throw Error("unreachable")}function tO(e){return"object"==typeof e&&null!==e&&("function"===e.role||"data"===e.role||"toolInvocations"in e||"parts"in e||"experimental_attachments"in e)?"has-ui-specific-parts":"object"==typeof e&&null!==e&&"content"in e&&(Array.isArray(e.content)||"experimental_providerMetadata"in e||"providerOptions"in e)?"has-core-specific-parts":"object"==typeof e&&null!==e&&"role"in e&&"content"in e&&"string"==typeof e.content&&["system","user","assistant","tool"].includes(e.role)?"message":"other"}function tP({promptTokens:e,completionTokens:t}){return{promptTokens:e,completionTokens:t,totalTokens:e+t}}function tM(e,t){return{promptTokens:e.promptTokens+t.promptTokens,completionTokens:e.completionTokens+t.completionTokens,totalTokens:e.totalTokens+t.totalTokens}}function t$({prompt:e,schema:t,schemaPrefix:r=null!=t?"JSON schema:":void 0,schemaSuffix:a=null!=t?"You MUST answer with a JSON object that matches the JSON schema above.":"You MUST answer with JSON."}){return[null!=e&&e.length>0?e:void 0,null!=e&&e.length>0?"":void 0,r,null!=t?JSON.stringify(t):void 0,a].filter(e=>null!=e).join("\n")}function tZ(e){let t=e.pipeThrough(new TransformStream);return t[Symbol.asyncIterator]=()=>{let e=t.getReader();return{async next(){let{done:t,value:r}=await e.read();return t?{done:!0,value:void 0}:{done:!1,value:r}}}},t}var tD={type:"no-schema",jsonSchema:void 0,validatePartialResult:({value:e,textDelta:t})=>({success:!0,value:{partial:e,textDelta:t}}),validateFinalResult:(e,t)=>void 0===e?{success:!1,error:new e9({message:"No object generated: response did not match schema.",text:t.text,response:t.response,usage:t.usage,finishReason:t.finishReason})}:{success:!0,value:e},createElementStream(){throw new b.b8({functionality:"element streams in no-schema mode"})}},tq=e=>({type:"object",jsonSchema:e.jsonSchema,validatePartialResult:({value:e,textDelta:t})=>({success:!0,value:{partial:e,textDelta:t}}),validateFinalResult:t=>safeValidateTypes2({value:t,schema:e}),createElementStream(){throw new UnsupportedFunctionalityError({functionality:"element streams in object mode"})}}),tL=e=>{let{$schema:t,...r}=e.jsonSchema;return{type:"enum",jsonSchema:{$schema:"http://json-schema.org/draft-07/schema#",type:"object",properties:{elements:{type:"array",items:r}},required:["elements"],additionalProperties:!1},validatePartialResult({value:t,latestObject:r,isFirstDelta:a,isFinalDelta:s}){var n;if(!isJSONObject(t)||!isJSONArray(t.elements))return{success:!1,error:new TypeValidationError({value:t,cause:"value must be an object that contains an array of elements"})};let i=t.elements,o=[];for(let t=0;t<i.length;t++){let r=safeValidateTypes2({value:i[t],schema:e});if(t!==i.length-1||s){if(!r.success)return r;o.push(r.value)}}let l=null!=(n=null==r?void 0:r.length)?n:0,u="";return a&&(u+="["),l>0&&(u+=","),u+=o.slice(l).map(e=>JSON.stringify(e)).join(","),s&&(u+="]"),{success:!0,value:{partial:o,textDelta:u}}},validateFinalResult(t){if(!isJSONObject(t)||!isJSONArray(t.elements))return{success:!1,error:new TypeValidationError({value:t,cause:"value must be an object that contains an array of elements"})};let r=t.elements;for(let t of r){let r=safeValidateTypes2({value:t,schema:e});if(!r.success)return r}return{success:!0,value:r}},createElementStream(e){let t=0;return tZ(e.pipeThrough(new TransformStream({transform(e,r){switch(e.type){case"object":{let a=e.object;for(;t<a.length;t++)r.enqueue(a[t]);break}case"text-delta":case"finish":case"error":break;default:throw Error(`Unsupported chunk type: ${e}`)}}})))}}},tF=e=>({type:"enum",jsonSchema:{$schema:"http://json-schema.org/draft-07/schema#",type:"object",properties:{result:{type:"string",enum:e}},required:["result"],additionalProperties:!1},validateFinalResult(t){if(!isJSONObject(t)||"string"!=typeof t.result)return{success:!1,error:new TypeValidationError({value:t,cause:'value must be an object that contains a string in the "result" property.'})};let r=t.result;return e.includes(r)?{success:!0,value:r}:{success:!1,error:new TypeValidationError({value:t,cause:"value must be a string in the enum"})}},validatePartialResult(){throw new UnsupportedFunctionalityError({functionality:"partial results in enum mode"})},createElementStream(){throw new UnsupportedFunctionalityError({functionality:"element streams in enum mode"})}});function tU(e){return JSON.stringify(e.map(e=>({...e,content:"string"==typeof e.content?e.content:e.content.map(tV)})))}function tV(e){return"image"===e.type?{...e,image:e.image instanceof Uint8Array?ts(e.image):e.image}:e}(0,w.hK)({prefix:"aiobj",size:24});var tB=class{constructor(){this.status={type:"pending"},this._resolve=void 0,this._reject=void 0}get value(){return this.promise||(this.promise=new Promise((e,t)=>{"resolved"===this.status.type?e(this.status.value):"rejected"===this.status.type&&t(this.status.error),this._resolve=e,this._reject=t})),this.promise}resolve(e){var t;this.status={type:"resolved",value:e},this.promise&&(null==(t=this._resolve)||t.call(this,e))}reject(e){var t;this.status={type:"rejected",error:e},this.promise&&(null==(t=this._reject)||t.call(this,e))}};function tJ(){let e,t;return{promise:new Promise((r,a)=>{e=r,t=a}),resolve:e,reject:t}}function tY(){let e=[],t=null,r=!1,a=tJ(),s=async()=>{if(r&&0===e.length){null==t||t.close();return}if(0===e.length)return a=tJ(),await a.promise,s();try{let{value:a,done:n}=await e[0].read();n?(e.shift(),e.length>0?await s():r&&(null==t||t.close())):null==t||t.enqueue(a)}catch(a){null==t||t.error(a),e.shift(),r&&0===e.length&&(null==t||t.close())}};return{stream:new ReadableStream({start(e){t=e},pull:s,async cancel(){for(let t of e)await t.cancel();e=[],r=!0}}),addStream:t=>{if(r)throw Error("Cannot add inner stream: outer stream is closed");e.push(t.getReader()),a.resolve()},close:()=>{r=!0,a.resolve(),0===e.length&&(null==t||t.close())},terminate:()=>{r=!0,a.resolve(),e.forEach(e=>e.cancel()),e=[],null==t||t.close()}}}function tK(){var e,t;return null!=(t=null==(e=null==globalThis?void 0:globalThis.performance)?void 0:e.now())?t:Date.now()}(0,w.hK)({prefix:"aiobj",size:24});var tW="AI_NoOutputSpecifiedError",tG=`vercel.ai.error.${tW}`,tH=Symbol.for(tG),tX=class extends b.bD{constructor({message:e="No output specified."}={}){super({name:tW,message:e}),this[p]=!0}static isInstance(e){return b.bD.hasMarker(e,tG)}};p=tH;var tQ="AI_ToolExecutionError",t0=`vercel.ai.error.${tQ}`,t1=Symbol.for(t0),t4=class extends b.bD{constructor({toolArgs:e,toolName:t,toolCallId:r,cause:a,message:s=`Error executing tool ${t}: ${(0,b.u1)(a)}`}){super({name:tQ,message:s,cause:a}),this[m]=!0,this.toolArgs=e,this.toolName=t,this.toolCallId=r}static isInstance(e){return b.bD.hasMarker(e,t0)}};function t9({tools:e,toolChoice:t,activeTools:r}){return null!=e&&Object.keys(e).length>0?{tools:(null!=r?Object.entries(e).filter(([e])=>r.includes(e)):Object.entries(e)).map(([e,t])=>{let r=t.type;switch(r){case void 0:case"function":return{type:"function",name:e,description:t.description,parameters:ev(t.parameters).jsonSchema};case"provider-defined":return{type:"provider-defined",name:e,id:t.id,args:t.args};default:throw Error(`Unsupported tool type: ${r}`)}}),toolChoice:null==t?{type:"auto"}:"string"==typeof t?{type:t}:{type:"tool",toolName:t.toolName}}:{tools:void 0,toolChoice:void 0}}m=t1;var t2=/^([\s\S]*?)(\s+)(\S*)$/;function t6(e){let t=e.match(t2);return t?{prefix:t[1],whitespace:t[2],suffix:t[3]}:void 0}var t3="AI_InvalidToolArgumentsError",t5=`vercel.ai.error.${t3}`,t8=Symbol.for(t5),t7=class extends b.bD{constructor({toolArgs:e,toolName:t,cause:r,message:a=`Invalid arguments for tool ${t}: ${(0,b.u1)(r)}`}){super({name:t3,message:a,cause:r}),this[h]=!0,this.toolArgs=e,this.toolName=t}static isInstance(e){return b.bD.hasMarker(e,t5)}};h=t8;var re="AI_NoSuchToolError",rt=`vercel.ai.error.${re}`,rr=Symbol.for(rt),ra=class extends b.bD{constructor({toolName:e,availableTools:t,message:r=`Model tried to call unavailable tool '${e}'. ${void 0===t?"No tools are available.":`Available tools: ${t.join(", ")}.`}`}){super({name:re,message:r}),this[f]=!0,this.toolName=e,this.availableTools=t}static isInstance(e){return b.bD.hasMarker(e,rt)}};f=rr;var rs="AI_ToolCallRepairError",rn=`vercel.ai.error.${rs}`,ri=Symbol.for(rn),ro=class extends b.bD{constructor({cause:e,originalError:t,message:r=`Error repairing tool call: ${(0,b.u1)(e)}`}){super({name:rs,message:r,cause:e}),this[g]=!0,this.originalError=t}static isInstance(e){return b.bD.hasMarker(e,rn)}};async function rl({toolCall:e,tools:t,repairToolCall:r,system:a,messages:s}){if(null==t)throw new ra({toolName:e.toolName});try{return await ru({toolCall:e,tools:t})}catch(i){if(null==r||!(ra.isInstance(i)||t7.isInstance(i)))throw i;let n=null;try{n=await r({toolCall:e,tools:t,parameterSchema:({toolName:e})=>ev(t[e].parameters).jsonSchema,system:a,messages:s,error:i})}catch(e){throw new ro({cause:e,originalError:i})}if(null==n)throw i;return await ru({toolCall:n,tools:t})}}async function ru({toolCall:e,tools:t}){let r=e.toolName,a=t[r];if(null==a)throw new ra({toolName:e.toolName,availableTools:Object.keys(t)});let s=ev(a.parameters),n=""===e.args.trim()?(0,w.ZZ)({value:{},schema:s}):(0,w.N8)({text:e.args,schema:s});if(!1===n.success)throw new t7({toolName:r,toolArgs:e.args,cause:n.error});return{type:"tool-call",toolCallId:e.toolCallId,toolName:r,args:n.value}}function rc(e){let t=e.filter(e=>"text"===e.type).map(e=>e.text).join("");return t.length>0?t:void 0}function rd({text:e="",files:t,reasoning:r,tools:a,toolCalls:s,toolResults:n,messageId:i,generateMessageId:o}){let l=[],u=[];return r.length>0&&u.push(...r.map(e=>"text"===e.type?{...e,type:"reasoning"}:{...e,type:"redacted-reasoning"})),t.length>0&&u.push(...t.map(e=>({type:"file",data:e.base64,mimeType:e.mimeType}))),e.length>0&&u.push({type:"text",text:e}),s.length>0&&u.push(...s),u.length>0&&l.push({role:"assistant",content:u,id:i}),n.length>0&&l.push({role:"tool",id:o(),content:n.map(e=>{let t=a[e.toolName];return(null==t?void 0:t.experimental_toToolResultContent)!=null?{type:"tool-result",toolCallId:e.toolCallId,toolName:e.toolName,result:t.experimental_toToolResultContent(e.result),experimental_content:t.experimental_toToolResultContent(e.result)}:{type:"tool-result",toolCallId:e.toolCallId,toolName:e.toolName,result:e.result}})}),l}g=ri;var rp=(0,w.hK)({prefix:"aitxt",size:24}),rm=(0,w.hK)({prefix:"msg",size:24});async function rh({model:e,tools:t,toolChoice:r,system:a,prompt:s,messages:n,maxRetries:i,abortSignal:o,headers:l,maxSteps:u=1,experimental_generateMessageId:c=rm,experimental_output:d,experimental_continueSteps:p=!1,experimental_telemetry:m,experimental_providerMetadata:h,providerOptions:f=h,experimental_activeTools:g,experimental_prepareStep:y,experimental_repairToolCall:v,_internal:{generateId:_=rp,currentDate:b=()=>new Date}={},onStepFinish:w,...x}){var k;if(u<1)throw new ez({parameter:"maxSteps",value:u,message:"maxSteps must be at least 1"});let{maxRetries:T,retry:I}=eO({maxRetries:i}),S=eM({model:e,telemetry:m,headers:l,settings:{...x,maxRetries:T}}),z=tR({prompt:{system:null!=(k=null==d?void 0:d.injectIntoSystemPrompt({system:a,model:e}))?k:a,prompt:s,messages:n},tools:t}),E=eq(m);return eL({name:"ai.generateText",attributes:eF({telemetry:m,attributes:{...eP({operationId:"ai.generateText",telemetry:m}),...S,"ai.model.provider":e.provider,"ai.model.id":e.modelId,"ai.prompt":{input:()=>JSON.stringify({system:a,prompt:s,messages:n})},"ai.settings.maxSteps":u}}),tracer:E,fn:async s=>{var n,i,h,k,T,A,j,C,N,R,O,P,M,$;let Z;let D=tp(x),q=[],L=[],F=[],U=0,V=[],B="",J=[],Y=[],K={completionTokens:0,promptTokens:0,totalTokens:0},W="initial";do{let s=0===U?z.type:"messages",P=[...z.messages,...V],M=await (null==y?void 0:y({model:e,steps:Y,maxSteps:u,stepNumber:U})),$=null!=(n=null==M?void 0:M.toolChoice)?n:r,G=null!=(i=null==M?void 0:M.experimental_activeTools)?i:g,H=null!=(h=null==M?void 0:M.model)?h:e,X=await tc({prompt:{type:s,system:z.system,messages:P},modelSupportsImageUrls:H.supportsImageUrls,modelSupportsUrl:null==(k=H.supportsUrl)?void 0:k.bind(H)}),Q={type:"regular",...t9({tools:t,toolChoice:$,activeTools:G})};Z=await I(()=>eL({name:"ai.generateText.doGenerate",attributes:eF({telemetry:m,attributes:{...eP({operationId:"ai.generateText.doGenerate",telemetry:m}),...S,"ai.model.provider":H.provider,"ai.model.id":H.modelId,"ai.prompt.format":{input:()=>s},"ai.prompt.messages":{input:()=>tU(X)},"ai.prompt.tools":{input:()=>{var e;return null==(e=Q.tools)?void 0:e.map(e=>JSON.stringify(e))}},"ai.prompt.toolChoice":{input:()=>null!=Q.toolChoice?JSON.stringify(Q.toolChoice):void 0},"gen_ai.system":H.provider,"gen_ai.request.model":H.modelId,"gen_ai.request.frequency_penalty":x.frequencyPenalty,"gen_ai.request.max_tokens":x.maxTokens,"gen_ai.request.presence_penalty":x.presencePenalty,"gen_ai.request.stop_sequences":x.stopSequences,"gen_ai.request.temperature":x.temperature,"gen_ai.request.top_k":x.topK,"gen_ai.request.top_p":x.topP}}),tracer:E,fn:async t=>{var r,a,n,i,u,c;let p=await H.doGenerate({mode:Q,...D,inputFormat:s,responseFormat:null==d?void 0:d.responseFormat({model:e}),prompt:X,providerMetadata:f,abortSignal:o,headers:l}),h={id:null!=(a=null==(r=p.response)?void 0:r.id)?a:_(),timestamp:null!=(i=null==(n=p.response)?void 0:n.timestamp)?i:b(),modelId:null!=(c=null==(u=p.response)?void 0:u.modelId)?c:H.modelId};return t.setAttributes(eF({telemetry:m,attributes:{"ai.response.finishReason":p.finishReason,"ai.response.text":{output:()=>p.text},"ai.response.toolCalls":{output:()=>JSON.stringify(p.toolCalls)},"ai.response.id":h.id,"ai.response.model":h.modelId,"ai.response.timestamp":h.timestamp.toISOString(),"ai.usage.promptTokens":p.usage.promptTokens,"ai.usage.completionTokens":p.usage.completionTokens,"gen_ai.response.finish_reasons":[p.finishReason],"gen_ai.response.id":h.id,"gen_ai.response.model":h.modelId,"gen_ai.usage.input_tokens":p.usage.promptTokens,"gen_ai.usage.output_tokens":p.usage.completionTokens}})),{...p,response:h}}})),q=await Promise.all((null!=(T=Z.toolCalls)?T:[]).map(e=>rl({toolCall:e,tools:t,repairToolCall:v,system:a,messages:P}))),L=null==t?[]:await rf({toolCalls:q,tools:t,tracer:E,telemetry:m,messages:P,abortSignal:o});let ee=tP(Z.usage);K=tM(K,ee);let et="done";++U<u&&(p&&"length"===Z.finishReason&&0===q.length?et="continue":q.length>0&&L.length===q.length&&(et="tool-result"));let er=null!=(A=Z.text)?A:"",ea="continue"===W&&B.trimEnd()!==B?er.trimStart():er,es="continue"===et?function(e){let t=t6(e);return t?t.prefix+t.whitespace:e}(ea):ea;if(B="continue"===et||"continue"===W?B+es:es,F=ry(Z.reasoning),J.push(...null!=(j=Z.sources)?j:[]),"continue"===W){let e=V[V.length-1];"string"==typeof e.content?e.content+=es:e.content.push({text:es,type:"text"})}else V.push(...rd({text:B,files:rv(Z.files),reasoning:ry(Z.reasoning),tools:null!=t?t:{},toolCalls:q,toolResults:L,messageId:c(),generateMessageId:c}));let en={stepType:W,text:es,reasoning:rc(F),reasoningDetails:F,files:rv(Z.files),sources:null!=(C=Z.sources)?C:[],toolCalls:q,toolResults:L,finishReason:Z.finishReason,usage:ee,warnings:Z.warnings,logprobs:Z.logprobs,request:null!=(N=Z.request)?N:{},response:{...Z.response,headers:null==(R=Z.rawResponse)?void 0:R.headers,body:null==(O=Z.rawResponse)?void 0:O.body,messages:structuredClone(V)},providerMetadata:Z.providerMetadata,experimental_providerMetadata:Z.providerMetadata,isContinued:"continue"===et};Y.push(en),await (null==w?void 0:w(en)),W=et}while("done"!==W);return s.setAttributes(eF({telemetry:m,attributes:{"ai.response.finishReason":Z.finishReason,"ai.response.text":{output:()=>Z.text},"ai.response.toolCalls":{output:()=>JSON.stringify(Z.toolCalls)},"ai.usage.promptTokens":Z.usage.promptTokens,"ai.usage.completionTokens":Z.usage.completionTokens}})),new rg({text:B,files:rv(Z.files),reasoning:rc(F),reasoningDetails:F,sources:J,outputResolver:()=>{if(null==d)throw new tX;return d.parseOutput({text:B},{response:Z.response,usage:K,finishReason:Z.finishReason})},toolCalls:q,toolResults:L,finishReason:Z.finishReason,usage:K,warnings:Z.warnings,request:null!=(P=Z.request)?P:{},response:{...Z.response,headers:null==(M=Z.rawResponse)?void 0:M.headers,body:null==($=Z.rawResponse)?void 0:$.body,messages:V},logprobs:Z.logprobs,steps:Y,providerMetadata:Z.providerMetadata})}})}async function rf({toolCalls:e,tools:t,tracer:r,telemetry:a,messages:s,abortSignal:n}){return(await Promise.all(e.map(async({toolCallId:e,toolName:i,args:o})=>{let l=t[i];if((null==l?void 0:l.execute)==null)return;let u=await eL({name:"ai.toolCall",attributes:eF({telemetry:a,attributes:{...eP({operationId:"ai.toolCall",telemetry:a}),"ai.toolCall.name":i,"ai.toolCall.id":e,"ai.toolCall.args":{output:()=>JSON.stringify(o)}}}),tracer:r,fn:async t=>{try{let r=await l.execute(o,{toolCallId:e,messages:s,abortSignal:n});try{t.setAttributes(eF({telemetry:a,attributes:{"ai.toolCall.result":{output:()=>JSON.stringify(r)}}}))}catch(e){}return r}catch(t){throw new t4({toolCallId:e,toolName:i,toolArgs:o,cause:t})}}});return{type:"tool-result",toolCallId:e,toolName:i,args:o,result:u}}))).filter(e=>null!=e)}var rg=class{constructor(e){this.text=e.text,this.files=e.files,this.reasoning=e.reasoning,this.reasoningDetails=e.reasoningDetails,this.toolCalls=e.toolCalls,this.toolResults=e.toolResults,this.finishReason=e.finishReason,this.usage=e.usage,this.warnings=e.warnings,this.request=e.request,this.response=e.response,this.steps=e.steps,this.experimental_providerMetadata=e.providerMetadata,this.providerMetadata=e.providerMetadata,this.logprobs=e.logprobs,this.outputResolver=e.outputResolver,this.sources=e.sources}get experimental_output(){return this.outputResolver()}};function ry(e){return null==e?[]:"string"==typeof e?[{type:"text",text:e}]:e}function rv(e){var t;return null!=(t=null==e?void 0:e.map(e=>new eY(e)))?t:[]}eb({},{object:()=>rz,text:()=>rS});var r_="AI_InvalidStreamPartError",rb=`vercel.ai.error.${r_}`,rw=Symbol.for(rb),rx=class extends b.bD{constructor({chunk:e,message:t}){super({name:r_,message:t}),this[y]=!0,this.chunk=e}static isInstance(e){return b.bD.hasMarker(e,rb)}};y=rw;var rk="vercel.ai.error.AI_MCPClientError",rT=Symbol.for(rk),rI=class extends b.bD{constructor({name:e="MCPClientError",message:t,cause:r}){super({name:e,message:t,cause:r}),this[v]=!0}static isInstance(e){return b.bD.hasMarker(e,rk)}};v=rT;var rS=()=>({type:"text",responseFormat:()=>({type:"text"}),injectIntoSystemPrompt:({system:e})=>e,parsePartial:({text:e})=>({partial:e}),parseOutput:({text:e})=>e}),rz=({schema:e})=>{let t=ev(e);return{type:"object",responseFormat:({model:e})=>({type:"json",schema:e.supportsStructuredOutputs?t.jsonSchema:void 0}),injectIntoSystemPrompt:({system:e,model:r})=>r.supportsStructuredOutputs?e:t$({prompt:e,schema:t.jsonSchema}),parsePartial({text:e}){let t=eu(e);switch(t.state){case"failed-parse":case"undefined-input":return;case"repaired-parse":case"successful-parse":return{partial:t.value};default:{let e=t.state;throw Error(`Unsupported parse state: ${e}`)}}},parseOutput({text:e},r){let a=(0,w.N8)({text:e});if(!a.success)throw new e9({message:"No object generated: could not parse the response.",cause:a.error,text:e,response:r.response,usage:r.usage,finishReason:r.finishReason});let s=(0,w.ZZ)({value:a.value,schema:t});if(!s.success)throw new e9({message:"No object generated: response did not match schema.",cause:s.error,text:e,response:r.response,usage:r.usage,finishReason:r.finishReason});return s.value}}};function rE(e){return void 0===e?[]:Array.isArray(e)?e:[e]}async function rA({stream:e,onError:t}){let r=e.getReader();try{for(;;){let{done:e}=await r.read();if(e)break}}catch(e){null==t||t(e)}finally{r.releaseLock()}}function rj(e,t){let r,a;let s=e.getReader(),n=t.getReader(),i=!1,o=!1;async function l(e){try{null==r&&(r=s.read());let t=await r;r=void 0,t.done?e.close():e.enqueue(t.value)}catch(t){e.error(t)}}async function u(e){try{null==a&&(a=n.read());let t=await a;a=void 0,t.done?e.close():e.enqueue(t.value)}catch(t){e.error(t)}}return new ReadableStream({async pull(e){try{if(i){await u(e);return}if(o){await l(e);return}null==r&&(r=s.read()),null==a&&(a=n.read());let{result:t,reader:c}=await Promise.race([r.then(e=>({result:e,reader:s})),a.then(e=>({result:e,reader:n}))]);t.done||e.enqueue(t.value),c===s?(r=void 0,t.done&&(await u(e),i=!0)):(a=void 0,t.done&&(o=!0,await l(e)))}catch(t){e.error(t)}},cancel(){s.cancel(),n.cancel()}})}var rC=(0,w.hK)({prefix:"aitxt",size:24}),rN=(0,w.hK)({prefix:"msg",size:24});function rR({model:e,tools:t,toolChoice:r,system:a,prompt:s,messages:n,maxRetries:i,abortSignal:o,headers:l,maxSteps:u=1,experimental_generateMessageId:c=rN,experimental_output:d,experimental_continueSteps:p=!1,experimental_telemetry:m,experimental_providerMetadata:h,providerOptions:f=h,experimental_toolCallStreaming:g=!1,toolCallStreaming:y=g,experimental_activeTools:v,experimental_repairToolCall:_,experimental_transform:b,onChunk:w,onError:x,onFinish:k,onStepFinish:T,_internal:{now:I=tK,generateId:S=rC,currentDate:z=()=>new Date}={},...E}){return new rO({model:e,telemetry:m,headers:l,settings:E,maxRetries:i,abortSignal:o,system:a,prompt:s,messages:n,tools:t,toolChoice:r,toolCallStreaming:y,transforms:rE(b),activeTools:v,repairToolCall:_,maxSteps:u,output:d,continueSteps:p,providerOptions:f,onChunk:w,onError:x,onFinish:k,onStepFinish:T,now:I,currentDate:z,generateId:S,generateMessageId:c})}var rO=class{constructor({model:e,telemetry:t,headers:r,settings:a,maxRetries:s,abortSignal:n,system:i,prompt:o,messages:l,tools:u,toolChoice:c,toolCallStreaming:d,transforms:p,activeTools:m,repairToolCall:h,maxSteps:f,output:g,continueSteps:y,providerOptions:v,now:_,currentDate:x,generateId:k,generateMessageId:T,onChunk:I,onError:S,onFinish:z,onStepFinish:E}){var A;let j,C,N,R;if(this.warningsPromise=new tB,this.usagePromise=new tB,this.finishReasonPromise=new tB,this.providerMetadataPromise=new tB,this.textPromise=new tB,this.reasoningPromise=new tB,this.reasoningDetailsPromise=new tB,this.sourcesPromise=new tB,this.filesPromise=new tB,this.toolCallsPromise=new tB,this.toolResultsPromise=new tB,this.requestPromise=new tB,this.responsePromise=new tB,this.stepsPromise=new tB,f<1)throw new ez({parameter:"maxSteps",value:f,message:"maxSteps must be at least 1"});this.output=g;let O="",P="",M="",$=[],Z=[],D=[],q=[],L={id:k(),timestamp:x(),modelId:e.modelId,messages:[]},F=[],U=[],V="initial",B=[],J=new TransformStream({async transform(e,t){t.enqueue(e);let{part:r}=e;if(("text-delta"===r.type||"reasoning"===r.type||"source"===r.type||"tool-call"===r.type||"tool-result"===r.type||"tool-call-streaming-start"===r.type||"tool-call-delta"===r.type)&&await (null==I?void 0:I({chunk:r})),"error"===r.type&&await (null==S?void 0:S({error:r.error})),"text-delta"===r.type&&(O+=r.textDelta,P+=r.textDelta,M+=r.textDelta),"reasoning"===r.type&&(null==C?(C={type:"text",text:r.textDelta},$.push(C)):C.text+=r.textDelta),"reasoning-signature"===r.type){if(null==C)throw new b.bD({name:"InvalidStreamPart",message:"reasoning-signature without reasoning"});C.signature=r.signature,C=void 0}if("redacted-reasoning"===r.type&&$.push({type:"redacted",data:r.data}),"file"===r.type&&Z.push(r),"source"===r.type&&(q.push(r.source),D.push(r.source)),"tool-call"===r.type&&F.push(r),"tool-result"===r.type&&U.push(r),"step-finish"===r.type){let e=rd({text:P,files:Z,reasoning:$,tools:null!=u?u:{},toolCalls:F,toolResults:U,messageId:r.messageId,generateMessageId:T}),t=B.length,a="done";t+1<f&&(y&&"length"===r.finishReason&&0===F.length?a="continue":F.length>0&&U.length===F.length&&(a="tool-result"));let s={stepType:V,text:O,reasoning:rc($),reasoningDetails:$,files:Z,sources:D,toolCalls:F,toolResults:U,finishReason:r.finishReason,usage:r.usage,warnings:r.warnings,logprobs:r.logprobs,request:r.request,response:{...r.response,messages:[...L.messages,...e]},providerMetadata:r.experimental_providerMetadata,experimental_providerMetadata:r.experimental_providerMetadata,isContinued:r.isContinued};await (null==E?void 0:E(s)),B.push(s),F=[],U=[],O="",D=[],$=[],Z=[],C=void 0,"done"!==a&&(V=a),"continue"!==a&&(L.messages.push(...e),P="")}"finish"===r.type&&(L.id=r.response.id,L.timestamp=r.response.timestamp,L.modelId=r.response.modelId,L.headers=r.response.headers,R=r.usage,N=r.finishReason)},async flush(e){var r;try{if(0===B.length)return;let e=B[B.length-1];ee.warningsPromise.resolve(e.warnings),ee.requestPromise.resolve(e.request),ee.responsePromise.resolve(e.response),ee.toolCallsPromise.resolve(e.toolCalls),ee.toolResultsPromise.resolve(e.toolResults),ee.providerMetadataPromise.resolve(e.experimental_providerMetadata),ee.reasoningPromise.resolve(e.reasoning),ee.reasoningDetailsPromise.resolve(e.reasoningDetails);let a=null!=N?N:"unknown",s=null!=R?R:{completionTokens:NaN,promptTokens:NaN,totalTokens:NaN};ee.finishReasonPromise.resolve(a),ee.usagePromise.resolve(s),ee.textPromise.resolve(M),ee.sourcesPromise.resolve(q),ee.filesPromise.resolve(e.files),ee.stepsPromise.resolve(B),await (null==z?void 0:z({finishReason:a,logprobs:void 0,usage:s,text:M,reasoning:e.reasoning,reasoningDetails:e.reasoningDetails,files:e.files,sources:e.sources,toolCalls:e.toolCalls,toolResults:e.toolResults,request:null!=(r=e.request)?r:{},response:e.response,warnings:e.warnings,providerMetadata:e.providerMetadata,experimental_providerMetadata:e.experimental_providerMetadata,steps:B})),j.setAttributes(eF({telemetry:t,attributes:{"ai.response.finishReason":a,"ai.response.text":{output:()=>M},"ai.response.toolCalls":{output:()=>{var t;return(null==(t=e.toolCalls)?void 0:t.length)?JSON.stringify(e.toolCalls):void 0}},"ai.usage.promptTokens":s.promptTokens,"ai.usage.completionTokens":s.completionTokens}}))}catch(t){e.error(t)}finally{j.end()}}}),Y=tY();this.addStream=Y.addStream,this.closeStream=Y.close;let K=Y.stream;for(let e of p)K=K.pipeThrough(e({tools:u,stopStream(){Y.terminate()}}));this.baseStream=K.pipeThrough(function(e){if(!e)return new TransformStream({transform(e,t){t.enqueue({part:e,partialOutput:void 0})}});let t="",r="",a="";function s({controller:e,partialOutput:t}){e.enqueue({part:{type:"text-delta",textDelta:r},partialOutput:t}),r=""}return new TransformStream({transform(n,i){if("step-finish"===n.type&&s({controller:i}),"text-delta"!==n.type){i.enqueue({part:n,partialOutput:void 0});return}t+=n.textDelta,r+=n.textDelta;let o=e.parsePartial({text:t});if(null!=o){let e=JSON.stringify(o.partial);e!==a&&(s({controller:i,partialOutput:o.partial}),a=e)}},flush(e){r.length>0&&s({controller:e})}})}(g)).pipeThrough(J);let{maxRetries:W,retry:G}=eO({maxRetries:s}),H=eq(t),X=eM({model:e,telemetry:t,headers:r,settings:{...a,maxRetries:W}}),Q=tR({prompt:{system:null!=(A=null==g?void 0:g.injectIntoSystemPrompt({system:i,model:e}))?A:i,prompt:o,messages:l},tools:u}),ee=this;eL({name:"ai.streamText",attributes:eF({telemetry:t,attributes:{...eP({operationId:"ai.streamText",telemetry:t}),...X,"ai.prompt":{input:()=>JSON.stringify({system:i,prompt:o,messages:l})},"ai.settings.maxSteps":f}}),tracer:H,endWhenDone:!1,fn:async s=>{async function o({currentStep:s,responseMessages:l,usage:p,stepType:b,previousStepText:I,hasLeadingWhitespace:S,messageId:z}){var E;let A,j,C;let N=0===l.length?Q.type:"messages",R=[...Q.messages,...l],O=await tc({prompt:{type:N,system:Q.system,messages:R},modelSupportsImageUrls:e.supportsImageUrls,modelSupportsUrl:null==(E=e.supportsUrl)?void 0:E.bind(e)}),P={type:"regular",...t9({tools:u,toolChoice:c,activeTools:m})},{result:{stream:M,warnings:$,rawResponse:Z,request:D},doStreamSpan:q,startTimestampMs:L}=await G(()=>eL({name:"ai.streamText.doStream",attributes:eF({telemetry:t,attributes:{...eP({operationId:"ai.streamText.doStream",telemetry:t}),...X,"ai.prompt.format":{input:()=>N},"ai.prompt.messages":{input:()=>tU(O)},"ai.prompt.tools":{input:()=>{var e;return null==(e=P.tools)?void 0:e.map(e=>JSON.stringify(e))}},"ai.prompt.toolChoice":{input:()=>null!=P.toolChoice?JSON.stringify(P.toolChoice):void 0},"gen_ai.system":e.provider,"gen_ai.request.model":e.modelId,"gen_ai.request.frequency_penalty":a.frequencyPenalty,"gen_ai.request.max_tokens":a.maxTokens,"gen_ai.request.presence_penalty":a.presencePenalty,"gen_ai.request.stop_sequences":a.stopSequences,"gen_ai.request.temperature":a.temperature,"gen_ai.request.top_k":a.topK,"gen_ai.request.top_p":a.topP}}),tracer:H,endWhenDone:!1,fn:async t=>({startTimestampMs:_(),doStreamSpan:t,result:await e.doStream({mode:P,...tp(a),inputFormat:N,responseFormat:null==g?void 0:g.responseFormat({model:e}),prompt:O,providerMetadata:v,abortSignal:n,headers:r})})})),F=function({tools:e,generatorStream:t,toolCallStreaming:r,tracer:a,telemetry:s,system:n,messages:i,abortSignal:o,repairToolCall:l}){let u,c=null,d=new ReadableStream({start(e){c=e}}),p={},m=new Set,h=!1;function f(){h&&0===m.size&&(null!=u&&c.enqueue(u),c.close())}let g=new TransformStream({async transform(t,d){let h=t.type;switch(h){case"text-delta":case"reasoning":case"reasoning-signature":case"redacted-reasoning":case"source":case"response-metadata":case"error":d.enqueue(t);break;case"file":d.enqueue(new eK({data:t.data,mimeType:t.mimeType}));break;case"tool-call-delta":r&&(p[t.toolCallId]||(d.enqueue({type:"tool-call-streaming-start",toolCallId:t.toolCallId,toolName:t.toolName}),p[t.toolCallId]=!0),d.enqueue({type:"tool-call-delta",toolCallId:t.toolCallId,toolName:t.toolName,argsTextDelta:t.argsTextDelta}));break;case"tool-call":try{let r=await rl({toolCall:t,tools:e,repairToolCall:l,system:n,messages:i});d.enqueue(r);let u=e[r.toolName];if(null!=u.execute){let e=(0,w.$C)();m.add(e),eL({name:"ai.toolCall",attributes:eF({telemetry:s,attributes:{...eP({operationId:"ai.toolCall",telemetry:s}),"ai.toolCall.name":r.toolName,"ai.toolCall.id":r.toolCallId,"ai.toolCall.args":{output:()=>JSON.stringify(r.args)}}}),tracer:a,fn:async t=>u.execute(r.args,{toolCallId:r.toolCallId,messages:i,abortSignal:o}).then(a=>{c.enqueue({...r,type:"tool-result",result:a}),m.delete(e),f();try{t.setAttributes(eF({telemetry:s,attributes:{"ai.toolCall.result":{output:()=>JSON.stringify(a)}}}))}catch(e){}},t=>{c.enqueue({type:"error",error:new t4({toolCallId:r.toolCallId,toolName:r.toolName,toolArgs:r.args,cause:t})}),m.delete(e),f()})})}}catch(e){c.enqueue({type:"error",error:e})}break;case"finish":u={type:"finish",finishReason:t.finishReason,logprobs:t.logprobs,usage:tP(t.usage),experimental_providerMetadata:t.providerMetadata};break;default:throw Error(`Unhandled chunk type: ${h}`)}},flush(){h=!0,f()}});return new ReadableStream({start:async e=>Promise.all([t.pipeThrough(g).pipeTo(new WritableStream({write(t){e.enqueue(t)},close(){}})),d.pipeTo(new WritableStream({write(t){e.enqueue(t)},close(){e.close()}}))])})}({tools:u,generatorStream:M,toolCallStreaming:d,tracer:H,telemetry:t,system:i,messages:R,repairToolCall:h,abortSignal:n}),U=null!=D?D:{},V=[],B=[],J=[],Y=[],K="unknown",W={promptTokens:0,completionTokens:0,totalTokens:0},et=!0,er="",ea="continue"===b?I:"",es={id:k(),timestamp:x(),modelId:e.modelId},en="",ei=!1,eo=!0,el=!1;async function eu({controller:e,chunk:t}){e.enqueue(t),er+=t.textDelta,ea+=t.textDelta,ei=!0,el=t.textDelta.trimEnd()!==t.textDelta}ee.addStream(F.pipeThrough(new TransformStream({async transform(e,t){var r,a,s;if(et){let e=_()-L;et=!1,q.addEvent("ai.stream.firstChunk",{"ai.response.msToFirstChunk":e}),q.setAttributes({"ai.response.msToFirstChunk":e}),t.enqueue({type:"step-start",messageId:z,request:U,warnings:null!=$?$:[]})}if("text-delta"===e.type&&0===e.textDelta.length)return;let n=e.type;switch(n){case"text-delta":if(y){let r=eo&&S?e.textDelta.trimStart():e.textDelta;if(0===r.length)break;eo=!1;let a=t6(en+=r);null!=a&&(en=a.suffix,await eu({controller:t,chunk:{type:"text-delta",textDelta:a.prefix+a.whitespace}}))}else await eu({controller:t,chunk:e});break;case"reasoning":t.enqueue(e),null==C?(C={type:"text",text:e.textDelta},J.push(C)):C.text+=e.textDelta;break;case"reasoning-signature":if(t.enqueue(e),null==C)throw new rx({chunk:e,message:"reasoning-signature without reasoning"});C.signature=e.signature,C=void 0;break;case"redacted-reasoning":t.enqueue(e),J.push({type:"redacted",data:e.data});break;case"tool-call":t.enqueue(e),V.push(e);break;case"tool-result":t.enqueue(e),B.push(e);break;case"response-metadata":es={id:null!=(r=e.id)?r:es.id,timestamp:null!=(a=e.timestamp)?a:es.timestamp,modelId:null!=(s=e.modelId)?s:es.modelId};break;case"finish":{W=e.usage,K=e.finishReason,A=e.experimental_providerMetadata,j=e.logprobs;let t=_()-L;q.addEvent("ai.stream.finish"),q.setAttributes({"ai.response.msToFinish":t,"ai.response.avgCompletionTokensPerSecond":1e3*W.completionTokens/t});break}case"file":Y.push(e),t.enqueue(e);break;case"source":case"tool-call-streaming-start":case"tool-call-delta":t.enqueue(e);break;case"error":t.enqueue(e),K="error";break;default:throw Error(`Unknown chunk type: ${n}`)}},async flush(e){let r=V.length>0?JSON.stringify(V):void 0,a="done";s+1<f&&(y&&"length"===K&&0===V.length?a="continue":V.length>0&&B.length===V.length&&(a="tool-result")),y&&en.length>0&&("continue"!==a||"continue"===b&&!ei)&&(await eu({controller:e,chunk:{type:"text-delta",textDelta:en}}),en="");try{q.setAttributes(eF({telemetry:t,attributes:{"ai.response.finishReason":K,"ai.response.text":{output:()=>er},"ai.response.toolCalls":{output:()=>r},"ai.response.id":es.id,"ai.response.model":es.modelId,"ai.response.timestamp":es.timestamp.toISOString(),"ai.usage.promptTokens":W.promptTokens,"ai.usage.completionTokens":W.completionTokens,"gen_ai.response.finish_reasons":[K],"gen_ai.response.id":es.id,"gen_ai.response.model":es.modelId,"gen_ai.usage.input_tokens":W.promptTokens,"gen_ai.usage.output_tokens":W.completionTokens}}))}catch(e){}finally{q.end()}e.enqueue({type:"step-finish",finishReason:K,usage:W,providerMetadata:A,experimental_providerMetadata:A,logprobs:j,request:U,response:{...es,headers:null==Z?void 0:Z.headers},warnings:$,isContinued:"continue"===a,messageId:z});let n=tM(p,W);if("done"===a)e.enqueue({type:"finish",finishReason:K,usage:n,providerMetadata:A,experimental_providerMetadata:A,logprobs:j,response:{...es,headers:null==Z?void 0:Z.headers}}),ee.closeStream();else{if("continue"===b){let e=l[l.length-1];"string"==typeof e.content?e.content+=er:e.content.push({text:er,type:"text"})}else l.push(...rd({text:er,files:Y,reasoning:J,tools:null!=u?u:{},toolCalls:V,toolResults:B,messageId:z,generateMessageId:T}));await o({currentStep:s+1,responseMessages:l,usage:n,stepType:a,previousStepText:ea,hasLeadingWhitespace:el,messageId:"continue"===a?z:T()})}}})))}j=s,await o({currentStep:0,responseMessages:[],usage:{promptTokens:0,completionTokens:0,totalTokens:0},previousStepText:"",stepType:"initial",hasLeadingWhitespace:!1,messageId:T()})}}).catch(e=>{ee.addStream(new ReadableStream({start(t){t.enqueue({type:"error",error:e}),t.close()}})),ee.closeStream()})}get warnings(){return this.warningsPromise.value}get usage(){return this.usagePromise.value}get finishReason(){return this.finishReasonPromise.value}get experimental_providerMetadata(){return this.providerMetadataPromise.value}get providerMetadata(){return this.providerMetadataPromise.value}get text(){return this.textPromise.value}get reasoning(){return this.reasoningPromise.value}get reasoningDetails(){return this.reasoningDetailsPromise.value}get sources(){return this.sourcesPromise.value}get files(){return this.filesPromise.value}get toolCalls(){return this.toolCallsPromise.value}get toolResults(){return this.toolResultsPromise.value}get request(){return this.requestPromise.value}get response(){return this.responsePromise.value}get steps(){return this.stepsPromise.value}teeStream(){let[e,t]=this.baseStream.tee();return this.baseStream=t,e}get textStream(){return tZ(this.teeStream().pipeThrough(new TransformStream({transform({part:e},t){"text-delta"===e.type&&t.enqueue(e.textDelta)}})))}get fullStream(){return tZ(this.teeStream().pipeThrough(new TransformStream({transform({part:e},t){t.enqueue(e)}})))}async consumeStream(e){var t;try{await rA({stream:this.fullStream,onError:null==e?void 0:e.onError})}catch(r){null==(t=null==e?void 0:e.onError)||t.call(e,r)}}get experimental_partialOutputStream(){if(null==this.output)throw new tX;return tZ(this.teeStream().pipeThrough(new TransformStream({transform({partialOutput:e},t){null!=e&&t.enqueue(e)}})))}toDataStreamInternal({getErrorMessage:e=()=>"An error occurred.",sendUsage:t=!0,sendReasoning:r=!1,sendSources:a=!1,experimental_sendFinish:s=!0}){return this.fullStream.pipeThrough(new TransformStream({transform:async(n,i)=>{let o=n.type;switch(o){case"text-delta":i.enqueue(eh("text",n.textDelta));break;case"reasoning":r&&i.enqueue(eh("reasoning",n.textDelta));break;case"redacted-reasoning":r&&i.enqueue(eh("redacted_reasoning",{data:n.data}));break;case"reasoning-signature":r&&i.enqueue(eh("reasoning_signature",{signature:n.signature}));break;case"file":i.enqueue(eh("file",{mimeType:n.mimeType,data:n.base64}));break;case"source":a&&i.enqueue(eh("source",n.source));break;case"tool-call-streaming-start":i.enqueue(eh("tool_call_streaming_start",{toolCallId:n.toolCallId,toolName:n.toolName}));break;case"tool-call-delta":i.enqueue(eh("tool_call_delta",{toolCallId:n.toolCallId,argsTextDelta:n.argsTextDelta}));break;case"tool-call":i.enqueue(eh("tool_call",{toolCallId:n.toolCallId,toolName:n.toolName,args:n.args}));break;case"tool-result":i.enqueue(eh("tool_result",{toolCallId:n.toolCallId,result:n.result}));break;case"error":i.enqueue(eh("error",e(n.error)));break;case"step-start":i.enqueue(eh("start_step",{messageId:n.messageId}));break;case"step-finish":i.enqueue(eh("finish_step",{finishReason:n.finishReason,usage:t?{promptTokens:n.usage.promptTokens,completionTokens:n.usage.completionTokens}:void 0,isContinued:n.isContinued}));break;case"finish":s&&i.enqueue(eh("finish_message",{finishReason:n.finishReason,usage:t?{promptTokens:n.usage.promptTokens,completionTokens:n.usage.completionTokens}:void 0}));break;default:throw Error(`Unknown chunk type: ${o}`)}}}))}pipeDataStreamToResponse(e,{status:t,statusText:r,headers:a,data:s,getErrorMessage:n,sendUsage:i,sendReasoning:o,sendSources:l,experimental_sendFinish:u}={}){ek({response:e,status:t,statusText:r,headers:ex(a,{contentType:"text/plain; charset=utf-8",dataStreamVersion:"v1"}),stream:this.toDataStream({data:s,getErrorMessage:n,sendUsage:i,sendReasoning:o,sendSources:l,experimental_sendFinish:u})})}pipeTextStreamToResponse(e,t){ek({response:e,status:null==t?void 0:t.status,statusText:null==t?void 0:t.statusText,headers:ex(null==t?void 0:t.headers,{contentType:"text/plain; charset=utf-8"}),stream:this.textStream.pipeThrough(new TextEncoderStream)})}toDataStream(e){let t=this.toDataStreamInternal({getErrorMessage:null==e?void 0:e.getErrorMessage,sendUsage:null==e?void 0:e.sendUsage,sendReasoning:null==e?void 0:e.sendReasoning,sendSources:null==e?void 0:e.sendSources,experimental_sendFinish:null==e?void 0:e.experimental_sendFinish}).pipeThrough(new TextEncoderStream);return(null==e?void 0:e.data)?rj(null==e?void 0:e.data.stream,t):t}mergeIntoDataStream(e,t){e.merge(this.toDataStreamInternal({getErrorMessage:e.onError,sendUsage:null==t?void 0:t.sendUsage,sendReasoning:null==t?void 0:t.sendReasoning,sendSources:null==t?void 0:t.sendSources,experimental_sendFinish:null==t?void 0:t.experimental_sendFinish}))}toDataStreamResponse({headers:e,status:t,statusText:r,data:a,getErrorMessage:s,sendUsage:n,sendReasoning:i,sendSources:o,experimental_sendFinish:l}={}){return new Response(this.toDataStream({data:a,getErrorMessage:s,sendUsage:n,sendReasoning:i,sendSources:o,experimental_sendFinish:l}),{status:t,statusText:r,headers:ew(e,{contentType:"text/plain; charset=utf-8",dataStreamVersion:"v1"})})}toTextStreamResponse(e){var t;return new Response(this.textStream.pipeThrough(new TextEncoderStream),{status:null!=(t=null==e?void 0:e.status)?t:200,headers:ew(null==e?void 0:e.headers,{contentType:"text/plain; charset=utf-8"})})}};function rP({tagName:e,separator:t="\n",startWithReasoning:r=!1}){let a=`<${e}>`,s=`</${e}>`;return{middlewareVersion:"v1",wrapGenerate:async({doGenerate:e})=>{let{text:n,...i}=await e();if(null==n)return{text:n,...i};let o=r?a+n:n,l=RegExp(`${a}(.*?)${s}`,"gs"),u=Array.from(o.matchAll(l));if(!u.length)return{text:o,...i};let c=u.map(e=>e[1]).join(t),d=o;for(let e=u.length-1;e>=0;e--){let r=u[e],a=d.slice(0,r.index),s=d.slice(r.index+r[0].length);d=a+(a.length>0&&s.length>0?t:"")+s}return{...i,text:d,reasoning:c}},wrapStream:async({doStream:e})=>{let{stream:n,...i}=await e(),o=!0,l=!0,u=!1,c=r,d="";return{stream:n.pipeThrough(new TransformStream({transform:(e,r)=>{if("text-delta"!==e.type){r.enqueue(e);return}function n(e){if(e.length>0){let a=!u||(c?o:l)?"":t;r.enqueue({type:c?"reasoning":"text-delta",textDelta:a+e}),u=!1,c?o=!1:l=!1}}for(d+=e.textDelta;;){let e=c?s:a,t=function(e,t){if(0===t.length)return null;let r=e.indexOf(t);if(-1!==r)return r;for(let r=e.length-1;r>=0;r--){let a=e.substring(r);if(t.startsWith(a))return r}return null}(d,e);if(null==t){n(d),d="";break}if(n(d.slice(0,t)),t+e.length<=d.length)d=d.slice(t+e.length),c=!c,u=!0;else{d=d.slice(t);break}}}})),...i}}}}b.bD,b.bD;var rM=({model:e,middleware:t,modelId:r,providerId:a})=>rE(t).reverse().reduce((e,t)=>r$({model:e,middleware:t,modelId:r,providerId:a}),e),r$=({model:e,middleware:{transformParams:t,wrapGenerate:r,wrapStream:a},modelId:s,providerId:n})=>{var i;async function o({params:e,type:r}){return t?await t({params:e,type:r}):e}return{specificationVersion:"v1",provider:null!=n?n:e.provider,modelId:null!=s?s:e.modelId,defaultObjectGenerationMode:e.defaultObjectGenerationMode,supportsImageUrls:e.supportsImageUrls,supportsUrl:null==(i=e.supportsUrl)?void 0:i.bind(e),supportsStructuredOutputs:e.supportsStructuredOutputs,async doGenerate(t){let a=await o({params:t,type:"generate"}),s=async()=>e.doGenerate(a),n=async()=>e.doStream(a);return r?r({doGenerate:s,doStream:n,params:a,model:e}):s()},async doStream(t){let r=await o({params:t,type:"stream"}),s=async()=>e.doGenerate(r),n=async()=>e.doStream(r);return a?a({doGenerate:s,doStream:n,params:r,model:e}):n()}}},rZ="AI_NoSuchProviderError",rD=`vercel.ai.error.${rZ}`,rq=Symbol.for(rD),rL=class extends b.eM{constructor({modelId:e,modelType:t,providerId:r,availableProviders:a,message:s=`No such provider: ${r} (available providers: ${a.join()})`}){super({errorName:rZ,modelId:e,modelType:t,message:s}),this[_]=!0,this.providerId=r,this.availableProviders=a}static isInstance(e){return b.bD.hasMarker(e,rD)}};_=rq;var rF="2024-11-05",rU=[rF,"2024-10-07"],rV=T.z.object({name:T.z.string(),version:T.z.string()}).passthrough(),rB=T.z.object({_meta:T.z.optional(T.z.object({}).passthrough())}).passthrough(),rJ=T.z.object({method:T.z.string(),params:T.z.optional(rB)}),rY=T.z.object({experimental:T.z.optional(T.z.object({}).passthrough()),logging:T.z.optional(T.z.object({}).passthrough()),prompts:T.z.optional(T.z.object({listChanged:T.z.optional(T.z.boolean())}).passthrough()),resources:T.z.optional(T.z.object({subscribe:T.z.optional(T.z.boolean()),listChanged:T.z.optional(T.z.boolean())}).passthrough()),tools:T.z.optional(T.z.object({listChanged:T.z.optional(T.z.boolean())}).passthrough())}).passthrough(),rK=rB.extend({protocolVersion:T.z.string(),capabilities:rY,serverInfo:rV,instructions:T.z.optional(T.z.string())}),rW=rB.extend({nextCursor:T.z.optional(T.z.string())}),rG=T.z.object({name:T.z.string(),description:T.z.optional(T.z.string()),inputSchema:T.z.object({type:T.z.literal("object"),properties:T.z.optional(T.z.object({}).passthrough())}).passthrough()}).passthrough(),rH=rW.extend({tools:T.z.array(rG)}),rX=T.z.object({type:T.z.literal("text"),text:T.z.string()}).passthrough(),rQ=T.z.object({type:T.z.literal("image"),data:T.z.string().base64(),mimeType:T.z.string()}).passthrough(),r0=T.z.object({uri:T.z.string(),mimeType:T.z.optional(T.z.string())}).passthrough(),r1=r0.extend({text:T.z.string()}),r4=r0.extend({blob:T.z.string().base64()}),r9=T.z.object({type:T.z.literal("resource"),resource:T.z.union([r1,r4])}).passthrough(),r2=rB.extend({content:T.z.array(T.z.union([rX,rQ,r9])),isError:T.z.boolean().default(!1).optional()}).or(rB.extend({toolResult:T.z.unknown()})),r6=T.z.object({jsonrpc:T.z.literal("2.0"),id:T.z.union([T.z.string(),T.z.number().int()])}).merge(rJ).strict(),r3=T.z.object({jsonrpc:T.z.literal("2.0"),id:T.z.union([T.z.string(),T.z.number().int()]),result:rB}).strict(),r5=T.z.object({jsonrpc:T.z.literal("2.0"),id:T.z.union([T.z.string(),T.z.number().int()]),error:T.z.object({code:T.z.number().int(),message:T.z.string(),data:T.z.optional(T.z.unknown())})}).strict(),r8=T.z.object({jsonrpc:T.z.literal("2.0")}).merge(T.z.object({method:T.z.string(),params:T.z.optional(rB)})).strict(),r7=T.z.union([r6,r8,r3,r5]),ae=class{constructor({url:e,headers:t}){this.connected=!1,this.url=new URL(e),this.headers=t}async start(){return new Promise((e,t)=>{if(this.connected)return e();this.abortController=new AbortController,(async()=>{var r,a,s;try{let s=new Headers(this.headers);s.set("Accept","text/event-stream");let n=await fetch(this.url.href,{headers:s,signal:null==(r=this.abortController)?void 0:r.signal});if(!n.ok||!n.body){let e=new rI({message:`MCP SSE Transport Error: ${n.status} ${n.statusText}`});return null==(a=this.onerror)||a.call(this,e),t(e)}let i=n.body.pipeThrough(new TextDecoderStream).pipeThrough(createEventSourceParserStream()).getReader(),o=async()=>{var r,a,s;try{for(;;){let{done:t,value:s}=await i.read();if(t){if(this.connected)throw this.connected=!1,new rI({message:"MCP SSE Transport Error: Connection closed unexpectedly"});return}let{event:n,data:o}=s;if("endpoint"===n){if(this.endpoint=new URL(o,this.url),this.endpoint.origin!==this.url.origin)throw new rI({message:`MCP SSE Transport Error: Endpoint origin does not match connection origin: ${this.endpoint.origin}`});this.connected=!0,e()}else if("message"===n)try{let e=r7.parse(JSON.parse(o));null==(r=this.onmessage)||r.call(this,e)}catch(t){let e=new rI({message:"MCP SSE Transport Error: Failed to parse message",cause:t});null==(a=this.onerror)||a.call(this,e)}}}catch(e){if(e instanceof Error&&"AbortError"===e.name)return;null==(s=this.onerror)||s.call(this,e),t(e)}};this.sseConnection={close:()=>i.cancel()},o()}catch(e){if(e instanceof Error&&"AbortError"===e.name)return;null==(s=this.onerror)||s.call(this,e),t(e)}})()})}async close(){var e,t,r;this.connected=!1,null==(e=this.sseConnection)||e.close(),null==(t=this.abortController)||t.abort(),null==(r=this.onclose)||r.call(this)}async send(e){var t,r,a;if(!this.endpoint||!this.connected)throw new rI({message:"MCP SSE Transport Error: Not connected"});try{let a=new Headers(this.headers);a.set("Content-Type","application/json");let s={method:"POST",headers:a,body:JSON.stringify(e),signal:null==(t=this.abortController)?void 0:t.signal},n=await fetch(this.endpoint,s);if(!n.ok){let e=await n.text().catch(()=>null),t=new rI({message:`MCP SSE Transport Error: POSTing to endpoint (HTTP ${n.status}): ${e}`});null==(r=this.onerror)||r.call(this,t);return}}catch(e){null==(a=this.onerror)||a.call(this,e);return}}};function at(e={}){let t=new TextEncoder,r="";return new TransformStream({async start(){e.onStart&&await e.onStart()},async transform(a,s){s.enqueue(t.encode(a)),r+=a,e.onToken&&await e.onToken(a),e.onText&&"string"==typeof a&&await e.onText(a)},async flush(){e.onCompletion&&await e.onCompletion(r),e.onFinal&&await e.onFinal(r)}})}function ar(e,t){return e.pipeThrough(new TransformStream({transform:async(e,t)=>{var r;if("string"==typeof e){t.enqueue(e);return}if("event"in e){"on_chat_model_stream"===e.event&&ai(null==(r=e.data)?void 0:r.chunk,t);return}ai(e,t)}})).pipeThrough(at(t)).pipeThrough(new TextDecoderStream).pipeThrough(new TransformStream({transform:async(e,t)=>{t.enqueue(eh("text",e))}}))}function aa(e,t){return ar(e,t).pipeThrough(new TextEncoderStream)}function as(e,t){var r;let a=ar(e,null==t?void 0:t.callbacks).pipeThrough(new TextEncoderStream),s=null==t?void 0:t.data,n=null==t?void 0:t.init;return new Response(s?rj(s.stream,a):a,{status:null!=(r=null==n?void 0:n.status)?r:200,statusText:null==n?void 0:n.statusText,headers:ew(null==n?void 0:n.headers,{contentType:"text/plain; charset=utf-8",dataStreamVersion:"v1"})})}function an(e,t){t.dataStream.merge(ar(e,t.callbacks))}function ai(e,t){if("string"==typeof e.content)t.enqueue(e.content);else for(let r of e.content)"text"===r.type&&t.enqueue(r.text)}function ao(e,t){let r;let a=(r=!0,e=>(r&&(e=e.trimStart())&&(r=!1),e));return(0,w.NR)(e[Symbol.asyncIterator]()).pipeThrough(new TransformStream({async transform(e,t){t.enqueue(a(e.delta))}})).pipeThrough(at(t)).pipeThrough(new TextDecoderStream).pipeThrough(new TransformStream({transform:async(e,t)=>{t.enqueue(eh("text",e))}}))}function al(e,t){return ao(e,t).pipeThrough(new TextEncoderStream)}function au(e,t={}){var r;let{init:a,data:s,callbacks:n}=t,i=ao(e,n).pipeThrough(new TextEncoderStream);return new Response(s?rj(s.stream,i):i,{status:null!=(r=null==a?void 0:a.status)?r:200,statusText:null==a?void 0:a.statusText,headers:ew(null==a?void 0:a.headers,{contentType:"text/plain; charset=utf-8",dataStreamVersion:"v1"})})}function ac(e,t){t.dataStream.merge(ao(e,t.callbacks))}eb({},{mergeIntoDataStream:()=>an,toDataStream:()=>aa,toDataStreamResponse:()=>as}),eb({},{mergeIntoDataStream:()=>ac,toDataStream:()=>al,toDataStreamResponse:()=>au})},48444:(e,t,r)=>{r.d(t,{d:()=>a});let a=(e,t=21)=>(r=t)=>{let a="",s=0|r;for(;s--;)a+=e[Math.random()*e.length|0];return a}},49715:(e,t,r)=>{r.d(t,{Ch:()=>eo,Di:()=>N,Kq:()=>K,M3:()=>M,Tt:()=>z,Vf:()=>X,b8:()=>eg,bD:()=>_,eM:()=>ea,hL:()=>k,iM:()=>ep,u1:()=>E,u6:()=>V,xn:()=>q});var a,s,n,i,o,l,u,c,d,p,m,h,f,g="vercel.ai.error",y=Symbol.for(g),v=class e extends Error{constructor({name:e,message:t,cause:r}){super(t),this[a]=!0,this.name=e,this.cause=r}static isInstance(t){return e.hasMarker(t,g)}static hasMarker(e,t){let r=Symbol.for(t);return null!=e&&"object"==typeof e&&r in e&&"boolean"==typeof e[r]&&!0===e[r]}};a=y;var _=v,b="AI_APICallError",w=`vercel.ai.error.${b}`,x=Symbol.for(w),k=class extends _{constructor({message:e,url:t,requestBodyValues:r,statusCode:a,responseHeaders:n,responseBody:i,cause:o,isRetryable:l=null!=a&&(408===a||409===a||429===a||a>=500),data:u}){super({name:b,message:e,cause:o}),this[s]=!0,this.url=t,this.requestBodyValues=r,this.statusCode=a,this.responseHeaders=n,this.responseBody=i,this.isRetryable=l,this.data=u}static isInstance(e){return _.hasMarker(e,w)}};s=x;var T="AI_EmptyResponseBodyError",I=`vercel.ai.error.${T}`,S=Symbol.for(I),z=class extends _{constructor({message:e="Empty response body"}={}){super({name:T,message:e}),this[n]=!0}static isInstance(e){return _.hasMarker(e,I)}};function E(e){return null==e?"unknown error":"string"==typeof e?e:e instanceof Error?e.message:JSON.stringify(e)}n=S;var A="AI_InvalidArgumentError",j=`vercel.ai.error.${A}`,C=Symbol.for(j),N=class extends _{constructor({message:e,cause:t,argument:r}){super({name:A,message:e,cause:t}),this[i]=!0,this.argument=r}static isInstance(e){return _.hasMarker(e,j)}};i=C;var R="AI_InvalidPromptError",O=`vercel.ai.error.${R}`,P=Symbol.for(O),M=class extends _{constructor({prompt:e,message:t,cause:r}){super({name:R,message:`Invalid prompt: ${t}`,cause:r}),this[o]=!0,this.prompt=e}static isInstance(e){return _.hasMarker(e,O)}};o=P;var $="AI_InvalidResponseDataError",Z=`vercel.ai.error.${$}`,D=Symbol.for(Z),q=class extends _{constructor({data:e,message:t=`Invalid response data: ${JSON.stringify(e)}.`}){super({name:$,message:t}),this[l]=!0,this.data=e}static isInstance(e){return _.hasMarker(e,Z)}};l=D;var L="AI_JSONParseError",F=`vercel.ai.error.${L}`,U=Symbol.for(F),V=class extends _{constructor({text:e,cause:t}){super({name:L,message:`JSON parsing failed: Text: ${e}.
Error message: ${E(t)}`,cause:t}),this[u]=!0,this.text=e}static isInstance(e){return _.hasMarker(e,F)}};u=U;var B="AI_LoadAPIKeyError",J=`vercel.ai.error.${B}`,Y=Symbol.for(J),K=class extends _{constructor({message:e}){super({name:B,message:e}),this[c]=!0}static isInstance(e){return _.hasMarker(e,J)}};c=Y;var W="AI_LoadSettingError",G=`vercel.ai.error.${W}`,H=Symbol.for(G),X=class extends _{constructor({message:e}){super({name:W,message:e}),this[d]=!0}static isInstance(e){return _.hasMarker(e,G)}};d=H;var Q=Symbol.for("vercel.ai.error.AI_NoContentGeneratedError"),ee="AI_NoSuchModelError",et=`vercel.ai.error.${ee}`,er=Symbol.for(et),ea=class extends _{constructor({errorName:e=ee,modelId:t,modelType:r,message:a=`No such ${r}: ${t}`}){super({name:e,message:a}),this[p]=!0,this.modelId=t,this.modelType=r}static isInstance(e){return _.hasMarker(e,et)}};p=er;var es="AI_TooManyEmbeddingValuesForCallError",en=`vercel.ai.error.${es}`,ei=Symbol.for(en),eo=class extends _{constructor(e){super({name:es,message:`Too many values for a single embedding call. The ${e.provider} model "${e.modelId}" can only embed up to ${e.maxEmbeddingsPerCall} values per call, but ${e.values.length} values were provided.`}),this[m]=!0,this.provider=e.provider,this.modelId=e.modelId,this.maxEmbeddingsPerCall=e.maxEmbeddingsPerCall,this.values=e.values}static isInstance(e){return _.hasMarker(e,en)}};m=ei;var el="AI_TypeValidationError",eu=`vercel.ai.error.${el}`,ec=Symbol.for(eu),ed=class e extends _{constructor({value:e,cause:t}){super({name:el,message:`Type validation failed: Value: ${JSON.stringify(e)}.
Error message: ${E(t)}`,cause:t}),this[h]=!0,this.value=e}static isInstance(e){return _.hasMarker(e,eu)}static wrap({value:t,cause:r}){return e.isInstance(r)&&r.value===t?r:new e({value:t,cause:r})}};h=ec;var ep=ed,em="AI_UnsupportedFunctionalityError",eh=`vercel.ai.error.${em}`,ef=Symbol.for(eh),eg=class extends _{constructor({functionality:e,message:t=`'${e}' functionality not supported.`}){super({name:em,message:t}),this[f]=!0,this.functionality=e}static isInstance(e){return _.hasMarker(e,eh)}};function ey(e){return null===e||"string"==typeof e||"number"==typeof e||"boolean"==typeof e||(Array.isArray(e)?e.every(ey):"object"==typeof e&&Object.entries(e).every(([e,t])=>"string"==typeof e&&ey(t)))}f=ef},57057:e=>{let t="undefined"!=typeof Buffer,r=/"(?:_|\\u005[Ff])(?:_|\\u005[Ff])(?:p|\\u0070)(?:r|\\u0072)(?:o|\\u006[Ff])(?:t|\\u0074)(?:o|\\u006[Ff])(?:_|\\u005[Ff])(?:_|\\u005[Ff])"\s*:/,a=/"(?:c|\\u0063)(?:o|\\u006[Ff])(?:n|\\u006[Ee])(?:s|\\u0073)(?:t|\\u0074)(?:r|\\u0072)(?:u|\\u0075)(?:c|\\u0063)(?:t|\\u0074)(?:o|\\u006[Ff])(?:r|\\u0072)"\s*:/;function s(e,s,i){null==i&&null!==s&&"object"==typeof s&&(i=s,s=void 0),t&&Buffer.isBuffer(e)&&(e=e.toString()),e&&65279===e.charCodeAt(0)&&(e=e.slice(1));let o=JSON.parse(e,s);if(null===o||"object"!=typeof o)return o;let l=i&&i.protoAction||"error",u=i&&i.constructorAction||"error";if("ignore"===l&&"ignore"===u)return o;if("ignore"!==l&&"ignore"!==u){if(!1===r.test(e)&&!1===a.test(e))return o}else if("ignore"!==l&&"ignore"===u){if(!1===r.test(e))return o}else if(!1===a.test(e))return o;return n(o,{protoAction:l,constructorAction:u,safe:i&&i.safe})}function n(e,{protoAction:t="error",constructorAction:r="error",safe:a}={}){let s=[e];for(;s.length;){let e=s;for(let n of(s=[],e)){if("ignore"!==t&&Object.prototype.hasOwnProperty.call(n,"__proto__")){if(!0===a)return null;if("error"===t)throw SyntaxError("Object contains forbidden prototype property");delete n.__proto__}if("ignore"!==r&&Object.prototype.hasOwnProperty.call(n,"constructor")&&Object.prototype.hasOwnProperty.call(n.constructor,"prototype")){if(!0===a)return null;if("error"===r)throw SyntaxError("Object contains forbidden prototype property");delete n.constructor}for(let e in n){let t=n[e];t&&"object"==typeof t&&s.push(t)}}}return e}function i(e,t,r){let a=Error.stackTraceLimit;Error.stackTraceLimit=0;try{return s(e,t,r)}finally{Error.stackTraceLimit=a}}e.exports=i,e.exports.default=i,e.exports.parse=i,e.exports.safeParse=function(e,t){let r=Error.stackTraceLimit;Error.stackTraceLimit=0;try{return s(e,t,{safe:!0})}catch(e){return null}finally{Error.stackTraceLimit=r}},e.exports.scan=n},71576:(e,t,r)=>{r.d(t,{Z:()=>n});class a extends Error{constructor(e,t){super(e),this.name="ParseError",this.type=t.type,this.field=t.field,this.value=t.value,this.line=t.line}}function s(e){}class n extends TransformStream{constructor({onError:e,onRetry:t,onComment:r}={}){let n;super({start(i){n=function(e){if("function"==typeof e)throw TypeError("`callbacks` must be an object, got a function instead. Did you mean `{onEvent: fn}`?");let{onEvent:t=s,onError:r=s,onRetry:n=s,onComment:i}=e,o="",l=!0,u,c="",d="";function p(e){if(""===e){c.length>0&&t({id:u,event:d||void 0,data:c.endsWith(`
`)?c.slice(0,-1):c}),u=void 0,c="",d="";return}if(e.startsWith(":")){i&&i(e.slice(e.startsWith(": ")?2:1));return}let r=e.indexOf(":");if(-1!==r){let t=e.slice(0,r),a=" "===e[r+1]?2:1;m(t,e.slice(r+a),e);return}m(e,"",e)}function m(e,t,s){switch(e){case"event":d=t;break;case"data":c=`${c}${t}
`;break;case"id":u=t.includes("\0")?void 0:t;break;case"retry":/^\d+$/.test(t)?n(parseInt(t,10)):r(new a(`Invalid \`retry\` value: "${t}"`,{type:"invalid-retry",value:t,line:s}));break;default:r(new a(`Unknown field "${e.length>20?`${e.slice(0,20)}\u2026`:e}"`,{type:"unknown-field",field:e,value:t,line:s}))}}return{feed:function(e){let t=l?e.replace(/^\xEF\xBB\xBF/,""):e,[r,a]=function(e){let t=[],r="",a=0;for(;a<e.length;){let s=e.indexOf("\r",a),n=e.indexOf(`
`,a),i=-1;if(-1!==s&&-1!==n?i=Math.min(s,n):-1!==s?i=s:-1!==n&&(i=n),-1===i){r=e.slice(a);break}{let r=e.slice(a,i);t.push(r),"\r"===e[(a=i+1)-1]&&e[a]===`
`&&a++}}return[t,r]}(`${o}${t}`);for(let e of r)p(e);o=a,l=!1},reset:function(e={}){o&&e.consume&&p(o),l=!0,u=void 0,c="",d="",o=""}}}({onEvent:e=>{i.enqueue(e)},onError(t){"terminate"===e?i.error(t):"function"==typeof e&&e(t)},onRetry:t,onComment:r})},transform(e){n.feed(e)}})}}},74306:(e,t,r)=>{r.d(t,{Di:()=>A,Kq:()=>F,Tt:()=>T,b8:()=>es,eM:()=>K,hL:()=>b,iM:()=>ee,u6:()=>Z,xn:()=>O});var a,s,n,i,o,l,u,c,d,p,m="vercel.ai.error",h=Symbol.for(m),f=class e extends Error{constructor({name:e,message:t,cause:r}){super(t),this[a]=!0,this.name=e,this.cause=r}static isInstance(t){return e.hasMarker(t,m)}static hasMarker(e,t){let r=Symbol.for(t);return null!=e&&"object"==typeof e&&r in e&&"boolean"==typeof e[r]&&!0===e[r]}};a=h;var g=f,y="AI_APICallError",v=`vercel.ai.error.${y}`,_=Symbol.for(v),b=class extends g{constructor({message:e,url:t,requestBodyValues:r,statusCode:a,responseHeaders:n,responseBody:i,cause:o,isRetryable:l=null!=a&&(408===a||409===a||429===a||a>=500),data:u}){super({name:y,message:e,cause:o}),this[s]=!0,this.url=t,this.requestBodyValues=r,this.statusCode=a,this.responseHeaders=n,this.responseBody=i,this.isRetryable=l,this.data=u}static isInstance(e){return g.hasMarker(e,v)}};s=_;var w="AI_EmptyResponseBodyError",x=`vercel.ai.error.${w}`,k=Symbol.for(x),T=class extends g{constructor({message:e="Empty response body"}={}){super({name:w,message:e}),this[n]=!0}static isInstance(e){return g.hasMarker(e,x)}};function I(e){return null==e?"unknown error":"string"==typeof e?e:e instanceof Error?e.message:JSON.stringify(e)}n=k;var S="AI_InvalidArgumentError",z=`vercel.ai.error.${S}`,E=Symbol.for(z),A=class extends g{constructor({message:e,cause:t,argument:r}){super({name:S,message:e,cause:t}),this[i]=!0,this.argument=r}static isInstance(e){return g.hasMarker(e,z)}};i=E;var j=Symbol.for("vercel.ai.error.AI_InvalidPromptError"),C="AI_InvalidResponseDataError",N=`vercel.ai.error.${C}`,R=Symbol.for(N),O=class extends g{constructor({data:e,message:t=`Invalid response data: ${JSON.stringify(e)}.`}){super({name:C,message:t}),this[o]=!0,this.data=e}static isInstance(e){return g.hasMarker(e,N)}};o=R;var P="AI_JSONParseError",M=`vercel.ai.error.${P}`,$=Symbol.for(M),Z=class extends g{constructor({text:e,cause:t}){super({name:P,message:`JSON parsing failed: Text: ${e}.
Error message: ${I(t)}`,cause:t}),this[l]=!0,this.text=e}static isInstance(e){return g.hasMarker(e,M)}};l=$;var D="AI_LoadAPIKeyError",q=`vercel.ai.error.${D}`,L=Symbol.for(q),F=class extends g{constructor({message:e}){super({name:D,message:e}),this[u]=!0}static isInstance(e){return g.hasMarker(e,q)}};u=L;var U=Symbol.for("vercel.ai.error.AI_LoadSettingError"),V=Symbol.for("vercel.ai.error.AI_NoContentGeneratedError"),B="AI_NoSuchModelError",J=`vercel.ai.error.${B}`,Y=Symbol.for(J),K=class extends g{constructor({errorName:e=B,modelId:t,modelType:r,message:a=`No such ${r}: ${t}`}){super({name:e,message:a}),this[c]=!0,this.modelId=t,this.modelType=r}static isInstance(e){return g.hasMarker(e,J)}};c=Y;var W=Symbol.for("vercel.ai.error.AI_TooManyEmbeddingValuesForCallError"),G="AI_TypeValidationError",H=`vercel.ai.error.${G}`,X=Symbol.for(H),Q=class e extends g{constructor({value:e,cause:t}){super({name:G,message:`Type validation failed: Value: ${JSON.stringify(e)}.
Error message: ${I(t)}`,cause:t}),this[d]=!0,this.value=e}static isInstance(e){return g.hasMarker(e,H)}static wrap({value:t,cause:r}){return e.isInstance(r)&&r.value===t?r:new e({value:t,cause:r})}};d=X;var ee=Q,et="AI_UnsupportedFunctionalityError",er=`vercel.ai.error.${et}`,ea=Symbol.for(er),es=class extends g{constructor({functionality:e,message:t=`'${e}' functionality not supported.`}){super({name:et,message:t}),this[p]=!0,this.functionality=e}static isInstance(e){return g.hasMarker(e,er)}};function en(e){return null===e||"string"==typeof e||"number"==typeof e||"boolean"==typeof e||(Array.isArray(e)?e.every(en):"object"==typeof e&&Object.entries(e).every(([e,t])=>"string"==typeof e&&en(t)))}p=ea},86230:(e,t,r)=>{r.d(t,{$C:()=>u,$b:()=>m,Ds:()=>T,GU:()=>b,HD:()=>S,WL:()=>h,ZZ:()=>g,ae:()=>j,cV:()=>I,hd:()=>x,m2:()=>o,n_:()=>A,sl:()=>k,v0:()=>v});var a=r(74306),s=r(48444),n=r(57057),i=r(71576);function o(...e){return e.reduce((e,t)=>({...e,...null!=t?t:{}}),{})}function l(e){let t={};return e.headers.forEach((e,r)=>{t[r]=e}),t}var u=(({prefix:e,size:t=16,alphabet:r="0123456789ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz",separator:n="-"}={})=>{let i=(0,s.d)(r,t);if(null==e)return i;if(r.includes(n))throw new a.Di({argument:"separator",message:`The separator "${n}" must not be part of the alphabet "${r}".`});return t=>`${e}${n}${i(t)}`})();function c(e){return Object.fromEntries(Object.entries(e).filter(([e,t])=>null!=t))}function d(e){return e instanceof Error&&("AbortError"===e.name||"TimeoutError"===e.name)}var p=()=>globalThis.fetch,m=async({url:e,headers:t={},successfulResponseHandler:r,failedResponseHandler:s,abortSignal:n,fetch:i=p()})=>{try{let o=await i(e,{method:"GET",headers:c(t),signal:n}),u=l(o);if(!o.ok){let t;try{t=await s({response:o,url:e,requestBodyValues:{}})}catch(t){if(d(t)||a.hL.isInstance(t))throw t;throw new a.hL({message:"Failed to process error response",cause:t,statusCode:o.status,url:e,responseHeaders:u,requestBodyValues:{}})}throw t.value}try{return await r({response:o,url:e,requestBodyValues:{}})}catch(t){if(t instanceof Error&&(d(t)||a.hL.isInstance(t)))throw t;throw new a.hL({message:"Failed to process successful response",cause:t,statusCode:o.status,url:e,responseHeaders:u,requestBodyValues:{}})}}catch(t){if(d(t))throw t;if(t instanceof TypeError&&"fetch failed"===t.message){let r=t.cause;if(null!=r)throw new a.hL({message:`Cannot connect to API: ${r.message}`,cause:r,url:e,isRetryable:!0,requestBodyValues:{}})}throw t}};function h({apiKey:e,environmentVariableName:t,apiKeyParameterName:r="apiKey",description:s}){if("string"==typeof e)return e;if(null!=e)throw new a.Kq({message:`${s} API key must be a string.`});if("undefined"==typeof process)throw new a.Kq({message:`${s} API key is missing. Pass it using the '${r}' parameter. Environment variables is not supported in this environment.`});if(null==(e=process.env[t]))throw new a.Kq({message:`${s} API key is missing. Pass it using the '${r}' parameter or the ${t} environment variable.`});if("string"!=typeof e)throw new a.Kq({message:`${s} API key must be a string. The value of the ${t} environment variable is not a string.`});return e}var f=Symbol.for("vercel.ai.validator");function g({value:e,schema:t}){var r;let s="object"==typeof t&&null!==t&&f in t&&!0===t[f]&&"validate"in t?t:(r=t,{[f]:!0,validate:e=>{let t=r.safeParse(e);return t.success?{success:!0,value:t.data}:{success:!1,error:t.error}}});try{if(null==s.validate)return{success:!0,value:e};let t=s.validate(e);if(t.success)return t;return{success:!1,error:a.iM.wrap({value:e,cause:t.error})}}catch(t){return{success:!1,error:a.iM.wrap({value:e,cause:t})}}}function y({text:e,schema:t}){try{let r=n.parse(e);if(null==t)return{success:!0,value:r,rawValue:r};let a=g({value:r,schema:t});return a.success?{...a,rawValue:r}:a}catch(t){return{success:!1,error:a.u6.isInstance(t)?t:new a.u6({text:e,cause:t})}}}function v(e){try{return n.parse(e),!0}catch(e){return!1}}var _=()=>globalThis.fetch,b=async({url:e,headers:t,body:r,failedResponseHandler:a,successfulResponseHandler:s,abortSignal:n,fetch:i})=>w({url:e,headers:{"Content-Type":"application/json",...t},body:{content:JSON.stringify(r),values:r},failedResponseHandler:a,successfulResponseHandler:s,abortSignal:n,fetch:i}),w=async({url:e,headers:t={},body:r,successfulResponseHandler:s,failedResponseHandler:n,abortSignal:i,fetch:o=_()})=>{try{let u=await o(e,{method:"POST",headers:c(t),body:r.content,signal:i}),p=l(u);if(!u.ok){let t;try{t=await n({response:u,url:e,requestBodyValues:r.values})}catch(t){if(d(t)||a.hL.isInstance(t))throw t;throw new a.hL({message:"Failed to process error response",cause:t,statusCode:u.status,url:e,responseHeaders:p,requestBodyValues:r.values})}throw t.value}try{return await s({response:u,url:e,requestBodyValues:r.values})}catch(t){if(t instanceof Error&&(d(t)||a.hL.isInstance(t)))throw t;throw new a.hL({message:"Failed to process successful response",cause:t,statusCode:u.status,url:e,responseHeaders:p,requestBodyValues:r.values})}}catch(t){if(d(t))throw t;if(t instanceof TypeError&&"fetch failed"===t.message){let s=t.cause;if(null!=s)throw new a.hL({message:`Cannot connect to API: ${s.message}`,cause:s,url:e,requestBodyValues:r.values,isRetryable:!0})}throw t}};async function x(e){return"function"==typeof e&&(e=e()),Promise.resolve(e)}var k=({errorSchema:e,errorToMessage:t,isRetryable:r})=>async({response:s,url:i,requestBodyValues:o})=>{let u=await s.text(),c=l(s);if(""===u.trim())return{responseHeaders:c,value:new a.hL({message:s.statusText,url:i,requestBodyValues:o,statusCode:s.status,responseHeaders:c,responseBody:u,isRetryable:null==r?void 0:r(s)})};try{let l=function({text:e,schema:t}){try{let r=n.parse(e);if(null==t)return r;return function({value:e,schema:t}){let r=g({value:e,schema:t});if(!r.success)throw a.iM.wrap({value:e,cause:r.error});return r.value}({value:r,schema:t})}catch(t){if(a.u6.isInstance(t)||a.iM.isInstance(t))throw t;throw new a.u6({text:e,cause:t})}}({text:u,schema:e});return{responseHeaders:c,value:new a.hL({message:t(l),url:i,requestBodyValues:o,statusCode:s.status,responseHeaders:c,responseBody:u,data:l,isRetryable:null==r?void 0:r(s,l)})}}catch(e){return{responseHeaders:c,value:new a.hL({message:s.statusText,url:i,requestBodyValues:o,statusCode:s.status,responseHeaders:c,responseBody:u,isRetryable:null==r?void 0:r(s)})}}},T=e=>async({response:t})=>{let r=l(t);if(null==t.body)throw new a.Tt({});return{responseHeaders:r,value:t.body.pipeThrough(new TextDecoderStream).pipeThrough(new i.Z).pipeThrough(new TransformStream({transform({data:t},r){"[DONE]"!==t&&r.enqueue(y({text:t,schema:e}))}}))}},I=e=>async({response:t,url:r,requestBodyValues:s})=>{let n=await t.text(),i=y({text:n,schema:e}),o=l(t);if(!i.success)throw new a.hL({message:"Invalid JSON response",cause:i.error,statusCode:t.status,responseHeaders:o,responseBody:n,url:r,requestBodyValues:s});return{responseHeaders:o,value:i.value,rawValue:i.rawValue}},S=()=>async({response:e,url:t,requestBodyValues:r})=>{let s=l(e);if(!e.body)throw new a.hL({message:"Response body is empty",url:t,requestBodyValues:r,statusCode:e.status,responseHeaders:s,responseBody:void 0});try{let t=await e.arrayBuffer();return{responseHeaders:s,value:new Uint8Array(t)}}catch(n){throw new a.hL({message:"Failed to read response as array buffer",url:t,requestBodyValues:r,statusCode:e.status,responseHeaders:s,responseBody:void 0,cause:n})}},{btoa:z,atob:E}=globalThis;function A(e){let t="";for(let r=0;r<e.length;r++)t+=String.fromCodePoint(e[r]);return z(t)}function j(e){return null==e?void 0:e.replace(/\/$/,"")}},91135:(e,t,r)=>{r.d(t,{N:()=>K});var a=r(92229),s=r(49715),n=r(12206);function i(e){var t,r;return null!=(r=null==(t=null==e?void 0:e.content)?void 0:t.map(({token:e,logprob:t,top_logprobs:r})=>({token:e,logprob:t,topLogprobs:r?r.map(({token:e,logprob:t})=>({token:e,logprob:t})):[]})))?r:void 0}function o(e){switch(e){case"stop":return"stop";case"length":return"length";case"content_filter":return"content-filter";case"function_call":case"tool_calls":return"tool-calls";default:return"unknown"}}var l=n.z.object({error:n.z.object({message:n.z.string(),type:n.z.string().nullish(),param:n.z.any().nullish(),code:n.z.union([n.z.string(),n.z.number()]).nullish()})}),u=(0,a.sl)({errorSchema:l,errorToMessage:e=>e.error.message});function c({id:e,model:t,created:r}){return{id:null!=e?e:void 0,modelId:null!=t?t:void 0,timestamp:null!=r?new Date(1e3*r):void 0}}var d=class{constructor(e,t,r){this.specificationVersion="v1",this.modelId=e,this.settings=t,this.config=r}get supportsStructuredOutputs(){var e;return null!=(e=this.settings.structuredOutputs)?e:f(this.modelId)}get defaultObjectGenerationMode(){return this.modelId.startsWith("gpt-4o-audio-preview")?"tool":this.supportsStructuredOutputs?"json":"tool"}get provider(){return this.config.provider}get supportsImageUrls(){return!this.settings.downloadImages}getArgs({mode:e,prompt:t,maxTokens:r,temperature:n,topP:i,topK:o,frequencyPenalty:l,presencePenalty:u,stopSequences:c,responseFormat:d,seed:p,providerMetadata:m}){var h,y,v,_,b,w,x,k,T,I,S;let z=e.type,E=[];null!=o&&E.push({type:"unsupported-setting",setting:"topK"}),(null==d?void 0:d.type)!=="json"||null==d.schema||this.supportsStructuredOutputs||E.push({type:"unsupported-setting",setting:"responseFormat",details:"JSON response format schema is only supported with structuredOutputs"});let A=this.settings.useLegacyFunctionCalling;if(A&&!0===this.settings.parallelToolCalls)throw new s.b8({functionality:"useLegacyFunctionCalling with parallelToolCalls"});if(A&&this.supportsStructuredOutputs)throw new s.b8({functionality:"structuredOutputs with useLegacyFunctionCalling"});let{messages:j,warnings:C}=function({prompt:e,useLegacyFunctionCalling:t=!1,systemMessageMode:r="system"}){let n=[],i=[];for(let{role:o,content:l}of e)switch(o){case"system":switch(r){case"system":n.push({role:"system",content:l});break;case"developer":n.push({role:"developer",content:l});break;case"remove":i.push({type:"other",message:"system messages are removed for this model"});break;default:throw Error(`Unsupported system message mode: ${r}`)}break;case"user":if(1===l.length&&"text"===l[0].type){n.push({role:"user",content:l[0].text});break}n.push({role:"user",content:l.map((e,t)=>{var r,n,i,o;switch(e.type){case"text":return{type:"text",text:e.text};case"image":return{type:"image_url",image_url:{url:e.image instanceof URL?e.image.toString():`data:${null!=(r=e.mimeType)?r:"image/jpeg"};base64,${(0,a.n_)(e.image)}`,detail:null==(i=null==(n=e.providerMetadata)?void 0:n.openai)?void 0:i.imageDetail}};case"file":if(e.data instanceof URL)throw new s.b8({functionality:"'File content parts with URL data' functionality not supported."});switch(e.mimeType){case"audio/wav":return{type:"input_audio",input_audio:{data:e.data,format:"wav"}};case"audio/mp3":case"audio/mpeg":return{type:"input_audio",input_audio:{data:e.data,format:"mp3"}};case"application/pdf":return{type:"file",file:{filename:null!=(o=e.filename)?o:`part-${t}.pdf`,file_data:`data:application/pdf;base64,${e.data}`}};default:throw new s.b8({functionality:`File content part type ${e.mimeType} in user messages`})}}})});break;case"assistant":{let e="",r=[];for(let t of l)switch(t.type){case"text":e+=t.text;break;case"tool-call":r.push({id:t.toolCallId,type:"function",function:{name:t.toolName,arguments:JSON.stringify(t.args)}})}if(t){if(r.length>1)throw new s.b8({functionality:"useLegacyFunctionCalling with multiple tool calls in one message"});n.push({role:"assistant",content:e,function_call:r.length>0?r[0].function:void 0})}else n.push({role:"assistant",content:e,tool_calls:r.length>0?r:void 0});break}case"tool":for(let e of l)t?n.push({role:"function",name:e.toolName,content:JSON.stringify(e.result)}):n.push({role:"tool",tool_call_id:e.toolCallId,content:JSON.stringify(e.result)});break;default:throw Error(`Unsupported role: ${o}`)}return{messages:n,warnings:i}}({prompt:t,useLegacyFunctionCalling:A,systemMessageMode:f(T=this.modelId)?null!=(S=null==(I=g[T])?void 0:I.systemMessageMode)?S:"developer":"system"});E.push(...C);let N={model:this.modelId,logit_bias:this.settings.logitBias,logprobs:!0===this.settings.logprobs||"number"==typeof this.settings.logprobs||void 0,top_logprobs:"number"==typeof this.settings.logprobs?this.settings.logprobs:"boolean"==typeof this.settings.logprobs&&this.settings.logprobs?0:void 0,user:this.settings.user,parallel_tool_calls:this.settings.parallelToolCalls,max_tokens:r,temperature:n,top_p:i,frequency_penalty:l,presence_penalty:u,response_format:(null==d?void 0:d.type)==="json"?this.supportsStructuredOutputs&&null!=d.schema?{type:"json_schema",json_schema:{schema:d.schema,strict:!0,name:null!=(h=d.name)?h:"response",description:d.description}}:{type:"json_object"}:void 0,stop:c,seed:p,max_completion_tokens:null==(y=null==m?void 0:m.openai)?void 0:y.maxCompletionTokens,store:null==(v=null==m?void 0:m.openai)?void 0:v.store,metadata:null==(_=null==m?void 0:m.openai)?void 0:_.metadata,prediction:null==(b=null==m?void 0:m.openai)?void 0:b.prediction,reasoning_effort:null!=(x=null==(w=null==m?void 0:m.openai)?void 0:w.reasoningEffort)?x:this.settings.reasoningEffort,messages:j};switch(f(this.modelId)?(null!=N.temperature&&(N.temperature=void 0,E.push({type:"unsupported-setting",setting:"temperature",details:"temperature is not supported for reasoning models"})),null!=N.top_p&&(N.top_p=void 0,E.push({type:"unsupported-setting",setting:"topP",details:"topP is not supported for reasoning models"})),null!=N.frequency_penalty&&(N.frequency_penalty=void 0,E.push({type:"unsupported-setting",setting:"frequencyPenalty",details:"frequencyPenalty is not supported for reasoning models"})),null!=N.presence_penalty&&(N.presence_penalty=void 0,E.push({type:"unsupported-setting",setting:"presencePenalty",details:"presencePenalty is not supported for reasoning models"})),null!=N.logit_bias&&(N.logit_bias=void 0,E.push({type:"other",message:"logitBias is not supported for reasoning models"})),null!=N.logprobs&&(N.logprobs=void 0,E.push({type:"other",message:"logprobs is not supported for reasoning models"})),null!=N.top_logprobs&&(N.top_logprobs=void 0,E.push({type:"other",message:"topLogprobs is not supported for reasoning models"})),null!=N.max_tokens&&(null==N.max_completion_tokens&&(N.max_completion_tokens=N.max_tokens),N.max_tokens=void 0)):(this.modelId.startsWith("gpt-4o-search-preview")||this.modelId.startsWith("gpt-4o-mini-search-preview"))&&null!=N.temperature&&(N.temperature=void 0,E.push({type:"unsupported-setting",setting:"temperature",details:"temperature is not supported for the search preview models and has been removed."})),z){case"regular":{let{tools:t,tool_choice:r,functions:a,function_call:n,toolWarnings:i}=function({mode:e,useLegacyFunctionCalling:t=!1,structuredOutputs:r}){var a;let n=(null==(a=e.tools)?void 0:a.length)?e.tools:void 0,i=[];if(null==n)return{tools:void 0,tool_choice:void 0,toolWarnings:i};let o=e.toolChoice;if(t){let e=[];for(let t of n)"provider-defined"===t.type?i.push({type:"unsupported-tool",tool:t}):e.push({name:t.name,description:t.description,parameters:t.parameters});if(null==o)return{functions:e,function_call:void 0,toolWarnings:i};switch(o.type){case"auto":case"none":case void 0:return{functions:e,function_call:void 0,toolWarnings:i};case"required":throw new s.b8({functionality:"useLegacyFunctionCalling and toolChoice: required"});default:return{functions:e,function_call:{name:o.toolName},toolWarnings:i}}}let l=[];for(let e of n)"provider-defined"===e.type?i.push({type:"unsupported-tool",tool:e}):l.push({type:"function",function:{name:e.name,description:e.description,parameters:e.parameters,strict:!!r||void 0}});if(null==o)return{tools:l,tool_choice:void 0,toolWarnings:i};let u=o.type;switch(u){case"auto":case"none":case"required":return{tools:l,tool_choice:u,toolWarnings:i};case"tool":return{tools:l,tool_choice:{type:"function",function:{name:o.toolName}},toolWarnings:i};default:throw new s.b8({functionality:`Unsupported tool choice type: ${u}`})}}({mode:e,useLegacyFunctionCalling:A,structuredOutputs:this.supportsStructuredOutputs});return{args:{...N,tools:t,tool_choice:r,functions:a,function_call:n},warnings:[...E,...i]}}case"object-json":return{args:{...N,response_format:this.supportsStructuredOutputs&&null!=e.schema?{type:"json_schema",json_schema:{schema:e.schema,strict:!0,name:null!=(k=e.name)?k:"response",description:e.description}}:{type:"json_object"}},warnings:E};case"object-tool":return{args:A?{...N,function_call:{name:e.tool.name},functions:[{name:e.tool.name,description:e.tool.description,parameters:e.tool.parameters}]}:{...N,tool_choice:{type:"function",function:{name:e.tool.name}},tools:[{type:"function",function:{name:e.tool.name,description:e.tool.description,parameters:e.tool.parameters,strict:!!this.supportsStructuredOutputs||void 0}}]},warnings:E};default:throw Error(`Unsupported type: ${z}`)}}async doGenerate(e){var t,r,s,n,l,d,p,h;let{args:f,warnings:g}=this.getArgs(e),{responseHeaders:y,value:v,rawValue:_}=await (0,a.GU)({url:this.config.url({path:"/chat/completions",modelId:this.modelId}),headers:(0,a.m2)(this.config.headers(),e.headers),body:f,failedResponseHandler:u,successfulResponseHandler:(0,a.cV)(m),abortSignal:e.abortSignal,fetch:this.config.fetch}),{messages:b,...w}=f,x=v.choices[0],k=null==(t=v.usage)?void 0:t.completion_tokens_details,T=null==(r=v.usage)?void 0:r.prompt_tokens_details,I={openai:{}};return(null==k?void 0:k.reasoning_tokens)!=null&&(I.openai.reasoningTokens=null==k?void 0:k.reasoning_tokens),(null==k?void 0:k.accepted_prediction_tokens)!=null&&(I.openai.acceptedPredictionTokens=null==k?void 0:k.accepted_prediction_tokens),(null==k?void 0:k.rejected_prediction_tokens)!=null&&(I.openai.rejectedPredictionTokens=null==k?void 0:k.rejected_prediction_tokens),(null==T?void 0:T.cached_tokens)!=null&&(I.openai.cachedPromptTokens=null==T?void 0:T.cached_tokens),{text:null!=(s=x.message.content)?s:void 0,toolCalls:this.settings.useLegacyFunctionCalling&&x.message.function_call?[{toolCallType:"function",toolCallId:(0,a.$C)(),toolName:x.message.function_call.name,args:x.message.function_call.arguments}]:null==(n=x.message.tool_calls)?void 0:n.map(e=>{var t;return{toolCallType:"function",toolCallId:null!=(t=e.id)?t:(0,a.$C)(),toolName:e.function.name,args:e.function.arguments}}),finishReason:o(x.finish_reason),usage:{promptTokens:null!=(d=null==(l=v.usage)?void 0:l.prompt_tokens)?d:NaN,completionTokens:null!=(h=null==(p=v.usage)?void 0:p.completion_tokens)?h:NaN},rawCall:{rawPrompt:b,rawSettings:w},rawResponse:{headers:y,body:_},request:{body:JSON.stringify(f)},response:c(v),warnings:g,logprobs:i(x.logprobs),providerMetadata:I}}async doStream(e){let t;if(this.settings.simulateStreaming){let t=await this.doGenerate(e);return{stream:new ReadableStream({start(e){if(e.enqueue({type:"response-metadata",...t.response}),t.text&&e.enqueue({type:"text-delta",textDelta:t.text}),t.toolCalls)for(let r of t.toolCalls)e.enqueue({type:"tool-call-delta",toolCallType:"function",toolCallId:r.toolCallId,toolName:r.toolName,argsTextDelta:r.args}),e.enqueue({type:"tool-call",...r});e.enqueue({type:"finish",finishReason:t.finishReason,usage:t.usage,logprobs:t.logprobs,providerMetadata:t.providerMetadata}),e.close()}}),rawCall:t.rawCall,rawResponse:t.rawResponse,warnings:t.warnings}}let{args:r,warnings:n}=this.getArgs(e),l={...r,stream:!0,stream_options:"strict"===this.config.compatibility?{include_usage:!0}:void 0},{responseHeaders:d,value:p}=await (0,a.GU)({url:this.config.url({path:"/chat/completions",modelId:this.modelId}),headers:(0,a.m2)(this.config.headers(),e.headers),body:l,failedResponseHandler:u,successfulResponseHandler:(0,a.Ds)(h),abortSignal:e.abortSignal,fetch:this.config.fetch}),{messages:m,...f}=r,g=[],y="unknown",v={promptTokens:void 0,completionTokens:void 0},_=!0,{useLegacyFunctionCalling:b}=this.settings,w={openai:{}};return{stream:p.pipeThrough(new TransformStream({transform(e,r){var n,l,u,d,p,m,h,f,x,k,T,I;if(!e.success){y="error",r.enqueue({type:"error",error:e.error});return}let S=e.value;if("error"in S){y="error",r.enqueue({type:"error",error:S.error});return}if(_&&(_=!1,r.enqueue({type:"response-metadata",...c(S)})),null!=S.usage){let{prompt_tokens:e,completion_tokens:t,prompt_tokens_details:r,completion_tokens_details:a}=S.usage;v={promptTokens:null!=e?e:void 0,completionTokens:null!=t?t:void 0},(null==a?void 0:a.reasoning_tokens)!=null&&(w.openai.reasoningTokens=null==a?void 0:a.reasoning_tokens),(null==a?void 0:a.accepted_prediction_tokens)!=null&&(w.openai.acceptedPredictionTokens=null==a?void 0:a.accepted_prediction_tokens),(null==a?void 0:a.rejected_prediction_tokens)!=null&&(w.openai.rejectedPredictionTokens=null==a?void 0:a.rejected_prediction_tokens),(null==r?void 0:r.cached_tokens)!=null&&(w.openai.cachedPromptTokens=null==r?void 0:r.cached_tokens)}let z=S.choices[0];if((null==z?void 0:z.finish_reason)!=null&&(y=o(z.finish_reason)),(null==z?void 0:z.delta)==null)return;let E=z.delta;null!=E.content&&r.enqueue({type:"text-delta",textDelta:E.content});let A=i(null==z?void 0:z.logprobs);(null==A?void 0:A.length)&&(void 0===t&&(t=[]),t.push(...A));let j=b&&null!=E.function_call?[{type:"function",id:(0,a.$C)(),function:E.function_call,index:0}]:E.tool_calls;if(null!=j)for(let e of j){let t=e.index;if(null==g[t]){if("function"!==e.type)throw new s.xn({data:e,message:"Expected 'function' type."});if(null==e.id)throw new s.xn({data:e,message:"Expected 'id' to be a string."});if((null==(n=e.function)?void 0:n.name)==null)throw new s.xn({data:e,message:"Expected 'function.name' to be a string."});g[t]={id:e.id,type:"function",function:{name:e.function.name,arguments:null!=(l=e.function.arguments)?l:""},hasFinished:!1};let i=g[t];(null==(u=i.function)?void 0:u.name)!=null&&(null==(d=i.function)?void 0:d.arguments)!=null&&(i.function.arguments.length>0&&r.enqueue({type:"tool-call-delta",toolCallType:"function",toolCallId:i.id,toolName:i.function.name,argsTextDelta:i.function.arguments}),(0,a.v0)(i.function.arguments)&&(r.enqueue({type:"tool-call",toolCallType:"function",toolCallId:null!=(p=i.id)?p:(0,a.$C)(),toolName:i.function.name,args:i.function.arguments}),i.hasFinished=!0));continue}let i=g[t];!i.hasFinished&&((null==(m=e.function)?void 0:m.arguments)!=null&&(i.function.arguments+=null!=(f=null==(h=e.function)?void 0:h.arguments)?f:""),r.enqueue({type:"tool-call-delta",toolCallType:"function",toolCallId:i.id,toolName:i.function.name,argsTextDelta:null!=(x=e.function.arguments)?x:""}),(null==(k=i.function)?void 0:k.name)!=null&&(null==(T=i.function)?void 0:T.arguments)!=null&&(0,a.v0)(i.function.arguments)&&(r.enqueue({type:"tool-call",toolCallType:"function",toolCallId:null!=(I=i.id)?I:(0,a.$C)(),toolName:i.function.name,args:i.function.arguments}),i.hasFinished=!0))}},flush(e){var r,a;e.enqueue({type:"finish",finishReason:y,logprobs:t,usage:{promptTokens:null!=(r=v.promptTokens)?r:NaN,completionTokens:null!=(a=v.completionTokens)?a:NaN},...null!=w?{providerMetadata:w}:{}})}})),rawCall:{rawPrompt:m,rawSettings:f},rawResponse:{headers:d},request:{body:JSON.stringify(l)},warnings:n}}},p=n.z.object({prompt_tokens:n.z.number().nullish(),completion_tokens:n.z.number().nullish(),prompt_tokens_details:n.z.object({cached_tokens:n.z.number().nullish()}).nullish(),completion_tokens_details:n.z.object({reasoning_tokens:n.z.number().nullish(),accepted_prediction_tokens:n.z.number().nullish(),rejected_prediction_tokens:n.z.number().nullish()}).nullish()}).nullish(),m=n.z.object({id:n.z.string().nullish(),created:n.z.number().nullish(),model:n.z.string().nullish(),choices:n.z.array(n.z.object({message:n.z.object({role:n.z.literal("assistant").nullish(),content:n.z.string().nullish(),function_call:n.z.object({arguments:n.z.string(),name:n.z.string()}).nullish(),tool_calls:n.z.array(n.z.object({id:n.z.string().nullish(),type:n.z.literal("function"),function:n.z.object({name:n.z.string(),arguments:n.z.string()})})).nullish()}),index:n.z.number(),logprobs:n.z.object({content:n.z.array(n.z.object({token:n.z.string(),logprob:n.z.number(),top_logprobs:n.z.array(n.z.object({token:n.z.string(),logprob:n.z.number()}))})).nullable()}).nullish(),finish_reason:n.z.string().nullish()})),usage:p}),h=n.z.union([n.z.object({id:n.z.string().nullish(),created:n.z.number().nullish(),model:n.z.string().nullish(),choices:n.z.array(n.z.object({delta:n.z.object({role:n.z.enum(["assistant"]).nullish(),content:n.z.string().nullish(),function_call:n.z.object({name:n.z.string().optional(),arguments:n.z.string().optional()}).nullish(),tool_calls:n.z.array(n.z.object({index:n.z.number(),id:n.z.string().nullish(),type:n.z.literal("function").nullish(),function:n.z.object({name:n.z.string().nullish(),arguments:n.z.string().nullish()})})).nullish()}).nullish(),logprobs:n.z.object({content:n.z.array(n.z.object({token:n.z.string(),logprob:n.z.number(),top_logprobs:n.z.array(n.z.object({token:n.z.string(),logprob:n.z.number()}))})).nullable()}).nullish(),finish_reason:n.z.string().nullish(),index:n.z.number()})),usage:p}),l]);function f(e){return e.startsWith("o")}var g={"o1-mini":{systemMessageMode:"remove"},"o1-mini-2024-09-12":{systemMessageMode:"remove"},"o1-preview":{systemMessageMode:"remove"},"o1-preview-2024-09-12":{systemMessageMode:"remove"},o3:{systemMessageMode:"developer"},"o3-2025-04-16":{systemMessageMode:"developer"},"o3-mini":{systemMessageMode:"developer"},"o3-mini-2025-01-31":{systemMessageMode:"developer"},"o4-mini":{systemMessageMode:"developer"},"o4-mini-2025-04-16":{systemMessageMode:"developer"}};function y(e){return null==e?void 0:e.tokens.map((t,r)=>({token:t,logprob:e.token_logprobs[r],topLogprobs:e.top_logprobs?Object.entries(e.top_logprobs[r]).map(([e,t])=>({token:e,logprob:t})):[]}))}var v=class{constructor(e,t,r){this.specificationVersion="v1",this.defaultObjectGenerationMode=void 0,this.modelId=e,this.settings=t,this.config=r}get provider(){return this.config.provider}getArgs({mode:e,inputFormat:t,prompt:r,maxTokens:a,temperature:n,topP:i,topK:o,frequencyPenalty:l,presencePenalty:u,stopSequences:c,responseFormat:d,seed:p}){var m;let h=e.type,f=[];null!=o&&f.push({type:"unsupported-setting",setting:"topK"}),null!=d&&"text"!==d.type&&f.push({type:"unsupported-setting",setting:"responseFormat",details:"JSON response format is not supported."});let{prompt:g,stopSequences:y}=function({prompt:e,inputFormat:t,user:r="user",assistant:a="assistant"}){if("prompt"===t&&1===e.length&&"user"===e[0].role&&1===e[0].content.length&&"text"===e[0].content[0].type)return{prompt:e[0].content[0].text};let n="";for(let{role:t,content:i}of("system"===e[0].role&&(n+=`${e[0].content}

`,e=e.slice(1)),e))switch(t){case"system":throw new s.M3({message:"Unexpected system message in prompt: ${content}",prompt:e});case"user":{let e=i.map(e=>{switch(e.type){case"text":return e.text;case"image":throw new s.b8({functionality:"images"})}}).join("");n+=`${r}:
${e}

`;break}case"assistant":{let e=i.map(e=>{switch(e.type){case"text":return e.text;case"tool-call":throw new s.b8({functionality:"tool-call messages"})}}).join("");n+=`${a}:
${e}

`;break}case"tool":throw new s.b8({functionality:"tool messages"});default:throw Error(`Unsupported role: ${t}`)}return{prompt:n+=`${a}:
`,stopSequences:[`
${r}:`]}}({prompt:r,inputFormat:t}),v=[...null!=y?y:[],...null!=c?c:[]],_={model:this.modelId,echo:this.settings.echo,logit_bias:this.settings.logitBias,logprobs:"number"==typeof this.settings.logprobs?this.settings.logprobs:"boolean"==typeof this.settings.logprobs&&this.settings.logprobs?0:void 0,suffix:this.settings.suffix,user:this.settings.user,max_tokens:a,temperature:n,top_p:i,frequency_penalty:l,presence_penalty:u,seed:p,prompt:g,stop:v.length>0?v:void 0};switch(h){case"regular":if(null==(m=e.tools)?void 0:m.length)throw new s.b8({functionality:"tools"});if(e.toolChoice)throw new s.b8({functionality:"toolChoice"});return{args:_,warnings:f};case"object-json":throw new s.b8({functionality:"object-json mode"});case"object-tool":throw new s.b8({functionality:"object-tool mode"});default:throw Error(`Unsupported type: ${h}`)}}async doGenerate(e){let{args:t,warnings:r}=this.getArgs(e),{responseHeaders:s,value:n,rawValue:i}=await (0,a.GU)({url:this.config.url({path:"/completions",modelId:this.modelId}),headers:(0,a.m2)(this.config.headers(),e.headers),body:t,failedResponseHandler:u,successfulResponseHandler:(0,a.cV)(_),abortSignal:e.abortSignal,fetch:this.config.fetch}),{prompt:l,...d}=t,p=n.choices[0];return{text:p.text,usage:{promptTokens:n.usage.prompt_tokens,completionTokens:n.usage.completion_tokens},finishReason:o(p.finish_reason),logprobs:y(p.logprobs),rawCall:{rawPrompt:l,rawSettings:d},rawResponse:{headers:s,body:i},response:c(n),warnings:r,request:{body:JSON.stringify(t)}}}async doStream(e){let t;let{args:r,warnings:s}=this.getArgs(e),n={...r,stream:!0,stream_options:"strict"===this.config.compatibility?{include_usage:!0}:void 0},{responseHeaders:i,value:l}=await (0,a.GU)({url:this.config.url({path:"/completions",modelId:this.modelId}),headers:(0,a.m2)(this.config.headers(),e.headers),body:n,failedResponseHandler:u,successfulResponseHandler:(0,a.Ds)(b),abortSignal:e.abortSignal,fetch:this.config.fetch}),{prompt:d,...p}=r,m="unknown",h={promptTokens:Number.NaN,completionTokens:Number.NaN},f=!0;return{stream:l.pipeThrough(new TransformStream({transform(e,r){if(!e.success){m="error",r.enqueue({type:"error",error:e.error});return}let a=e.value;if("error"in a){m="error",r.enqueue({type:"error",error:a.error});return}f&&(f=!1,r.enqueue({type:"response-metadata",...c(a)})),null!=a.usage&&(h={promptTokens:a.usage.prompt_tokens,completionTokens:a.usage.completion_tokens});let s=a.choices[0];(null==s?void 0:s.finish_reason)!=null&&(m=o(s.finish_reason)),(null==s?void 0:s.text)!=null&&r.enqueue({type:"text-delta",textDelta:s.text});let n=y(null==s?void 0:s.logprobs);(null==n?void 0:n.length)&&(void 0===t&&(t=[]),t.push(...n))},flush(e){e.enqueue({type:"finish",finishReason:m,logprobs:t,usage:h})}})),rawCall:{rawPrompt:d,rawSettings:p},rawResponse:{headers:i},warnings:s,request:{body:JSON.stringify(n)}}}},_=n.z.object({id:n.z.string().nullish(),created:n.z.number().nullish(),model:n.z.string().nullish(),choices:n.z.array(n.z.object({text:n.z.string(),finish_reason:n.z.string(),logprobs:n.z.object({tokens:n.z.array(n.z.string()),token_logprobs:n.z.array(n.z.number()),top_logprobs:n.z.array(n.z.record(n.z.string(),n.z.number())).nullable()}).nullish()})),usage:n.z.object({prompt_tokens:n.z.number(),completion_tokens:n.z.number()})}),b=n.z.union([n.z.object({id:n.z.string().nullish(),created:n.z.number().nullish(),model:n.z.string().nullish(),choices:n.z.array(n.z.object({text:n.z.string(),finish_reason:n.z.string().nullish(),index:n.z.number(),logprobs:n.z.object({tokens:n.z.array(n.z.string()),token_logprobs:n.z.array(n.z.number()),top_logprobs:n.z.array(n.z.record(n.z.string(),n.z.number())).nullable()}).nullish()})),usage:n.z.object({prompt_tokens:n.z.number(),completion_tokens:n.z.number()}).nullish()}),l]),w=class{constructor(e,t,r){this.specificationVersion="v1",this.modelId=e,this.settings=t,this.config=r}get provider(){return this.config.provider}get maxEmbeddingsPerCall(){var e;return null!=(e=this.settings.maxEmbeddingsPerCall)?e:2048}get supportsParallelCalls(){var e;return null==(e=this.settings.supportsParallelCalls)||e}async doEmbed({values:e,headers:t,abortSignal:r}){if(e.length>this.maxEmbeddingsPerCall)throw new s.Ch({provider:this.provider,modelId:this.modelId,maxEmbeddingsPerCall:this.maxEmbeddingsPerCall,values:e});let{responseHeaders:n,value:i}=await (0,a.GU)({url:this.config.url({path:"/embeddings",modelId:this.modelId}),headers:(0,a.m2)(this.config.headers(),t),body:{model:this.modelId,input:e,encoding_format:"float",dimensions:this.settings.dimensions,user:this.settings.user},failedResponseHandler:u,successfulResponseHandler:(0,a.cV)(x),abortSignal:r,fetch:this.config.fetch});return{embeddings:i.data.map(e=>e.embedding),usage:i.usage?{tokens:i.usage.prompt_tokens}:void 0,rawResponse:{headers:n}}}},x=n.z.object({data:n.z.array(n.z.object({embedding:n.z.array(n.z.number())})),usage:n.z.object({prompt_tokens:n.z.number()}).nullish()}),k={"dall-e-3":1,"dall-e-2":10,"gpt-image-1":10},T=new Set(["gpt-image-1"]),I=class{constructor(e,t,r){this.modelId=e,this.settings=t,this.config=r,this.specificationVersion="v1"}get maxImagesPerCall(){var e,t;return null!=(t=null!=(e=this.settings.maxImagesPerCall)?e:k[this.modelId])?t:1}get provider(){return this.config.provider}async doGenerate({prompt:e,n:t,size:r,aspectRatio:s,seed:n,providerOptions:i,headers:o,abortSignal:l}){var c,d,p,m;let h=[];null!=s&&h.push({type:"unsupported-setting",setting:"aspectRatio",details:"This model does not support aspect ratio. Use `size` instead."}),null!=n&&h.push({type:"unsupported-setting",setting:"seed"});let f=null!=(p=null==(d=null==(c=this.config._internal)?void 0:c.currentDate)?void 0:d.call(c))?p:new Date,{value:g,responseHeaders:y}=await (0,a.GU)({url:this.config.url({path:"/images/generations",modelId:this.modelId}),headers:(0,a.m2)(this.config.headers(),o),body:{model:this.modelId,prompt:e,n:t,size:r,...null!=(m=i.openai)?m:{},...T.has(this.modelId)?{}:{response_format:"b64_json"}},failedResponseHandler:u,successfulResponseHandler:(0,a.cV)(S),abortSignal:l,fetch:this.config.fetch});return{images:g.data.map(e=>e.b64_json),warnings:h,response:{timestamp:f,modelId:this.modelId,headers:y}}}},S=n.z.object({data:n.z.array(n.z.object({b64_json:n.z.string()}))}),z=n.z.object({include:n.z.array(n.z.string()).nullish(),language:n.z.string().nullish(),prompt:n.z.string().nullish(),temperature:n.z.number().min(0).max(1).nullish().default(0),timestampGranularities:n.z.array(n.z.enum(["word","segment"])).nullish().default(["segment"])}),E={afrikaans:"af",arabic:"ar",armenian:"hy",azerbaijani:"az",belarusian:"be",bosnian:"bs",bulgarian:"bg",catalan:"ca",chinese:"zh",croatian:"hr",czech:"cs",danish:"da",dutch:"nl",english:"en",estonian:"et",finnish:"fi",french:"fr",galician:"gl",german:"de",greek:"el",hebrew:"he",hindi:"hi",hungarian:"hu",icelandic:"is",indonesian:"id",italian:"it",japanese:"ja",kannada:"kn",kazakh:"kk",korean:"ko",latvian:"lv",lithuanian:"lt",macedonian:"mk",malay:"ms",marathi:"mr",maori:"mi",nepali:"ne",norwegian:"no",persian:"fa",polish:"pl",portuguese:"pt",romanian:"ro",russian:"ru",serbian:"sr",slovak:"sk",slovenian:"sl",spanish:"es",swahili:"sw",swedish:"sv",tagalog:"tl",tamil:"ta",thai:"th",turkish:"tr",ukrainian:"uk",urdu:"ur",vietnamese:"vi",welsh:"cy"},A=class{constructor(e,t){this.modelId=e,this.config=t,this.specificationVersion="v1"}get provider(){return this.config.provider}getArgs({audio:e,mediaType:t,providerOptions:r}){var s,n,i,o,l;let u=(0,a.xI)({provider:"openai",providerOptions:r,schema:z}),c=new FormData,d=e instanceof Uint8Array?new Blob([e]):new Blob([(0,a.Z9)(e)]);if(c.append("model",this.modelId),c.append("file",new File([d],"audio",{type:t})),u){let e={include:null!=(s=u.include)?s:void 0,language:null!=(n=u.language)?n:void 0,prompt:null!=(i=u.prompt)?i:void 0,temperature:null!=(o=u.temperature)?o:void 0,timestamp_granularities:null!=(l=u.timestampGranularities)?l:void 0};for(let t in e){let r=e[t];void 0!==r&&c.append(t,String(r))}}return{formData:c,warnings:[]}}async doGenerate(e){var t,r,s,n,i,o;let l=null!=(s=null==(r=null==(t=this.config._internal)?void 0:t.currentDate)?void 0:r.call(t))?s:new Date,{formData:c,warnings:d}=this.getArgs(e),{value:p,responseHeaders:m,rawValue:h}=await (0,a.S)({url:this.config.url({path:"/audio/transcriptions",modelId:this.modelId}),headers:(0,a.m2)(this.config.headers(),e.headers),formData:c,failedResponseHandler:u,successfulResponseHandler:(0,a.cV)(j),abortSignal:e.abortSignal,fetch:this.config.fetch}),f=null!=p.language&&p.language in E?E[p.language]:void 0;return{text:p.text,segments:null!=(i=null==(n=p.words)?void 0:n.map(e=>({text:e.word,startSecond:e.start,endSecond:e.end})))?i:[],language:f,durationInSeconds:null!=(o=p.duration)?o:void 0,warnings:d,response:{timestamp:l,modelId:this.modelId,headers:m,body:h}}}},j=n.z.object({text:n.z.string(),language:n.z.string().nullish(),duration:n.z.number().nullish(),words:n.z.array(n.z.object({word:n.z.string(),start:n.z.number(),end:n.z.number()})).nullish()});function C({finishReason:e,hasToolCalls:t}){switch(e){case void 0:case null:return t?"tool-calls":"stop";case"max_output_tokens":return"length";case"content_filter":return"content-filter";default:return t?"tool-calls":"unknown"}}var N=class{constructor(e,t){this.specificationVersion="v1",this.defaultObjectGenerationMode="json",this.supportsStructuredOutputs=!0,this.modelId=e,this.config=t}get provider(){return this.config.provider}getArgs({mode:e,maxTokens:t,temperature:r,stopSequences:n,topP:i,topK:o,presencePenalty:l,frequencyPenalty:u,seed:c,prompt:d,providerMetadata:p,responseFormat:m}){var h,f,g,y;let v=[],_=(y=this.modelId).startsWith("o")?y.startsWith("o1-mini")||y.startsWith("o1-preview")?{isReasoningModel:!0,systemMessageMode:"remove",requiredAutoTruncation:!1}:{isReasoningModel:!0,systemMessageMode:"developer",requiredAutoTruncation:!1}:{isReasoningModel:!1,systemMessageMode:"system",requiredAutoTruncation:!1},b=e.type;null!=o&&v.push({type:"unsupported-setting",setting:"topK"}),null!=c&&v.push({type:"unsupported-setting",setting:"seed"}),null!=l&&v.push({type:"unsupported-setting",setting:"presencePenalty"}),null!=u&&v.push({type:"unsupported-setting",setting:"frequencyPenalty"}),null!=n&&v.push({type:"unsupported-setting",setting:"stopSequences"});let{messages:w,warnings:x}=function({prompt:e,systemMessageMode:t}){let r=[],n=[];for(let{role:i,content:o}of e)switch(i){case"system":switch(t){case"system":r.push({role:"system",content:o});break;case"developer":r.push({role:"developer",content:o});break;case"remove":n.push({type:"other",message:"system messages are removed for this model"});break;default:throw Error(`Unsupported system message mode: ${t}`)}break;case"user":r.push({role:"user",content:o.map((e,t)=>{var r,n,i,o;switch(e.type){case"text":return{type:"input_text",text:e.text};case"image":return{type:"input_image",image_url:e.image instanceof URL?e.image.toString():`data:${null!=(r=e.mimeType)?r:"image/jpeg"};base64,${(0,a.n_)(e.image)}`,detail:null==(i=null==(n=e.providerMetadata)?void 0:n.openai)?void 0:i.imageDetail};case"file":if(e.data instanceof URL)throw new s.b8({functionality:"File URLs in user messages"});if("application/pdf"===e.mimeType)return{type:"input_file",filename:null!=(o=e.filename)?o:`part-${t}.pdf`,file_data:`data:application/pdf;base64,${e.data}`};throw new s.b8({functionality:"Only PDF files are supported in user messages"})}})});break;case"assistant":for(let e of o)switch(e.type){case"text":r.push({role:"assistant",content:[{type:"output_text",text:e.text}]});break;case"tool-call":r.push({type:"function_call",call_id:e.toolCallId,name:e.toolName,arguments:JSON.stringify(e.args)})}break;case"tool":for(let e of o)r.push({type:"function_call_output",call_id:e.toolCallId,output:JSON.stringify(e.result)});break;default:throw Error(`Unsupported role: ${i}`)}return{messages:r,warnings:n}}({prompt:d,systemMessageMode:_.systemMessageMode});v.push(...x);let k=(0,a.xI)({provider:"openai",providerOptions:p,schema:U}),T=null==(h=null==k?void 0:k.strictSchemas)||h,I={model:this.modelId,input:w,temperature:r,top_p:i,max_output_tokens:t,...(null==m?void 0:m.type)==="json"&&{text:{format:null!=m.schema?{type:"json_schema",strict:T,name:null!=(f=m.name)?f:"response",description:m.description,schema:m.schema}:{type:"json_object"}}},metadata:null==k?void 0:k.metadata,parallel_tool_calls:null==k?void 0:k.parallelToolCalls,previous_response_id:null==k?void 0:k.previousResponseId,store:null==k?void 0:k.store,user:null==k?void 0:k.user,instructions:null==k?void 0:k.instructions,..._.isReasoningModel&&((null==k?void 0:k.reasoningEffort)!=null||(null==k?void 0:k.reasoningSummary)!=null)&&{reasoning:{...(null==k?void 0:k.reasoningEffort)!=null&&{effort:k.reasoningEffort},...(null==k?void 0:k.reasoningSummary)!=null&&{summary:k.reasoningSummary}}},..._.requiredAutoTruncation&&{truncation:"auto"}};switch(_.isReasoningModel&&(null!=I.temperature&&(I.temperature=void 0,v.push({type:"unsupported-setting",setting:"temperature",details:"temperature is not supported for reasoning models"})),null!=I.top_p&&(I.top_p=void 0,v.push({type:"unsupported-setting",setting:"topP",details:"topP is not supported for reasoning models"}))),b){case"regular":{let{tools:t,tool_choice:r,toolWarnings:a}=function({mode:e,strict:t}){var r;let a=(null==(r=e.tools)?void 0:r.length)?e.tools:void 0,n=[];if(null==a)return{tools:void 0,tool_choice:void 0,toolWarnings:n};let i=e.toolChoice,o=[];for(let e of a)switch(e.type){case"function":o.push({type:"function",name:e.name,description:e.description,parameters:e.parameters,strict:!!t||void 0});break;case"provider-defined":"openai.web_search_preview"===e.id?o.push({type:"web_search_preview",search_context_size:e.args.searchContextSize,user_location:e.args.userLocation}):n.push({type:"unsupported-tool",tool:e});break;default:n.push({type:"unsupported-tool",tool:e})}if(null==i)return{tools:o,tool_choice:void 0,toolWarnings:n};let l=i.type;switch(l){case"auto":case"none":case"required":return{tools:o,tool_choice:l,toolWarnings:n};case"tool":if("web_search_preview"===i.toolName)return{tools:o,tool_choice:{type:"web_search_preview"},toolWarnings:n};return{tools:o,tool_choice:{type:"function",name:i.toolName},toolWarnings:n};default:throw new s.b8({functionality:`Unsupported tool choice type: ${l}`})}}({mode:e,strict:T});return{args:{...I,tools:t,tool_choice:r},warnings:[...v,...a]}}case"object-json":return{args:{...I,text:{format:null!=e.schema?{type:"json_schema",strict:T,name:null!=(g=e.name)?g:"response",description:e.description,schema:e.schema}:{type:"json_object"}}},warnings:v};case"object-tool":return{args:{...I,tool_choice:{type:"function",name:e.tool.name},tools:[{type:"function",name:e.tool.name,description:e.tool.description,parameters:e.tool.parameters,strict:T}]},warnings:v};default:throw Error(`Unsupported type: ${b}`)}}async doGenerate(e){var t,r,s,i,o,l,c;let{args:d,warnings:p}=this.getArgs(e),{responseHeaders:m,value:h,rawValue:f}=await (0,a.GU)({url:this.config.url({path:"/responses",modelId:this.modelId}),headers:(0,a.m2)(this.config.headers(),e.headers),body:d,failedResponseHandler:u,successfulResponseHandler:(0,a.cV)(n.z.object({id:n.z.string(),created_at:n.z.number(),model:n.z.string(),output:n.z.array(n.z.discriminatedUnion("type",[n.z.object({type:n.z.literal("message"),role:n.z.literal("assistant"),content:n.z.array(n.z.object({type:n.z.literal("output_text"),text:n.z.string(),annotations:n.z.array(n.z.object({type:n.z.literal("url_citation"),start_index:n.z.number(),end_index:n.z.number(),url:n.z.string(),title:n.z.string()}))}))}),n.z.object({type:n.z.literal("function_call"),call_id:n.z.string(),name:n.z.string(),arguments:n.z.string()}),n.z.object({type:n.z.literal("web_search_call")}),n.z.object({type:n.z.literal("computer_call")}),n.z.object({type:n.z.literal("reasoning"),summary:n.z.array(n.z.object({type:n.z.literal("summary_text"),text:n.z.string()}))})])),incomplete_details:n.z.object({reason:n.z.string()}).nullable(),usage:R})),abortSignal:e.abortSignal,fetch:this.config.fetch}),g=h.output.filter(e=>"message"===e.type).flatMap(e=>e.content).filter(e=>"output_text"===e.type),y=h.output.filter(e=>"function_call"===e.type).map(e=>({toolCallType:"function",toolCallId:e.call_id,toolName:e.name,args:e.arguments})),v=null!=(r=null==(t=h.output.find(e=>"reasoning"===e.type))?void 0:t.summary)?r:null;return{text:g.map(e=>e.text).join("\n"),sources:g.flatMap(e=>e.annotations.map(e=>{var t,r,s;return{sourceType:"url",id:null!=(s=null==(r=(t=this.config).generateId)?void 0:r.call(t))?s:(0,a.$C)(),url:e.url,title:e.title}})),finishReason:C({finishReason:null==(s=h.incomplete_details)?void 0:s.reason,hasToolCalls:y.length>0}),toolCalls:y.length>0?y:void 0,reasoning:v?v.map(e=>({type:"text",text:e.text})):void 0,usage:{promptTokens:h.usage.input_tokens,completionTokens:h.usage.output_tokens},rawCall:{rawPrompt:void 0,rawSettings:{}},rawResponse:{headers:m,body:f},request:{body:JSON.stringify(d)},response:{id:h.id,timestamp:new Date(1e3*h.created_at),modelId:h.model},providerMetadata:{openai:{responseId:h.id,cachedPromptTokens:null!=(o=null==(i=h.usage.input_tokens_details)?void 0:i.cached_tokens)?o:null,reasoningTokens:null!=(c=null==(l=h.usage.output_tokens_details)?void 0:l.reasoning_tokens)?c:null}},warnings:p}}async doStream(e){let{args:t,warnings:r}=this.getArgs(e),{responseHeaders:s,value:n}=await (0,a.GU)({url:this.config.url({path:"/responses",modelId:this.modelId}),headers:(0,a.m2)(this.config.headers(),e.headers),body:{...t,stream:!0},failedResponseHandler:u,successfulResponseHandler:(0,a.Ds)(F),abortSignal:e.abortSignal,fetch:this.config.fetch}),i=this,o="unknown",l=NaN,c=NaN,d=null,p=null,m=null,h={},f=!1;return{stream:n.pipeThrough(new TransformStream({transform(e,t){var r,s,n,u,g,y,v,_,b;if(!e.success){o="error",t.enqueue({type:"error",error:e.error});return}let w=e.value;if("response.output_item.added"===w.type)"function_call"===w.item.type&&(h[w.output_index]={toolName:w.item.name,toolCallId:w.item.call_id},t.enqueue({type:"tool-call-delta",toolCallType:"function",toolCallId:w.item.call_id,toolName:w.item.name,argsTextDelta:w.item.arguments}));else if("response.function_call_arguments.delta"===w.type){let e=h[w.output_index];null!=e&&t.enqueue({type:"tool-call-delta",toolCallType:"function",toolCallId:e.toolCallId,toolName:e.toolName,argsTextDelta:w.delta})}else{"response.created"===w.type?(m=w.response.id,t.enqueue({type:"response-metadata",id:w.response.id,timestamp:new Date(1e3*w.response.created_at),modelId:w.response.model})):"response.output_text.delta"===w.type?t.enqueue({type:"text-delta",textDelta:w.delta}):"response.reasoning_summary_text.delta"===w.type?t.enqueue({type:"reasoning",textDelta:w.delta}):"response.output_item.done"===w.type&&"function_call"===w.item.type?(h[w.output_index]=void 0,f=!0,t.enqueue({type:"tool-call",toolCallType:"function",toolCallId:w.item.call_id,toolName:w.item.name,args:w.item.arguments})):"response.completed"===(b=w).type||"response.incomplete"===b.type?(o=C({finishReason:null==(r=w.response.incomplete_details)?void 0:r.reason,hasToolCalls:f}),l=w.response.usage.input_tokens,c=w.response.usage.output_tokens,d=null!=(n=null==(s=w.response.usage.input_tokens_details)?void 0:s.cached_tokens)?n:d,p=null!=(g=null==(u=w.response.usage.output_tokens_details)?void 0:u.reasoning_tokens)?g:p):"response.output_text.annotation.added"===w.type&&t.enqueue({type:"source",source:{sourceType:"url",id:null!=(_=null==(v=(y=i.config).generateId)?void 0:v.call(y))?_:(0,a.$C)(),url:w.annotation.url,title:w.annotation.title}})}},flush(e){e.enqueue({type:"finish",finishReason:o,usage:{promptTokens:l,completionTokens:c},...(null!=d||null!=p)&&{providerMetadata:{openai:{responseId:m,cachedPromptTokens:d,reasoningTokens:p}}}})}})),rawCall:{rawPrompt:void 0,rawSettings:{}},rawResponse:{headers:s},request:{body:JSON.stringify(t)},warnings:r}}},R=n.z.object({input_tokens:n.z.number(),input_tokens_details:n.z.object({cached_tokens:n.z.number().nullish()}).nullish(),output_tokens:n.z.number(),output_tokens_details:n.z.object({reasoning_tokens:n.z.number().nullish()}).nullish()}),O=n.z.object({type:n.z.literal("response.output_text.delta"),delta:n.z.string()}),P=n.z.object({type:n.z.enum(["response.completed","response.incomplete"]),response:n.z.object({incomplete_details:n.z.object({reason:n.z.string()}).nullish(),usage:R})}),M=n.z.object({type:n.z.literal("response.created"),response:n.z.object({id:n.z.string(),created_at:n.z.number(),model:n.z.string()})}),$=n.z.object({type:n.z.literal("response.output_item.done"),output_index:n.z.number(),item:n.z.discriminatedUnion("type",[n.z.object({type:n.z.literal("message")}),n.z.object({type:n.z.literal("function_call"),id:n.z.string(),call_id:n.z.string(),name:n.z.string(),arguments:n.z.string(),status:n.z.literal("completed")})])}),Z=n.z.object({type:n.z.literal("response.function_call_arguments.delta"),item_id:n.z.string(),output_index:n.z.number(),delta:n.z.string()}),D=n.z.object({type:n.z.literal("response.output_item.added"),output_index:n.z.number(),item:n.z.discriminatedUnion("type",[n.z.object({type:n.z.literal("message")}),n.z.object({type:n.z.literal("function_call"),id:n.z.string(),call_id:n.z.string(),name:n.z.string(),arguments:n.z.string()})])}),q=n.z.object({type:n.z.literal("response.output_text.annotation.added"),annotation:n.z.object({type:n.z.literal("url_citation"),url:n.z.string(),title:n.z.string()})}),L=n.z.object({type:n.z.literal("response.reasoning_summary_text.delta"),item_id:n.z.string(),output_index:n.z.number(),summary_index:n.z.number(),delta:n.z.string()}),F=n.z.union([O,P,M,$,Z,D,q,L,n.z.object({type:n.z.string()}).passthrough()]),U=n.z.object({metadata:n.z.any().nullish(),parallelToolCalls:n.z.boolean().nullish(),previousResponseId:n.z.string().nullish(),store:n.z.boolean().nullish(),user:n.z.string().nullish(),reasoningEffort:n.z.string().nullish(),strictSchemas:n.z.boolean().nullish(),instructions:n.z.string().nullish(),reasoningSummary:n.z.string().nullish()}),V=n.z.object({}),B={webSearchPreview:function({searchContextSize:e,userLocation:t}={}){return{type:"provider-defined",id:"openai.web_search_preview",args:{searchContextSize:e,userLocation:t},parameters:V}}},J=n.z.object({instructions:n.z.string().nullish(),speed:n.z.number().min(.25).max(4).default(1).nullish()}),Y=class{constructor(e,t){this.modelId=e,this.config=t,this.specificationVersion="v1"}get provider(){return this.config.provider}getArgs({text:e,voice:t="alloy",outputFormat:r="mp3",speed:s,instructions:n,providerOptions:i}){let o=[],l=(0,a.xI)({provider:"openai",providerOptions:i,schema:J}),u={model:this.modelId,input:e,voice:t,response_format:"mp3",speed:s,instructions:n};if(r&&(["mp3","opus","aac","flac","wav","pcm"].includes(r)?u.response_format=r:o.push({type:"unsupported-setting",setting:"outputFormat",details:`Unsupported output format: ${r}. Using mp3 instead.`})),l){let e={};for(let t in e){let r=e[t];void 0!==r&&(u[t]=r)}}return{requestBody:u,warnings:o}}async doGenerate(e){var t,r,s;let n=null!=(s=null==(r=null==(t=this.config._internal)?void 0:t.currentDate)?void 0:r.call(t))?s:new Date,{requestBody:i,warnings:o}=this.getArgs(e),{value:l,responseHeaders:c,rawValue:d}=await (0,a.GU)({url:this.config.url({path:"/audio/speech",modelId:this.modelId}),headers:(0,a.m2)(this.config.headers(),e.headers),body:i,failedResponseHandler:u,successfulResponseHandler:(0,a.HD)(),abortSignal:e.abortSignal,fetch:this.config.fetch});return{audio:l,warnings:o,request:{body:JSON.stringify(i)},response:{timestamp:n,modelId:this.modelId,headers:c,body:d}}}},K=function(e={}){var t,r,s;let n=null!=(t=(0,a.ae)(e.baseURL))?t:"https://api.openai.com/v1",i=null!=(r=e.compatibility)?r:"compatible",o=null!=(s=e.name)?s:"openai",l=()=>({Authorization:`Bearer ${(0,a.WL)({apiKey:e.apiKey,environmentVariableName:"OPENAI_API_KEY",description:"OpenAI"})}`,"OpenAI-Organization":e.organization,"OpenAI-Project":e.project,...e.headers}),u=(t,r={})=>new d(t,r,{provider:`${o}.chat`,url:({path:e})=>`${n}${e}`,headers:l,compatibility:i,fetch:e.fetch}),c=(t,r={})=>new v(t,r,{provider:`${o}.completion`,url:({path:e})=>`${n}${e}`,headers:l,compatibility:i,fetch:e.fetch}),p=(t,r={})=>new w(t,r,{provider:`${o}.embedding`,url:({path:e})=>`${n}${e}`,headers:l,fetch:e.fetch}),m=(t,r={})=>new I(t,r,{provider:`${o}.image`,url:({path:e})=>`${n}${e}`,headers:l,fetch:e.fetch}),h=t=>new A(t,{provider:`${o}.transcription`,url:({path:e})=>`${n}${e}`,headers:l,fetch:e.fetch}),f=t=>new Y(t,{provider:`${o}.speech`,url:({path:e})=>`${n}${e}`,headers:l,fetch:e.fetch}),g=(e,t)=>{if(new.target)throw Error("The OpenAI model function cannot be called with the new keyword.");return"gpt-3.5-turbo-instruct"===e?c(e,t):u(e,t)},y=function(e,t){return g(e,t)};return y.languageModel=g,y.chat=u,y.completion=c,y.responses=t=>new N(t,{provider:`${o}.responses`,url:({path:e})=>`${n}${e}`,headers:l,fetch:e.fetch}),y.embedding=p,y.textEmbedding=p,y.textEmbeddingModel=p,y.image=m,y.imageModel=m,y.transcription=h,y.transcriptionModel=h,y.speech=f,y.speechModel=f,y.tools=B,y}({compatibility:"strict"})},92229:(e,t,r)=>{r.d(t,{$C:()=>d,Ds:()=>S,Fv:()=>f,GU:()=>x,HD:()=>E,N8:()=>v,NR:()=>o,S:()=>k,WL:()=>h,Z9:()=>C,ZZ:()=>y,ae:()=>R,cV:()=>z,cb:()=>l,eu:()=>g,hK:()=>c,m2:()=>i,n_:()=>N,sl:()=>I,u1:()=>p,v0:()=>_,xI:()=>b,zf:()=>m});var a=r(49715),s=r(48444),n=r(57057);function i(...e){return e.reduce((e,t)=>({...e,...null!=t?t:{}}),{})}function o(e){return new ReadableStream({async pull(t){try{let{value:r,done:a}=await e.next();a?t.close():t.enqueue(r)}catch(e){t.error(e)}},cancel(){}})}async function l(e){return null==e?Promise.resolve():new Promise(t=>setTimeout(t,e))}function u(e){let t={};return e.headers.forEach((e,r)=>{t[r]=e}),t}var c=({prefix:e,size:t=16,alphabet:r="0123456789ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz",separator:n="-"}={})=>{let i=(0,s.d)(r,t);if(null==e)return i;if(r.includes(n))throw new a.Di({argument:"separator",message:`The separator "${n}" must not be part of the alphabet "${r}".`});return t=>`${e}${n}${i(t)}`},d=c();function p(e){return null==e?"unknown error":"string"==typeof e?e:e instanceof Error?e.message:JSON.stringify(e)}function m(e){return e instanceof Error&&("AbortError"===e.name||"TimeoutError"===e.name)}function h({apiKey:e,environmentVariableName:t,apiKeyParameterName:r="apiKey",description:s}){if("string"==typeof e)return e;if(null!=e)throw new a.Kq({message:`${s} API key must be a string.`});if("undefined"==typeof process)throw new a.Kq({message:`${s} API key is missing. Pass it using the '${r}' parameter. Environment variables is not supported in this environment.`});if(null==(e=process.env[t]))throw new a.Kq({message:`${s} API key is missing. Pass it using the '${r}' parameter or the ${t} environment variable.`});if("string"!=typeof e)throw new a.Kq({message:`${s} API key must be a string. The value of the ${t} environment variable is not a string.`});return e}function f({settingValue:e,environmentVariableName:t,settingName:r,description:s}){if("string"==typeof e)return e;if(null!=e)throw new a.Vf({message:`${s} setting must be a string.`});if("undefined"==typeof process)throw new a.Vf({message:`${s} setting is missing. Pass it using the '${r}' parameter. Environment variables is not supported in this environment.`});if(null==(e=process.env[t]))throw new a.Vf({message:`${s} setting is missing. Pass it using the '${r}' parameter or the ${t} environment variable.`});if("string"!=typeof e)throw new a.Vf({message:`${s} setting must be a string. The value of the ${t} environment variable is not a string.`});return e}var g=Symbol.for("vercel.ai.validator");function y({value:e,schema:t}){var r;let s="object"==typeof t&&null!==t&&g in t&&!0===t[g]&&"validate"in t?t:(r=t,{[g]:!0,validate:e=>{let t=r.safeParse(e);return t.success?{success:!0,value:t.data}:{success:!1,error:t.error}}});try{if(null==s.validate)return{success:!0,value:e};let t=s.validate(e);if(t.success)return t;return{success:!1,error:a.iM.wrap({value:e,cause:t.error})}}catch(t){return{success:!1,error:a.iM.wrap({value:e,cause:t})}}}function v({text:e,schema:t}){try{let r=n.parse(e);if(null==t)return{success:!0,value:r,rawValue:r};let a=y({value:r,schema:t});return a.success?{...a,rawValue:r}:a}catch(t){return{success:!1,error:a.u6.isInstance(t)?t:new a.u6({text:e,cause:t})}}}function _(e){try{return n.parse(e),!0}catch(e){return!1}}function b({provider:e,providerOptions:t,schema:r}){if((null==t?void 0:t[e])==null)return;let s=y({value:t[e],schema:r});if(!s.success)throw new a.Di({argument:"providerOptions",message:`invalid ${e} provider options`,cause:s.error});return s.value}var w=()=>globalThis.fetch,x=async({url:e,headers:t,body:r,failedResponseHandler:a,successfulResponseHandler:s,abortSignal:n,fetch:i})=>T({url:e,headers:{"Content-Type":"application/json",...t},body:{content:JSON.stringify(r),values:r},failedResponseHandler:a,successfulResponseHandler:s,abortSignal:n,fetch:i}),k=async({url:e,headers:t,formData:r,failedResponseHandler:a,successfulResponseHandler:s,abortSignal:n,fetch:i})=>T({url:e,headers:t,body:{content:r,values:Object.fromEntries(r.entries())},failedResponseHandler:a,successfulResponseHandler:s,abortSignal:n,fetch:i}),T=async({url:e,headers:t={},body:r,successfulResponseHandler:s,failedResponseHandler:n,abortSignal:i,fetch:o=w()})=>{try{var l;let c=await o(e,{method:"POST",headers:(l=t,Object.fromEntries(Object.entries(l).filter(([e,t])=>null!=t))),body:r.content,signal:i}),d=u(c);if(!c.ok){let t;try{t=await n({response:c,url:e,requestBodyValues:r.values})}catch(t){if(m(t)||a.hL.isInstance(t))throw t;throw new a.hL({message:"Failed to process error response",cause:t,statusCode:c.status,url:e,responseHeaders:d,requestBodyValues:r.values})}throw t.value}try{return await s({response:c,url:e,requestBodyValues:r.values})}catch(t){if(t instanceof Error&&(m(t)||a.hL.isInstance(t)))throw t;throw new a.hL({message:"Failed to process successful response",cause:t,statusCode:c.status,url:e,responseHeaders:d,requestBodyValues:r.values})}}catch(t){if(m(t))throw t;if(t instanceof TypeError&&"fetch failed"===t.message){let s=t.cause;if(null!=s)throw new a.hL({message:`Cannot connect to API: ${s.message}`,cause:s,url:e,requestBodyValues:r.values,isRetryable:!0})}throw t}},I=({errorSchema:e,errorToMessage:t,isRetryable:r})=>async({response:s,url:i,requestBodyValues:o})=>{let l=await s.text(),c=u(s);if(""===l.trim())return{responseHeaders:c,value:new a.hL({message:s.statusText,url:i,requestBodyValues:o,statusCode:s.status,responseHeaders:c,responseBody:l,isRetryable:null==r?void 0:r(s)})};try{let u=function({text:e,schema:t}){try{let r=n.parse(e);if(null==t)return r;return function({value:e,schema:t}){let r=y({value:e,schema:t});if(!r.success)throw a.iM.wrap({value:e,cause:r.error});return r.value}({value:r,schema:t})}catch(t){if(a.u6.isInstance(t)||a.iM.isInstance(t))throw t;throw new a.u6({text:e,cause:t})}}({text:l,schema:e});return{responseHeaders:c,value:new a.hL({message:t(u),url:i,requestBodyValues:o,statusCode:s.status,responseHeaders:c,responseBody:l,data:u,isRetryable:null==r?void 0:r(s,u)})}}catch(e){return{responseHeaders:c,value:new a.hL({message:s.statusText,url:i,requestBodyValues:o,statusCode:s.status,responseHeaders:c,responseBody:l,isRetryable:null==r?void 0:r(s)})}}},S=e=>async({response:t})=>{let r=u(t);if(null==t.body)throw new a.Tt({});return{responseHeaders:r,value:t.body.pipeThrough(new TextDecoderStream).pipeThrough(function(){let e,t,r,a="",s=[];function n(e,t){if(""===e){i(t);return}if(e.startsWith(":"))return;let r=e.indexOf(":");if(-1===r){o(e,"");return}let a=e.slice(0,r),s=r+1;o(a,s<e.length&&" "===e[s]?e.slice(s+1):e.slice(s))}function i(a){s.length>0&&(a.enqueue({event:e,data:s.join("\n"),id:t,retry:r}),s=[],e=void 0,r=void 0)}function o(a,n){switch(a){case"event":e=n;break;case"data":s.push(n);break;case"id":t=n;break;case"retry":let i=parseInt(n,10);isNaN(i)||(r=i)}}return new TransformStream({transform(e,t){let{lines:r,incompleteLine:s}=function(e,t){let r=[],a=e;for(let e=0;e<t.length;){let s=t[e++];"\n"===s?(r.push(a),a=""):"\r"===s?(r.push(a),a="","\n"===t[e]&&e++):a+=s}return{lines:r,incompleteLine:a}}(a,e);a=s;for(let e=0;e<r.length;e++)n(r[e],t)},flush(e){n(a,e),i(e)}})}()).pipeThrough(new TransformStream({transform({data:t},r){"[DONE]"!==t&&r.enqueue(v({text:t,schema:e}))}}))}},z=e=>async({response:t,url:r,requestBodyValues:s})=>{let n=await t.text(),i=v({text:n,schema:e}),o=u(t);if(!i.success)throw new a.hL({message:"Invalid JSON response",cause:i.error,statusCode:t.status,responseHeaders:o,responseBody:n,url:r,requestBodyValues:s});return{responseHeaders:o,value:i.value,rawValue:i.rawValue}},E=()=>async({response:e,url:t,requestBodyValues:r})=>{let s=u(e);if(!e.body)throw new a.hL({message:"Response body is empty",url:t,requestBodyValues:r,statusCode:e.status,responseHeaders:s,responseBody:void 0});try{let t=await e.arrayBuffer();return{responseHeaders:s,value:new Uint8Array(t)}}catch(n){throw new a.hL({message:"Failed to read response as array buffer",url:t,requestBodyValues:r,statusCode:e.status,responseHeaders:s,responseBody:void 0,cause:n})}},{btoa:A,atob:j}=globalThis;function C(e){let t=j(e.replace(/-/g,"+").replace(/_/g,"/"));return Uint8Array.from(t,e=>e.codePointAt(0))}function N(e){let t="";for(let r=0;r<e.length;r++)t+=String.fromCodePoint(e[r]);return A(t)}function R(e){return null==e?void 0:e.replace(/\/$/,"")}}};