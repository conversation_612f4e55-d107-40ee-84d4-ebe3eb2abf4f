exports.id=3351,exports.ids=[3351],exports.modules={3764:(e,t,r)=>{"use strict";r.d(t,{p:()=>i});var a=r(2569);r(29068);var n=r(75673);function i({className:e,type:t,...r}){return(0,a.jsx)("input",{type:t,"data-slot":"input",className:(0,n.cn)("file:text-foreground placeholder:text-muted-foreground selection:bg-primary selection:text-primary-foreground dark:bg-input/30 border-input flex h-9 w-full min-w-0 rounded-md border bg-transparent px-3 py-1 text-base shadow-xs transition-[color,box-shadow] outline-none file:inline-flex file:h-7 file:border-0 file:bg-transparent file:text-sm file:font-medium disabled:pointer-events-none disabled:cursor-not-allowed disabled:opacity-50 md:text-sm","focus-visible:border-ring focus-visible:ring-ring/50 focus-visible:ring-[3px]","aria-invalid:ring-destructive/20 dark:aria-invalid:ring-destructive/40 aria-invalid:border-destructive",e),...r})}},18586:(e,t,r)=>{"use strict";r.d(t,{BT:()=>l,Wu:()=>c,X9:()=>d,ZB:()=>o,Zp:()=>i,aR:()=>s,wL:()=>u});var a=r(34411);r(37582);var n=r(25039);function i({className:e,...t}){return(0,a.jsx)("div",{"data-slot":"card",className:(0,n.cn)("bg-card text-card-foreground flex flex-col gap-6 rounded-2xl border border-border/50 p-6 shadow-lg backdrop-blur-sm transition-all duration-200 hover:shadow-xl hover:scale-[1.01]",e),...t})}function s({className:e,...t}){return(0,a.jsx)("div",{"data-slot":"card-header",className:(0,n.cn)("@container/card-header grid auto-rows-min grid-rows-[auto_auto] items-start gap-2 has-data-[slot=card-action]:grid-cols-[1fr_auto] [.border-b]:pb-6",e),...t})}function o({className:e,...t}){return(0,a.jsx)("div",{"data-slot":"card-title",className:(0,n.cn)("leading-none font-semibold",e),...t})}function l({className:e,...t}){return(0,a.jsx)("div",{"data-slot":"card-description",className:(0,n.cn)("text-muted-foreground text-sm",e),...t})}function d({className:e,...t}){return(0,a.jsx)("div",{"data-slot":"card-action",className:(0,n.cn)("col-start-2 row-span-2 row-start-1 self-start justify-self-end",e),...t})}function c({className:e,...t}){return(0,a.jsx)("div",{"data-slot":"card-content",className:(0,n.cn)("px-6",e),...t})}function u({className:e,...t}){return(0,a.jsx)("div",{"data-slot":"card-footer",className:(0,n.cn)("flex items-center px-6 [.border-t]:pt-6",e),...t})}},21218:(e,t,r)=>{"use strict";r.d(t,{SidebarInset:()=>n,SidebarProvider:()=>i,SidebarTrigger:()=>s});var a=r(29037);(0,a.registerClientReference)(function(){throw Error("Attempted to call Sidebar() from the server but Sidebar is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"/Users/<USER>/My-Project/fluxkrea_test/components/ui/sidebar.tsx","Sidebar"),(0,a.registerClientReference)(function(){throw Error("Attempted to call SidebarContent() from the server but SidebarContent is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"/Users/<USER>/My-Project/fluxkrea_test/components/ui/sidebar.tsx","SidebarContent"),(0,a.registerClientReference)(function(){throw Error("Attempted to call SidebarFooter() from the server but SidebarFooter is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"/Users/<USER>/My-Project/fluxkrea_test/components/ui/sidebar.tsx","SidebarFooter"),(0,a.registerClientReference)(function(){throw Error("Attempted to call SidebarGroup() from the server but SidebarGroup is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"/Users/<USER>/My-Project/fluxkrea_test/components/ui/sidebar.tsx","SidebarGroup"),(0,a.registerClientReference)(function(){throw Error("Attempted to call SidebarGroupAction() from the server but SidebarGroupAction is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"/Users/<USER>/My-Project/fluxkrea_test/components/ui/sidebar.tsx","SidebarGroupAction"),(0,a.registerClientReference)(function(){throw Error("Attempted to call SidebarGroupContent() from the server but SidebarGroupContent is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"/Users/<USER>/My-Project/fluxkrea_test/components/ui/sidebar.tsx","SidebarGroupContent"),(0,a.registerClientReference)(function(){throw Error("Attempted to call SidebarGroupLabel() from the server but SidebarGroupLabel is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"/Users/<USER>/My-Project/fluxkrea_test/components/ui/sidebar.tsx","SidebarGroupLabel"),(0,a.registerClientReference)(function(){throw Error("Attempted to call SidebarHeader() from the server but SidebarHeader is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"/Users/<USER>/My-Project/fluxkrea_test/components/ui/sidebar.tsx","SidebarHeader"),(0,a.registerClientReference)(function(){throw Error("Attempted to call SidebarInput() from the server but SidebarInput is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"/Users/<USER>/My-Project/fluxkrea_test/components/ui/sidebar.tsx","SidebarInput");let n=(0,a.registerClientReference)(function(){throw Error("Attempted to call SidebarInset() from the server but SidebarInset is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"/Users/<USER>/My-Project/fluxkrea_test/components/ui/sidebar.tsx","SidebarInset");(0,a.registerClientReference)(function(){throw Error("Attempted to call SidebarMenu() from the server but SidebarMenu is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"/Users/<USER>/My-Project/fluxkrea_test/components/ui/sidebar.tsx","SidebarMenu"),(0,a.registerClientReference)(function(){throw Error("Attempted to call SidebarMenuAction() from the server but SidebarMenuAction is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"/Users/<USER>/My-Project/fluxkrea_test/components/ui/sidebar.tsx","SidebarMenuAction"),(0,a.registerClientReference)(function(){throw Error("Attempted to call SidebarMenuBadge() from the server but SidebarMenuBadge is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"/Users/<USER>/My-Project/fluxkrea_test/components/ui/sidebar.tsx","SidebarMenuBadge"),(0,a.registerClientReference)(function(){throw Error("Attempted to call SidebarMenuButton() from the server but SidebarMenuButton is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"/Users/<USER>/My-Project/fluxkrea_test/components/ui/sidebar.tsx","SidebarMenuButton"),(0,a.registerClientReference)(function(){throw Error("Attempted to call SidebarMenuItem() from the server but SidebarMenuItem is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"/Users/<USER>/My-Project/fluxkrea_test/components/ui/sidebar.tsx","SidebarMenuItem"),(0,a.registerClientReference)(function(){throw Error("Attempted to call SidebarMenuSkeleton() from the server but SidebarMenuSkeleton is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"/Users/<USER>/My-Project/fluxkrea_test/components/ui/sidebar.tsx","SidebarMenuSkeleton"),(0,a.registerClientReference)(function(){throw Error("Attempted to call SidebarMenuSub() from the server but SidebarMenuSub is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"/Users/<USER>/My-Project/fluxkrea_test/components/ui/sidebar.tsx","SidebarMenuSub"),(0,a.registerClientReference)(function(){throw Error("Attempted to call SidebarMenuSubButton() from the server but SidebarMenuSubButton is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"/Users/<USER>/My-Project/fluxkrea_test/components/ui/sidebar.tsx","SidebarMenuSubButton"),(0,a.registerClientReference)(function(){throw Error("Attempted to call SidebarMenuSubItem() from the server but SidebarMenuSubItem is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"/Users/<USER>/My-Project/fluxkrea_test/components/ui/sidebar.tsx","SidebarMenuSubItem");let i=(0,a.registerClientReference)(function(){throw Error("Attempted to call SidebarProvider() from the server but SidebarProvider is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"/Users/<USER>/My-Project/fluxkrea_test/components/ui/sidebar.tsx","SidebarProvider");(0,a.registerClientReference)(function(){throw Error("Attempted to call SidebarRail() from the server but SidebarRail is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"/Users/<USER>/My-Project/fluxkrea_test/components/ui/sidebar.tsx","SidebarRail"),(0,a.registerClientReference)(function(){throw Error("Attempted to call SidebarSeparator() from the server but SidebarSeparator is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"/Users/<USER>/My-Project/fluxkrea_test/components/ui/sidebar.tsx","SidebarSeparator");let s=(0,a.registerClientReference)(function(){throw Error("Attempted to call SidebarTrigger() from the server but SidebarTrigger is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"/Users/<USER>/My-Project/fluxkrea_test/components/ui/sidebar.tsx","SidebarTrigger");(0,a.registerClientReference)(function(){throw Error("Attempted to call useSidebar() from the server but useSidebar is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"/Users/<USER>/My-Project/fluxkrea_test/components/ui/sidebar.tsx","useSidebar")},27419:(e,t,r)=>{"use strict";r.d(t,{A:()=>h});var a=r(34411),n=r(37582),i=r(91357),s=r(25039);function o({...e}){return(0,a.jsx)("nav",{"aria-label":"breadcrumb","data-slot":"breadcrumb",...e})}function l({className:e,...t}){return(0,a.jsx)("ol",{"data-slot":"breadcrumb-list",className:(0,s.cn)("text-muted-foreground flex flex-wrap items-center gap-1.5 text-sm break-words sm:gap-2.5",e),...t})}function d({className:e,...t}){return(0,a.jsx)("li",{"data-slot":"breadcrumb-item",className:(0,s.cn)("inline-flex items-center gap-1.5",e),...t})}function c({className:e,...t}){return(0,a.jsx)("span",{"data-slot":"breadcrumb-page",role:"link","aria-disabled":"true","aria-current":"page",className:(0,s.cn)("text-foreground font-normal",e),...t})}function u({children:e,className:t,...r}){return(0,a.jsx)("li",{"data-slot":"breadcrumb-separator",role:"presentation","aria-hidden":"true",className:(0,s.cn)("[&>svg]:size-3.5",t),...r,children:e??(0,a.jsx)(i.A,{})})}var p=r(10050),m=r.n(p),f=r(70439),b=r(21218);function h({crumb:e}){return(0,a.jsx)("header",{className:"flex border-b border-border py-3 shrink-0 items-center gap-2 transition-[width,height] ease-linear group-has-[[data-collapsible=icon]]/sidebar-wrapper:h-12",children:(0,a.jsxs)("div",{className:"flex items-center gap-2 px-4",children:[(0,a.jsx)(b.SidebarTrigger,{className:"-ml-1"}),e&&e.items&&e.items.length>0&&(0,a.jsxs)(a.Fragment,{children:[(0,a.jsx)(f.Separator,{orientation:"vertical",className:"mr-2 h-4"}),(0,a.jsx)(o,{children:(0,a.jsx)(l,{children:e.items.map((e,t)=>e.is_active?(0,a.jsx)(d,{children:(0,a.jsx)(c,{children:e.title})},t):(0,a.jsxs)(n.Fragment,{children:[(0,a.jsx)(d,{className:"hidden md:block",children:(0,a.jsx)(m(),{href:e.url||"",className:"hover:text-primary",children:e.title})}),(0,a.jsx)(u,{className:"hidden md:block"})]},t))})})]})]})})}},28432:(e,t,r)=>{"use strict";r.d(t,{j2:()=>f,Y9:()=>u});var a=r(62364),n=r(36157),i=r(72648),s=r(39201),o=r(4961),l=r(29452),d=r(60736);let c=[];c.push((0,n.A)({id:"google-one-tap",name:"google-one-tap",credentials:{credential:{type:"text"}},async authorize(e,t){let r=e.credential,a=await fetch("https://oauth2.googleapis.com/tokeninfo?id_token="+r);if(!a.ok)return console.log("Failed to verify token"),null;let n=await a.json();if(!n)return console.log("invalid payload from token"),null;let{email:i,sub:s,given_name:o,family_name:l,email_verified:d,picture:c}=n;return i?{id:s,name:[o,l].join(" "),email:i,image:c,emailVerified:d?new Date:null}:(console.log("invalid email in payload"),null)}})),process.env.AUTH_GOOGLE_ID&&process.env.AUTH_GOOGLE_SECRET&&c.push((0,i.A)({clientId:process.env.AUTH_GOOGLE_ID,clientSecret:process.env.AUTH_GOOGLE_SECRET,authorization:{params:{prompt:"consent",access_type:"offline",response_type:"code"}}})),c.map(e=>{if("function"!=typeof e)return{id:e.id,name:e.name};{let t=e();return{id:t.id,name:t.name}}}).filter(e=>"google-one-tap"!==e.id);let{handlers:u,signIn:p,signOut:m,auth:f}=(0,a.Ay)({providers:c,pages:{signIn:"/auth/signin"},callbacks:{signIn:async({user:e,account:t,profile:r,email:a,credentials:n})=>!0,redirect:async({url:e,baseUrl:t})=>e.startsWith("/")?`${t}${e}`:new URL(e).origin===t?e:t,session:async({session:e,token:t,user:r})=>(t&&t.user&&t.user&&(e.user=t.user),e),async jwt({token:e,user:t,account:r}){try{if(t&&t.email&&r){let a={uuid:(0,l.YJ)(),email:t.email,nickname:t.name||"",avatar_url:t.image||"",signin_type:r.type,signin_provider:r.provider,signin_openid:r.providerAccountId,created_at:(0,o.iq)(),signin_ip:await (0,s.T)()};try{let t=await (0,d.$c)(a);e.user={uuid:t.uuid,email:t.email,nickname:t.nickname,avatar_url:t.avatar_url,created_at:t.created_at}}catch(e){console.error("save user failed:",e)}}return e}catch(t){return console.error("jwt callback error:",t),e}}}})},39201:(e,t,r)=>{"use strict";r.d(t,{T:()=>i});var a=r(72784);r(97468);var n=r(62225);async function i(){let e=await (0,n.b3)();return e.get("cf-connecting-ip")||e.get("x-real-ip")||(e.get("x-forwarded-for")||"127.0.0.1").split(",")[0]}(0,r(78564).D)([i]),(0,a.A)(i,"00880263a376180560adeff1fdd2511a7543726d4a",null)},58770:(e,t,r)=>{"use strict";r.r(t),r.d(t,{default:()=>c});var a=r(34411),n=r(21218),i=r(90605);function s({children:e,sidebar:t}){return(0,a.jsxs)(n.SidebarProvider,{style:{"--sidebar-width":"calc(var(--spacing) * 60)","--header-height":"calc(var(--spacing) * 12)"},children:[(0,a.jsx)(i.default,{variant:"inset",sidebar:t}),(0,a.jsx)(n.SidebarInset,{children:e})]})}var o=r(92132),l=r(60736),d=r(79502);async function c({children:e}){let t=await (0,l.ug)();t&&t.email||(0,d.redirect)("/auth/signin");let r=process.env.ADMIN_EMAILS?.split(",");return r?.includes(t?.email)?(0,a.jsx)(s,{sidebar:{brand:{title:"ShipAny",logo:{src:"/logo.png",alt:"ShipAny"},url:"/admin"},nav:{items:[{title:"Dashboard",url:"/admin",icon:"RiDashboardLine"}]},library:{title:"Menu",items:[{title:"Users",url:"/admin/users",icon:"RiUserLine"},{title:"Orders",icon:"RiOrderPlayLine",url:"/admin/orders"},{title:"Posts",url:"/admin/posts",icon:"RiArticleLine"},{title:"Feedbacks",url:"/admin/feedbacks",icon:"RiMessage2Line"}]},bottomNav:{items:[{title:"Documents",url:"https://docs.shipany.ai",target:"_blank",icon:"RiFileTextLine"},{title:"Blocks",url:"https://shipany.ai/blocks",target:"_blank",icon:"RiDashboardLine"},{title:"Showcases",url:"https://shipany.ai/showcase",target:"_blank",icon:"RiAppsLine"}]},social:{items:[{title:"Home",url:"/",target:"_blank",icon:"RiHomeLine"},{title:"Github",url:"https://github.com/shipanyai/shipany-template-one",target:"_blank",icon:"RiGithubLine"},{title:"Discord",url:"https://discord.gg/HQNnrzjZQS",target:"_blank",icon:"RiDiscordLine"},{title:"X",url:"https://x.com/shipanyai",target:"_blank",icon:"RiTwitterLine"}]},account:{items:[{title:"Home",url:"/",icon:"RiHomeLine",target:"_blank"},{title:"Recharge",url:"/pricing",icon:"RiMoneyDollarBoxLine",target:"_blank"}]}},children:e}):(0,a.jsx)(o.A,{message:"No access"})}},60736:(e,t,r)=>{"use strict";r.d(t,{$c:()=>d,TG:()=>c,qo:()=>p,ug:()=>m});var a=r(87830),n=r(35050),i=r(28432),s=r(4961),o=r(78054),l=r(62225);async function d(e){try{let t=await (0,n.Gs)(e.email);return t?(e.id=t.id,e.uuid=t.uuid,e.created_at=t.created_at):(await (0,n.HW)(e),await (0,a.d_)({user_uuid:e.uuid||"",trans_type:a.H3.NewUser,credits:a.rN.NewUserGet,expired_at:(0,s.MI)()})),e}catch(e){throw console.log("save user failed: ",e),e}}async function c(){let e="",t=await u();if(t&&t.startsWith("sk-"))return await (0,o.zz)(t)||"";let r=await (0,i.j2)();return r&&r.user&&r.user.uuid&&(e=r.user.uuid),e}async function u(){let e=(await (0,l.b3)()).get("Authorization");return e?e.replace("Bearer ",""):""}async function p(){let e="",t=await (0,i.j2)();return t&&t.user&&t.user.email&&(e=t.user.email),e}async function m(){let e=await c();if(e)return await (0,n.pX)(e)}},61716:(e,t,r)=>{"use strict";r.d(t,{Bx:()=>j,Yv:()=>k,CG:()=>S,Cn:()=>N,rQ:()=>A,jj:()=>_,Gh:()=>C,SidebarInset:()=>y,wZ:()=>M,Uj:()=>U,FX:()=>I,SidebarProvider:()=>v,SidebarTrigger:()=>w,cL:()=>g});var a=r(2569),n=r(29068),i=r(70166),s=r(75958),o=r(31619),l=r(71765),d=r(75673),c=r(92622);r(3764),r(80081);var u=r(84601),p=r(22069);function m({delayDuration:e=0,...t}){return(0,a.jsx)(p.Kq,{"data-slot":"tooltip-provider",delayDuration:e,...t})}function f({...e}){return(0,a.jsx)(m,{children:(0,a.jsx)(p.bL,{"data-slot":"tooltip",...e})})}function b({...e}){return(0,a.jsx)(p.l9,{"data-slot":"tooltip-trigger",...e})}function h({className:e,sideOffset:t=0,children:r,...n}){return(0,a.jsx)(p.ZL,{children:(0,a.jsxs)(p.UC,{"data-slot":"tooltip-content",sideOffset:t,className:(0,d.cn)("bg-primary text-primary-foreground animate-in fade-in-0 zoom-in-95 data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=closed]:zoom-out-95 data-[side=bottom]:slide-in-from-top-2 data-[side=left]:slide-in-from-right-2 data-[side=right]:slide-in-from-left-2 data-[side=top]:slide-in-from-bottom-2 z-50 w-fit origin-(--radix-tooltip-content-transform-origin) rounded-md px-3 py-1.5 text-xs text-balance",e),...n,children:[r,(0,a.jsx)(p.i3,{className:"bg-primary fill-primary z-50 size-2.5 translate-y-[calc(-50%_-_2px)] rotate-45 rounded-[2px]"})]})})}let x=n.createContext(null);function g(){let e=n.useContext(x);if(!e)throw Error("useSidebar must be used within a SidebarProvider.");return e}function v({defaultOpen:e=!0,open:t,onOpenChange:r,className:i,style:s,children:o,...c}){let u=(0,l.a)(),[p,f]=n.useState(!1),[b,h]=n.useState(e),g=t??b,v=n.useCallback(e=>{let t="function"==typeof e?e(g):e;r?r(t):h(t),document.cookie=`sidebar_state=${t}; path=/; max-age=604800`},[r,g]),j=n.useCallback(()=>u?f(e=>!e):v(e=>!e),[u,v,f]);n.useEffect(()=>{let e=e=>{"b"===e.key&&(e.metaKey||e.ctrlKey)&&(e.preventDefault(),j())};return window.addEventListener("keydown",e),()=>window.removeEventListener("keydown",e)},[j]);let w=g?"expanded":"collapsed",y=n.useMemo(()=>({state:w,open:g,setOpen:v,isMobile:u,openMobile:p,setOpenMobile:f,toggleSidebar:j}),[w,g,v,u,p,f,j]);return(0,a.jsx)(x.Provider,{value:y,children:(0,a.jsx)(m,{delayDuration:0,children:(0,a.jsx)("div",{"data-slot":"sidebar-wrapper",style:{"--sidebar-width":"16rem","--sidebar-width-icon":"3rem",...s},className:(0,d.cn)("group/sidebar-wrapper has-data-[variant=inset]:bg-sidebar flex min-h-svh w-full",i),...c,children:o})})})}function j({side:e="left",variant:t="sidebar",collapsible:r="offcanvas",className:n,children:i,...s}){let{isMobile:o,state:l,openMobile:c,setOpenMobile:p}=g();return"none"===r?(0,a.jsx)("div",{"data-slot":"sidebar",className:(0,d.cn)("bg-sidebar text-sidebar-foreground flex h-full w-(--sidebar-width) flex-col",n),...s,children:i}):o?(0,a.jsx)(u.cj,{open:c,onOpenChange:p,...s,children:(0,a.jsxs)(u.h,{"data-sidebar":"sidebar","data-slot":"sidebar","data-mobile":"true",className:"bg-sidebar text-sidebar-foreground w-(--sidebar-width) p-0 [&>button]:hidden",style:{"--sidebar-width":"18rem"},side:e,children:[(0,a.jsxs)(u.Fm,{className:"sr-only",children:[(0,a.jsx)(u.qp,{children:"Sidebar"}),(0,a.jsx)(u.Qs,{children:"Displays the mobile sidebar."})]}),(0,a.jsx)("div",{className:"flex h-full w-full flex-col",children:i})]})}):(0,a.jsxs)("div",{className:"group peer text-sidebar-foreground hidden md:block","data-state":l,"data-collapsible":"collapsed"===l?r:"","data-variant":t,"data-side":e,"data-slot":"sidebar",children:[(0,a.jsx)("div",{"data-slot":"sidebar-gap",className:(0,d.cn)("relative w-(--sidebar-width) bg-transparent transition-[width] duration-200 ease-linear","group-data-[collapsible=offcanvas]:w-0","group-data-[side=right]:rotate-180","floating"===t||"inset"===t?"group-data-[collapsible=icon]:w-[calc(var(--sidebar-width-icon)+(--spacing(4)))]":"group-data-[collapsible=icon]:w-(--sidebar-width-icon)")}),(0,a.jsx)("div",{"data-slot":"sidebar-container",className:(0,d.cn)("fixed inset-y-0 z-10 hidden h-svh w-(--sidebar-width) transition-[left,right,width] duration-200 ease-linear md:flex","left"===e?"left-0 group-data-[collapsible=offcanvas]:left-[calc(var(--sidebar-width)*-1)]":"right-0 group-data-[collapsible=offcanvas]:right-[calc(var(--sidebar-width)*-1)]","floating"===t||"inset"===t?"p-2 group-data-[collapsible=icon]:w-[calc(var(--sidebar-width-icon)+(--spacing(4))+2px)]":"group-data-[collapsible=icon]:w-(--sidebar-width-icon) group-data-[side=left]:border-r group-data-[side=right]:border-l",n),...s,children:(0,a.jsx)("div",{"data-sidebar":"sidebar","data-slot":"sidebar-inner",className:"bg-sidebar group-data-[variant=floating]:border-sidebar-border flex h-full w-full flex-col group-data-[variant=floating]:rounded-lg group-data-[variant=floating]:border group-data-[variant=floating]:shadow-sm",children:i})})]})}function w({className:e,onClick:t,...r}){let{toggleSidebar:n}=g();return(0,a.jsxs)(c.$,{"data-sidebar":"trigger","data-slot":"sidebar-trigger",variant:"ghost",size:"icon",className:(0,d.cn)("size-7",e),onClick:e=>{t?.(e),n()},...r,children:[(0,a.jsx)(o.A,{}),(0,a.jsx)("span",{className:"sr-only",children:"Toggle Sidebar"})]})}function y({className:e,...t}){return(0,a.jsx)("main",{"data-slot":"sidebar-inset",className:(0,d.cn)("bg-background relative flex w-full flex-1 flex-col","md:peer-data-[variant=inset]:m-2 md:peer-data-[variant=inset]:ml-0 md:peer-data-[variant=inset]:rounded-xl md:peer-data-[variant=inset]:shadow-sm md:peer-data-[variant=inset]:peer-data-[state=collapsed]:ml-2",e),...t})}function C({className:e,...t}){return(0,a.jsx)("div",{"data-slot":"sidebar-header","data-sidebar":"header",className:(0,d.cn)("flex flex-col gap-2 p-2",e),...t})}function S({className:e,...t}){return(0,a.jsx)("div",{"data-slot":"sidebar-footer","data-sidebar":"footer",className:(0,d.cn)("flex flex-col gap-2 p-2",e),...t})}function k({className:e,...t}){return(0,a.jsx)("div",{"data-slot":"sidebar-content","data-sidebar":"content",className:(0,d.cn)("flex min-h-0 flex-1 flex-col gap-2 overflow-auto group-data-[collapsible=icon]:overflow-hidden",e),...t})}function N({className:e,...t}){return(0,a.jsx)("div",{"data-slot":"sidebar-group","data-sidebar":"group",className:(0,d.cn)("relative flex w-full min-w-0 flex-col p-2",e),...t})}function _({className:e,asChild:t=!1,...r}){let n=t?i.DX:"div";return(0,a.jsx)(n,{"data-slot":"sidebar-group-label","data-sidebar":"group-label",className:(0,d.cn)("text-sidebar-foreground/70 ring-sidebar-ring flex h-8 shrink-0 items-center rounded-md px-2 text-xs font-medium outline-hidden transition-[margin,opacity] duration-200 ease-linear focus-visible:ring-2 [&>svg]:size-4 [&>svg]:shrink-0","group-data-[collapsible=icon]:-mt-8 group-data-[collapsible=icon]:opacity-0",e),...r})}function A({className:e,...t}){return(0,a.jsx)("div",{"data-slot":"sidebar-group-content","data-sidebar":"group-content",className:(0,d.cn)("w-full text-sm",e),...t})}function M({className:e,...t}){return(0,a.jsx)("ul",{"data-slot":"sidebar-menu","data-sidebar":"menu",className:(0,d.cn)("flex w-full min-w-0 flex-col gap-1",e),...t})}function I({className:e,...t}){return(0,a.jsx)("li",{"data-slot":"sidebar-menu-item","data-sidebar":"menu-item",className:(0,d.cn)("group/menu-item relative",e),...t})}let R=(0,s.F)("peer/menu-button flex w-full items-center gap-2 overflow-hidden rounded-md p-2 text-left text-sm outline-hidden ring-sidebar-ring transition-[width,height,padding] hover:bg-sidebar-accent hover:text-sidebar-accent-foreground focus-visible:ring-2 active:bg-sidebar-accent active:text-sidebar-accent-foreground disabled:pointer-events-none disabled:opacity-50 group-has-data-[sidebar=menu-action]/menu-item:pr-8 aria-disabled:pointer-events-none aria-disabled:opacity-50 data-[active=true]:bg-sidebar-accent data-[active=true]:font-medium data-[active=true]:text-sidebar-accent-foreground data-[state=open]:hover:bg-sidebar-accent data-[state=open]:hover:text-sidebar-accent-foreground group-data-[collapsible=icon]:size-8! group-data-[collapsible=icon]:p-2! [&>span:last-child]:truncate [&>svg]:size-4 [&>svg]:shrink-0",{variants:{variant:{default:"hover:bg-sidebar-accent hover:text-sidebar-accent-foreground",outline:"bg-background shadow-[0_0_0_1px_hsl(var(--sidebar-border))] hover:bg-sidebar-accent hover:text-sidebar-accent-foreground hover:shadow-[0_0_0_1px_hsl(var(--sidebar-accent))]"},size:{default:"h-8 text-sm",sm:"h-7 text-xs",lg:"h-12 text-sm group-data-[collapsible=icon]:p-0!"}},defaultVariants:{variant:"default",size:"default"}});function U({asChild:e=!1,isActive:t=!1,variant:r="default",size:n="default",tooltip:s,className:o,...l}){let c=e?i.DX:"button",{isMobile:u,state:p}=g(),m=(0,a.jsx)(c,{"data-slot":"sidebar-menu-button","data-sidebar":"menu-button","data-size":n,"data-active":t,className:(0,d.cn)(R({variant:r,size:n}),o),...l});return s?("string"==typeof s&&(s={children:s}),(0,a.jsxs)(f,{children:[(0,a.jsx)(b,{asChild:!0,children:m}),(0,a.jsx)(h,{side:"right",align:"center",hidden:"collapsed"!==p||u,...s})]})):m}},64949:(e,t,r)=>{Promise.resolve().then(r.bind(r,91695)),Promise.resolve().then(r.bind(r,61716))},70439:(e,t,r)=>{"use strict";r.d(t,{Separator:()=>a});let a=(0,r(29037).registerClientReference)(function(){throw Error("Attempted to call Separator() from the server but Separator is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"/Users/<USER>/My-Project/fluxkrea_test/components/ui/separator.tsx","Separator")},71765:(e,t,r)=>{"use strict";r.d(t,{a:()=>n});var a=r(29068);function n(){let[e,t]=a.useState(void 0);return a.useEffect(()=>{let e=window.matchMedia("(max-width: 767px)"),r=()=>{t(window.innerWidth<768)};return e.addEventListener("change",r),t(window.innerWidth<768),()=>e.removeEventListener("change",r)},[]),!!e}},74677:(e,t,r)=>{Promise.resolve().then(r.bind(r,90605)),Promise.resolve().then(r.bind(r,21218))},78054:(e,t,r)=>{"use strict";r.d(t,{N1:()=>i,ai:()=>n,ox:()=>s,zz:()=>o});var a=r(4995),n=function(e){return e.Created="created",e.Deleted="deleted",e}({});async function i(e){let t=(0,a.A)(),{data:r,error:n}=await t.from("apikeys").insert(e);if(n)throw n;return r}async function s(e,t=1,r=50){let n=(t-1)*r,i=(0,a.A)(),{data:o,error:l}=await i.from("apikeys").select("*").eq("user_uuid",e).neq("status","deleted").order("created_at",{ascending:!1}).range(n,n+r-1);if(!l)return o}async function o(e){let t=(0,a.A)(),{data:r,error:n}=await t.from("apikeys").select("user_uuid").eq("api_key",e).eq("status","created").limit(1).single();if(!n)return r?.user_uuid}},80081:(e,t,r)=>{"use strict";r.d(t,{Separator:()=>s});var a=r(2569);r(29068);var n=r(36393),i=r(75673);function s({className:e,orientation:t="horizontal",decorative:r=!0,...s}){return(0,a.jsx)(n.b,{"data-slot":"separator-root",decorative:r,orientation:t,className:(0,i.cn)("bg-border shrink-0 data-[orientation=horizontal]:h-px data-[orientation=horizontal]:w-full data-[orientation=vertical]:h-full data-[orientation=vertical]:w-px",e),...s})}},90605:(e,t,r)=>{"use strict";r.d(t,{default:()=>a});let a=(0,r(29037).registerClientReference)(function(){throw Error("Attempted to call the default export of \"/Users/<USER>/My-Project/fluxkrea_test/components/dashboard/sidebar/index.tsx\" from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"/Users/<USER>/My-Project/fluxkrea_test/components/dashboard/sidebar/index.tsx","default")},91695:(e,t,r)=>{"use strict";r.d(t,{default:()=>S});var a=r(2569),n=r(29068),i=r(61716),s=r(37967),o=r(92227),l=r(14059);function d({nav:e}){let t=(0,l.usePathname)();return(0,a.jsx)(i.Cn,{children:(0,a.jsx)(i.rQ,{className:"flex flex-col gap-2 mt-4",children:(0,a.jsx)(i.wZ,{children:e?.items?.map(e=>a.jsx(i.FX,{children:a.jsx(i.Uj,{tooltip:e.title,className:`${e.is_active||t.endsWith(e.url)?"bg-primary text-primary-foreground hover:bg-primary/90 hover:text-primary-foreground active:bg-primary/90 active:text-primary-foreground min-w-8 duration-200 ease-linear":""}`,children:e.url?a.jsxs(s.N_,{href:e.url,target:e.target,className:"w-full flex items-center gap-2 cursor-pointer",children:[e.icon&&a.jsx(o.default,{name:e.icon}),a.jsx("span",{children:e.title})]}):a.jsxs(a.Fragment,{children:[e.icon&&a.jsx(o.default,{name:e.icon}),a.jsx("span",{children:e.title})]})})},e.title))})})})}var c=r(13748),u=r(78701),p=r(88006),m=r(16448),f=r(78341),b=r(92622),h=r(54487),x=r(6637),g=r(73054);function v({account:e}){let t=(0,g.c3)(),{user:r,setShowSignModal:l}=(0,x.U)(),{isMobile:d,open:c}=(0,i.cL)();return(0,a.jsx)(a.Fragment,{children:r?(0,a.jsxs)(i.wZ,{className:"gap-4",children:[!c&&(0,a.jsx)(i.FX,{children:(0,a.jsx)(i.Uj,{className:"cursor-pointer",asChild:!0,children:(0,a.jsx)(i.SidebarTrigger,{})})}),(0,a.jsx)(i.FX,{children:(0,a.jsxs)(f.rI,{children:[(0,a.jsx)(f.ty,{asChild:!0,children:(0,a.jsxs)(i.Uj,{size:"lg",className:"data-[state=open]:bg-sidebar-accent data-[state=open]:text-sidebar-accent-foreground",children:[(0,a.jsxs)(u.Avatar,{className:"h-8 w-8 rounded-lg",children:[(0,a.jsx)(u.AvatarImage,{src:r?.avatar_url,alt:r?.nickname}),(0,a.jsx)(u.q,{className:"rounded-lg",children:"CN"})]}),(0,a.jsxs)("div",{className:"grid flex-1 text-left text-sm leading-tight",children:[(0,a.jsx)("span",{className:"truncate font-semibold",children:r.nickname}),(0,a.jsx)("span",{className:"truncate text-xs",children:r.email})]}),(0,a.jsx)(p.A,{className:"ml-auto size-4"})]})}),(0,a.jsxs)(f.SQ,{className:"w-(--radix-dropdown-menu-trigger-width) min-w-56 rounded-lg bg-background",side:d?"bottom":"right",align:"end",sideOffset:4,children:[(0,a.jsx)(f.lp,{className:"p-0 font-normal",children:(0,a.jsxs)("div",{className:"flex items-center gap-2 px-1 py-1.5 text-left text-sm",children:[(0,a.jsxs)(u.Avatar,{className:"h-8 w-8 rounded-lg",children:[(0,a.jsx)(u.AvatarImage,{src:r?.avatar_url,alt:r?.nickname}),(0,a.jsx)(u.q,{className:"rounded-lg",children:"CN"})]}),(0,a.jsxs)("div",{className:"grid flex-1 text-left text-sm leading-tight",children:[(0,a.jsx)("span",{className:"truncate font-semibold",children:r?.nickname}),(0,a.jsx)("span",{className:"truncate text-xs",children:r?.email})]})]})}),(0,a.jsx)(f.mB,{}),(0,a.jsxs)(f.I,{children:[e?.items?.map((e,t)=>a.jsxs(n.Fragment,{children:[a.jsx(f._2,{className:"cursor-pointer",children:a.jsxs(s.N_,{href:e.url,target:e.target,className:"w-full flex items-center gap-2",children:[e.icon&&a.jsx(o.default,{name:e.icon}),e.title]})}),a.jsx(f.mB,{})]},t)),(0,a.jsxs)(f._2,{className:"cursor-pointer",onClick:()=>(0,h.CI)(),children:[(0,a.jsx)(m.A,{}),t("user.sign_out")]})]})]})]})})]}):(0,a.jsx)(a.Fragment,{children:c?(0,a.jsx)("div",{className:"flex justify-center items-center h-full px-4 py-4",children:(0,a.jsx)(b.$,{className:"w-full",onClick:()=>l(!0),children:t("user.sign_in")})}):(0,a.jsx)(i.wZ,{children:(0,a.jsx)(i.FX,{children:(0,a.jsx)(i.Uj,{className:"cursor-pointer",asChild:!0,children:(0,a.jsx)(i.SidebarTrigger,{})})})})})})}function j({social:e}){let{open:t}=(0,i.cL)();return(0,a.jsx)(a.Fragment,{children:t?(0,a.jsx)("div",{className:"w-full flex items-center justify-center mx-auto gap-x-4 px-4 py-4 border-t border-gray-200",children:e?.items?.map((e,t)=>a.jsx("div",{className:"cursor-pointer hover:text-primary",children:a.jsx(s.N_,{href:e.url,target:e.target||"_self",className:"cursor-pointer",children:e.icon&&a.jsx(o.default,{name:e.icon,className:"text-xl"})})},t))}):null})}var w=r(69751);function y({library:e}){let{isMobile:t}=(0,i.cL)(),r=(0,s.a8)();return(0,a.jsxs)(i.Cn,{className:"group-data-[collapsible=icon]:hidden",children:[(0,a.jsx)(i.jj,{children:e.title}),(0,a.jsxs)(i.wZ,{children:[e.items?.map((e,t)=>a.jsxs(i.FX,{children:[a.jsx(i.Uj,{tooltip:e.title,className:`${e.is_active||r.endsWith(e.url)?"bg-primary text-primary-foreground hover:bg-primary/90 hover:text-primary-foreground active:bg-primary/90 active:text-primary-foreground min-w-8 duration-200 ease-linear":""}`,children:a.jsxs(s.N_,{href:e.url||"",target:e.target,className:"w-full flex items-center gap-2 cursor-pointer",children:[e.icon&&a.jsx(o.default,{name:e.icon}),a.jsx("span",{children:e.title})]})}),a.jsx(f.rI,{})]},t)),e.more&&(0,a.jsx)(i.FX,{children:(0,a.jsxs)(i.Uj,{className:"text-sidebar-foreground/70",children:[(0,a.jsx)(w.A,{className:"text-sidebar-foreground/70"}),(0,a.jsx)("span",{children:e.more.title})]})})]})]})}function C({nav:e,...t}){return(0,a.jsx)(i.Cn,{...t,children:(0,a.jsx)(i.rQ,{children:(0,a.jsx)(i.wZ,{children:e.items?.map((e,t)=>a.jsx(i.FX,{children:a.jsx(i.Uj,{asChild:!0,children:a.jsxs(s.N_,{href:e.url,target:e.target,children:[e.icon&&a.jsx(o.default,{name:e.icon}),a.jsx("span",{children:e.title})]})})},t))})})})}function S({sidebar:e,...t}){return(0,a.jsxs)(i.Bx,{collapsible:"offcanvas",...t,children:[(0,a.jsx)(i.Gh,{children:(0,a.jsx)(i.wZ,{children:(0,a.jsx)(i.FX,{children:(0,a.jsx)(i.Uj,{asChild:!0,className:"data-[slot=sidebar-menu-button]:!p-1.5",children:(0,a.jsxs)(s.N_,{href:e.brand?.url,className:"flex items-center gap-2",children:[e.brand?.logo&&(0,a.jsx)(c.default,{src:e.brand?.logo?.src,alt:e.brand?.title,width:28,height:28,className:"rounded-full"}),(0,a.jsx)("span",{className:"text-base font-semibold",children:e.brand?.title})]})})})})}),(0,a.jsxs)(i.Yv,{children:[e.nav&&(0,a.jsx)(d,{nav:e.nav}),e.library&&(0,a.jsx)(y,{library:e.library}),e.bottomNav&&(0,a.jsx)(C,{nav:e.bottomNav,className:"mt-auto"})]}),(0,a.jsxs)(i.CG,{children:[(0,a.jsx)(v,{account:e.account}),e?.social&&(0,a.jsx)(j,{social:e.social})]})]})}},92132:(e,t,r)=>{"use strict";r.d(t,{A:()=>n});var a=r(34411);function n({message:e}){return(0,a.jsx)("div",{className:"flex flex-col items-center justify-center w-full h-[50vh]",children:(0,a.jsx)("p",{children:e})})}}};