"use strict";exports.id=6329,exports.ids=[6329],exports.modules={43637:(t,e,r)=>{r.d(e,{_:()=>tU});var a,s,i,o,n,l,u,c=r(91043);class AbortSignal{constructor(){this.onabort=null,this._aborted=!1,Object.defineProperty(this,"_aborted",{value:!1,writable:!0})}get aborted(){return this._aborted}abort(){this._aborted=!0,this.onabort&&(this.onabort(this),this.onabort=null)}}class h{constructor(){this.signal=new AbortSignal}abort(){this.signal.abort()}}let d=async t=>{let e=t?.Bucket||"";if("string"==typeof t.Bucket&&(t.Bucket=e.replace(/#/g,encodeURIComponent("#")).replace(/\?/g,encodeURIComponent("?"))),g(e)){if(!0===t.ForcePathStyle)throw Error("Path-style addressing cannot be used with ARN buckets")}else y(e)&&(-1===e.indexOf(".")||String(t.Endpoint).startsWith("http:"))&&e.toLowerCase()===e&&!(e.length<3)||(t.ForcePathStyle=!0);return t.DisableMultiRegionAccessPoints&&(t.disableMultiRegionAccessPoints=!0,t.DisableMRAP=!0),t},p=/^[a-z0-9][a-z0-9\.\-]{1,61}[a-z0-9]$/,f=/(\d+\.){3}\d+/,m=/\.\./,y=t=>p.test(t)&&!f.test(t)&&!m.test(t),g=t=>{let[e,r,a,,,s]=t.split(":"),i="arn"===e&&t.split(":").length>=6,o=!!(i&&r&&a&&s);if(i&&!o)throw Error(`Invalid ARN: ${t} was an invalid ARN.`);return o},b=(t,e,r)=>{let a=async()=>{let a=r[t]??r[e];return"function"==typeof a?a():a};return"credentialScope"===t||"CredentialScope"===e?async()=>{let t="function"==typeof r.credentials?await r.credentials():r.credentials;return t?.credentialScope??t?.CredentialScope}:"accountId"===t||"AccountId"===e?async()=>{let t="function"==typeof r.credentials?await r.credentials():r.credentials;return t?.accountId??t?.AccountId}:"endpoint"===t||"endpoint"===e?async()=>{let t=await a();if(t&&"object"==typeof t){if("url"in t)return t.url.href;if("hostname"in t){let{protocol:e,hostname:r,port:a,path:s}=t;return`${e}//${r}${a?":"+a:""}${s}`}}return t}:a};class S extends Error{constructor(t,e=!0){let r;let a=!0;"boolean"==typeof e?(r=void 0,a=e):null!=e&&"object"==typeof e&&(r=e.logger,a=e.tryNextLink??!0),super(t),this.name="ProviderError",this.tryNextLink=a,Object.setPrototypeOf(this,S.prototype),r?.debug?.(`@smithy/property-provider ${a?"->":"(!)"} ${t}`)}static from(t,e=!0){return Object.assign(new this(t.message,e),t)}}class C extends S{constructor(t,e=!0){super(t,e),this.name="CredentialsProviderError",Object.setPrototypeOf(this,C.prototype)}}let E=(...t)=>async()=>{let e;if(0===t.length)throw new S("No providers in chain");for(let r of t)try{return await r()}catch(t){if(e=t,t?.tryNextLink)continue;throw t}throw e},w=t=>()=>Promise.resolve(t),P=(t,e,r)=>{let a,s,i;let o=!1,n=async()=>{s||(s=t());try{a=await s,i=!0,o=!1}finally{s=void 0}return a};return void 0===e?async t=>((!i||t?.forceRefresh)&&(a=await n()),a):async t=>((!i||t?.forceRefresh)&&(a=await n()),o||(r&&!r(a)?o=!0:e(a)&&await n()),a)};function _(t){try{let e=new Set(Array.from(t.match(/([A-Z_]){3,}/g)??[]));return e.delete("CONFIG"),e.delete("CONFIG_PREFIX_SEPARATOR"),e.delete("ENV"),[...e].join(", ")}catch(e){return t}}let A=(t,e)=>async()=>{try{let r=t(process.env,e);if(void 0===r)throw Error();return r}catch(r){throw new C(r.message||`Not found in ENV: ${_(t.toString())}`,{logger:e?.logger})}};var v=r(21820),U=r(33873);let R={},I=()=>process&&process.geteuid?`${process.geteuid()}`:"DEFAULT",k=()=>{let{HOME:t,USERPROFILE:e,HOMEPATH:r,HOMEDRIVE:a=`C:${U.sep}`}=process.env;if(t)return t;if(e)return e;if(r)return`${a}${r}`;let s=I();return R[s]||(R[s]=(0,v.homedir)()),R[s]},O=t=>t.profile||process.env.AWS_PROFILE||"default";r(55511);var B=r(29021);let{readFile:$}=B.promises;(function(t){t.HEADER="header",t.QUERY="query"})(a||(a={})),function(t){t.HEADER="header",t.QUERY="query"}(s||(s={})),function(t){t.HTTP="http",t.HTTPS="https"}(i||(i={})),function(t){t.MD5="md5",t.CRC32="crc32",t.CRC32C="crc32c",t.SHA1="sha1",t.SHA256="sha256"}(o||(o={})),function(t){t[t.HEADER=0]="HEADER",t[t.TRAILER=1]="TRAILER"}(n||(n={})),function(t){t.PROFILE="profile",t.SSO_SESSION="sso-session",t.SERVICES="services"}(l||(l={})),function(t){t.HTTP_0_9="http/0.9",t.HTTP_1_0="http/1.0",t.TDS_8_0="tds/8.0"}(u||(u={}));let N=t=>Object.entries(t).filter(([t])=>{let e=t.indexOf(D);return -1!==e&&Object.values(l).includes(t.substring(0,e))}).reduce((t,[e,r])=>{let a=e.indexOf(D);return t[e.substring(0,a)===l.PROFILE?e.substring(a+1):e]=r,t},{...t.default&&{default:t.default}}),T=()=>process.env.AWS_CONFIG_FILE||(0,U.join)(k(),".aws","config"),M=()=>process.env.AWS_SHARED_CREDENTIALS_FILE||(0,U.join)(k(),".aws","credentials"),j=/^([\w-]+)\s(["'])?([\w-@\+\.%:/]+)\2$/,L=["__proto__","profile __proto__"],x=t=>{let e,r;let a={};for(let s of t.split(/\r?\n/)){let t=s.split(/(^|\s)[;#]/)[0].trim();if("["===t[0]&&"]"===t[t.length-1]){e=void 0,r=void 0;let a=t.substring(1,t.length-1),s=j.exec(a);if(s){let[,t,,r]=s;Object.values(l).includes(t)&&(e=[t,r].join(D))}else e=a;if(L.includes(a))throw Error(`Found invalid profile name "${a}"`)}else if(e){let i=t.indexOf("=");if(![0,-1].includes(i)){let[o,n]=[t.substring(0,i).trim(),t.substring(i+1).trim()];if(""===n)r=o;else{r&&s.trimStart()===s&&(r=void 0),a[e]=a[e]||{};let t=r?[r,o].join(D):o;a[e][t]=n}}}}return a},{readFile:z}=B.promises,F={},H=(t,e)=>((!F[t]||e?.ignoreCache)&&(F[t]=z(t,"utf8")),F[t]),q=()=>({}),D=".",K=async(t={})=>{let{filepath:e=M(),configFilepath:r=T()}=t,a=k(),s=e;e.startsWith("~/")&&(s=(0,U.join)(a,e.slice(2)));let i=r;r.startsWith("~/")&&(i=(0,U.join)(a,r.slice(2)));let o=await Promise.all([H(i,{ignoreCache:t.ignoreCache}).then(x).then(N).catch(q),H(s,{ignoreCache:t.ignoreCache}).then(x).catch(q)]);return{configFile:o[0],credentialsFile:o[1]}},W=(t,{preferredFile:e="config",...r}={})=>async()=>{let a=O(r),{configFile:s,credentialsFile:i}=await K(r),o=i[a]||{},n=s[a]||{},l="config"===e?{...o,...n}:{...n,...o};try{let r="config"===e?s:i,a=t(l,r);if(void 0===a)throw Error();return a}catch(e){throw new C(e.message||`Not found in config files w/ profile [${a}]: ${_(t.toString())}`,{logger:r.logger})}},J=t=>"function"==typeof t,V=t=>J(t)?async()=>await t():w(t),Z=({environmentVariableSelector:t,configFileSelector:e,default:r},a={})=>{let{signingName:s,logger:i}=a;return P(E(A(t,{signingName:s,logger:i}),W(e,a),V(r)))},X="AWS_ENDPOINT_URL",Y="endpoint_url",G=t=>({environmentVariableSelector:e=>{let r=e[[X,...t.split(" ").map(t=>t.toUpperCase())].join("_")];if(r)return r;let a=e[X];if(a)return a},configFileSelector:(e,r)=>{if(r&&e.services){let a=r[["services",e.services].join(D)];if(a){let e=a[[t.split(" ").map(t=>t.toLowerCase()).join("_"),Y].join(D)];if(e)return e}}let a=e[Y];if(a)return a},default:void 0}),Q=async t=>Z(G(t??""))(),tt=t=>{let e;if("string"==typeof t)return tt(new URL(t));let{hostname:r,pathname:a,port:s,protocol:i,search:o}=t;return o&&(e=function(t){let e={};if(t=t.replace(/^\?/,""))for(let r of t.split("&")){let[t,a=null]=r.split("=");t=decodeURIComponent(t),a&&(a=decodeURIComponent(a)),t in e?Array.isArray(e[t])?e[t].push(a):e[t]=[e[t],a]:e[t]=a}return e}(o)),{hostname:r,port:s?parseInt(s):void 0,protocol:i,path:a,query:e}},te=t=>"object"==typeof t?"url"in t?tt(t.url):t:tt(t),tr=async(t,e,r,a)=>{if(!r.endpoint){let t;(t=r.serviceConfiguredEndpoint?await r.serviceConfiguredEndpoint():await Q(r.serviceId))&&(r.endpoint=()=>Promise.resolve(te(t)))}let s=await ta(t,e,r);if("function"!=typeof r.endpointProvider)throw Error("config.endpointProvider is not set.");return r.endpointProvider(s,a)},ta=async(t,e,r)=>{let a={},s=e?.getEndpointParameterInstructions?.()||{};for(let[e,i]of Object.entries(s))switch(i.type){case"staticContextParams":a[e]=i.value;break;case"contextParams":a[e]=t[i.name];break;case"clientContextParams":case"builtInParams":a[e]=await b(i.name,e,r)();break;case"operationContextParams":a[e]=i.get(t);break;default:throw Error("Unrecognized endpoint parameter instruction: "+JSON.stringify(i))}return 0===Object.keys(s).length&&Object.assign(a,r),"s3"===String(r.serviceId).toLowerCase()&&await d(a),a},ts={name:"serializerMiddleware"};ts.name;let ti=t=>"function"==typeof ArrayBuffer&&t instanceof ArrayBuffer||"[object ArrayBuffer]"===Object.prototype.toString.call(t);var to=r(79428);let tn=(t,e=0,r=t.byteLength-e)=>{if(!ti(t))throw TypeError(`The "input" argument must be ArrayBuffer. Received type ${typeof t} (${t})`);return to.Buffer.from(t,e,r)},tl=(t,e)=>{if("string"!=typeof t)throw TypeError(`The "input" argument must be of type string. Received type ${typeof t} (${t})`);return e?to.Buffer.from(t,e):to.Buffer.from(t)},tu=t=>{let e=tl(t,"utf8");return new Uint8Array(e.buffer,e.byteOffset,e.byteLength/Uint8Array.BYTES_PER_ELEMENT)};var tc=r(27910);tc.Duplex,"function"==typeof ReadableStream&&ReadableStream,r(57075),tc.Writable,r(81630),r(55591),r(73496),Symbol.iterator,tc.Writable;let th={},td={};for(let t=0;t<256;t++){let e=t.toString(16).toLowerCase();1===e.length&&(e=`0${e}`),th[t]=e,td[e]=t}function tp(t){return encodeURIComponent(t).replace(/[!'()*]/g,function(t){return"%"+t.charCodeAt(0).toString(16).toUpperCase()})}class tf{constructor(t,e=new Map){this.namespace=t,this.schemas=e}static for(t){return tf.registries.has(t)||tf.registries.set(t,new tf(t)),tf.registries.get(t)}register(t,e){let r=this.normalizeShapeId(t);tf.for(this.getNamespace(t)).schemas.set(r,e)}getSchema(t){let e=this.normalizeShapeId(t);if(!this.schemas.has(e))throw Error(`@smithy/core/schema - schema not found for ${e}`);return this.schemas.get(e)}getBaseException(){for(let[t,e]of this.schemas.entries())if(t.startsWith("smithy.ts.sdk.synthetic.")&&t.endsWith("ServiceException"))return e}find(t){return[...this.schemas.values()].find(t)}destroy(){tf.registries.delete(this.namespace),this.schemas.clear()}normalizeShapeId(t){return t.includes("#")?t:this.namespace+"#"+t}getNamespace(t){return this.normalizeShapeId(t).split("#")[0]}}tf.registries=new Map;class tm{constructor(t,e){this.name=t,this.traits=e}}class ty extends tm{constructor(t,e,r,a){super(t,e),this.name=t,this.traits=e,this.memberNames=r,this.memberList=a,this.members={};for(let t=0;t<r.length;++t)this.members[r[t]]=Array.isArray(a[t])?a[t]:[a[t],0]}}console.warn;let tg=function(t){return Object.assign(new String(t),{deserializeJSON:()=>JSON.parse(String(t)),toString:()=>String(t),toJSON:()=>String(t)})};tg.from=t=>t&&"object"==typeof t&&(t instanceof tg||"deserializeJSON"in t)?t:"string"==typeof t||Object.getPrototypeOf(t)===String.prototype?tg(String(t)):tg(JSON.stringify(t)),tg.fromObject=tg.from,Symbol.hasInstance;ts.name;class tb extends Error{constructor(t){super(t.message),Object.setPrototypeOf(this,Object.getPrototypeOf(this).constructor.prototype),this.name=t.name,this.$fault=t.$fault,this.$metadata=t.$metadata}static isInstance(t){return!!t&&(tb.prototype.isPrototypeOf(t)||!!t.$fault&&!!t.$metadata&&("client"===t.$fault||"server"===t.$fault))}static[Symbol.hasInstance](t){return!!t&&(this===tb?tb.isInstance(t):!!tb.isInstance(t)&&(t.name&&this.name?this.prototype.isPrototypeOf(t)||t.name===this.name:this.prototype.isPrototypeOf(t)))}}var tS=r(94735);let tC={lstatSync:()=>{},runtime:"node",lstatSync:B.lstatSync},tE=t=>{if(null==t)return 0;if("string"==typeof t)return to.Buffer.byteLength(t);if("number"==typeof t.byteLength)return t.byteLength;if("number"==typeof t.length)return t.length;if("number"==typeof t.size)return t.size;if("string"==typeof t.path)try{return tC.lstatSync(t.path).size}catch(t){}};async function*tw(t,e,r){let a=1,s={chunks:[],length:0};for await(let i of r(t))for(s.chunks.push(i),s.length+=i.byteLength;s.length>e;){let t=s.chunks.length>1?to.Buffer.concat(s.chunks):s.chunks[0];yield{partNumber:a,data:t.subarray(0,e)},s.chunks=[t.subarray(e)],s.length=s.chunks[0].byteLength,a+=1}yield{partNumber:a,data:1!==s.chunks.length?to.Buffer.concat(s.chunks):s.chunks[0],lastPart:!0}}async function*tP(t,e){let r=1,a=0,s=e;for(;s<t.byteLength;)yield{partNumber:r,data:t.subarray(a,s)},r+=1,s=(a=s)+e;yield{partNumber:r,data:t.subarray(a),lastPart:!0}}async function*t_(t){for await(let e of t)to.Buffer.isBuffer(e)||e instanceof Uint8Array?yield e:yield to.Buffer.from(e)}async function*tA(t){let e=t.getReader();try{for(;;){let{done:t,value:r}=await e.read();if(t)return;to.Buffer.isBuffer(r)||r instanceof Uint8Array?yield r:yield to.Buffer.from(r)}}catch(t){throw t}finally{e.releaseLock()}}let tv=(t,e)=>{if(t instanceof Uint8Array)return tP(t,e);if(t instanceof tc.Readable)return tw(t,e,t_);if(t instanceof String||"string"==typeof t)return tP(to.Buffer.from(t),e);if("function"==typeof t.stream)return tw(t.stream(),e,tA);if(t instanceof ReadableStream)return tw(t,e,tA);throw Error("Body Data is unsupported format, expected data to be one of: string | Uint8Array | Buffer | Readable | ReadableStream | Blob;.")};class tU extends tS.EventEmitter{static MIN_PART_SIZE=5242880;MAX_PARTS=1e4;queueSize=4;partSize=tU.MIN_PART_SIZE;leavePartsOnError=!1;tags=[];client;params;totalBytes;bytesUploadedSoFar;abortController;concurrentUploaders=[];createMultiPartPromise;abortMultipartUploadCommand=null;uploadedParts=[];uploadEnqueuedPartsCount=0;uploadId;uploadEvent;isMultiPart=!0;singleUploadResult;sent=!1;constructor(t){super(),this.queueSize=t.queueSize||this.queueSize,this.partSize=t.partSize||this.partSize,this.leavePartsOnError=t.leavePartsOnError||this.leavePartsOnError,this.tags=t.tags||this.tags,this.client=t.client,this.params=t.params,this.__validateInput(),this.totalBytes=tE(this.params.Body),this.bytesUploadedSoFar=0,this.abortController=t.abortController??new h}async abort(){this.abortController.abort()}async done(){if(this.sent)throw Error("@aws-sdk/lib-storage: this instance of Upload has already executed .done(). Create a new instance.");return this.sent=!0,await Promise.race([this.__doMultipartUpload(),this.__abortTimeout(this.abortController.signal)])}on(t,e){return this.uploadEvent=t,super.on(t,e)}async __uploadUsingPut(t){this.isMultiPart=!1;let e={...this.params,Body:t.data},r=this.client.config,a=r.requestHandler,s=a instanceof tS.EventEmitter?a:null,i=e=>{this.bytesUploadedSoFar=e.loaded,this.totalBytes=e.total,this.__notifyProgress({loaded:this.bytesUploadedSoFar,total:this.totalBytes,part:t.partNumber,Key:this.params.Key,Bucket:this.params.Bucket})};null!==s&&s.on("xhr.upload.progress",i);let o=await Promise.all([this.client.send(new c.PutObjectCommand(e)),r?.endpoint?.()]),n=o[0],l=o[1];if(l||(l=te(await tr(e,c.PutObjectCommand,{...r}))),!l)throw Error('Could not resolve endpoint from S3 "client.config.endpoint()" nor EndpointsV2.');null!==s&&s.off("xhr.upload.progress",i);let u=this.params.Key.split("/").map(t=>tp(t)).join("/"),h=tp(this.params.Bucket),d=(()=>{let t=l.hostname.startsWith(`${h}.`),e=this.client.config.forcePathStyle,r=l.port?`:${l.port}`:"";return e?`${l.protocol}//${l.hostname}${r}/${h}/${u}`:t?`${l.protocol}//${l.hostname}${r}/${u}`:`${l.protocol}//${h}.${l.hostname}${r}/${u}`})();this.singleUploadResult={...n,Bucket:this.params.Bucket,Key:this.params.Key,Location:d};let p=tE(t.data);this.__notifyProgress({loaded:p,total:p,part:1,Key:this.params.Key,Bucket:this.params.Bucket})}async __createMultipartUpload(){let t=await this.client.config.requestChecksumCalculation();if(!this.createMultiPartPromise){let e={...this.params,Body:void 0};"WHEN_SUPPORTED"===t&&(e.ChecksumAlgorithm=this.params.ChecksumAlgorithm||c.ChecksumAlgorithm.CRC32),this.createMultiPartPromise=this.client.send(new c.CreateMultipartUploadCommand(e)).then(t=>(this.abortMultipartUploadCommand=new c.AbortMultipartUploadCommand({Bucket:this.params.Bucket,Key:this.params.Key,UploadId:t.UploadId}),t))}return this.createMultiPartPromise}async __doConcurrentUpload(t){for await(let e of t){if(this.uploadEnqueuedPartsCount>this.MAX_PARTS)throw Error(`Exceeded ${this.MAX_PARTS} parts in multipart upload to Bucket: ${this.params.Bucket} Key: ${this.params.Key}.`);if(this.abortController.signal.aborted)return;if(1===e.partNumber&&e.lastPart)return await this.__uploadUsingPut(e);if(!this.uploadId){let{UploadId:t}=await this.__createMultipartUpload();if(this.uploadId=t,this.abortController.signal.aborted)return}let t=tE(e.data)||0,r=this.client.config.requestHandler,a=r instanceof tS.EventEmitter?r:null,s=0,i=(r,a)=>{(Number(a.query.partNumber)||-1)===e.partNumber&&(r.total&&t&&(this.bytesUploadedSoFar+=r.loaded-s,s=r.loaded),this.__notifyProgress({loaded:this.bytesUploadedSoFar,total:this.totalBytes,part:e.partNumber,Key:this.params.Key,Bucket:this.params.Bucket}))};null!==a&&a.on("xhr.upload.progress",i),this.uploadEnqueuedPartsCount+=1;let o=await this.client.send(new c.UploadPartCommand({...this.params,ContentLength:void 0,UploadId:this.uploadId,Body:e.data,PartNumber:e.partNumber}));if(null!==a&&a.off("xhr.upload.progress",i),this.abortController.signal.aborted)return;if(!o.ETag)throw Error(`Part ${e.partNumber} is missing ETag in UploadPart response. Missing Bucket CORS configuration for ETag header?`);this.uploadedParts.push({PartNumber:e.partNumber,ETag:o.ETag,...o.ChecksumCRC32&&{ChecksumCRC32:o.ChecksumCRC32},...o.ChecksumCRC32C&&{ChecksumCRC32C:o.ChecksumCRC32C},...o.ChecksumSHA1&&{ChecksumSHA1:o.ChecksumSHA1},...o.ChecksumSHA256&&{ChecksumSHA256:o.ChecksumSHA256}}),null===a&&(this.bytesUploadedSoFar+=t),this.__notifyProgress({loaded:this.bytesUploadedSoFar,total:this.totalBytes,part:e.partNumber,Key:this.params.Key,Bucket:this.params.Bucket})}}async __doMultipartUpload(){let t;let e=tv(this.params.Body,this.partSize),r=[];for(let t=0;t<this.queueSize;t++){let t=this.__doConcurrentUpload(e).catch(t=>{r.push(t)});this.concurrentUploaders.push(t)}if(await Promise.all(this.concurrentUploaders),r.length>=1)throw await this.markUploadAsAborted(),r[0];if(this.abortController.signal.aborted)throw await this.markUploadAsAborted(),Object.assign(Error("Upload aborted."),{name:"AbortError"});if(this.isMultiPart){this.uploadedParts.sort((t,e)=>t.PartNumber-e.PartNumber);let e={...this.params,Body:void 0,UploadId:this.uploadId,MultipartUpload:{Parts:this.uploadedParts}};t=await this.client.send(new c.CompleteMultipartUploadCommand(e)),"string"==typeof t?.Location&&t.Location.includes("%2F")&&(t.Location=t.Location.replace(/%2F/g,"/"))}else t=this.singleUploadResult;return this.abortMultipartUploadCommand=null,this.tags.length&&await this.client.send(new c.PutObjectTaggingCommand({...this.params,Tagging:{TagSet:this.tags}})),t}async markUploadAsAborted(){this.uploadId&&!this.leavePartsOnError&&null!==this.abortMultipartUploadCommand&&(await this.client.send(this.abortMultipartUploadCommand),this.abortMultipartUploadCommand=null)}__notifyProgress(t){this.uploadEvent&&this.emit(this.uploadEvent,t)}async __abortTimeout(t){return new Promise((e,r)=>{t.onabort=()=>{let t=Error("Upload aborted.");t.name="AbortError",r(t)}})}__validateInput(){if(!this.params)throw Error("InputError: Upload requires params to be passed to upload.");if(!this.client)throw Error("InputError: Upload requires a AWS client to do uploads with.");if(this.partSize<tU.MIN_PART_SIZE)throw Error(`EntityTooSmall: Your proposed upload partsize [${this.partSize}] is smaller than the minimum allowed size [${tU.MIN_PART_SIZE}] (5MB)`);if(this.queueSize<1)throw Error("Queue size: Must have at least one uploading queue.")}}},79341:(t,e,r)=>{t.exports=r(44870)}};