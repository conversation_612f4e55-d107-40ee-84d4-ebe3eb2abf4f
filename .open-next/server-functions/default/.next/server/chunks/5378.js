exports.id=5378,exports.ids=[5378],exports.modules={14066:(e,t,r)=>{"use strict";r.d(t,{default:()=>o});var a=r(2569),s=r(53467),n=r(39365);function o({text:e,children:t}){return(0,a.jsx)(s.<PERSON>oClipboard,{text:e,onCopy:()=>n.oR.success("Copied"),children:(0,a.jsx)("div",{className:"cursor-pointer",children:t})})}},15585:(e,t,r)=>{"use strict";r.d(t,{A:()=>m});var a=r(34411),s=r(60050);function n({value:e,options:t,className:r}){return(0,a.jsx)("img",{src:e,alt:e,className:`w-10 h-10 rounded-full ${r}`})}var o=r(58243);function i({value:e,options:t,className:r}){return(0,a.jsx)(o.E,{variant:t?.variant??"secondary",className:r,children:e})}var l=r(22740),c=r.n(l);function d({value:e,options:t,className:r}){return(0,a.jsx)("div",{className:r,children:t?.format?c()(e).format(t?.format):c()(e).fromNow()})}var u=r(16976);function m({columns:e,data:t,emptyMessage:r}){return e||(e=[]),(0,a.jsxs)(s.Table,{className:"w-full",children:[(0,a.jsx)(s.TableHeader,{className:"",children:(0,a.jsx)(s.TableRow,{className:"rounded-md",children:e&&e.map((e,t)=>(0,a.jsx)(s.TableHead,{className:e.className,children:e.title},t))})}),(0,a.jsx)(s.TableBody,{children:t&&t.length>0?t.map((t,r)=>(0,a.jsx)(s.TableRow,{className:"h-16",children:e&&e.map((e,r)=>{let o=t[e.name],l=e.callback?e.callback(t):o,c=l;return"image"===e.type?c=(0,a.jsx)(n,{value:o,options:e.options,className:e.className}):"time"===e.type?c=(0,a.jsx)(d,{value:o,options:e.options,className:e.className}):"label"===e.type?c=(0,a.jsx)(i,{value:o,options:e.options,className:e.className}):"copy"===e.type&&o&&(c=(0,a.jsx)(u.default,{text:o,children:l})),(0,a.jsx)(s.TableCell,{className:e.className,children:c},r)})},r)):(0,a.jsx)(s.TableRow,{className:"",children:(0,a.jsx)(s.TableCell,{colSpan:e.length,children:(0,a.jsx)("div",{className:"flex w-full justify-center items-center py-8 text-muted-foreground",children:(0,a.jsx)("p",{children:r})})})})})]})}},16976:(e,t,r)=>{"use strict";r.d(t,{default:()=>a});let a=(0,r(29037).registerClientReference)(function(){throw Error("Attempted to call the default export of \"/Users/<USER>/My-Project/fluxkrea_test/components/blocks/table/copy.tsx\" from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"/Users/<USER>/My-Project/fluxkrea_test/components/blocks/table/copy.tsx","default")},26820:(e,t,r)=>{"use strict";r.r(t),r.d(t,{"00880263a376180560adeff1fdd2511a7543726d4a":()=>a.T});var a=r(39201)},28432:(e,t,r)=>{"use strict";r.d(t,{j2:()=>f,Y9:()=>u});var a=r(62364),s=r(36157),n=r(72648),o=r(39201),i=r(4961),l=r(29452),c=r(60736);let d=[];d.push((0,s.A)({id:"google-one-tap",name:"google-one-tap",credentials:{credential:{type:"text"}},async authorize(e,t){let r=e.credential,a=await fetch("https://oauth2.googleapis.com/tokeninfo?id_token="+r);if(!a.ok)return console.log("Failed to verify token"),null;let s=await a.json();if(!s)return console.log("invalid payload from token"),null;let{email:n,sub:o,given_name:i,family_name:l,email_verified:c,picture:d}=s;return n?{id:o,name:[i,l].join(" "),email:n,image:d,emailVerified:c?new Date:null}:(console.log("invalid email in payload"),null)}})),process.env.AUTH_GOOGLE_ID&&process.env.AUTH_GOOGLE_SECRET&&d.push((0,n.A)({clientId:process.env.AUTH_GOOGLE_ID,clientSecret:process.env.AUTH_GOOGLE_SECRET,authorization:{params:{prompt:"consent",access_type:"offline",response_type:"code"}}})),d.map(e=>{if("function"!=typeof e)return{id:e.id,name:e.name};{let t=e();return{id:t.id,name:t.name}}}).filter(e=>"google-one-tap"!==e.id);let{handlers:u,signIn:m,signOut:p,auth:f}=(0,a.Ay)({providers:d,pages:{signIn:"/auth/signin"},callbacks:{signIn:async({user:e,account:t,profile:r,email:a,credentials:s})=>!0,redirect:async({url:e,baseUrl:t})=>e.startsWith("/")?`${t}${e}`:new URL(e).origin===t?e:t,session:async({session:e,token:t,user:r})=>(t&&t.user&&t.user&&(e.user=t.user),e),async jwt({token:e,user:t,account:r}){try{if(t&&t.email&&r){let a={uuid:(0,l.YJ)(),email:t.email,nickname:t.name||"",avatar_url:t.image||"",signin_type:r.type,signin_provider:r.provider,signin_openid:r.providerAccountId,created_at:(0,i.iq)(),signin_ip:await (0,o.T)()};try{let t=await (0,c.$c)(a);e.user={uuid:t.uuid,email:t.email,nickname:t.nickname,avatar_url:t.avatar_url,created_at:t.created_at}}catch(e){console.error("save user failed:",e)}}return e}catch(t){return console.error("jwt callback error:",t),e}}}})},31547:(e,t,r)=>{Promise.resolve().then(r.bind(r,35211)),Promise.resolve().then(r.bind(r,20518))},34414:(e,t,r)=>{"use strict";r.d(t,{A:()=>i});var a=r(34411),s=r(92172),n=r(57595),o=r(78933);function i({items:e}){return(0,a.jsx)("div",{className:"flex space-x-4 mb-8",children:e?.map((e,t)=>e.url?a.jsx(o.N_,{href:e.url,children:a.jsxs(s.$,{variant:e.variant,size:"sm",className:e.className,children:[e.icon&&a.jsx(n.default,{name:e.icon,className:"w-4 h-4 mr-1"}),e.title]})},t):a.jsxs(s.$,{variant:e.variant,size:"sm",className:e.className,children:[e.icon&&a.jsx(n.default,{name:e.icon,className:"w-4 h-4 mr-1"}),e.title]},t))})}},35211:(e,t,r)=>{"use strict";r.d(t,{default:()=>c});var a=r(2569),s=r(92227),n=r(37967),o=r(92622),i=r(75673),l=r(14059);function c({className:e,items:t,...r}){let c=(0,l.usePathname)();return console.log(c),(0,a.jsx)("nav",{className:(0,i.cn)("flex space-x-2 lg:flex-col lg:space-x-0 lg:space-y-1",e),...r,children:t.map((e,t)=>(0,a.jsxs)(n.N_,{href:e.url,className:(0,i.cn)((0,o.r)({variant:"ghost"}),e.is_active||c.includes(e.url)?"bg-muted/50 text-primary hover:bg-muted hover:text-primary":"hover:bg-transparent hover:underline","justify-start"),children:[e.icon&&(0,a.jsx)(s.default,{name:e.icon,className:"w-4 h-4"}),e.title]},t))})}},36393:(e,t,r)=>{"use strict";r.d(t,{b:()=>c});var a=r(29068),s=r(85137),n=r(2569),o="horizontal",i=["horizontal","vertical"],l=a.forwardRef((e,t)=>{var r;let{decorative:a,orientation:l=o,...c}=e,d=(r=l,i.includes(r))?l:o;return(0,n.jsx)(s.sG.div,{"data-orientation":d,...a?{role:"none"}:{"aria-orientation":"vertical"===d?d:void 0,role:"separator"},...c,ref:t})});l.displayName="Separator";var c=l},39201:(e,t,r)=>{"use strict";r.d(t,{T:()=>n});var a=r(72784);r(97468);var s=r(62225);async function n(){let e=await (0,s.b3)();return e.get("cf-connecting-ip")||e.get("x-real-ip")||(e.get("x-forwarded-for")||"127.0.0.1").split(",")[0]}(0,r(78564).D)([n]),(0,a.A)(n,"00880263a376180560adeff1fdd2511a7543726d4a",null)},49969:(e,t,r)=>{Promise.resolve().then(r.bind(r,14066)),Promise.resolve().then(r.bind(r,92227)),Promise.resolve().then(r.bind(r,80081)),Promise.resolve().then(r.bind(r,72568)),Promise.resolve().then(r.bind(r,20518))},50641:(e,t,r)=>{Promise.resolve().then(r.bind(r,16976)),Promise.resolve().then(r.bind(r,57595)),Promise.resolve().then(r.bind(r,70439)),Promise.resolve().then(r.bind(r,60050)),Promise.resolve().then(r.bind(r,37032))},55977:(e,t,r)=>{"use strict";r.d(t,{default:()=>a});let a=(0,r(29037).registerClientReference)(function(){throw Error("Attempted to call the default export of \"/Users/<USER>/My-Project/fluxkrea_test/components/console/sidebar/nav.tsx\" from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"/Users/<USER>/My-Project/fluxkrea_test/components/console/sidebar/nav.tsx","default")},58243:(e,t,r)=>{"use strict";r.d(t,{E:()=>l});var a=r(34411);r(37582);var s=r(20392),n=r(38e3),o=r(25039);let i=(0,n.F)("inline-flex items-center justify-center rounded-md border px-2 py-0.5 text-xs font-medium w-fit whitespace-nowrap shrink-0 [&>svg]:size-3 gap-1 [&>svg]:pointer-events-none focus-visible:border-ring focus-visible:ring-ring/50 focus-visible:ring-[3px] aria-invalid:ring-destructive/20 dark:aria-invalid:ring-destructive/40 aria-invalid:border-destructive transition-[color,box-shadow] overflow-hidden",{variants:{variant:{default:"border-transparent bg-primary text-primary-foreground [a&]:hover:bg-primary/90",secondary:"border-transparent bg-secondary text-secondary-foreground [a&]:hover:bg-secondary/90",destructive:"border-transparent bg-destructive text-white [a&]:hover:bg-destructive/90 focus-visible:ring-destructive/20 dark:focus-visible:ring-destructive/40 dark:bg-destructive/60",outline:"text-foreground [a&]:hover:bg-accent [a&]:hover:text-accent-foreground"}},defaultVariants:{variant:"default"}});function l({className:e,variant:t,asChild:r=!1,...n}){let l=r?s.DX:"span";return(0,a.jsx)(l,{"data-slot":"badge",className:(0,o.cn)(i({variant:t}),e),...n})}},60050:(e,t,r)=>{"use strict";r.d(t,{Table:()=>s,TableBody:()=>o,TableCell:()=>c,TableHead:()=>i,TableHeader:()=>n,TableRow:()=>l});var a=r(29037);let s=(0,a.registerClientReference)(function(){throw Error("Attempted to call Table() from the server but Table is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"/Users/<USER>/My-Project/fluxkrea_test/components/ui/table.tsx","Table"),n=(0,a.registerClientReference)(function(){throw Error("Attempted to call TableHeader() from the server but TableHeader is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"/Users/<USER>/My-Project/fluxkrea_test/components/ui/table.tsx","TableHeader"),o=(0,a.registerClientReference)(function(){throw Error("Attempted to call TableBody() from the server but TableBody is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"/Users/<USER>/My-Project/fluxkrea_test/components/ui/table.tsx","TableBody");(0,a.registerClientReference)(function(){throw Error("Attempted to call TableFooter() from the server but TableFooter is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"/Users/<USER>/My-Project/fluxkrea_test/components/ui/table.tsx","TableFooter");let i=(0,a.registerClientReference)(function(){throw Error("Attempted to call TableHead() from the server but TableHead is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"/Users/<USER>/My-Project/fluxkrea_test/components/ui/table.tsx","TableHead"),l=(0,a.registerClientReference)(function(){throw Error("Attempted to call TableRow() from the server but TableRow is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"/Users/<USER>/My-Project/fluxkrea_test/components/ui/table.tsx","TableRow"),c=(0,a.registerClientReference)(function(){throw Error("Attempted to call TableCell() from the server but TableCell is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"/Users/<USER>/My-Project/fluxkrea_test/components/ui/table.tsx","TableCell");(0,a.registerClientReference)(function(){throw Error("Attempted to call TableCaption() from the server but TableCaption is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"/Users/<USER>/My-Project/fluxkrea_test/components/ui/table.tsx","TableCaption")},60736:(e,t,r)=>{"use strict";r.d(t,{$c:()=>c,TG:()=>d,qo:()=>m,ug:()=>p});var a=r(87830),s=r(35050),n=r(28432),o=r(4961),i=r(78054),l=r(62225);async function c(e){try{let t=await (0,s.Gs)(e.email);return t?(e.id=t.id,e.uuid=t.uuid,e.created_at=t.created_at):(await (0,s.HW)(e),await (0,a.d_)({user_uuid:e.uuid||"",trans_type:a.H3.NewUser,credits:a.rN.NewUserGet,expired_at:(0,o.MI)()})),e}catch(e){throw console.log("save user failed: ",e),e}}async function d(){let e="",t=await u();if(t&&t.startsWith("sk-"))return await (0,i.zz)(t)||"";let r=await (0,n.j2)();return r&&r.user&&r.user.uuid&&(e=r.user.uuid),e}async function u(){let e=(await (0,l.b3)()).get("Authorization");return e?e.replace("Bearer ",""):""}async function m(){let e="",t=await (0,n.j2)();return t&&t.user&&t.user.email&&(e=t.user.email),e}async function p(){let e=await d();if(e)return await (0,s.pX)(e)}},65690:(e,t,r)=>{"use strict";r.r(t),r.d(t,{default:()=>c});var a=r(34411),s=r(55977);async function n({children:e,sidebar:t}){return(0,a.jsx)("div",{className:"container md:max-w-7xl py-8 mx-auto",children:(0,a.jsx)("div",{className:"w-full space-y-6 p-4 pb-16 block",children:(0,a.jsxs)("div",{className:"flex flex-col space-y-8 lg:flex-row lg:space-x-12 lg:space-y-0",children:[t?.nav?.items&&(0,a.jsx)("aside",{className:"md:min-w-40 flex-shrink-0",children:(0,a.jsx)(s.default,{items:t.nav?.items})}),(0,a.jsx)("div",{className:"flex-1 lg:max-w-full",children:e})]})})})}var o=r(68791),i=r(60736),l=r(79502);async function c({children:e}){let t=await (0,i.ug)();t&&t.email||(0,l.redirect)("/auth/signin");let r=await (0,o.A)(),s={nav:{items:[{title:r("user.my_orders"),url:"/my-orders",icon:"RiOrderPlayLine",is_active:!1},{title:r("my_credits.title"),url:"/my-credits",icon:"RiBankCardLine",is_active:!1},{title:r("my_invites.title"),url:"/my-invites",icon:"RiMoneyCnyCircleFill",is_active:!1},{title:r("api_keys.title"),url:"/api-keys",icon:"RiKey2Line",is_active:!1}]}};return(0,a.jsx)(n,{sidebar:s,children:e})}},65891:(e,t,r)=>{Promise.resolve().then(r.bind(r,55977)),Promise.resolve().then(r.bind(r,37032))},68791:(e,t,r)=>{"use strict";r.d(t,{A:()=>i});var a=r(37582),s=r(93134),n=r(21972),o=(0,a.cache)(function(e,t){return function({_cache:e=(0,n.d)(),_formatters:t=(0,n.b)(e),getMessageFallback:r=n.f,messages:a,namespace:s,onError:o=n.g,...i}){return function({messages:e,namespace:t,...r},a){return e=e["!"],t=(0,n.r)(t,"!"),(0,n.e)({...r,messages:e,namespace:t})}({...i,onError:o,cache:e,formatters:t,getMessageFallback:r,messages:{"!":a},namespace:s?`!.${s}`:"!"},"!")}({...e,namespace:t})}),i=(0,a.cache)(async function(e){let t,r;return"string"==typeof e?t=e:e&&(r=e.locale,t=e.namespace),o(await (0,s.A)(r),t)})},70439:(e,t,r)=>{"use strict";r.d(t,{Separator:()=>a});let a=(0,r(29037).registerClientReference)(function(){throw Error("Attempted to call Separator() from the server but Separator is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"/Users/<USER>/My-Project/fluxkrea_test/components/ui/separator.tsx","Separator")},72568:(e,t,r)=>{"use strict";r.d(t,{Table:()=>n,TableBody:()=>i,TableCell:()=>d,TableHead:()=>c,TableHeader:()=>o,TableRow:()=>l});var a=r(2569);r(29068);var s=r(75673);function n({className:e,...t}){return(0,a.jsx)("div",{"data-slot":"table-container",className:"relative w-full overflow-x-auto",children:(0,a.jsx)("table",{"data-slot":"table",className:(0,s.cn)("w-full caption-bottom text-sm",e),...t})})}function o({className:e,...t}){return(0,a.jsx)("thead",{"data-slot":"table-header",className:(0,s.cn)("[&_tr]:border-b",e),...t})}function i({className:e,...t}){return(0,a.jsx)("tbody",{"data-slot":"table-body",className:(0,s.cn)("[&_tr:last-child]:border-0",e),...t})}function l({className:e,...t}){return(0,a.jsx)("tr",{"data-slot":"table-row",className:(0,s.cn)("hover:bg-muted/50 data-[state=selected]:bg-muted border-b transition-colors",e),...t})}function c({className:e,...t}){return(0,a.jsx)("th",{"data-slot":"table-head",className:(0,s.cn)("text-foreground h-10 px-2 text-left align-middle font-medium whitespace-nowrap [&:has([role=checkbox])]:pr-0 [&>[role=checkbox]]:translate-y-[2px]",e),...t})}function d({className:e,...t}){return(0,a.jsx)("td",{"data-slot":"table-cell",className:(0,s.cn)("p-2 align-middle whitespace-nowrap [&:has([role=checkbox])]:pr-0 [&>[role=checkbox]]:translate-y-[2px]",e),...t})}},78054:(e,t,r)=>{"use strict";r.d(t,{N1:()=>n,ai:()=>s,ox:()=>o,zz:()=>i});var a=r(4995),s=function(e){return e.Created="created",e.Deleted="deleted",e}({});async function n(e){let t=(0,a.A)(),{data:r,error:s}=await t.from("apikeys").insert(e);if(s)throw s;return r}async function o(e,t=1,r=50){let s=(t-1)*r,n=(0,a.A)(),{data:i,error:l}=await n.from("apikeys").select("*").eq("user_uuid",e).neq("status","deleted").order("created_at",{ascending:!1}).range(s,s+r-1);if(!l)return i}async function i(e){let t=(0,a.A)(),{data:r,error:s}=await t.from("apikeys").select("user_uuid").eq("api_key",e).eq("status","created").limit(1).single();if(!s)return r?.user_uuid}},80081:(e,t,r)=>{"use strict";r.d(t,{Separator:()=>o});var a=r(2569);r(29068);var s=r(36393),n=r(75673);function o({className:e,orientation:t="horizontal",decorative:r=!0,...o}){return(0,a.jsx)(s.b,{"data-slot":"separator-root",decorative:r,orientation:t,className:(0,n.cn)("bg-border shrink-0 data-[orientation=horizontal]:h-px data-[orientation=horizontal]:w-full data-[orientation=vertical]:h-full data-[orientation=vertical]:w-px",e),...o})}},88456:(e,t,r)=>{"use strict";r.d(t,{A:()=>i});var a=r(34411),s=r(70439),n=r(15585),o=r(34414);function i({...e}){return(0,a.jsxs)("div",{className:"space-y-6",children:[(0,a.jsxs)("div",{children:[(0,a.jsx)("h3",{className:"text-lg font-medium",children:e.title}),(0,a.jsx)("p",{className:"text-sm text-muted-foreground",children:e.description})]}),e.tip&&(0,a.jsx)("p",{className:"text-sm text-muted-foreground",children:e.tip.description||e.tip.title}),e.toolbar&&(0,a.jsx)(o.A,{items:e.toolbar.items}),(0,a.jsx)(s.Separator,{}),(0,a.jsx)(n.A,{...e})]})}},92172:(e,t,r)=>{"use strict";r.d(t,{$:()=>l});var a=r(34411);r(37582);var s=r(20392),n=r(38e3),o=r(25039);let i=(0,n.F)("inline-flex items-center justify-center gap-2 whitespace-nowrap rounded-lg text-sm font-medium transition-all duration-200 disabled:pointer-events-none disabled:opacity-50 [&_svg]:pointer-events-none [&_svg:not([class*='size-'])]:size-4 shrink-0 [&_svg]:shrink-0 outline-none focus-visible:border-ring focus-visible:ring-ring/50 focus-visible:ring-[3px] aria-invalid:ring-destructive/20 dark:aria-invalid:ring-destructive/40 aria-invalid:border-destructive",{variants:{variant:{default:"bg-gradient-to-r from-primary to-accent text-primary-foreground shadow-md hover:shadow-lg hover:scale-[1.02] active:scale-[0.98]",destructive:"bg-destructive text-destructive-foreground shadow-md hover:bg-destructive/90 hover:shadow-lg hover:scale-[1.02] active:scale-[0.98] focus-visible:ring-destructive/20 dark:focus-visible:ring-destructive/40",outline:"border-2 border-primary/20 bg-background/50 backdrop-blur-sm shadow-sm hover:bg-accent/50 hover:text-accent-foreground hover:border-primary/40 hover:shadow-md hover:scale-[1.02] active:scale-[0.98] dark:bg-input/30 dark:border-input dark:hover:bg-input/50",secondary:"bg-gradient-to-r from-secondary to-muted text-secondary-foreground shadow-sm hover:shadow-md hover:scale-[1.02] active:scale-[0.98]",ghost:"hover:bg-accent/50 hover:text-accent-foreground hover:scale-[1.02] active:scale-[0.98] dark:hover:bg-accent/30",link:"text-primary underline-offset-4 hover:underline hover:text-accent"},size:{default:"h-10 px-6 py-2 has-[>svg]:px-4",sm:"h-8 rounded-lg gap-1.5 px-4 has-[>svg]:px-3",lg:"h-12 rounded-lg px-8 has-[>svg]:px-6 text-base",icon:"size-10 rounded-full"}},defaultVariants:{variant:"default",size:"default"}});function l({className:e,variant:t,size:r,asChild:n=!1,...l}){let c=n?s.DX:"button";return(0,a.jsx)(c,{"data-slot":"button",className:(0,o.cn)(i({variant:t,size:r,className:e})),...l})}}};