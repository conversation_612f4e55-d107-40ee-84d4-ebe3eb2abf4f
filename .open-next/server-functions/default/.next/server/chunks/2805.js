exports.id=2805,exports.ids=[2805],exports.modules={13373:()=>{},26820:(e,t,a)=>{"use strict";a.r(t),a.d(t,{"00880263a376180560adeff1fdd2511a7543726d4a":()=>i.T});var i=a(39201)},28432:(e,t,a)=>{"use strict";a.d(t,{j2:()=>m,Y9:()=>d});var i=a(62364),r=a(36157),s=a(72648),n=a(39201),o=a(4961),u=a(29452),l=a(60736);let c=[];c.push((0,r.A)({id:"google-one-tap",name:"google-one-tap",credentials:{credential:{type:"text"}},async authorize(e,t){let a=e.credential,i=await fetch("https://oauth2.googleapis.com/tokeninfo?id_token="+a);if(!i.ok)return console.log("Failed to verify token"),null;let r=await i.json();if(!r)return console.log("invalid payload from token"),null;let{email:s,sub:n,given_name:o,family_name:u,email_verified:l,picture:c}=r;return s?{id:n,name:[o,u].join(" "),email:s,image:c,emailVerified:l?new Date:null}:(console.log("invalid email in payload"),null)}})),process.env.AUTH_GOOGLE_ID&&process.env.AUTH_GOOGLE_SECRET&&c.push((0,s.A)({clientId:process.env.AUTH_GOOGLE_ID,clientSecret:process.env.AUTH_GOOGLE_SECRET,authorization:{params:{prompt:"consent",access_type:"offline",response_type:"code"}}})),c.map(e=>{if("function"!=typeof e)return{id:e.id,name:e.name};{let t=e();return{id:t.id,name:t.name}}}).filter(e=>"google-one-tap"!==e.id);let{handlers:d,signIn:p,signOut:_,auth:m}=(0,i.Ay)({providers:c,pages:{signIn:"/auth/signin"},callbacks:{signIn:async({user:e,account:t,profile:a,email:i,credentials:r})=>!0,redirect:async({url:e,baseUrl:t})=>e.startsWith("/")?`${t}${e}`:new URL(e).origin===t?e:t,session:async({session:e,token:t,user:a})=>(t&&t.user&&t.user&&(e.user=t.user),e),async jwt({token:e,user:t,account:a}){try{if(t&&t.email&&a){let i={uuid:(0,u.YJ)(),email:t.email,nickname:t.name||"",avatar_url:t.image||"",signin_type:a.type,signin_provider:a.provider,signin_openid:a.providerAccountId,created_at:(0,o.iq)(),signin_ip:await (0,n.T)()};try{let t=await (0,l.$c)(i);e.user={uuid:t.uuid,email:t.email,nickname:t.nickname,avatar_url:t.avatar_url,created_at:t.created_at}}catch(e){console.error("save user failed:",e)}}return e}catch(t){return console.error("jwt callback error:",t),e}}}})},29042:(e,t,a)=>{"use strict";function i(e){return n(0,"ok",e||[])}function r(){return n(0,"ok")}function s(e){return n(-1,e)}function n(e,t,a){let i={code:e,message:t,data:a};return a&&(i.data=a),Response.json(i)}a.d(t,{DQ:()=>i,YS:()=>s,j7:()=>n,rn:()=>r})},39201:(e,t,a)=>{"use strict";a.d(t,{T:()=>s});var i=a(72784);a(97468);var r=a(62225);async function s(){let e=await (0,r.b3)();return e.get("cf-connecting-ip")||e.get("x-real-ip")||(e.get("x-forwarded-for")||"127.0.0.1").split(",")[0]}(0,a(78564).D)([s]),(0,i.A)(s,"00880263a376180560adeff1fdd2511a7543726d4a",null)},60736:(e,t,a)=>{"use strict";a.d(t,{$c:()=>l,TG:()=>c,qo:()=>p,ug:()=>_});var i=a(87830),r=a(35050),s=a(28432),n=a(4961),o=a(78054),u=a(62225);async function l(e){try{let t=await (0,r.Gs)(e.email);return t?(e.id=t.id,e.uuid=t.uuid,e.created_at=t.created_at):(await (0,r.HW)(e),await (0,i.d_)({user_uuid:e.uuid||"",trans_type:i.H3.NewUser,credits:i.rN.NewUserGet,expired_at:(0,n.MI)()})),e}catch(e){throw console.log("save user failed: ",e),e}}async function c(){let e="",t=await d();if(t&&t.startsWith("sk-"))return await (0,o.zz)(t)||"";let a=await (0,s.j2)();return a&&a.user&&a.user.uuid&&(e=a.user.uuid),e}async function d(){let e=(await (0,u.b3)()).get("Authorization");return e?e.replace("Bearer ",""):""}async function p(){let e="",t=await (0,s.j2)();return t&&t.user&&t.user.email&&(e=t.user.email),e}async function _(){let e=await c();if(e)return await (0,r.pX)(e)}},74245:(e,t,a)=>{"use strict";a.d(t,{QR:()=>n});var i=a(29452);let r={baseURL:"https://openspeech.bytedance.com",timeout:6e4,retryAttempts:3,retryDelay:1e3};class s{constructor(e,t){if(this.apiKey=e||process.env.VOLCENGINE_ACCESS_TOKEN||"",this.appId=t||process.env.VOLCENGINE_APP_ID||"",this.baseURL=r.baseURL,!this.apiKey||!this.appId)throw Error("Volcengine API key and App ID are required")}async synthesizeText(e){let t=(0,i.YJ)(),a={app:{appid:this.appId,token:"access_token",cluster:"volcano_tts"},user:{uid:"web_user"},audio:{voice_type:e.voice_type||"BV700_streaming",encoding:e.encoding||"mp3",rate:e.rate||24e3,speed_ratio:e.speed_ratio||1,volume_ratio:e.volume_ratio||1,pitch_ratio:e.pitch_ratio||1,emotion:e.emotion,language:e.language||"cn"},request:{reqid:t,text:e.text,text_type:"plain",operation:"query"}};return await this.makeRequest("/api/v1/tts","POST",a)}async submitAsyncTTS(e){let t=(0,i.YJ)(),a={appid:this.appId,reqid:t,text:e.text,format:e.format||"mp3",voice_type:e.voice_type||"BV701_streaming",sample_rate:e.sample_rate||24e3,volume:e.volume||1,speed:e.speed||1,pitch:e.pitch||1,enable_subtitle:e.enable_subtitle||0,callback_url:e.callback_url};return await this.makeRequest("/api/v1/tts_async/submit","POST",a,{"Resource-Id":"volc.tts_async.default"})}async queryAsyncTTS(e){let t=`/api/v1/tts_async/query?appid=${this.appId}&task_id=${e}`;return await this.makeRequest(t,"GET",null,{"Resource-Id":"volc.tts_async.default"})}async submitASRBigModel(e){let t=(0,i.YJ)(),a={appid:this.appId,reqid:t,url:e.url,language:e.language||"zh",use_itn:!1!==e.use_itn,use_capitalize:!1!==e.use_capitalize,max_lines:e.max_lines||1,callback_url:e.callback_url};return await this.makeRequest("/api/v3/auc/bigmodel/submit","POST",a,{"Resource-Id":"volc.bigasr.sauc"})}async queryASRBigModel(e){let t={appid:this.appId,id:e};return await this.makeRequest("/api/v3/auc/bigmodel/query","POST",t,{"Resource-Id":"volc.bigasr.sauc"})}async submitASRStandard(e,t="fast"){let a=(0,i.YJ)(),r={appid:this.appId,reqid:a,cluster:"standard"===t?"volcano_asr_standard":"volcano_asr",url:e.url,language:e.language||"zh-CN",use_itn:!1!==e.use_itn,use_capitalize:!1!==e.use_capitalize,callback_url:e.callback_url};return await this.makeRequest("/api/v1/auc/submit","POST",r,{"Resource-Id":"volc.auc.sauc"})}async queryASRStandard(e){let t={appid:this.appId,id:e};return await this.makeRequest("/api/v1/auc/query","POST",t,{"Resource-Id":"volc.auc.sauc"})}async uploadVoiceClone(e){let t={appid:this.appId,speaker_id:e.speaker_id,audios:[{audio_bytes:e.audio_bytes,audio_format:e.audio_format,text:e.text}],source:2,language:e.language||0,model_type:e.model_type||1};return await this.makeRequest("/api/v1/mega_tts/audio/upload","POST",t,{"Resource-Id":"volc.megatts.voiceclone"})}async queryVoiceCloneStatus(e){let t={appid:this.appId,speaker_id:e};return await this.makeRequest("/api/v1/mega_tts/status","POST",t,{"Resource-Id":"volc.megatts.voiceclone"})}async makeRequest(e,t="POST",a,i){let s=`${this.baseURL}${e}`,n={Authorization:`Bearer;${this.apiKey}`,"Content-Type":"application/json",...i},o={method:t,headers:n,signal:AbortSignal.timeout(r.timeout)};"POST"===t&&a&&(o.body=JSON.stringify(a)),console.log("=== Volcengine API Request Debug Info ==="),console.log("URL:",s),console.log("Method:",t),console.log("Headers:",JSON.stringify(n,null,2)),console.log("Body:",o.body),console.log("==========================================");let u=Error("Unknown error");for(let e=1;e<=r.retryAttempts;e++)try{let e=await fetch(s,o);if(!e.ok)throw Error(`HTTP ${e.status}: ${e.statusText}`);let t=await e.json();return console.log("=== Volcengine API Response ==="),console.log("Response:",JSON.stringify(t,null,2)),console.log("==============================="),t}catch(t){u=t,console.log(`=== Attempt ${e} failed ===`),console.log("Error:",u.message),console.log("============================="),e<r.retryAttempts&&await new Promise(t=>setTimeout(t,r.retryDelay*e))}throw Error(`Volcengine API request failed after ${r.retryAttempts} attempts: ${u.message}`)}}function n(e,t){return new s(e,t)}},78054:(e,t,a)=>{"use strict";a.d(t,{N1:()=>s,ai:()=>r,ox:()=>n,zz:()=>o});var i=a(4995),r=function(e){return e.Created="created",e.Deleted="deleted",e}({});async function s(e){let t=(0,i.A)(),{data:a,error:r}=await t.from("apikeys").insert(e);if(r)throw r;return a}async function n(e,t=1,a=50){let r=(t-1)*a,s=(0,i.A)(),{data:o,error:u}=await s.from("apikeys").select("*").eq("user_uuid",e).neq("status","deleted").order("created_at",{ascending:!1}).range(r,r+a-1);if(!u)return o}async function o(e){let t=(0,i.A)(),{data:a,error:r}=await t.from("apikeys").select("user_uuid").eq("api_key",e).eq("status","created").limit(1).single();if(!r)return a?.user_uuid}},79341:(e,t,a)=>{"use strict";e.exports=a(44870)},94813:()=>{}};