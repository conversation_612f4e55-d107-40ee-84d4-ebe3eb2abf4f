"use strict";exports.id=5683,exports.ids=[5683],exports.modules={73785:(e,t,r)=>{r.d(t,{jr:()=>eM});var n,s,o,a,l,i,u,c,p,d,h="vercel.ai.error",m=Symbol.for(h),f=class e extends Error{constructor({name:e,message:t,cause:r}){super(t),this[n]=!0,this.name=e,this.cause=r}static isInstance(t){return e.hasMarker(t,h)}static hasMarker(e,t){let r=Symbol.for(t);return null!=e&&"object"==typeof e&&r in e&&"boolean"==typeof e[r]&&!0===e[r]}};n=m;var g=f,y="AI_APICallError",b=`vercel.ai.error.${y}`,v=Symbol.for(b),w=class extends g{constructor({message:e,url:t,requestBodyValues:r,statusCode:n,responseHeaders:o,responseBody:a,cause:l,isRetryable:i=null!=n&&(408===n||409===n||429===n||n>=500),data:u}){super({name:y,message:e,cause:l}),this[s]=!0,this.url=t,this.requestBodyValues=r,this.statusCode=n,this.responseHeaders=o,this.responseBody=a,this.isRetryable=i,this.data=u}static isInstance(e){return g.hasMarker(e,b)}};s=v;var z="AI_EmptyResponseBodyError",_=`vercel.ai.error.${z}`,k=Symbol.for(_),x=class extends g{constructor({message:e="Empty response body"}={}){super({name:z,message:e}),this[o]=!0}static isInstance(e){return g.hasMarker(e,_)}};function j(e){return null==e?"unknown error":"string"==typeof e?e:e instanceof Error?e.message:JSON.stringify(e)}o=k;var S="AI_InvalidArgumentError",I=`vercel.ai.error.${S}`,E=Symbol.for(I),T=class extends g{constructor({message:e,cause:t,argument:r}){super({name:S,message:e,cause:t}),this[a]=!0,this.argument=r}static isInstance(e){return g.hasMarker(e,I)}};a=E;var C="AI_InvalidPromptError",N=`vercel.ai.error.${C}`,$=Symbol.for(N),O=class extends g{constructor({prompt:e,message:t,cause:r}){super({name:C,message:`Invalid prompt: ${t}`,cause:r}),this[l]=!0,this.prompt=e}static isInstance(e){return g.hasMarker(e,N)}};l=$;var R="AI_InvalidResponseDataError",q=`vercel.ai.error.${R}`,A=Symbol.for(q),M=class extends g{constructor({data:e,message:t=`Invalid response data: ${JSON.stringify(e)}.`}){super({name:R,message:t}),this[i]=!0,this.data=e}static isInstance(e){return g.hasMarker(e,q)}};i=A;var P="AI_JSONParseError",J=`vercel.ai.error.${P}`,B=Symbol.for(J),H=class extends g{constructor({text:e,cause:t}){super({name:P,message:`JSON parsing failed: Text: ${e}.
Error message: ${j(t)}`,cause:t}),this[u]=!0,this.text=e}static isInstance(e){return g.hasMarker(e,J)}};u=B;var U=Symbol.for("vercel.ai.error.AI_LoadAPIKeyError"),V=Symbol.for("vercel.ai.error.AI_LoadSettingError"),D=Symbol.for("vercel.ai.error.AI_NoContentGeneratedError"),F=Symbol.for("vercel.ai.error.AI_NoSuchModelError"),G="AI_TooManyEmbeddingValuesForCallError",L=`vercel.ai.error.${G}`,K=Symbol.for(L),Z=class extends g{constructor(e){super({name:G,message:`Too many values for a single embedding call. The ${e.provider} model "${e.modelId}" can only embed up to ${e.maxEmbeddingsPerCall} values per call, but ${e.values.length} values were provided.`}),this[c]=!0,this.provider=e.provider,this.modelId=e.modelId,this.maxEmbeddingsPerCall=e.maxEmbeddingsPerCall,this.values=e.values}static isInstance(e){return g.hasMarker(e,L)}};c=K;var Y="AI_TypeValidationError",W=`vercel.ai.error.${Y}`,Q=Symbol.for(W),X=class e extends g{constructor({value:e,cause:t}){super({name:Y,message:`Type validation failed: Value: ${JSON.stringify(e)}.
Error message: ${j(t)}`,cause:t}),this[p]=!0,this.value=e}static isInstance(e){return g.hasMarker(e,W)}static wrap({value:t,cause:r}){return e.isInstance(r)&&r.value===t?r:new e({value:t,cause:r})}};p=Q;var ee="AI_UnsupportedFunctionalityError",et=`vercel.ai.error.${ee}`,er=Symbol.for(et),en=class extends g{constructor({functionality:e,message:t=`'${e}' functionality not supported.`}){super({name:ee,message:t}),this[d]=!0,this.functionality=e}static isInstance(e){return g.hasMarker(e,et)}};function es(e){return null===e||"string"==typeof e||"number"==typeof e||"boolean"==typeof e||(Array.isArray(e)?e.every(es):"object"==typeof e&&Object.entries(e).every(([e,t])=>"string"==typeof e&&es(t)))}d=er;var eo=r(48444),ea=r(57057),el=r(71576);function ei(...e){return e.reduce((e,t)=>({...e,...null!=t?t:{}}),{})}function eu(e){let t={};return e.headers.forEach((e,r)=>{t[r]=e}),t}var ec=(({prefix:e,size:t=16,alphabet:r="0123456789ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz",separator:n="-"}={})=>{let s=(0,eo.d)(r,t);if(null==e)return s;if(r.includes(n))throw new T({argument:"separator",message:`The separator "${n}" must not be part of the alphabet "${r}".`});return t=>`${e}${n}${s(t)}`})();function ep(e){return e instanceof Error&&("AbortError"===e.name||"TimeoutError"===e.name)}var ed=Symbol.for("vercel.ai.validator");function eh({value:e,schema:t}){var r;let n="object"==typeof t&&null!==t&&ed in t&&!0===t[ed]&&"validate"in t?t:(r=t,{[ed]:!0,validate:e=>{let t=r.safeParse(e);return t.success?{success:!0,value:t.data}:{success:!1,error:t.error}}});try{if(null==n.validate)return{success:!0,value:e};let t=n.validate(e);if(t.success)return t;return{success:!1,error:X.wrap({value:e,cause:t.error})}}catch(t){return{success:!1,error:X.wrap({value:e,cause:t})}}}function em({text:e,schema:t}){try{let r=ea.parse(e);if(null==t)return{success:!0,value:r};return eh({value:r,schema:t})}catch(t){return{success:!1,error:H.isInstance(t)?t:new H({text:e,cause:t})}}}function ef(e){try{return ea.parse(e),!0}catch(e){return!1}}var eg=()=>globalThis.fetch,ey=async({url:e,headers:t,body:r,failedResponseHandler:n,successfulResponseHandler:s,abortSignal:o,fetch:a})=>eb({url:e,headers:{"Content-Type":"application/json",...t},body:{content:JSON.stringify(r),values:r},failedResponseHandler:n,successfulResponseHandler:s,abortSignal:o,fetch:a}),eb=async({url:e,headers:t={},body:r,successfulResponseHandler:n,failedResponseHandler:s,abortSignal:o,fetch:a=eg()})=>{try{let l=await a(e,{method:"POST",headers:Object.fromEntries(Object.entries(t).filter(([e,t])=>null!=t)),body:r.content,signal:o}),i=eu(l);if(!l.ok){let t;try{t=await s({response:l,url:e,requestBodyValues:r.values})}catch(t){if(ep(t)||w.isInstance(t))throw t;throw new w({message:"Failed to process error response",cause:t,statusCode:l.status,url:e,responseHeaders:i,requestBodyValues:r.values})}throw t.value}try{return await n({response:l,url:e,requestBodyValues:r.values})}catch(t){if(t instanceof Error&&(ep(t)||w.isInstance(t)))throw t;throw new w({message:"Failed to process successful response",cause:t,statusCode:l.status,url:e,responseHeaders:i,requestBodyValues:r.values})}}catch(t){if(ep(t))throw t;if(t instanceof TypeError&&"fetch failed"===t.message){let n=t.cause;if(null!=n)throw new w({message:`Cannot connect to API: ${n.message}`,cause:n,url:e,requestBodyValues:r.values,isRetryable:!0})}throw t}},ev=({errorSchema:e,errorToMessage:t,isRetryable:r})=>async({response:n,url:s,requestBodyValues:o})=>{let a=await n.text(),l=eu(n);if(""===a.trim())return{responseHeaders:l,value:new w({message:n.statusText,url:s,requestBodyValues:o,statusCode:n.status,responseHeaders:l,responseBody:a,isRetryable:null==r?void 0:r(n)})};try{let i=function({text:e,schema:t}){try{let r=ea.parse(e);if(null==t)return r;return function({value:e,schema:t}){let r=eh({value:e,schema:t});if(!r.success)throw X.wrap({value:e,cause:r.error});return r.value}({value:r,schema:t})}catch(t){if(H.isInstance(t)||X.isInstance(t))throw t;throw new H({text:e,cause:t})}}({text:a,schema:e});return{responseHeaders:l,value:new w({message:t(i),url:s,requestBodyValues:o,statusCode:n.status,responseHeaders:l,responseBody:a,data:i,isRetryable:null==r?void 0:r(n,i)})}}catch(e){return{responseHeaders:l,value:new w({message:n.statusText,url:s,requestBodyValues:o,statusCode:n.status,responseHeaders:l,responseBody:a,isRetryable:null==r?void 0:r(n)})}}},ew=e=>async({response:t})=>{let r=eu(t);if(null==t.body)throw new x({});return{responseHeaders:r,value:t.body.pipeThrough(new TextDecoderStream).pipeThrough(new el.Z).pipeThrough(new TransformStream({transform({data:t},r){"[DONE]"!==t&&r.enqueue(em({text:t,schema:e}))}}))}},ez=e=>async({response:t,url:r,requestBodyValues:n})=>{let s=await t.text(),o=em({text:s,schema:e}),a=eu(t);if(!o.success)throw new w({message:"Invalid JSON response",cause:o.error,statusCode:t.status,responseHeaders:a,responseBody:s,url:r,requestBodyValues:n});return{responseHeaders:a,value:o.value}},{btoa:e_,atob:ek}=globalThis,ex=r(12206);function ej(e){var t,r;return null!=(r=null==(t=null==e?void 0:e.providerMetadata)?void 0:t.openaiCompatible)?r:{}}function eS({id:e,model:t,created:r}){return{id:null!=e?e:void 0,modelId:null!=t?t:void 0,timestamp:null!=r?new Date(1e3*r):void 0}}function eI(e){switch(e){case"stop":return"stop";case"length":return"length";case"content_filter":return"content-filter";case"function_call":case"tool_calls":return"tool-calls";default:return"unknown"}}var eE={errorSchema:ex.z.object({error:ex.z.object({message:ex.z.string(),type:ex.z.string().nullish(),param:ex.z.any().nullish(),code:ex.z.union([ex.z.string(),ex.z.number()]).nullish()})}),errorToMessage:e=>e.error.message},eT=class{constructor(e,t,r){var n,s;this.specificationVersion="v1",this.modelId=e,this.settings=t,this.config=r;let o=null!=(n=r.errorStructure)?n:eE;this.chunkSchema=eN(o.errorSchema),this.failedResponseHandler=ev(o),this.supportsStructuredOutputs=null!=(s=r.supportsStructuredOutputs)&&s}get defaultObjectGenerationMode(){return this.config.defaultObjectGenerationMode}get provider(){return this.config.provider}getArgs({mode:e,prompt:t,maxTokens:r,temperature:n,topP:s,topK:o,frequencyPenalty:a,presencePenalty:l,stopSequences:i,responseFormat:u,seed:c}){var p,d;let h=e.type,m=[];null!=o&&m.push({type:"unsupported-setting",setting:"topK"}),(null==u?void 0:u.type)!=="json"||null==u.schema||this.supportsStructuredOutputs||m.push({type:"unsupported-setting",setting:"responseFormat",details:"JSON response format schema is only supported with structuredOutputs"});let f={model:this.modelId,user:this.settings.user,max_tokens:r,temperature:n,top_p:s,frequency_penalty:a,presence_penalty:l,response_format:(null==u?void 0:u.type)==="json"?!0===this.supportsStructuredOutputs&&null!=u.schema?{type:"json_schema",json_schema:{schema:u.schema,name:null!=(p=u.name)?p:"response",description:u.description}}:{type:"json_object"}:void 0,stop:i,seed:c,messages:function(e){let t=[];for(let{role:r,content:n,...s}of e){let e=ej({...s});switch(r){case"system":t.push({role:"system",content:n,...e});break;case"user":if(1===n.length&&"text"===n[0].type){t.push({role:"user",content:n[0].text,...ej(n[0])});break}t.push({role:"user",content:n.map(e=>{var t;let r=ej(e);switch(e.type){case"text":return{type:"text",text:e.text,...r};case"image":return{type:"image_url",image_url:{url:e.image instanceof URL?e.image.toString():`data:${null!=(t=e.mimeType)?t:"image/jpeg"};base64,${function(e){let t="";for(let r=0;r<e.length;r++)t+=String.fromCodePoint(e[r]);return e_(t)}(e.image)}`},...r};case"file":throw new en({functionality:"File content parts in user messages"})}}),...e});break;case"assistant":{let r="",s=[];for(let e of n){let t=ej(e);switch(e.type){case"text":r+=e.text;break;case"tool-call":s.push({id:e.toolCallId,type:"function",function:{name:e.toolName,arguments:JSON.stringify(e.args)},...t});break;default:throw Error(`Unsupported part: ${e}`)}}t.push({role:"assistant",content:r,tool_calls:s.length>0?s:void 0,...e});break}case"tool":for(let e of n){let r=ej(e);t.push({role:"tool",tool_call_id:e.toolCallId,content:JSON.stringify(e.result),...r})}break;default:throw Error(`Unsupported role: ${r}`)}}return t}(t)};switch(h){case"regular":{let{tools:t,tool_choice:r,toolWarnings:n}=function({mode:e,structuredOutputs:t}){var r;let n=(null==(r=e.tools)?void 0:r.length)?e.tools:void 0,s=[];if(null==n)return{tools:void 0,tool_choice:void 0,toolWarnings:s};let o=e.toolChoice,a=[];for(let e of n)"provider-defined"===e.type?s.push({type:"unsupported-tool",tool:e}):a.push({type:"function",function:{name:e.name,description:e.description,parameters:e.parameters}});if(null==o)return{tools:a,tool_choice:void 0,toolWarnings:s};let l=o.type;switch(l){case"auto":case"none":case"required":return{tools:a,tool_choice:l,toolWarnings:s};case"tool":return{tools:a,tool_choice:{type:"function",function:{name:o.toolName}},toolWarnings:s};default:throw new en({functionality:`Unsupported tool choice type: ${l}`})}}({mode:e,structuredOutputs:this.supportsStructuredOutputs});return{args:{...f,tools:t,tool_choice:r},warnings:[...m,...n]}}case"object-json":return{args:{...f,response_format:!0===this.supportsStructuredOutputs&&null!=e.schema?{type:"json_schema",json_schema:{schema:e.schema,name:null!=(d=e.name)?d:"response",description:e.description}}:{type:"json_object"}},warnings:m};case"object-tool":return{args:{...f,tool_choice:{type:"function",function:{name:e.tool.name}},tools:[{type:"function",function:{name:e.tool.name,description:e.tool.description,parameters:e.tool.parameters}}]},warnings:m};default:throw Error(`Unsupported type: ${h}`)}}async doGenerate(e){var t,r,n,s,o,a;let{args:l,warnings:i}=this.getArgs({...e}),u=JSON.stringify(l),{responseHeaders:c,value:p}=await ey({url:this.config.url({path:"/chat/completions",modelId:this.modelId}),headers:ei(this.config.headers(),e.headers),body:l,failedResponseHandler:this.failedResponseHandler,successfulResponseHandler:ez(eC),abortSignal:e.abortSignal,fetch:this.config.fetch}),{messages:d,...h}=l,m=p.choices[0];return{text:null!=(t=m.message.content)?t:void 0,toolCalls:null==(r=m.message.tool_calls)?void 0:r.map(e=>{var t;return{toolCallType:"function",toolCallId:null!=(t=e.id)?t:ec(),toolName:e.function.name,args:e.function.arguments}}),finishReason:eI(m.finish_reason),usage:{promptTokens:null!=(s=null==(n=p.usage)?void 0:n.prompt_tokens)?s:NaN,completionTokens:null!=(a=null==(o=p.usage)?void 0:o.completion_tokens)?a:NaN},rawCall:{rawPrompt:d,rawSettings:h},rawResponse:{headers:c},response:eS(p),warnings:i,request:{body:u}}}async doStream(e){let t;if(this.settings.simulateStreaming){let t=await this.doGenerate(e);return{stream:new ReadableStream({start(e){if(e.enqueue({type:"response-metadata",...t.response}),t.text&&e.enqueue({type:"text-delta",textDelta:t.text}),t.toolCalls)for(let r of t.toolCalls)e.enqueue({type:"tool-call",...r});e.enqueue({type:"finish",finishReason:t.finishReason,usage:t.usage,logprobs:t.logprobs,providerMetadata:t.providerMetadata}),e.close()}}),rawCall:t.rawCall,rawResponse:t.rawResponse,warnings:t.warnings}}let{args:r,warnings:n}=this.getArgs({...e}),s=JSON.stringify({...r,stream:!0}),{responseHeaders:o,value:a}=await ey({url:this.config.url({path:"/chat/completions",modelId:this.modelId}),headers:ei(this.config.headers(),e.headers),body:{...r,stream:!0},failedResponseHandler:this.failedResponseHandler,successfulResponseHandler:ew(this.chunkSchema),abortSignal:e.abortSignal,fetch:this.config.fetch}),{messages:l,...i}=r,u=[],c="unknown",p={promptTokens:void 0,completionTokens:void 0},d=!0;return{stream:a.pipeThrough(new TransformStream({transform(e,t){var r,n,s,o,a,l,i,h,m,f,g,y,b,v;if(!e.success){c="error",t.enqueue({type:"error",error:e.error});return}let w=e.value;if("error"in w){c="error",t.enqueue({type:"error",error:w.error.message});return}d&&(d=!1,t.enqueue({type:"response-metadata",...eS(w)})),null!=w.usage&&(p={promptTokens:null!=(r=w.usage.prompt_tokens)?r:void 0,completionTokens:null!=(n=w.usage.completion_tokens)?n:void 0});let z=w.choices[0];if((null==z?void 0:z.finish_reason)!=null&&(c=eI(z.finish_reason)),(null==z?void 0:z.delta)==null)return;let _=z.delta;if(null!=_.content&&t.enqueue({type:"text-delta",textDelta:_.content}),null!=_.tool_calls)for(let e of _.tool_calls){let r=e.index;if(null==u[r]){if("function"!==e.type)throw new M({data:e,message:"Expected 'function' type."});if(null==e.id)throw new M({data:e,message:"Expected 'id' to be a string."});if((null==(s=e.function)?void 0:s.name)==null)throw new M({data:e,message:"Expected 'function.name' to be a string."});u[r]={id:e.id,type:"function",function:{name:e.function.name,arguments:null!=(o=e.function.arguments)?o:""},hasFinished:!1};let n=u[r];(null==(a=n.function)?void 0:a.name)!=null&&(null==(l=n.function)?void 0:l.arguments)!=null&&(n.function.arguments.length>0&&t.enqueue({type:"tool-call-delta",toolCallType:"function",toolCallId:n.id,toolName:n.function.name,argsTextDelta:n.function.arguments}),ef(n.function.arguments)&&(t.enqueue({type:"tool-call",toolCallType:"function",toolCallId:null!=(i=n.id)?i:ec(),toolName:n.function.name,args:n.function.arguments}),n.hasFinished=!0));continue}let n=u[r];!n.hasFinished&&((null==(h=e.function)?void 0:h.arguments)!=null&&(n.function.arguments+=null!=(f=null==(m=e.function)?void 0:m.arguments)?f:""),t.enqueue({type:"tool-call-delta",toolCallType:"function",toolCallId:n.id,toolName:n.function.name,argsTextDelta:null!=(g=e.function.arguments)?g:""}),(null==(y=n.function)?void 0:y.name)!=null&&(null==(b=n.function)?void 0:b.arguments)!=null&&ef(n.function.arguments)&&(t.enqueue({type:"tool-call",toolCallType:"function",toolCallId:null!=(v=n.id)?v:ec(),toolName:n.function.name,args:n.function.arguments}),n.hasFinished=!0))}},flush(e){var r,n;e.enqueue({type:"finish",finishReason:c,usage:{promptTokens:null!=(r=p.promptTokens)?r:NaN,completionTokens:null!=(n=p.completionTokens)?n:NaN},...null!=t?{providerMetadata:t}:{}})}})),rawCall:{rawPrompt:l,rawSettings:i},rawResponse:{headers:o},warnings:n,request:{body:s}}}},eC=ex.z.object({id:ex.z.string().nullish(),created:ex.z.number().nullish(),model:ex.z.string().nullish(),choices:ex.z.array(ex.z.object({message:ex.z.object({role:ex.z.literal("assistant").nullish(),content:ex.z.string().nullish(),tool_calls:ex.z.array(ex.z.object({id:ex.z.string().nullish(),type:ex.z.literal("function"),function:ex.z.object({name:ex.z.string(),arguments:ex.z.string()})})).nullish()}),finish_reason:ex.z.string().nullish()})),usage:ex.z.object({prompt_tokens:ex.z.number().nullish(),completion_tokens:ex.z.number().nullish()}).nullish()}),eN=e=>ex.z.union([ex.z.object({id:ex.z.string().nullish(),created:ex.z.number().nullish(),model:ex.z.string().nullish(),choices:ex.z.array(ex.z.object({delta:ex.z.object({role:ex.z.enum(["assistant"]).nullish(),content:ex.z.string().nullish(),tool_calls:ex.z.array(ex.z.object({index:ex.z.number(),id:ex.z.string().nullish(),type:ex.z.literal("function").optional(),function:ex.z.object({name:ex.z.string().nullish(),arguments:ex.z.string().nullish()})})).nullish()}).nullish(),finish_reason:ex.z.string().nullish()})),usage:ex.z.object({prompt_tokens:ex.z.number().nullish(),completion_tokens:ex.z.number().nullish()}).nullish()}),e]),e$=class{constructor(e,t,r){var n;this.specificationVersion="v1",this.defaultObjectGenerationMode=void 0,this.modelId=e,this.settings=t,this.config=r;let s=null!=(n=r.errorStructure)?n:eE;this.chunkSchema=eR(s.errorSchema),this.failedResponseHandler=ev(s)}get provider(){return this.config.provider}getArgs({mode:e,inputFormat:t,prompt:r,maxTokens:n,temperature:s,topP:o,topK:a,frequencyPenalty:l,presencePenalty:i,stopSequences:u,responseFormat:c,seed:p}){var d;let h=e.type,m=[];null!=a&&m.push({type:"unsupported-setting",setting:"topK"}),null!=c&&"text"!==c.type&&m.push({type:"unsupported-setting",setting:"responseFormat",details:"JSON response format is not supported."});let{prompt:f,stopSequences:g}=function({prompt:e,inputFormat:t,user:r="user",assistant:n="assistant"}){if("prompt"===t&&1===e.length&&"user"===e[0].role&&1===e[0].content.length&&"text"===e[0].content[0].type)return{prompt:e[0].content[0].text};let s="";for(let{role:t,content:o}of("system"===e[0].role&&(s+=`${e[0].content}

`,e=e.slice(1)),e))switch(t){case"system":throw new O({message:"Unexpected system message in prompt: ${content}",prompt:e});case"user":{let e=o.map(e=>{switch(e.type){case"text":return e.text;case"image":throw new en({functionality:"images"})}}).join("");s+=`${r}:
${e}

`;break}case"assistant":{let e=o.map(e=>{switch(e.type){case"text":return e.text;case"tool-call":throw new en({functionality:"tool-call messages"})}}).join("");s+=`${n}:
${e}

`;break}case"tool":throw new en({functionality:"tool messages"});default:throw Error(`Unsupported role: ${t}`)}return{prompt:s+=`${n}:
`,stopSequences:[`
${r}:`]}}({prompt:r,inputFormat:t}),y=[...null!=g?g:[],...null!=u?u:[]],b={model:this.modelId,echo:this.settings.echo,logit_bias:this.settings.logitBias,suffix:this.settings.suffix,user:this.settings.user,max_tokens:n,temperature:s,top_p:o,frequency_penalty:l,presence_penalty:i,seed:p,prompt:f,stop:y.length>0?y:void 0};switch(h){case"regular":if(null==(d=e.tools)?void 0:d.length)throw new en({functionality:"tools"});if(e.toolChoice)throw new en({functionality:"toolChoice"});return{args:b,warnings:m};case"object-json":throw new en({functionality:"object-json mode"});case"object-tool":throw new en({functionality:"object-tool mode"});default:throw Error(`Unsupported type: ${h}`)}}async doGenerate(e){var t,r,n,s;let{args:o,warnings:a}=this.getArgs(e),{responseHeaders:l,value:i}=await ey({url:this.config.url({path:"/completions",modelId:this.modelId}),headers:ei(this.config.headers(),e.headers),body:o,failedResponseHandler:this.failedResponseHandler,successfulResponseHandler:ez(eO),abortSignal:e.abortSignal,fetch:this.config.fetch}),{prompt:u,...c}=o,p=i.choices[0];return{text:p.text,usage:{promptTokens:null!=(r=null==(t=i.usage)?void 0:t.prompt_tokens)?r:NaN,completionTokens:null!=(s=null==(n=i.usage)?void 0:n.completion_tokens)?s:NaN},finishReason:eI(p.finish_reason),rawCall:{rawPrompt:u,rawSettings:c},rawResponse:{headers:l},response:eS(i),warnings:a,request:{body:JSON.stringify(o)}}}async doStream(e){let{args:t,warnings:r}=this.getArgs(e),n={...t,stream:!0},{responseHeaders:s,value:o}=await ey({url:this.config.url({path:"/completions",modelId:this.modelId}),headers:ei(this.config.headers(),e.headers),body:n,failedResponseHandler:this.failedResponseHandler,successfulResponseHandler:ew(this.chunkSchema),abortSignal:e.abortSignal,fetch:this.config.fetch}),{prompt:a,...l}=t,i="unknown",u={promptTokens:Number.NaN,completionTokens:Number.NaN},c=!0;return{stream:o.pipeThrough(new TransformStream({transform(e,t){if(!e.success){i="error",t.enqueue({type:"error",error:e.error});return}let r=e.value;if("error"in r){i="error",t.enqueue({type:"error",error:r.error});return}c&&(c=!1,t.enqueue({type:"response-metadata",...eS(r)})),null!=r.usage&&(u={promptTokens:r.usage.prompt_tokens,completionTokens:r.usage.completion_tokens});let n=r.choices[0];(null==n?void 0:n.finish_reason)!=null&&(i=eI(n.finish_reason)),(null==n?void 0:n.text)!=null&&t.enqueue({type:"text-delta",textDelta:n.text})},flush(e){e.enqueue({type:"finish",finishReason:i,usage:u})}})),rawCall:{rawPrompt:a,rawSettings:l},rawResponse:{headers:s},warnings:r,request:{body:JSON.stringify(n)}}}},eO=ex.z.object({id:ex.z.string().nullish(),created:ex.z.number().nullish(),model:ex.z.string().nullish(),choices:ex.z.array(ex.z.object({text:ex.z.string(),finish_reason:ex.z.string()})),usage:ex.z.object({prompt_tokens:ex.z.number(),completion_tokens:ex.z.number()}).nullish()}),eR=e=>ex.z.union([ex.z.object({id:ex.z.string().nullish(),created:ex.z.number().nullish(),model:ex.z.string().nullish(),choices:ex.z.array(ex.z.object({text:ex.z.string(),finish_reason:ex.z.string().nullish(),index:ex.z.number()})),usage:ex.z.object({prompt_tokens:ex.z.number(),completion_tokens:ex.z.number()}).nullish()}),e]),eq=class{constructor(e,t,r){this.specificationVersion="v1",this.modelId=e,this.settings=t,this.config=r}get provider(){return this.config.provider}get maxEmbeddingsPerCall(){var e;return null!=(e=this.config.maxEmbeddingsPerCall)?e:2048}get supportsParallelCalls(){var e;return null==(e=this.config.supportsParallelCalls)||e}async doEmbed({values:e,headers:t,abortSignal:r}){var n;if(e.length>this.maxEmbeddingsPerCall)throw new Z({provider:this.provider,modelId:this.modelId,maxEmbeddingsPerCall:this.maxEmbeddingsPerCall,values:e});let{responseHeaders:s,value:o}=await ey({url:this.config.url({path:"/embeddings",modelId:this.modelId}),headers:ei(this.config.headers(),t),body:{model:this.modelId,input:e,encoding_format:"float",dimensions:this.settings.dimensions,user:this.settings.user},failedResponseHandler:ev(null!=(n=this.config.errorStructure)?n:eE),successfulResponseHandler:ez(eA),abortSignal:r,fetch:this.config.fetch});return{embeddings:o.data.map(e=>e.embedding),usage:o.usage?{tokens:o.usage.prompt_tokens}:void 0,rawResponse:{headers:s}}}},eA=ex.z.object({data:ex.z.array(ex.z.object({embedding:ex.z.array(ex.z.number())})),usage:ex.z.object({prompt_tokens:ex.z.number()}).nullish()});function eM(e){var t;if(!e.baseURL)throw Error("Base URL is required");let r=null==(t=e.baseURL)?void 0:t.replace(/\/$/,"");if(!e.name)throw Error("Provider name is required");let n=e.name,s=()=>({...e.apiKey&&{Authorization:`Bearer ${e.apiKey}`},...e.headers}),o=t=>({provider:`${n}.${t}`,url:({path:t})=>{let n=new URL(`${r}${t}`);return e.queryParams&&(n.search=new URLSearchParams(e.queryParams).toString()),n.toString()},headers:s,fetch:e.fetch}),a=(e,t={})=>l(e,t),l=(e,t={})=>new eT(e,t,{...o("chat"),defaultObjectGenerationMode:"tool"}),i=(e,t)=>a(e,t);return i.languageModel=a,i.chatModel=l,i.completionModel=(e,t={})=>new e$(e,t,o("completion")),i.textEmbeddingModel=(e,t={})=>new eq(e,t,o("embedding")),i}},75399:(e,t,r)=>{r.d(t,{Z:()=>z});var n=r(74306),s=r(86230),o=r(12206);function a(e){var t,r;return null!=(r=null==(t=null==e?void 0:e.providerMetadata)?void 0:t.openaiCompatible)?r:{}}function l({id:e,model:t,created:r}){return{id:null!=e?e:void 0,modelId:null!=t?t:void 0,timestamp:null!=r?new Date(1e3*r):void 0}}function i(e){switch(e){case"stop":return"stop";case"length":return"length";case"content_filter":return"content-filter";case"function_call":case"tool_calls":return"tool-calls";default:return"unknown"}}var u={errorSchema:o.z.object({error:o.z.object({message:o.z.string(),type:o.z.string().nullish(),param:o.z.any().nullish(),code:o.z.union([o.z.string(),o.z.number()]).nullish()})}),errorToMessage:e=>e.error.message},c=class{constructor(e,t,r){var n,o;this.specificationVersion="v1",this.modelId=e,this.settings=t,this.config=r;let a=null!=(n=r.errorStructure)?n:u;this.chunkSchema=d(a.errorSchema),this.failedResponseHandler=(0,s.sl)(a),this.supportsStructuredOutputs=null!=(o=r.supportsStructuredOutputs)&&o}get defaultObjectGenerationMode(){return this.config.defaultObjectGenerationMode}get provider(){return this.config.provider}get providerOptionsName(){return this.config.provider.split(".")[0].trim()}getArgs({mode:e,prompt:t,maxTokens:r,temperature:o,topP:l,topK:i,frequencyPenalty:u,presencePenalty:c,providerMetadata:p,stopSequences:d,responseFormat:h,seed:m}){var f,g;let y=e.type,b=[];null!=i&&b.push({type:"unsupported-setting",setting:"topK"}),(null==h?void 0:h.type)!=="json"||null==h.schema||this.supportsStructuredOutputs||b.push({type:"unsupported-setting",setting:"responseFormat",details:"JSON response format schema is only supported with structuredOutputs"});let v={model:this.modelId,user:this.settings.user,max_tokens:r,temperature:o,top_p:l,frequency_penalty:u,presence_penalty:c,response_format:(null==h?void 0:h.type)==="json"?!0===this.supportsStructuredOutputs&&null!=h.schema?{type:"json_schema",json_schema:{schema:h.schema,name:null!=(f=h.name)?f:"response",description:h.description}}:{type:"json_object"}:void 0,stop:d,seed:m,...null==p?void 0:p[this.providerOptionsName],messages:function(e){let t=[];for(let{role:r,content:o,...l}of e){let e=a({...l});switch(r){case"system":t.push({role:"system",content:o,...e});break;case"user":if(1===o.length&&"text"===o[0].type){t.push({role:"user",content:o[0].text,...a(o[0])});break}t.push({role:"user",content:o.map(e=>{var t;let r=a(e);switch(e.type){case"text":return{type:"text",text:e.text,...r};case"image":return{type:"image_url",image_url:{url:e.image instanceof URL?e.image.toString():`data:${null!=(t=e.mimeType)?t:"image/jpeg"};base64,${(0,s.n_)(e.image)}`},...r};case"file":throw new n.b8({functionality:"File content parts in user messages"})}}),...e});break;case"assistant":{let r="",n=[];for(let e of o){let t=a(e);switch(e.type){case"text":r+=e.text;break;case"tool-call":n.push({id:e.toolCallId,type:"function",function:{name:e.toolName,arguments:JSON.stringify(e.args)},...t})}}t.push({role:"assistant",content:r,tool_calls:n.length>0?n:void 0,...e});break}case"tool":for(let e of o){let r=a(e);t.push({role:"tool",tool_call_id:e.toolCallId,content:JSON.stringify(e.result),...r})}break;default:throw Error(`Unsupported role: ${r}`)}}return t}(t)};switch(y){case"regular":{let{tools:t,tool_choice:r,toolWarnings:s}=function({mode:e,structuredOutputs:t}){var r;let s=(null==(r=e.tools)?void 0:r.length)?e.tools:void 0,o=[];if(null==s)return{tools:void 0,tool_choice:void 0,toolWarnings:o};let a=e.toolChoice,l=[];for(let e of s)"provider-defined"===e.type?o.push({type:"unsupported-tool",tool:e}):l.push({type:"function",function:{name:e.name,description:e.description,parameters:e.parameters}});if(null==a)return{tools:l,tool_choice:void 0,toolWarnings:o};let i=a.type;switch(i){case"auto":case"none":case"required":return{tools:l,tool_choice:i,toolWarnings:o};case"tool":return{tools:l,tool_choice:{type:"function",function:{name:a.toolName}},toolWarnings:o};default:throw new n.b8({functionality:`Unsupported tool choice type: ${i}`})}}({mode:e,structuredOutputs:this.supportsStructuredOutputs});return{args:{...v,tools:t,tool_choice:r},warnings:[...b,...s]}}case"object-json":return{args:{...v,response_format:!0===this.supportsStructuredOutputs&&null!=e.schema?{type:"json_schema",json_schema:{schema:e.schema,name:null!=(g=e.name)?g:"response",description:e.description}}:{type:"json_object"}},warnings:b};case"object-tool":return{args:{...v,tool_choice:{type:"function",function:{name:e.tool.name}},tools:[{type:"function",function:{name:e.tool.name,description:e.tool.description,parameters:e.tool.parameters}}]},warnings:b};default:throw Error(`Unsupported type: ${y}`)}}async doGenerate(e){var t,r,n,o,a,u,c,d,h;let{args:m,warnings:f}=this.getArgs({...e}),g=JSON.stringify(m),{responseHeaders:y,value:b,rawValue:v}=await (0,s.GU)({url:this.config.url({path:"/chat/completions",modelId:this.modelId}),headers:(0,s.m2)(this.config.headers(),e.headers),body:m,failedResponseHandler:this.failedResponseHandler,successfulResponseHandler:(0,s.cV)(p),abortSignal:e.abortSignal,fetch:this.config.fetch}),{messages:w,...z}=m,_=b.choices[0],k=null==(r=null==(t=this.config.metadataExtractor)?void 0:t.extractMetadata)?void 0:r.call(t,{parsedBody:v});return{text:null!=(n=_.message.content)?n:void 0,reasoning:null!=(o=_.message.reasoning_content)?o:void 0,toolCalls:null==(a=_.message.tool_calls)?void 0:a.map(e=>{var t;return{toolCallType:"function",toolCallId:null!=(t=e.id)?t:(0,s.$C)(),toolName:e.function.name,args:e.function.arguments}}),finishReason:i(_.finish_reason),usage:{promptTokens:null!=(c=null==(u=b.usage)?void 0:u.prompt_tokens)?c:NaN,completionTokens:null!=(h=null==(d=b.usage)?void 0:d.completion_tokens)?h:NaN},...k&&{providerMetadata:k},rawCall:{rawPrompt:w,rawSettings:z},rawResponse:{headers:y,body:v},response:l(b),warnings:f,request:{body:g}}}async doStream(e){var t;if(this.settings.simulateStreaming){let t=await this.doGenerate(e);return{stream:new ReadableStream({start(e){if(e.enqueue({type:"response-metadata",...t.response}),t.reasoning){if(Array.isArray(t.reasoning))for(let r of t.reasoning)"text"===r.type&&e.enqueue({type:"reasoning",textDelta:r.text});else e.enqueue({type:"reasoning",textDelta:t.reasoning})}if(t.text&&e.enqueue({type:"text-delta",textDelta:t.text}),t.toolCalls)for(let r of t.toolCalls)e.enqueue({type:"tool-call",...r});e.enqueue({type:"finish",finishReason:t.finishReason,usage:t.usage,logprobs:t.logprobs,providerMetadata:t.providerMetadata}),e.close()}}),rawCall:t.rawCall,rawResponse:t.rawResponse,warnings:t.warnings}}let{args:r,warnings:o}=this.getArgs({...e}),a=JSON.stringify({...r,stream:!0}),u=null==(t=this.config.metadataExtractor)?void 0:t.createStreamExtractor(),{responseHeaders:c,value:p}=await (0,s.GU)({url:this.config.url({path:"/chat/completions",modelId:this.modelId}),headers:(0,s.m2)(this.config.headers(),e.headers),body:{...r,stream:!0},failedResponseHandler:this.failedResponseHandler,successfulResponseHandler:(0,s.Ds)(this.chunkSchema),abortSignal:e.abortSignal,fetch:this.config.fetch}),{messages:d,...h}=r,m=[],f="unknown",g={promptTokens:void 0,completionTokens:void 0},y=!0;return{stream:p.pipeThrough(new TransformStream({transform(e,t){var r,o,a,c,p,d,h,b,v,w,z,_,k,x;if(!e.success){f="error",t.enqueue({type:"error",error:e.error});return}let j=e.value;if(null==u||u.processChunk(e.rawValue),"error"in j){f="error",t.enqueue({type:"error",error:j.error.message});return}y&&(y=!1,t.enqueue({type:"response-metadata",...l(j)})),null!=j.usage&&(g={promptTokens:null!=(r=j.usage.prompt_tokens)?r:void 0,completionTokens:null!=(o=j.usage.completion_tokens)?o:void 0});let S=j.choices[0];if((null==S?void 0:S.finish_reason)!=null&&(f=i(S.finish_reason)),(null==S?void 0:S.delta)==null)return;let I=S.delta;if(null!=I.reasoning_content&&t.enqueue({type:"reasoning",textDelta:I.reasoning_content}),null!=I.content&&t.enqueue({type:"text-delta",textDelta:I.content}),null!=I.tool_calls)for(let e of I.tool_calls){let r=e.index;if(null==m[r]){if("function"!==e.type)throw new n.xn({data:e,message:"Expected 'function' type."});if(null==e.id)throw new n.xn({data:e,message:"Expected 'id' to be a string."});if((null==(a=e.function)?void 0:a.name)==null)throw new n.xn({data:e,message:"Expected 'function.name' to be a string."});m[r]={id:e.id,type:"function",function:{name:e.function.name,arguments:null!=(c=e.function.arguments)?c:""},hasFinished:!1};let o=m[r];(null==(p=o.function)?void 0:p.name)!=null&&(null==(d=o.function)?void 0:d.arguments)!=null&&(o.function.arguments.length>0&&t.enqueue({type:"tool-call-delta",toolCallType:"function",toolCallId:o.id,toolName:o.function.name,argsTextDelta:o.function.arguments}),(0,s.v0)(o.function.arguments)&&(t.enqueue({type:"tool-call",toolCallType:"function",toolCallId:null!=(h=o.id)?h:(0,s.$C)(),toolName:o.function.name,args:o.function.arguments}),o.hasFinished=!0));continue}let o=m[r];!o.hasFinished&&((null==(b=e.function)?void 0:b.arguments)!=null&&(o.function.arguments+=null!=(w=null==(v=e.function)?void 0:v.arguments)?w:""),t.enqueue({type:"tool-call-delta",toolCallType:"function",toolCallId:o.id,toolName:o.function.name,argsTextDelta:null!=(z=e.function.arguments)?z:""}),(null==(_=o.function)?void 0:_.name)!=null&&(null==(k=o.function)?void 0:k.arguments)!=null&&(0,s.v0)(o.function.arguments)&&(t.enqueue({type:"tool-call",toolCallType:"function",toolCallId:null!=(x=o.id)?x:(0,s.$C)(),toolName:o.function.name,args:o.function.arguments}),o.hasFinished=!0))}},flush(e){var t,r;let n=null==u?void 0:u.buildMetadata();e.enqueue({type:"finish",finishReason:f,usage:{promptTokens:null!=(t=g.promptTokens)?t:NaN,completionTokens:null!=(r=g.completionTokens)?r:NaN},...n&&{providerMetadata:n}})}})),rawCall:{rawPrompt:d,rawSettings:h},rawResponse:{headers:c},warnings:o,request:{body:a}}}},p=o.z.object({id:o.z.string().nullish(),created:o.z.number().nullish(),model:o.z.string().nullish(),choices:o.z.array(o.z.object({message:o.z.object({role:o.z.literal("assistant").nullish(),content:o.z.string().nullish(),reasoning_content:o.z.string().nullish(),tool_calls:o.z.array(o.z.object({id:o.z.string().nullish(),type:o.z.literal("function"),function:o.z.object({name:o.z.string(),arguments:o.z.string()})})).nullish()}),finish_reason:o.z.string().nullish()})),usage:o.z.object({prompt_tokens:o.z.number().nullish(),completion_tokens:o.z.number().nullish()}).nullish()}),d=e=>o.z.union([o.z.object({id:o.z.string().nullish(),created:o.z.number().nullish(),model:o.z.string().nullish(),choices:o.z.array(o.z.object({delta:o.z.object({role:o.z.enum(["assistant"]).nullish(),content:o.z.string().nullish(),reasoning_content:o.z.string().nullish(),tool_calls:o.z.array(o.z.object({index:o.z.number(),id:o.z.string().nullish(),type:o.z.literal("function").optional(),function:o.z.object({name:o.z.string().nullish(),arguments:o.z.string().nullish()})})).nullish()}).nullish(),finish_reason:o.z.string().nullish()})),usage:o.z.object({prompt_tokens:o.z.number().nullish(),completion_tokens:o.z.number().nullish()}).nullish()}),e]),h=o.z.object({id:o.z.string().nullish(),created:o.z.number().nullish(),model:o.z.string().nullish(),choices:o.z.array(o.z.object({text:o.z.string(),finish_reason:o.z.string()})),usage:o.z.object({prompt_tokens:o.z.number(),completion_tokens:o.z.number()}).nullish()}),m=e=>z3.union([z3.object({id:z3.string().nullish(),created:z3.number().nullish(),model:z3.string().nullish(),choices:z3.array(z3.object({text:z3.string(),finish_reason:z3.string().nullish(),index:z3.number()})),usage:z3.object({prompt_tokens:z3.number(),completion_tokens:z3.number()}).nullish()}),e]),f=o.z.object({data:o.z.array(o.z.object({embedding:o.z.array(o.z.number())})),usage:o.z.object({prompt_tokens:o.z.number()}).nullish()}),g=e=>{var t,r;return null==e?void 0:{deepseek:{promptCacheHitTokens:null!=(t=e.prompt_cache_hit_tokens)?t:NaN,promptCacheMissTokens:null!=(r=e.prompt_cache_miss_tokens)?r:NaN}}},y={extractMetadata:({parsedBody:e})=>{let t=(0,s.ZZ)({value:e,schema:v});return t.success&&null!=t.value.usage?g(t.value.usage):void 0},createStreamExtractor:()=>{let e;return{processChunk:t=>{var r,n;let o=(0,s.ZZ)({value:t,schema:w});o.success&&(null==(n=null==(r=o.value.choices)?void 0:r[0])?void 0:n.finish_reason)==="stop"&&o.value.usage&&(e=o.value.usage)},buildMetadata:()=>g(e)}}},b=o.z.object({prompt_cache_hit_tokens:o.z.number().nullish(),prompt_cache_miss_tokens:o.z.number().nullish()}),v=o.z.object({usage:b.nullish()}),w=o.z.object({choices:o.z.array(o.z.object({finish_reason:o.z.string().nullish()})).nullish(),usage:b.nullish()}),z=function(e={}){var t;let r=(0,s.ae)(null!=(t=e.baseURL)?t:"https://api.deepseek.com/v1"),o=()=>({Authorization:`Bearer ${(0,s.WL)({apiKey:e.apiKey,environmentVariableName:"DEEPSEEK_API_KEY",description:"DeepSeek API key"})}`,...e.headers}),a=(t,n={})=>new c(t,n,{provider:"deepseek.chat",url:({path:e})=>`${r}${e}`,headers:o,fetch:e.fetch,defaultObjectGenerationMode:"json",metadataExtractor:y}),l=(e,t)=>a(e,t);return l.languageModel=a,l.chat=a,l.textEmbeddingModel=e=>{throw new n.eM({modelId:e,modelType:"textEmbeddingModel"})},l}()},79341:(e,t,r)=>{e.exports=r(44870)},93797:(e,t,r)=>{r.d(t,{ER:()=>eG});var n,s,o,a,l,i,u,c,p,d,h="vercel.ai.error",m=Symbol.for(h),f=class e extends Error{constructor({name:e,message:t,cause:r}){super(t),this[n]=!0,this.name=e,this.cause=r}static isInstance(t){return e.hasMarker(t,h)}static hasMarker(e,t){let r=Symbol.for(t);return null!=e&&"object"==typeof e&&r in e&&"boolean"==typeof e[r]&&!0===e[r]}toJSON(){return{name:this.name,message:this.message}}};n=m;var g=f,y="AI_APICallError",b=`vercel.ai.error.${y}`,v=Symbol.for(b),w=class extends g{constructor({message:e,url:t,requestBodyValues:r,statusCode:n,responseHeaders:o,responseBody:a,cause:l,isRetryable:i=null!=n&&(408===n||409===n||429===n||n>=500),data:u}){super({name:y,message:e,cause:l}),this[s]=!0,this.url=t,this.requestBodyValues=r,this.statusCode=n,this.responseHeaders=o,this.responseBody=a,this.isRetryable=i,this.data=u}static isInstance(e){return g.hasMarker(e,b)}static isAPICallError(e){return e instanceof Error&&e.name===y&&"string"==typeof e.url&&"object"==typeof e.requestBodyValues&&(null==e.statusCode||"number"==typeof e.statusCode)&&(null==e.responseHeaders||"object"==typeof e.responseHeaders)&&(null==e.responseBody||"string"==typeof e.responseBody)&&(null==e.cause||"object"==typeof e.cause)&&"boolean"==typeof e.isRetryable&&(null==e.data||"object"==typeof e.data)}toJSON(){return{name:this.name,message:this.message,url:this.url,requestBodyValues:this.requestBodyValues,statusCode:this.statusCode,responseHeaders:this.responseHeaders,responseBody:this.responseBody,cause:this.cause,isRetryable:this.isRetryable,data:this.data}}};s=v;var z="AI_EmptyResponseBodyError",_=`vercel.ai.error.${z}`,k=Symbol.for(_),x=class extends g{constructor({message:e="Empty response body"}={}){super({name:z,message:e}),this[o]=!0}static isInstance(e){return g.hasMarker(e,_)}static isEmptyResponseBodyError(e){return e instanceof Error&&e.name===z}};function j(e){return null==e?"unknown error":"string"==typeof e?e:e instanceof Error?e.message:JSON.stringify(e)}o=k;var S="AI_InvalidArgumentError",I=`vercel.ai.error.${S}`,E=Symbol.for(I),T=class extends g{constructor({message:e,cause:t,argument:r}){super({name:S,message:e,cause:t}),this[a]=!0,this.argument=r}static isInstance(e){return g.hasMarker(e,I)}};a=E;var C="AI_InvalidPromptError",N=`vercel.ai.error.${C}`,$=Symbol.for(N),O=class extends g{constructor({prompt:e,message:t,cause:r}){super({name:C,message:`Invalid prompt: ${t}`,cause:r}),this[l]=!0,this.prompt=e}static isInstance(e){return g.hasMarker(e,N)}static isInvalidPromptError(e){return e instanceof Error&&e.name===C&&null!=prompt}toJSON(){return{name:this.name,message:this.message,stack:this.stack,prompt:this.prompt}}};l=$;var R="AI_InvalidResponseDataError",q=`vercel.ai.error.${R}`,A=Symbol.for(q),M=class extends g{constructor({data:e,message:t=`Invalid response data: ${JSON.stringify(e)}.`}){super({name:R,message:t}),this[i]=!0,this.data=e}static isInstance(e){return g.hasMarker(e,q)}static isInvalidResponseDataError(e){return e instanceof Error&&e.name===R&&null!=e.data}toJSON(){return{name:this.name,message:this.message,stack:this.stack,data:this.data}}};i=A;var P="AI_JSONParseError",J=`vercel.ai.error.${P}`,B=Symbol.for(J),H=class extends g{constructor({text:e,cause:t}){super({name:P,message:`JSON parsing failed: Text: ${e}.
Error message: ${j(t)}`,cause:t}),this[u]=!0,this.text=e}static isInstance(e){return g.hasMarker(e,J)}static isJSONParseError(e){return e instanceof Error&&e.name===P&&"text"in e&&"string"==typeof e.text}toJSON(){return{name:this.name,message:this.message,cause:this.cause,stack:this.stack,valueText:this.text}}};u=B;var U="AI_LoadAPIKeyError",V=`vercel.ai.error.${U}`,D=Symbol.for(V),F=class extends g{constructor({message:e}){super({name:U,message:e}),this[c]=!0}static isInstance(e){return g.hasMarker(e,V)}static isLoadAPIKeyError(e){return e instanceof Error&&e.name===U}};c=D;var G=Symbol.for("vercel.ai.error.AI_LoadSettingError"),L=Symbol.for("vercel.ai.error.AI_NoContentGeneratedError"),K=Symbol.for("vercel.ai.error.AI_NoSuchModelError"),Z=Symbol.for("vercel.ai.error.AI_TooManyEmbeddingValuesForCallError"),Y="AI_TypeValidationError",W=`vercel.ai.error.${Y}`,Q=Symbol.for(W),X=class e extends g{constructor({value:e,cause:t}){super({name:Y,message:`Type validation failed: Value: ${JSON.stringify(e)}.
Error message: ${j(t)}`,cause:t}),this[p]=!0,this.value=e}static isInstance(e){return g.hasMarker(e,W)}static wrap({value:t,cause:r}){return e.isInstance(r)&&r.value===t?r:new e({value:t,cause:r})}static isTypeValidationError(e){return e instanceof Error&&e.name===Y}toJSON(){return{name:this.name,message:this.message,cause:this.cause,stack:this.stack,value:this.value}}};p=Q;var ee="AI_UnsupportedFunctionalityError",et=`vercel.ai.error.${ee}`,er=Symbol.for(et),en=class extends g{constructor({functionality:e}){super({name:ee,message:`'${e}' functionality not supported.`}),this[d]=!0,this.functionality=e}static isInstance(e){return g.hasMarker(e,et)}static isUnsupportedFunctionalityError(e){return e instanceof Error&&e.name===ee&&"string"==typeof e.functionality}toJSON(){return{name:this.name,message:this.message,stack:this.stack,functionality:this.functionality}}};function es(e){return null===e||"string"==typeof e||"number"==typeof e||"boolean"==typeof e||(Array.isArray(e)?e.every(es):"object"==typeof e&&Object.entries(e).every(([e,t])=>"string"==typeof e&&es(t)))}d=er;var eo=r(48444),ea=r(57057);let el=[239,187,191];class ei extends TransformStream{constructor(){let e;super({start(t){e=function(e){let t,r,n,s,o,a,l;return i(),{feed:function(i){var u;r=r?r+i:i,t&&(u=r,el.every((e,t)=>u.charCodeAt(t)===e))&&(r=r.slice(el.length)),t=!1;let c=r.length,p=0,d=!1;for(;p<c;){let t;d&&("\n"===r[p]&&++p,d=!1);let i=-1,u=s;for(let e=n;i<0&&e<c;++e)":"===(t=r[e])&&u<0?u=e-p:"\r"===t?(d=!0,i=e-p):"\n"===t&&(i=e-p);if(i<0){n=c-p,s=u;break}n=0,s=-1,function(t,r,n,s){if(0===s){l.length>0&&(e({type:"event",id:o,event:a||void 0,data:l.slice(0,-1)}),l="",o=void 0),a=void 0;return}let i=n<0,u=t.slice(r,r+(i?s:n)),c=0;c=i?s:" "===t[r+n+1]?n+2:n+1;let p=r+c,d=s-c,h=t.slice(p,p+d).toString();if("data"===u)l+=h?"".concat(h,"\n"):"\n";else if("event"===u)a=h;else if("id"!==u||h.includes("\0")){if("retry"===u){let t=parseInt(h,10);Number.isNaN(t)||e({type:"reconnect-interval",value:t})}}else o=h}(r,p,u,i),p+=i+1}p===c?r="":p>0&&(r=r.slice(p))},reset:i};function i(){t=!0,r="",n=0,s=-1,o=void 0,a=void 0,l=""}}(e=>{"event"===e.type&&t.enqueue(e)})},transform(t){e.feed(t)}})}}function eu(...e){return e.reduce((e,t)=>({...e,...null!=t?t:{}}),{})}function ec(e){let t={};return e.headers.forEach((e,r)=>{t[r]=e}),t}var ep=(({prefix:e,size:t=7,alphabet:r="0123456789ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz",separator:n="-"}={})=>{let s=(0,eo.d)(r,t);if(null==e)return s;if(r.includes(n))throw new T({argument:"separator",message:`The separator "${n}" must not be part of the alphabet "${r}".`});return t=>`${e}${n}${s(t)}`})();function ed(e){return e instanceof Error&&("AbortError"===e.name||"TimeoutError"===e.name)}var eh=Symbol.for("vercel.ai.validator");function em({value:e,schema:t}){var r;let n="object"==typeof t&&null!==t&&eh in t&&!0===t[eh]&&"validate"in t?t:(r=t,{[eh]:!0,validate:e=>{let t=r.safeParse(e);return t.success?{success:!0,value:t.data}:{success:!1,error:t.error}}});try{if(null==n.validate)return{success:!0,value:e};let t=n.validate(e);if(t.success)return t;return{success:!1,error:X.wrap({value:e,cause:t.error})}}catch(t){return{success:!1,error:X.wrap({value:e,cause:t})}}}function ef({text:e,schema:t}){try{let r=ea.parse(e);if(null==t)return{success:!0,value:r};return em({value:r,schema:t})}catch(t){return{success:!1,error:H.isJSONParseError(t)?t:new H({text:e,cause:t})}}}function eg(e){try{return ea.parse(e),!0}catch(e){return!1}}var ey=()=>globalThis.fetch,eb=async({url:e,headers:t,body:r,failedResponseHandler:n,successfulResponseHandler:s,abortSignal:o,fetch:a})=>ev({url:e,headers:{"Content-Type":"application/json",...t},body:{content:JSON.stringify(r),values:r},failedResponseHandler:n,successfulResponseHandler:s,abortSignal:o,fetch:a}),ev=async({url:e,headers:t={},body:r,successfulResponseHandler:n,failedResponseHandler:s,abortSignal:o,fetch:a=ey()})=>{try{let l=await a(e,{method:"POST",headers:Object.fromEntries(Object.entries(t).filter(([e,t])=>null!=t)),body:r.content,signal:o}),i=ec(l);if(!l.ok){let t;try{t=await s({response:l,url:e,requestBodyValues:r.values})}catch(t){if(ed(t)||w.isAPICallError(t))throw t;throw new w({message:"Failed to process error response",cause:t,statusCode:l.status,url:e,responseHeaders:i,requestBodyValues:r.values})}throw t.value}try{return await n({response:l,url:e,requestBodyValues:r.values})}catch(t){if(t instanceof Error&&(ed(t)||w.isAPICallError(t)))throw t;throw new w({message:"Failed to process successful response",cause:t,statusCode:l.status,url:e,responseHeaders:i,requestBodyValues:r.values})}}catch(t){if(ed(t))throw t;if(t instanceof TypeError&&"fetch failed"===t.message){let n=t.cause;if(null!=n)throw new w({message:`Cannot connect to API: ${n.message}`,cause:n,url:e,requestBodyValues:r.values,isRetryable:!0})}throw t}},ew=e=>async({response:t})=>{let r=ec(t);if(null==t.body)throw new x({});return{responseHeaders:r,value:t.body.pipeThrough(new TextDecoderStream).pipeThrough(new ei).pipeThrough(new TransformStream({transform({data:t},r){"[DONE]"!==t&&r.enqueue(ef({text:t,schema:e}))}}))}},ez=e=>async({response:t,url:r,requestBodyValues:n})=>{let s=await t.text(),o=ef({text:s,schema:e}),a=ec(t);if(!o.success)throw new w({message:"Invalid JSON response",cause:o.error,statusCode:t.status,responseHeaders:a,responseBody:s,url:r,requestBodyValues:n});return{responseHeaders:a,value:o.value}},{btoa:e_,atob:ek}=globalThis,ex=r(12206),ej=Object.defineProperty,eS=Object.defineProperties,eI=Object.getOwnPropertyDescriptors,eE=Object.getOwnPropertySymbols,eT=Object.prototype.hasOwnProperty,eC=Object.prototype.propertyIsEnumerable,eN=(e,t,r)=>t in e?ej(e,t,{enumerable:!0,configurable:!0,writable:!0,value:r}):e[t]=r,e$=(e,t)=>{for(var r in t||(t={}))eT.call(t,r)&&eN(e,r,t[r]);if(eE)for(var r of eE(t))eC.call(t,r)&&eN(e,r,t[r]);return e},eO=(e,t)=>eS(e,eI(t)),eR=(e,t)=>{var r={};for(var n in e)eT.call(e,n)&&0>t.indexOf(n)&&(r[n]=e[n]);if(null!=e&&eE)for(var n of eE(e))0>t.indexOf(n)&&eC.call(e,n)&&(r[n]=e[n]);return r};function eq(e){var t,r;return null!=(r=null==(t=null==e?void 0:e.content)?void 0:t.map(({token:e,logprob:t,top_logprobs:r})=>({token:e,logprob:t,topLogprobs:r?r.map(({token:e,logprob:t})=>({token:e,logprob:t})):[]})))?r:void 0}function eA(e){switch(e){case"stop":return"stop";case"length":return"length";case"content_filter":return"content-filter";case"function_call":case"tool_calls":return"tool-calls";default:return"unknown"}}var eM=ex.z.object({error:ex.z.object({message:ex.z.string(),type:ex.z.string(),param:ex.z.any().nullable(),code:ex.z.string().nullable()})}),eP=(({errorSchema:e,errorToMessage:t,isRetryable:r})=>async({response:n,url:s,requestBodyValues:o})=>{let a=await n.text(),l=ec(n);if(""===a.trim())return{responseHeaders:l,value:new w({message:n.statusText,url:s,requestBodyValues:o,statusCode:n.status,responseHeaders:l,responseBody:a,isRetryable:null==r?void 0:r(n)})};try{let i=function({text:e,schema:t}){try{let r=ea.parse(e);if(null==t)return r;return function({value:e,schema:t}){let r=em({value:e,schema:t});if(!r.success)throw X.wrap({value:e,cause:r.error});return r.value}({value:r,schema:t})}catch(t){if(H.isJSONParseError(t)||X.isTypeValidationError(t))throw t;throw new H({text:e,cause:t})}}({text:a,schema:e});return{responseHeaders:l,value:new w({message:t(i),url:s,requestBodyValues:o,statusCode:n.status,responseHeaders:l,responseBody:a,data:i,isRetryable:null==r?void 0:r(n,i)})}}catch(e){return{responseHeaders:l,value:new w({message:n.statusText,url:s,requestBodyValues:o,statusCode:n.status,responseHeaders:l,responseBody:a,isRetryable:null==r?void 0:r(n)})}}})({errorSchema:eM,errorToMessage:e=>e.error.message}),eJ=class{constructor(e,t,r){this.specificationVersion="v1",this.defaultObjectGenerationMode="tool",this.modelId=e,this.settings=t,this.config=r}get provider(){return this.config.provider}getArgs({mode:e,prompt:t,maxTokens:r,temperature:n,topP:s,frequencyPenalty:o,presencePenalty:a,seed:l}){let i=e.type,u=e$({model:this.modelId,logit_bias:this.settings.logitBias,logprobs:!0===this.settings.logprobs||"number"==typeof this.settings.logprobs||void 0,top_logprobs:"number"==typeof this.settings.logprobs?this.settings.logprobs:"boolean"==typeof this.settings.logprobs&&this.settings.logprobs?0:void 0,user:this.settings.user,parallel_tool_calls:this.settings.parallelToolCalls,max_tokens:r,temperature:n,top_p:s,frequency_penalty:o,presence_penalty:a,seed:l,messages:function(e){var t;let r=[];for(let{role:n,content:s}of e)switch(n){case"system":r.push({role:"system",content:s});break;case"user":{if(1===s.length&&(null==(t=s[0])?void 0:t.type)==="text"){r.push({role:"user",content:s[0].text});break}let e=s.map(e=>{var t;switch(e.type){case"text":return{type:"text",text:e.text};case"image":return{type:"image_url",image_url:{url:e.image instanceof URL?e.image.toString():`data:${null!=(t=e.mimeType)?t:"image/jpeg"};base64,${function(e){let t="";for(let r=0;r<e.length;r++)t+=String.fromCodePoint(e[r]);return e_(t)}(e.image)}`}};case"file":return{type:"text",text:e.data instanceof URL?e.data.toString():e.data};default:throw Error(`Unsupported content part type: ${e}`)}});r.push({role:"user",content:e});break}case"assistant":{let e="",t=[];for(let r of s)switch(r.type){case"text":e+=r.text;break;case"tool-call":t.push({id:r.toolCallId,type:"function",function:{name:r.toolName,arguments:JSON.stringify(r.args)}});break;default:throw Error(`Unsupported part: ${r}`)}r.push({role:"assistant",content:e,tool_calls:t.length>0?t:void 0});break}case"tool":for(let e of s)r.push({role:"tool",tool_call_id:e.toolCallId,content:JSON.stringify(e.result)});break;default:throw Error(`Unsupported role: ${n}`)}return r}(t)},this.config.extraBody);switch(i){case"regular":return e$(e$({},u),function(e){var t;let r=(null==(t=e.tools)?void 0:t.length)?e.tools:void 0;if(null==r)return{tools:void 0,tool_choice:void 0};let n=r.map(e=>"parameters"in e?{type:"function",function:{name:e.name,description:e.description,parameters:e.parameters}}:{type:"function",function:{name:e.name}}),s=e.toolChoice;if(null==s)return{tools:n,tool_choice:void 0};let o=s.type;switch(o){case"auto":case"none":case"required":return{tools:n,tool_choice:o};case"tool":return{tools:n,tool_choice:{type:"function",function:{name:s.toolName}}};default:throw Error(`Unsupported tool choice type: ${o}`)}}(e));case"object-json":return eO(e$({},u),{response_format:{type:"json_object"}});case"object-tool":return eO(e$({},u),{tool_choice:{type:"function",function:{name:e.tool.name}},tools:[{type:"function",function:{name:e.tool.name,description:e.tool.description,parameters:e.tool.parameters}}]});default:throw new en({functionality:`${i} mode`})}}async doGenerate(e){var t,r;let n=this.getArgs(e),{responseHeaders:s,value:o}=await eb({url:this.config.url({path:"/chat/completions",modelId:this.modelId}),headers:eu(this.config.headers(),e.headers),body:n,failedResponseHandler:eP,successfulResponseHandler:ez(eB),abortSignal:e.abortSignal,fetch:this.config.fetch}),{messages:a}=n,l=eR(n,["messages"]),i=o.choices[0];if(null==i)throw Error("No choice in response");return{text:null!=(t=i.message.content)?t:void 0,toolCalls:null==(r=i.message.tool_calls)?void 0:r.map(e=>{var t;return{toolCallType:"function",toolCallId:null!=(t=e.id)?t:ep(),toolName:e.function.name,args:e.function.arguments}}),finishReason:eA(i.finish_reason),usage:{promptTokens:o.usage.prompt_tokens,completionTokens:o.usage.completion_tokens},rawCall:{rawPrompt:a,rawSettings:l},rawResponse:{headers:s},warnings:[],logprobs:eq(i.logprobs)}}async doStream(e){let t;let r=this.getArgs(e),{responseHeaders:n,value:s}=await eb({url:this.config.url({path:"/chat/completions",modelId:this.modelId}),headers:eu(this.config.headers(),e.headers),body:eO(e$({},r),{stream:!0,stream_options:"strict"===this.config.compatibility?{include_usage:!0}:void 0}),failedResponseHandler:eP,successfulResponseHandler:ew(eH),abortSignal:e.abortSignal,fetch:this.config.fetch}),{messages:o}=r,a=eR(r,["messages"]),l=[],i="other",u={promptTokens:Number.NaN,completionTokens:Number.NaN};return{stream:s.pipeThrough(new TransformStream({transform(e,r){var n,s,o,a,c,p,d,h,m,f,g,y;if(!e.success){i="error",r.enqueue({type:"error",error:e.error});return}let b=e.value;if("error"in b){i="error",r.enqueue({type:"error",error:b.error});return}null!=b.usage&&(u={promptTokens:b.usage.prompt_tokens,completionTokens:b.usage.completion_tokens});let v=b.choices[0];if((null==v?void 0:v.finish_reason)!=null&&(i=eA(v.finish_reason)),(null==v?void 0:v.delta)==null)return;let w=v.delta;null!=w.content&&r.enqueue({type:"text-delta",textDelta:w.content});let z=eq(null==v?void 0:v.logprobs);if((null==z?void 0:z.length)&&(void 0===t&&(t=[]),t.push(...z)),null!=w.tool_calls)for(let e of w.tool_calls){let t=e.index;if(null==l[t]){if("function"!==e.type)throw new M({data:e,message:"Expected 'function' type."});if(null==e.id)throw new M({data:e,message:"Expected 'id' to be a string."});if((null==(n=e.function)?void 0:n.name)==null)throw new M({data:e,message:"Expected 'function.name' to be a string."});l[t]={id:e.id,type:"function",function:{name:e.function.name,arguments:null!=(s=e.function.arguments)?s:""}};let i=l[t];if(null==i)throw Error("Tool call is missing");(null==(o=i.function)?void 0:o.name)!=null&&(null==(a=i.function)?void 0:a.arguments)!=null&&eg(i.function.arguments)&&(r.enqueue({type:"tool-call-delta",toolCallType:"function",toolCallId:i.id,toolName:i.function.name,argsTextDelta:i.function.arguments}),r.enqueue({type:"tool-call",toolCallType:"function",toolCallId:null!=(c=i.id)?c:ep(),toolName:i.function.name,args:i.function.arguments}));continue}let i=l[t];if(null==i)throw Error("Tool call is missing");(null==(p=e.function)?void 0:p.arguments)!=null&&(i.function.arguments+=null!=(h=null==(d=e.function)?void 0:d.arguments)?h:""),r.enqueue({type:"tool-call-delta",toolCallType:"function",toolCallId:i.id,toolName:i.function.name,argsTextDelta:null!=(m=e.function.arguments)?m:""}),(null==(f=i.function)?void 0:f.name)!=null&&(null==(g=i.function)?void 0:g.arguments)!=null&&eg(i.function.arguments)&&r.enqueue({type:"tool-call",toolCallType:"function",toolCallId:null!=(y=i.id)?y:ep(),toolName:i.function.name,args:i.function.arguments})}},flush(e){e.enqueue({type:"finish",finishReason:i,logprobs:t,usage:u})}})),rawCall:{rawPrompt:o,rawSettings:a},rawResponse:{headers:n},warnings:[]}}},eB=ex.z.object({choices:ex.z.array(ex.z.object({message:ex.z.object({role:ex.z.literal("assistant"),content:ex.z.string().nullable().optional(),tool_calls:ex.z.array(ex.z.object({id:ex.z.string().optional().nullable(),type:ex.z.literal("function"),function:ex.z.object({name:ex.z.string(),arguments:ex.z.string()})})).optional()}),index:ex.z.number(),logprobs:ex.z.object({content:ex.z.array(ex.z.object({token:ex.z.string(),logprob:ex.z.number(),top_logprobs:ex.z.array(ex.z.object({token:ex.z.string(),logprob:ex.z.number()}))})).nullable()}).nullable().optional(),finish_reason:ex.z.string().optional().nullable()})),usage:ex.z.object({prompt_tokens:ex.z.number(),completion_tokens:ex.z.number()})}),eH=ex.z.union([ex.z.object({choices:ex.z.array(ex.z.object({delta:ex.z.object({role:ex.z.enum(["assistant"]).optional(),content:ex.z.string().nullish(),tool_calls:ex.z.array(ex.z.object({index:ex.z.number(),id:ex.z.string().nullish(),type:ex.z.literal("function").optional(),function:ex.z.object({name:ex.z.string().nullish(),arguments:ex.z.string().nullish()})})).nullish()}).nullish(),logprobs:ex.z.object({content:ex.z.array(ex.z.object({token:ex.z.string(),logprob:ex.z.number(),top_logprobs:ex.z.array(ex.z.object({token:ex.z.string(),logprob:ex.z.number()}))})).nullable()}).nullish(),finish_reason:ex.z.string().nullable().optional(),index:ex.z.number()})),usage:ex.z.object({prompt_tokens:ex.z.number(),completion_tokens:ex.z.number()}).nullish()}),eM]);function eU(e){return null==e?void 0:e.tokens.map((t,r)=>{var n,s;return{token:t,logprob:null!=(n=e.token_logprobs[r])?n:0,topLogprobs:e.top_logprobs?Object.entries(null!=(s=e.top_logprobs[r])?s:{}).map(([e,t])=>({token:e,logprob:t})):[]}})}var eV=class{constructor(e,t,r){this.specificationVersion="v1",this.defaultObjectGenerationMode=void 0,this.modelId=e,this.settings=t,this.config=r}get provider(){return this.config.provider}getArgs({mode:e,inputFormat:t,prompt:r,maxTokens:n,temperature:s,topP:o,frequencyPenalty:a,presencePenalty:l,seed:i}){var u;let c=e.type,{prompt:p,stopSequences:d}=function({prompt:e,inputFormat:t,user:r="user",assistant:n="assistant"}){if("prompt"===t&&1===e.length&&e[0]&&"user"===e[0].role&&1===e[0].content.length&&e[0].content[0]&&"text"===e[0].content[0].type)return{prompt:e[0].content[0].text};let s="";for(let{role:t,content:o}of(e[0]&&"system"===e[0].role&&(s+=`${e[0].content}

`,e=e.slice(1)),e))switch(t){case"system":throw new O({message:"Unexpected system message in prompt: ${content}",prompt:e});case"user":{let e=o.map(e=>{switch(e.type){case"text":return e.text;case"image":throw new en({functionality:"images"});case"file":throw new en({functionality:"file attachments"});default:throw Error(`Unsupported content type: ${e}`)}}).join("");s+=`${r}:
${e}

`;break}case"assistant":{let e=o.map(e=>{switch(e.type){case"text":return e.text;case"tool-call":throw new en({functionality:"tool-call messages"});default:throw Error(`Unsupported content type: ${e}`)}}).join("");s+=`${n}:
${e}

`;break}case"tool":throw new en({functionality:"tool messages"});default:throw Error(`Unsupported role: ${t}`)}return{prompt:s+=`${n}:
`,stopSequences:[`
${r}:`]}}({prompt:r,inputFormat:t}),h=e$({model:this.modelId,echo:this.settings.echo,logit_bias:this.settings.logitBias,logprobs:"number"==typeof this.settings.logprobs?this.settings.logprobs:"boolean"==typeof this.settings.logprobs&&this.settings.logprobs?0:void 0,suffix:this.settings.suffix,user:this.settings.user,max_tokens:n,temperature:s,top_p:o,frequency_penalty:a,presence_penalty:l,seed:i,prompt:p,stop:d},this.config.extraBody);switch(c){case"regular":if(null==(u=e.tools)?void 0:u.length)throw new en({functionality:"tools"});if(e.toolChoice)throw new en({functionality:"toolChoice"});return h;case"object-json":throw new en({functionality:"object-json mode"});case"object-tool":throw new en({functionality:"object-tool mode"});default:throw new en({functionality:`${c} mode`})}}async doGenerate(e){let t=this.getArgs(e),{responseHeaders:r,value:n}=await eb({url:this.config.url({path:"/completions",modelId:this.modelId}),headers:eu(this.config.headers(),e.headers),body:t,failedResponseHandler:eP,successfulResponseHandler:ez(eD),abortSignal:e.abortSignal,fetch:this.config.fetch}),{prompt:s}=t,o=eR(t,["prompt"]),a=n.choices[0];if(!a)throw Error("No choice in OpenRouter completion response");return{text:a.text,usage:{promptTokens:n.usage.prompt_tokens,completionTokens:n.usage.completion_tokens},finishReason:eA(a.finish_reason),logprobs:eU(a.logprobs),rawCall:{rawPrompt:s,rawSettings:o},rawResponse:{headers:r},warnings:[]}}async doStream(e){let t;let r=this.getArgs(e),{responseHeaders:n,value:s}=await eb({url:this.config.url({path:"/completions",modelId:this.modelId}),headers:eu(this.config.headers(),e.headers),body:eO(e$({},this.getArgs(e)),{stream:!0,stream_options:"strict"===this.config.compatibility?{include_usage:!0}:void 0}),failedResponseHandler:eP,successfulResponseHandler:ew(eF),abortSignal:e.abortSignal,fetch:this.config.fetch}),{prompt:o}=r,a=eR(r,["prompt"]),l="other",i={promptTokens:Number.NaN,completionTokens:Number.NaN};return{stream:s.pipeThrough(new TransformStream({transform(e,r){if(!e.success){l="error",r.enqueue({type:"error",error:e.error});return}let n=e.value;if("error"in n){l="error",r.enqueue({type:"error",error:n.error});return}null!=n.usage&&(i={promptTokens:n.usage.prompt_tokens,completionTokens:n.usage.completion_tokens});let s=n.choices[0];(null==s?void 0:s.finish_reason)!=null&&(l=eA(s.finish_reason)),(null==s?void 0:s.text)!=null&&r.enqueue({type:"text-delta",textDelta:s.text});let o=eU(null==s?void 0:s.logprobs);(null==o?void 0:o.length)&&(void 0===t&&(t=[]),t.push(...o))},flush(e){e.enqueue({type:"finish",finishReason:l,logprobs:t,usage:i})}})),rawCall:{rawPrompt:o,rawSettings:a},rawResponse:{headers:n},warnings:[]}}},eD=ex.z.object({choices:ex.z.array(ex.z.object({text:ex.z.string(),finish_reason:ex.z.string(),logprobs:ex.z.object({tokens:ex.z.array(ex.z.string()),token_logprobs:ex.z.array(ex.z.number()),top_logprobs:ex.z.array(ex.z.record(ex.z.string(),ex.z.number())).nullable()}).nullable().optional()})),usage:ex.z.object({prompt_tokens:ex.z.number(),completion_tokens:ex.z.number()})}),eF=ex.z.union([ex.z.object({choices:ex.z.array(ex.z.object({text:ex.z.string(),finish_reason:ex.z.string().nullish(),index:ex.z.number(),logprobs:ex.z.object({tokens:ex.z.array(ex.z.string()),token_logprobs:ex.z.array(ex.z.number()),top_logprobs:ex.z.array(ex.z.record(ex.z.string(),ex.z.number())).nullable()}).nullable().optional()})),usage:ex.z.object({prompt_tokens:ex.z.number(),completion_tokens:ex.z.number()}).optional().nullable()}),eM]);function eG(e={}){var t,r,n,s;let o=null!=(r=null==(s=null!=(t=e.baseURL)?t:e.baseUrl)?void 0:s.replace(/\/$/,""))?r:"https://openrouter.ai/api/v1",a=null!=(n=e.compatibility)?n:"compatible",l=()=>e$({Authorization:`Bearer ${function({apiKey:e,environmentVariableName:t,apiKeyParameterName:r="apiKey",description:n}){if("string"==typeof e)return e;if(null!=e)throw new F({message:`${n} API key must be a string.`});if("undefined"==typeof process)throw new F({message:`${n} API key is missing. Pass it using the '${r}' parameter. Environment variables is not supported in this environment.`});if(null==(e=process.env[t]))throw new F({message:`${n} API key is missing. Pass it using the '${r}' parameter or the ${t} environment variable.`});if("string"!=typeof e)throw new F({message:`${n} API key must be a string. The value of the ${t} environment variable is not a string.`});return e}({apiKey:e.apiKey,environmentVariableName:"OPENROUTER_API_KEY",description:"OpenRouter"})}`},e.headers),i=(t,r={})=>new eJ(t,r,{provider:"openrouter.chat",url:({path:e})=>`${o}${e}`,headers:l,compatibility:a,fetch:e.fetch,extraBody:e.extraBody}),u=(t,r={})=>new eV(t,r,{provider:"openrouter.completion",url:({path:e})=>`${o}${e}`,headers:l,compatibility:a,fetch:e.fetch,extraBody:e.extraBody}),c=(e,t)=>{if(new.target)throw Error("The OpenRouter model function cannot be called with the new keyword.");return"openai/gpt-3.5-turbo-instruct"===e?u(e,t):i(e,t)},p=function(e,t){return c(e,t)};return p.languageModel=c,p.chat=i,p.completion=u,p}eG({compatibility:"strict"})}};