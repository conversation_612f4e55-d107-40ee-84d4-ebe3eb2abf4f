exports.id=4452,exports.ids=[4452],exports.modules={12667:(e,t,s)=>{var a={"./ai-dashboard/en.json":[45989,5989],"./ai-dashboard/zh.json":[9776,9776],"./landing/en.json":[42881,2881],"./landing/zh.json":[30956,956],"./pricing/en.json":[71718,1718],"./pricing/zh.json":[42743,2743],"./showcase/en.json":[20651,651],"./showcase/zh.json":[10817,3198]};function r(e){if(!s.o(a,e))return Promise.resolve().then(()=>{var t=Error("Cannot find module '"+e+"'");throw t.code="MODULE_NOT_FOUND",t});var t=a[e],r=t[0];return s.e(t[1]).then(()=>s.t(r,19))}r.keys=()=>Object.keys(a),r.id=12667,e.exports=r},17890:(e,t,s)=>{"use strict";s.r(t),s.d(t,{default:()=>i});var a=s(34411),r=s(87184),n=s(98406),l=s(64476);async function i({children:e,params:t}){let{locale:s}=await t,i=await (0,l.JE)(s);return(0,a.jsxs)(a.Fragment,{children:[i.header&&(0,a.jsx)(n.default,{header:i.header}),(0,a.jsx)("main",{className:"overflow-x-hidden",children:e}),i.footer&&(0,a.jsx)(r.A,{footer:i.footer})]})}},29096:(e,t,s)=>{"use strict";s.d(t,{default:()=>q});var a=s(2569),r=s(63686),n=s(92622),l=s(29068),i=s(38182),o=s(75958),c=s(61865),d=s(75673);let m=l.forwardRef(({className:e,children:t,...s},r)=>(0,a.jsxs)(i.bL,{ref:r,className:(0,d.cn)("relative z-10 flex max-w-max flex-1 items-center justify-center",e),...s,children:[t,(0,a.jsx)(g,{})]}));m.displayName=i.bL.displayName;let x=l.forwardRef(({className:e,...t},s)=>(0,a.jsx)(i.B8,{ref:s,className:(0,d.cn)("group flex flex-1 list-none items-center justify-center space-x-1",e),...t}));x.displayName=i.B8.displayName;let h=i.q7,f=(0,o.F)("group inline-flex h-10 w-max items-center justify-center rounded-md bg-background px-4 py-2 text-sm font-medium transition-colors hover:bg-accent hover:text-accent-foreground focus:bg-accent focus:text-accent-foreground focus:outline-hidden disabled:pointer-events-none disabled:opacity-50 data-active:bg-accent/50 data-[state=open]:bg-accent/50"),u=l.forwardRef(({className:e,children:t,...s},r)=>(0,a.jsxs)(i.l9,{ref:r,className:(0,d.cn)(f(),"group",e),...s,children:[t," ",(0,a.jsx)(c.A,{className:"relative top-[1px] ml-1 h-3 w-3 transition duration-200 group-data-[state=open]:rotate-180","aria-hidden":"true"})]}));u.displayName=i.l9.displayName;let j=l.forwardRef(({className:e,...t},s)=>(0,a.jsx)(i.UC,{ref:s,className:(0,d.cn)("left-0 top-0 w-full data-[motion^=from-]:animate-in data-[motion^=to-]:animate-out data-[motion^=from-]:fade-in data-[motion^=to-]:fade-out data-[motion=from-end]:slide-in-from-right-52 data-[motion=from-start]:slide-in-from-left-52 data-[motion=to-end]:slide-out-to-right-52 data-[motion=to-start]:slide-out-to-left-52 md:absolute md:w-auto ",e),...t}));j.displayName=i.UC.displayName;let p=i.N_,g=l.forwardRef(({className:e,...t},s)=>(0,a.jsx)("div",{className:(0,d.cn)("absolute left-0 top-full flex justify-center"),children:(0,a.jsx)(i.LM,{className:(0,d.cn)("origin-top-center relative mt-1.5 h-[var(--radix-navigation-menu-viewport-height)] w-full overflow-hidden rounded-md border bg-popover text-popover-foreground shadow-lg data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:zoom-out-95 data-[state=open]:zoom-in-90 md:w-[var(--radix-navigation-menu-viewport-width)]",e),ref:s,...t})}));g.displayName=i.LM.displayName,l.forwardRef(({className:e,...t},s)=>(0,a.jsx)(i.C1,{ref:s,className:(0,d.cn)("top-full z-1 flex h-1.5 items-end justify-center overflow-hidden data-[state=visible]:animate-in data-[state=hidden]:animate-out data-[state=hidden]:fade-out data-[state=visible]:fade-in",e),...t,children:(0,a.jsx)("div",{className:"relative top-[60%] h-2 w-2 rotate-45 rounded-tl-sm bg-border shadow-md"})})).displayName=i.C1.displayName;var N=s(84601),b=s(92227),v=s(37967),w=s(14078),y=s(14059),k=s(62254),_=s(19115);function C({isIcon:e=!1}){let t=(0,y.useParams)().locale,s=(0,y.useRouter)(),r=(0,y.usePathname)();return(0,a.jsxs)(w.l6,{value:t,onValueChange:e=>{if(e!==t){let a=r.replace(`/${t}`,`/${e}`);a.startsWith(`/${e}`)||(a=`/${e}${a}`),s.push(a)}},children:[(0,a.jsxs)(w.bq,{className:"flex items-center gap-2 border-none text-muted-foreground hover:text-foreground hover:bg-accent/10 focus:ring-0 focus:ring-offset-0 transition-colors",children:[(0,a.jsx)(k.aVW,{className:"text-xl"}),!e&&(0,a.jsx)("span",{className:"hidden md:block",children:_.L$[t]})]}),(0,a.jsx)(w.gC,{className:"z-50 ",children:Object.keys(_.L$).map(e=>{let t=_.L$[e];return(0,a.jsx)(w.eb,{className:"cursor-pointer px-4",value:e,children:t},e)})})]})}var z=s(48784),$=s(6637),U=s(73054);function P(){let e=(0,U.c3)(),{setShowSignModal:t}=(0,$.U)();return(0,a.jsx)(n.$,{variant:"default",onClick:()=>t(!0),className:"cursor-pointer",children:e("user.sign_in")})}var A=s(78701),R=s(78341),L=s(54487);function I({user:e}){let t=(0,U.c3)(),s=[{title:e.nickname},{title:t("user.user_center"),url:"/my-orders"},{title:t("user.admin_system"),url:"/admin/users"},{title:t("user.sign_out"),onClick:()=>(0,L.CI)()}];return(0,a.jsxs)(R.rI,{children:[(0,a.jsx)(R.ty,{asChild:!0,children:(0,a.jsxs)(A.Avatar,{className:"cursor-pointer",children:[(0,a.jsx)(A.AvatarImage,{src:e.avatar_url,alt:e.nickname}),(0,a.jsx)(A.q,{children:e.nickname})]})}),(0,a.jsx)(R.SQ,{className:"mx-4 bg-background",children:s.map((e,t)=>(0,a.jsxs)(l.Fragment,{children:[(0,a.jsx)(R._2,{className:"flex justify-center cursor-pointer",children:e.url?(0,a.jsx)(v.N_,{href:e.url,target:e.target,children:e.title}):(0,a.jsx)("button",{onClick:e.onClick,children:e.title})},t),t!==s.length-1&&(0,a.jsx)(R.mB,{})]},t))})]})}function O(){(0,U.c3)();let{user:e}=(0,$.U)();return(0,a.jsx)("div",{className:"flex items-center gap-x-2 px-2 cursor-pointer",children:e?(0,a.jsx)(I,{user:e}):(0,a.jsx)(P,{})})}var D=s(26872),E=s(99293),M=s(67534);function F(){let{theme:e,setTheme:t}=(0,$.U)(),s=function(s){s!==e&&((0,M.PW)(E.wD.Theme,s,-1),t(s))};return(0,a.jsx)("div",{className:"flex items-center gap-x-2 px-2",children:"dark"===e?(0,a.jsx)(D.uSI,{className:"cursor-pointer text-lg text-muted-foreground",onClick:()=>s("light"),width:80,height:20}):(0,a.jsx)(D.KsI,{className:"cursor-pointer text-lg text-muted-foreground",onClick:()=>s("dark"),width:80,height:20})})}function q({header:e}){return e.disabled?null:(0,a.jsx)("section",{className:"py-3",children:(0,a.jsxs)("div",{className:"container",children:[(0,a.jsxs)("nav",{className:"hidden justify-between lg:flex",children:[(0,a.jsxs)("div",{className:"flex items-center gap-6",children:[(0,a.jsxs)(v.N_,{href:e.brand?.url||"/",className:"flex items-center gap-2",children:[e.brand?.logo?.src&&(0,a.jsx)("img",{src:e.brand.logo.src,alt:e.brand.logo.alt||e.brand.title,className:"w-8"}),e.brand?.title&&(0,a.jsx)("span",{className:"text-xl text-primary font-bold",children:e.brand?.title||""})]}),(0,a.jsx)("div",{className:"flex items-center",children:(0,a.jsx)(m,{children:(0,a.jsx)(x,{children:e.nav?.items?.map((e,t)=>e.children&&e.children.length>0?a.jsxs(h,{className:"text-muted-foreground",children:[a.jsxs(u,{children:[e.icon&&a.jsx(b.default,{name:e.icon,className:"size-4 shrink-0 mr-2"}),a.jsx("span",{children:e.title})]}),a.jsx(j,{children:a.jsx("ul",{className:"w-80 p-3",children:a.jsx(p,{children:e.children.map((e,t)=>a.jsx("li",{children:a.jsxs(v.N_,{className:d.cn("flex select-none gap-4 rounded-md p-3 leading-none no-underline outline-hidden transition-colors hover:bg-accent hover:text-accent-foreground focus:bg-accent focus:text-accent-foreground"),href:e.url,target:e.target,children:[e.icon&&a.jsx(b.default,{name:e.icon,className:"size-5 shrink-0"}),a.jsxs("div",{children:[a.jsx("div",{className:"text-sm font-semibold",children:e.title}),a.jsx("p",{className:"text-sm leading-snug text-muted-foreground",children:e.description})]})]})},t))})})})]},t):a.jsx(h,{children:a.jsxs(v.N_,{className:d.cn("text-muted-foreground",f,n.r({variant:"ghost"})),href:e.url,target:e.target,children:[e.icon&&a.jsx(b.default,{name:e.icon,className:"size-4 shrink-0 mr-0"}),e.title]})},t))})})})]}),(0,a.jsxs)("div",{className:"shrink-0 flex gap-2 items-center",children:[e.show_locale&&(0,a.jsx)(C,{}),e.show_theme&&(0,a.jsx)(F,{}),e.buttons?.map((e,t)=>a.jsx(n.$,{variant:e.variant,children:a.jsxs(v.N_,{href:e.url,target:e.target||"",className:"flex items-center gap-1 cursor-pointer",children:[e.title,e.icon&&a.jsx(b.default,{name:e.icon,className:"size-4 shrink-0"})]})},t)),e.show_sign&&(0,a.jsx)(O,{})]})]}),(0,a.jsx)("div",{className:"block lg:hidden",children:(0,a.jsxs)("div",{className:"flex items-center justify-between",children:[(0,a.jsxs)(v.N_,{href:e.brand?.url||"/",className:"flex items-center gap-2",children:[e.brand?.logo?.src&&(0,a.jsx)("img",{src:e.brand.logo.src,alt:e.brand.logo.alt||e.brand.title,className:"w-8"}),e.brand?.title&&(0,a.jsx)("span",{className:"text-xl font-bold",children:e.brand?.title||""})]}),(0,a.jsxs)(N.cj,{children:[(0,a.jsx)(N.CG,{asChild:!0,children:(0,a.jsx)(n.$,{variant:"default",size:"icon",children:(0,a.jsx)(z.A,{className:"size-4"})})}),(0,a.jsxs)(N.h,{className:"overflow-y-auto",children:[(0,a.jsx)(N.Fm,{children:(0,a.jsx)(N.qp,{children:(0,a.jsxs)(v.N_,{href:e.brand?.url||"/",className:"flex items-center gap-2",children:[e.brand?.logo?.src&&(0,a.jsx)("img",{src:e.brand.logo.src,alt:e.brand.logo.alt||e.brand.title,className:"w-8"}),e.brand?.title&&(0,a.jsx)("span",{className:"text-xl font-bold",children:e.brand?.title||""})]})})}),(0,a.jsx)("div",{className:"mb-8 mt-8 flex flex-col gap-4",children:(0,a.jsx)(r.nD,{type:"single",collapsible:!0,className:"w-full",children:e.nav?.items?.map((e,t)=>e.children&&e.children.length>0?a.jsxs(r.As,{value:e.title||"",className:"border-b-0",children:[a.jsx(r.$m,{className:"mb-4 py-0 font-semibold hover:no-underline text-left",children:e.title}),a.jsx(r.ub,{className:"mt-2",children:e.children.map((e,t)=>a.jsxs(v.N_,{className:d.cn("flex select-none gap-4 rounded-md p-3 leading-none outline-hidden transition-colors hover:bg-accent hover:text-accent-foreground focus:bg-accent focus:text-accent-foreground"),href:e.url,target:e.target,children:[e.icon&&a.jsx(b.default,{name:e.icon,className:"size-4 shrink-0"}),a.jsxs("div",{children:[a.jsx("div",{className:"text-sm font-semibold",children:e.title}),a.jsx("p",{className:"text-sm leading-snug text-muted-foreground",children:e.description})]})]},t))})]},t):a.jsxs(v.N_,{href:e.url,target:e.target,className:"font-semibold my-4 flex items-center gap-2 px-4",children:[e.icon&&a.jsx(b.default,{name:e.icon,className:"size-4 shrink-0"}),e.title]},t))})}),(0,a.jsx)("div",{className:"flex-1"}),(0,a.jsxs)("div",{className:"border-t pt-4",children:[(0,a.jsxs)("div",{className:"mt-2 flex flex-col gap-3",children:[e.buttons?.map((e,t)=>a.jsx(n.$,{variant:e.variant,children:a.jsxs(v.N_,{href:e.url,target:e.target||"",className:"flex items-center gap-1",children:[e.title,e.icon&&a.jsx(b.default,{name:e.icon,className:"size-4 shrink-0"})]})},t)),e.show_sign&&(0,a.jsx)(O,{})]}),(0,a.jsxs)("div",{className:"mt-4 flex items-center gap-2",children:[e.show_locale&&(0,a.jsx)(C,{}),(0,a.jsx)("div",{className:"flex-1"}),e.show_theme&&(0,a.jsx)(F,{})]})]})]})]})]})})]})})}},57595:(e,t,s)=>{"use strict";s.r(t),s.d(t,{default:()=>a});let a=(0,s(29037).registerClientReference)(function(){throw Error("Attempted to call the default export of \"/Users/<USER>/My-Project/fluxkrea_test/components/icon/index.tsx\" from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"/Users/<USER>/My-Project/fluxkrea_test/components/icon/index.tsx","default")},57732:(e,t,s)=>{Promise.resolve().then(s.bind(s,98406)),Promise.resolve().then(s.bind(s,57595))},63686:(e,t,s)=>{"use strict";s.d(t,{$m:()=>d,As:()=>c,nD:()=>o,ub:()=>m});var a=s(2569),r=s(29068),n=s(48455),l=s(61865),i=s(75673);let o=n.bL,c=r.forwardRef(({className:e,...t},s)=>(0,a.jsx)(n.q7,{ref:s,className:(0,i.cn)("border-b",e),...t}));c.displayName="AccordionItem";let d=r.forwardRef(({className:e,children:t,...s},r)=>(0,a.jsx)(n.Y9,{className:"flex",children:(0,a.jsxs)(n.l9,{ref:r,className:(0,i.cn)("flex flex-1 items-center justify-between py-4 font-medium transition-all hover:underline [&[data-state=open]>svg]:rotate-180",e),...s,children:[t,(0,a.jsx)(l.A,{className:"h-4 w-4 shrink-0 transition-transform duration-200"})]})}));d.displayName=n.l9.displayName;let m=r.forwardRef(({className:e,children:t,...s},r)=>(0,a.jsx)(n.UC,{ref:r,className:"overflow-hidden text-sm transition-all data-[state=closed]:animate-accordion-up data-[state=open]:animate-accordion-down",...s,children:(0,a.jsx)("div",{className:(0,i.cn)("pb-4 pt-0",e),children:t})}));m.displayName=n.UC.displayName},64476:(e,t,s)=>{"use strict";async function a(e){return await l("landing",e)}async function r(e){return await l("pricing",e)}async function n(e){return await l("showcase",e)}async function l(e,t){try{return"zh-CN"===t&&(t="zh"),await s(12667)(`./${e}/${t.toLowerCase()}.json`).then(e=>e.default)}catch(a){return console.warn(`Failed to load ${t}.json, falling back to en.json`),await s(87188)(`./${e}/en.json`).then(e=>e.default)}}s.d(t,{DN:()=>n,JE:()=>a,eI:()=>r})},87184:(e,t,s)=>{"use strict";s.d(t,{A:()=>n});var a=s(34411),r=s(57595);function n({footer:e}){return e.disabled?null:(0,a.jsx)("section",{id:e.name,className:"py-16",children:(0,a.jsx)("div",{className:"max-w-7xl mx-auto px-8",children:(0,a.jsxs)("footer",{children:[(0,a.jsxs)("div",{className:"flex flex-col items-center justify-between gap-10 text-center lg:flex-row lg:text-left",children:[(0,a.jsxs)("div",{className:"flex w-full max-w-96 shrink flex-col items-center justify-between gap-6 lg:items-start",children:[e.brand&&(0,a.jsxs)("div",{children:[(0,a.jsxs)("div",{className:"flex items-center justify-center gap-2 lg:justify-start",children:[e.brand.logo&&(0,a.jsx)("img",{src:e.brand.logo.src,alt:e.brand.logo.alt||e.brand.title,className:"h-11"}),e.brand.title&&(0,a.jsx)("p",{className:"text-3xl font-semibold",children:e.brand.title})]}),e.brand.description&&(0,a.jsx)("p",{className:"mt-6 text-md text-muted-foreground",children:e.brand.description})]}),e.social&&(0,a.jsx)("ul",{className:"flex items-center space-x-6 text-muted-foreground",children:e.social.items?.map((e,t)=>a.jsx("li",{className:"font-medium hover:text-primary",children:a.jsx("a",{href:e.url,target:e.target,children:e.icon&&a.jsx(r.default,{name:e.icon,className:"size-4"})})},t))})]}),(0,a.jsx)("div",{className:"grid grid-cols-3 gap-6 lg:gap-20",children:e.nav?.items?.map((e,t)=>a.jsxs("div",{children:[a.jsx("p",{className:"mb-6 font-bold",children:e.title}),a.jsx("ul",{className:"space-y-4 text-sm text-muted-foreground",children:e.children?.map((e,t)=>a.jsx("li",{className:"font-medium hover:text-primary",children:a.jsx("a",{href:e.url,target:e.target,children:e.title})},t))})]},t))})]}),(0,a.jsxs)("div",{className:"mt-8 flex flex-col justify-between gap-4 border-t pt-8 text-center text-sm font-medium text-muted-foreground lg:flex-row lg:items-center lg:text-left",children:[e.copyright&&(0,a.jsx)("p",{children:e.copyright}),e.agreement&&(0,a.jsx)("ul",{className:"flex justify-center gap-4 lg:justify-start",children:e.agreement.items?.map((e,t)=>a.jsx("li",{className:"hover:text-primary",children:a.jsx("a",{href:e.url,target:e.target,children:e.title})},t))})]})]})})})}},87188:(e,t,s)=>{var a={"./ai-dashboard/en.json":[45989,5989],"./landing/en.json":[42881,2881],"./pricing/en.json":[71718,1718],"./showcase/en.json":[20651,651]};function r(e){if(!s.o(a,e))return Promise.resolve().then(()=>{var t=Error("Cannot find module '"+e+"'");throw t.code="MODULE_NOT_FOUND",t});var t=a[e],r=t[0];return s.e(t[1]).then(()=>s.t(r,19))}r.keys=()=>Object.keys(a),r.id=87188,e.exports=r},94532:(e,t,s)=>{Promise.resolve().then(s.bind(s,29096)),Promise.resolve().then(s.bind(s,92227))},98406:(e,t,s)=>{"use strict";s.d(t,{default:()=>a});let a=(0,s(29037).registerClientReference)(function(){throw Error("Attempted to call the default export of \"/Users/<USER>/My-Project/fluxkrea_test/components/blocks/header/index.tsx\" from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"/Users/<USER>/My-Project/fluxkrea_test/components/blocks/header/index.tsx","default")}};