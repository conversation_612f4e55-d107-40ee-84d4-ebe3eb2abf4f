"use strict";exports.id=9206,exports.ids=[9206],exports.modules={2013:(e,t,s)=>{s.d(t,{default:()=>r});let r=(0,s(29037).registerClientReference)(function(){throw Error("Attempted to call the default export of \"/Users/<USER>/My-Project/fluxkrea_test/components/blocks/feature2/index.tsx\" from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"/Users/<USER>/My-Project/fluxkrea_test/components/blocks/feature2/index.tsx","default")},4648:(e,t,s)=>{s.d(t,{A:()=>n});var r=s(34411),a=s(38498),l=s(58243);function n({section:e}){return e.disabled?null:(0,r.jsx)("section",{className:"py-16",children:(0,r.jsxs)("div",{className:"container px-8",children:[(0,r.jsxs)("div",{className:"mb-16 max-w-xl px-8 lg:px-0",children:[e.label&&(0,r.jsx)(l.E,{variant:"outline",className:"mb-4",children:e.label}),(0,r.jsx)("h2",{className:"mb-6 text-pretty text-3xl font-bold lg:text-4xl",children:e.title}),(0,r.jsx)("p",{className:"mb-4 max-w-xl text-muted-foreground lg:max-w-none lg:text-lg",children:e.description})]}),(0,r.jsx)("div",{children:(0,r.jsx)(a.Tabs,{defaultValue:"tab-1",children:(0,r.jsxs)(a.TabsList,{className:"relative grid items-start gap-6 lg:grid-cols-4",children:[(0,r.jsx)("div",{className:"absolute left-4 right-0 top-[30px] -z-10 hidden h-px bg-input lg:block"}),e.items?.map((e,t)=>r.jsxs(a.TabsTrigger,{value:`tab-${t+1}`,className:"group pointer-events-none lg:pointer-events-auto",children:[r.jsxs("div",{className:"flex gap-4 rounded-md px-8 py-4 text-left hover:bg-muted/50 lg:block lg:px-4",children:[r.jsxs("div",{className:"flex flex-col items-center lg:contents",children:[r.jsx("span",{className:"flex size-7 shrink-0 items-center justify-center rounded-full border bg-background font-mono text-xs font-medium lg:group-data-[state=active]:bg-primary lg:group-data-[state=active]:text-primary-foreground lg:group-data-[state=active]:ring-3 lg:group-data-[state=active]:ring-primary/40",children:t+1}),r.jsx("span",{className:"h-full w-px bg-input lg:hidden"})]}),r.jsxs("div",{children:[r.jsx("h3",{className:"mb-1 font-medium lg:mt-4",children:e.title}),r.jsx("p",{className:"text-sm",children:e.description})]})]}),e.image&&r.jsx("div",{className:"mt-6 block border bg-muted/50 px-4 py-6 lg:hidden",children:r.jsx("div",{className:"aspect-video",children:r.jsx("img",{src:e.image?.src,alt:e.image?.alt||e.title,className:"h-full w-full rounded-md border object-cover shadow-sm"})})})]},t))]})})})]})})}},18586:(e,t,s)=>{s.d(t,{BT:()=>o,Wu:()=>d,X9:()=>c,ZB:()=>i,Zp:()=>l,aR:()=>n,wL:()=>m});var r=s(34411);s(37582);var a=s(25039);function l({className:e,...t}){return(0,r.jsx)("div",{"data-slot":"card",className:(0,a.cn)("bg-card text-card-foreground flex flex-col gap-6 rounded-2xl border border-border/50 p-6 shadow-lg backdrop-blur-sm transition-all duration-200 hover:shadow-xl hover:scale-[1.01]",e),...t})}function n({className:e,...t}){return(0,r.jsx)("div",{"data-slot":"card-header",className:(0,a.cn)("@container/card-header grid auto-rows-min grid-rows-[auto_auto] items-start gap-2 has-data-[slot=card-action]:grid-cols-[1fr_auto] [.border-b]:pb-6",e),...t})}function i({className:e,...t}){return(0,r.jsx)("div",{"data-slot":"card-title",className:(0,a.cn)("leading-none font-semibold",e),...t})}function o({className:e,...t}){return(0,r.jsx)("div",{"data-slot":"card-description",className:(0,a.cn)("text-muted-foreground text-sm",e),...t})}function c({className:e,...t}){return(0,r.jsx)("div",{"data-slot":"card-action",className:(0,a.cn)("col-start-2 row-span-2 row-start-1 self-start justify-self-end",e),...t})}function d({className:e,...t}){return(0,r.jsx)("div",{"data-slot":"card-content",className:(0,a.cn)("px-6",e),...t})}function m({className:e,...t}){return(0,r.jsx)("div",{"data-slot":"card-footer",className:(0,a.cn)("flex items-center px-6 [.border-t]:pt-6",e),...t})}},23312:(e,t,s)=>{s.d(t,{A:()=>a});var r=s(34411);function a({section:e}){return e.disabled?null:(0,r.jsx)("section",{id:e.name,className:"py-16",children:(0,r.jsx)("div",{className:"container flex flex-row items-center justify-center",children:(0,r.jsxs)("div",{className:"flex flex-col items-center gap-4",children:[(0,r.jsx)("h2",{className:"text-center: text-muted-foreground lg:text-left",children:e.title}),(0,r.jsx)("div",{className:"flex flex-wrap items-center justify-center gap-8 mt-4",children:e.items?.map((e,t)=>{if(e.image)return r.jsx("img",{src:e.image.src,alt:e.image.alt||e.title,className:"h-7 dark:invert"},t)})})]})})})}},23463:(e,t,s)=>{s.d(t,{A:()=>m});var r=s(34411),a=s(58243),l=s(92172),n=s(51247),i=s(57533);function o(){return(0,r.jsxs)("div",{className:"mx-auto mt-8 flex w-fit flex-col items-center gap-2 sm:flex-row",children:[(0,r.jsx)("span",{className:"mx-4 inline-flex items-center -space-x-2",children:Array.from({length:5}).map((e,t)=>(0,r.jsx)(n.Avatar,{className:"size-12 border",children:(0,r.jsx)(n.AvatarImage,{src:`/imgs/users/${t+6}.png`,alt:`Happy user ${t+1}`})},t))}),(0,r.jsxs)("div",{className:"flex flex-col items-center gap-1 md:items-start",children:[(0,r.jsx)("div",{className:"flex items-center gap-1",children:Array.from({length:5}).map((e,t)=>(0,r.jsx)(i.A,{className:"size-5 fill-yellow-400 text-yellow-400"},t))}),(0,r.jsx)("p",{className:"text-left font-medium text-muted-foreground",children:"from 99+ happy users"})]})]})}var c=s(57595),d=s(78933);function m({hero:e}){if(e.disabled)return null;let t=e.highlight_text,s=null;return t&&(s=e.title?.split(t,2)),(0,r.jsx)(r.Fragment,{children:(0,r.jsx)("section",{className:"py-24",children:(0,r.jsxs)("div",{className:"container",children:[e.show_badge&&(0,r.jsx)("div",{className:"flex items-center justify-center mb-8"}),(0,r.jsxs)("div",{className:"text-center",children:[e.announcement&&(0,r.jsxs)(d.N_,{href:e.announcement.url,className:"mx-auto mb-3 inline-flex items-center gap-3 rounded-full border px-2 py-1 text-sm",children:[e.announcement.label&&(0,r.jsx)(a.E,{children:e.announcement.label}),e.announcement.title]}),s&&s.length>1?(0,r.jsxs)("h1",{className:"mx-auto mb-3 mt-4 max-w-6xl text-balance text-4xl font-bold lg:mb-7 lg:text-7xl",children:[s[0],(0,r.jsx)("span",{className:"bg-linear-to-r from-primary via-primary to-primary bg-clip-text text-transparent",children:t}),s[1]]}):(0,r.jsx)("h1",{className:"mx-auto mb-3 mt-4 max-w-6xl text-balance text-4xl font-bold lg:mb-7 lg:text-7xl",children:e.title}),(0,r.jsx)("p",{className:"m mx-auto max-w-3xl text-muted-foreground lg:text-xl",dangerouslySetInnerHTML:{__html:e.description||""}}),e.buttons&&(0,r.jsx)("div",{className:"mt-8 flex flex-col justify-center gap-4 sm:flex-row",children:e.buttons.map((e,t)=>(0,r.jsx)(d.N_,{href:e.url,target:e.target||"",className:"flex items-center",children:(0,r.jsxs)(l.$,{className:"w-full",size:"lg",variant:e.variant||"default",children:[e.icon&&(0,r.jsx)(c.default,{name:e.icon,className:""}),e.title]})},t))}),e.tip&&(0,r.jsx)("p",{className:"mt-8 text-md text-muted-foreground",children:e.tip}),e.show_happy_users&&(0,r.jsx)(o,{})]})]})})})}},30044:(e,t,s)=>{s.d(t,{A7:()=>f,FN:()=>x,Wk:()=>u});var r=s(2569),a=s(29068),l=s(36326),n=s(43846),i=s(19405),o=s(75673),c=s(92622);let d=a.createContext(null);function m(){let e=a.useContext(d);if(!e)throw Error("useCarousel must be used within a <Carousel />");return e}let x=a.forwardRef(({orientation:e="horizontal",opts:t,setApi:s,plugins:n,className:i,children:c,...m},x)=>{let[u,f]=(0,l.A)({...t,axis:"horizontal"===e?"x":"y"},n),[p,h]=a.useState(!1),[g,v]=a.useState(!1),b=a.useCallback(e=>{e&&(h(e.canScrollPrev()),v(e.canScrollNext()))},[]),j=a.useCallback(()=>{f?.scrollPrev()},[f]),N=a.useCallback(()=>{f?.scrollNext()},[f]),y=a.useCallback(e=>{"ArrowLeft"===e.key?(e.preventDefault(),j()):"ArrowRight"===e.key&&(e.preventDefault(),N())},[j,N]);return a.useEffect(()=>{f&&s&&s(f)},[f,s]),a.useEffect(()=>{if(f)return b(f),f.on("reInit",b),f.on("select",b),()=>{f?.off("select",b)}},[f,b]),(0,r.jsx)(d.Provider,{value:{carouselRef:u,api:f,opts:t,orientation:e||(t?.axis==="y"?"vertical":"horizontal"),scrollPrev:j,scrollNext:N,canScrollPrev:p,canScrollNext:g},children:(0,r.jsx)("div",{ref:x,onKeyDownCapture:y,className:(0,o.cn)("relative",i),role:"region","aria-roledescription":"carousel",...m,children:c})})});x.displayName="Carousel";let u=a.forwardRef(({className:e,...t},s)=>{let{carouselRef:a,orientation:l}=m();return(0,r.jsx)("div",{ref:a,className:"overflow-hidden",children:(0,r.jsx)("div",{ref:s,className:(0,o.cn)("flex","horizontal"===l?"-ml-4":"-mt-4 flex-col",e),...t})})});u.displayName="CarouselContent";let f=a.forwardRef(({className:e,...t},s)=>{let{orientation:a}=m();return(0,r.jsx)("div",{ref:s,role:"group","aria-roledescription":"slide",className:(0,o.cn)("min-w-0 shrink-0 grow-0 basis-full","horizontal"===a?"pl-4":"pt-4",e),...t})});f.displayName="CarouselItem",a.forwardRef(({className:e,variant:t="outline",size:s="icon",...a},l)=>{let{orientation:i,scrollPrev:d,canScrollPrev:x}=m();return(0,r.jsxs)(c.$,{ref:l,variant:t,size:s,className:(0,o.cn)("absolute  h-8 w-8 rounded-full","horizontal"===i?"-left-12 top-1/2 -translate-y-1/2":"-top-12 left-1/2 -translate-x-1/2 rotate-90",e),disabled:!x,onClick:d,...a,children:[(0,r.jsx)(n.A,{className:"h-4 w-4"}),(0,r.jsx)("span",{className:"sr-only",children:"Previous slide"})]})}).displayName="CarouselPrevious",a.forwardRef(({className:e,variant:t="outline",size:s="icon",...a},l)=>{let{orientation:n,scrollNext:d,canScrollNext:x}=m();return(0,r.jsxs)(c.$,{ref:l,variant:t,size:s,className:(0,o.cn)("absolute h-8 w-8 rounded-full","horizontal"===n?"-right-12 top-1/2 -translate-y-1/2":"-bottom-12 left-1/2 -translate-x-1/2 rotate-90",e),disabled:!x,onClick:d,...a,children:[(0,r.jsx)(i.A,{className:"h-4 w-4"}),(0,r.jsx)("span",{className:"sr-only",children:"Next slide"})]})}).displayName="CarouselNext"},38246:(e,t,s)=>{s.d(t,{default:()=>r});let r=(0,s(29037).registerClientReference)(function(){throw Error("Attempted to call the default export of \"/Users/<USER>/My-Project/fluxkrea_test/components/blocks/showcase/prompt-showcase.tsx\" from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"/Users/<USER>/My-Project/fluxkrea_test/components/blocks/showcase/prompt-showcase.tsx","default")},51247:(e,t,s)=>{s.d(t,{Avatar:()=>a,AvatarImage:()=>l});var r=s(29037);let a=(0,r.registerClientReference)(function(){throw Error("Attempted to call Avatar() from the server but Avatar is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"/Users/<USER>/My-Project/fluxkrea_test/components/ui/avatar.tsx","Avatar"),l=(0,r.registerClientReference)(function(){throw Error("Attempted to call AvatarImage() from the server but AvatarImage is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"/Users/<USER>/My-Project/fluxkrea_test/components/ui/avatar.tsx","AvatarImage");(0,r.registerClientReference)(function(){throw Error("Attempted to call AvatarFallback() from the server but AvatarFallback is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"/Users/<USER>/My-Project/fluxkrea_test/components/ui/avatar.tsx","AvatarFallback")},58243:(e,t,s)=>{s.d(t,{E:()=>o});var r=s(34411);s(37582);var a=s(20392),l=s(38e3),n=s(25039);let i=(0,l.F)("inline-flex items-center justify-center rounded-md border px-2 py-0.5 text-xs font-medium w-fit whitespace-nowrap shrink-0 [&>svg]:size-3 gap-1 [&>svg]:pointer-events-none focus-visible:border-ring focus-visible:ring-ring/50 focus-visible:ring-[3px] aria-invalid:ring-destructive/20 dark:aria-invalid:ring-destructive/40 aria-invalid:border-destructive transition-[color,box-shadow] overflow-hidden",{variants:{variant:{default:"border-transparent bg-primary text-primary-foreground [a&]:hover:bg-primary/90",secondary:"border-transparent bg-secondary text-secondary-foreground [a&]:hover:bg-secondary/90",destructive:"border-transparent bg-destructive text-white [a&]:hover:bg-destructive/90 focus-visible:ring-destructive/20 dark:focus-visible:ring-destructive/40 dark:bg-destructive/60",outline:"text-foreground [a&]:hover:bg-accent [a&]:hover:text-accent-foreground"}},defaultVariants:{variant:"default"}});function o({className:e,variant:t,asChild:s=!1,...l}){let o=s?a.DX:"span";return(0,r.jsx)(o,{"data-slot":"badge",className:(0,n.cn)(i({variant:t}),e),...l})}},60854:(e,t,s)=>{s.d(t,{Ay:()=>o});var r=s(34411),a=s(18586),l=s(77094),n=s(10050),i=s.n(n);function o({section:e}){return e.disabled?null:(0,r.jsxs)("section",{className:"container py-16",children:[(0,r.jsxs)("div",{className:"mx-auto mb-12 text-center",children:[(0,r.jsx)("h2",{className:"mb-6 text-pretty text-3xl font-bold lg:text-4xl",children:e.title}),(0,r.jsx)("p",{className:"mb-4 max-w-xl text-muted-foreground lg:max-w-none lg:text-lg",children:e.description})]}),(0,r.jsx)("div",{className:"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8",children:e.items?.map((e,t)=>r.jsx(i(),{href:e.url||"",target:e.target,children:r.jsx(a.Zp,{className:"overflow-hidden transition-all hover:shadow-lg dark:hover:shadow-primary/10 p-0",children:r.jsxs(a.Wu,{className:"p-0",children:[r.jsx("div",{className:"relative aspect-[16/10] w-full overflow-hidden",children:r.jsx(l.default,{src:e.image?.src||"",alt:e.image?.alt||e.title||"",fill:!0,className:"object-cover rounded-t-lg transition-transform duration-300 hover:scale-110"})}),r.jsxs("div",{className:"p-6",children:[r.jsx("h3",{className:"text-xl font-semibold mb-2 line-clamp-1",children:e.title}),r.jsx("p",{className:"text-sm text-muted-foreground line-clamp-3",children:e.description})]})]})})},t))})]})}s(38246),s(83093)},75525:(e,t,s)=>{s.d(t,{A:()=>l});var r=s(34411),a=s(57595);function l({section:e}){return e.disabled?null:(0,r.jsx)("section",{id:e.name,className:"py-16",children:(0,r.jsxs)("div",{className:"container",children:[(0,r.jsxs)("div",{className:"mx-auto flex max-w-(--breakpoint-md) flex-col items-center gap-2",children:[(0,r.jsx)("h2",{className:"mb-2 text-pretty text-3xl font-bold lg:text-4xl",children:e.title}),(0,r.jsx)("p",{className:"mb-8 max-w-xl text-muted-foreground lg:max-w-none lg:text-lg",children:e.description})]}),(0,r.jsx)("div",{className:"grid gap-10 md:grid-cols-2 lg:grid-cols-3",children:e.items?.map((e,t)=>r.jsxs("div",{className:"flex flex-col",children:[e.icon&&r.jsx("div",{className:"mb-5 flex size-16 items-center justify-center rounded-full border border-primary",children:r.jsx(a.default,{name:e.icon,className:"size-8 text-primary"})}),r.jsx("h3",{className:"mb-2 text-xl font-semibold",children:e.title}),r.jsx("p",{className:"text-muted-foreground",children:e.description})]},t))})]})})}},83093:(e,t,s)=>{s.d(t,{default:()=>r});let r=(0,s(29037).registerClientReference)(function(){throw Error("Attempted to call the default export of \"/Users/<USER>/My-Project/fluxkrea_test/components/blocks/showcase/comparison-showcase.tsx\" from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"/Users/<USER>/My-Project/fluxkrea_test/components/blocks/showcase/comparison-showcase.tsx","default")},89395:(e,t,s)=>{s.d(t,{default:()=>d});var r=s(2569),a=s(63686),l=s(30044),n=s(29068),i=s(21637),o=s(56175),c=s(92227);function d({section:e}){if(e.disabled)return null;let[t,s]=(0,n.useState)(),[d,m]=(0,n.useState)("1");return(0,r.jsx)("section",{id:e.name,className:"py-32",children:(0,r.jsx)("div",{className:"container",children:(0,r.jsxs)("div",{className:"mx-auto grid gap-20 lg:grid-cols-2",children:[(0,r.jsxs)("div",{children:[e.label&&(0,r.jsx)(i.E,{variant:"outline",className:"mb-4",children:e.label}),(0,r.jsx)("h2",{className:"mb-6 text-pretty text-3xl font-bold lg:text-4xl",children:e.title}),(0,r.jsx)("p",{className:"mb-4 max-w-xl text-muted-foreground lg:max-w-none lg:text-lg",children:e.description}),(0,r.jsx)(a.nD,{type:"single",value:d,onValueChange:e=>{m(e),console.log(e),t?.scrollTo(+e-1)},children:e.items?.map((e,t)=>r.jsxs(a.As,{value:(t+1).toString(),className:"border-b-0 border-secondary",children:[r.jsx(a.$m,{className:"text-left data-[state=closed]:text-muted-foreground",children:r.jsxs("div",{className:"flex items-center justify-between gap-2",children:[e.icon&&r.jsx("p",{className:"flex size-9 items-center justify-center rounded-lg bg-muted",children:r.jsx(c.default,{name:e.icon,className:"size-5 shrink-0 lg:size-6"})}),r.jsx("span",{className:"font-medium lg:text-lg",children:e.title})]})}),r.jsxs(a.ub,{className:"text-muted-foreground lg:text-base",children:[e.description,r.jsx("div",{className:"mt-8 h-px bg-muted",children:r.jsx("div",{className:"h-px animate-progress bg-primary",style:{animationDuration:"5000ms"}})})]})]},t))})]}),(0,r.jsx)("div",{children:(0,r.jsx)(l.FN,{opts:{duration:50},setApi:s,plugins:[(0,o.A)()],children:(0,r.jsx)(l.Wk,{children:e.items?.map((e,t)=>r.jsx(l.A7,{children:r.jsx("div",{children:r.jsx("img",{src:e.image?.src,alt:e.image?.alt||e.title,className:"max-h-auto w-full object-cover lg:max-h-none rounded-md"})})},t))})})})]})})})}},92172:(e,t,s)=>{s.d(t,{$:()=>o});var r=s(34411);s(37582);var a=s(20392),l=s(38e3),n=s(25039);let i=(0,l.F)("inline-flex items-center justify-center gap-2 whitespace-nowrap rounded-lg text-sm font-medium transition-all duration-200 disabled:pointer-events-none disabled:opacity-50 [&_svg]:pointer-events-none [&_svg:not([class*='size-'])]:size-4 shrink-0 [&_svg]:shrink-0 outline-none focus-visible:border-ring focus-visible:ring-ring/50 focus-visible:ring-[3px] aria-invalid:ring-destructive/20 dark:aria-invalid:ring-destructive/40 aria-invalid:border-destructive",{variants:{variant:{default:"bg-gradient-to-r from-primary to-accent text-primary-foreground shadow-md hover:shadow-lg hover:scale-[1.02] active:scale-[0.98]",destructive:"bg-destructive text-destructive-foreground shadow-md hover:bg-destructive/90 hover:shadow-lg hover:scale-[1.02] active:scale-[0.98] focus-visible:ring-destructive/20 dark:focus-visible:ring-destructive/40",outline:"border-2 border-primary/20 bg-background/50 backdrop-blur-sm shadow-sm hover:bg-accent/50 hover:text-accent-foreground hover:border-primary/40 hover:shadow-md hover:scale-[1.02] active:scale-[0.98] dark:bg-input/30 dark:border-input dark:hover:bg-input/50",secondary:"bg-gradient-to-r from-secondary to-muted text-secondary-foreground shadow-sm hover:shadow-md hover:scale-[1.02] active:scale-[0.98]",ghost:"hover:bg-accent/50 hover:text-accent-foreground hover:scale-[1.02] active:scale-[0.98] dark:hover:bg-accent/30",link:"text-primary underline-offset-4 hover:underline hover:text-accent"},size:{default:"h-10 px-6 py-2 has-[>svg]:px-4",sm:"h-8 rounded-lg gap-1.5 px-4 has-[>svg]:px-3",lg:"h-12 rounded-lg px-8 has-[>svg]:px-6 text-base",icon:"size-10 rounded-full"}},defaultVariants:{variant:"default",size:"default"}});function o({className:e,variant:t,size:s,asChild:l=!1,...o}){let c=l?a.DX:"button";return(0,r.jsx)(c,{"data-slot":"button",className:(0,n.cn)(i({variant:t,size:s,className:e})),...o})}},95826:(e,t,s)=>{s.d(t,{A:()=>l});var r=s(34411),a=s(57595);function l({section:e}){return e.disabled?null:(0,r.jsx)("section",{id:e.name,className:"py-16",children:(0,r.jsxs)("div",{className:"container flex flex-col items-center gap-4",children:[e.label&&(0,r.jsxs)("div",{className:"flex items-center gap-1 text-sm font-semibold text-primary",children:[e.icon&&(0,r.jsx)(a.default,{name:e.icon,className:"h-6 w-auto border-primary"}),e.label]}),(0,r.jsx)("h2",{className:"text-center text-3xl font-semibold lg:text-4xl",children:e.title}),(0,r.jsx)("p",{className:"text-center text-muted-foreground lg:text-lg",children:e.description}),(0,r.jsx)("div",{className:"w-full grid gap-10 md:grid-cols-3 lg:gap-0 mt-8",children:e.items?.map((e,t)=>r.jsxs("div",{className:"text-center",children:[r.jsx("p",{className:"text-lg font-semibold text-muted-foreground",children:e.title}),r.jsx("p",{className:"pt-2 text-7xl font-semibold lg:pt-4 text-primary",children:e.label}),r.jsx("p",{className:"text-xl mt-2 font-normal text-muted-foreground",children:e.description})]},t))})]})})}},97594:(e,t,s)=>{s.d(t,{A:()=>l});var r=s(34411),a=s(57595);function l({section:e}){return e.disabled?null:(0,r.jsx)("section",{id:e.name,className:"py-16",children:(0,r.jsx)("div",{className:"container",children:(0,r.jsxs)("div",{className:"grid items-center gap-8 lg:grid-cols-2 lg:gap-16",children:[e.image&&(0,r.jsx)("img",{src:e.image?.src,alt:"placeholder hero",className:"max-h-full w-full rounded-md object-cover"}),(0,r.jsxs)("div",{className:"flex flex-col lg:text-left",children:[e.title&&(0,r.jsx)("h2",{className:"mb-6 text-pretty text-3xl font-bold lg:text-4xl",children:e.title}),e.description&&(0,r.jsx)("p",{className:"mb-8 max-w-xl text-muted-foreground lg:max-w-none lg:text-lg",children:e.description}),(0,r.jsx)("ul",{className:"flex flex-col justify-center gap-y-8",children:e.items?.map((e,t)=>r.jsxs("li",{className:"flex",children:[e.icon&&r.jsx(a.default,{name:e.icon,className:"mr-2 size-6 shrink-0 lg:mr-2 lg:size-6"}),r.jsxs("div",{children:[r.jsx("div",{className:"mb-3 h-5 text-sm font-semibold text-accent-foreground md:text-base",children:e.title}),r.jsx("div",{className:"text-sm font-medium text-muted-foreground md:text-base",children:e.description})]})]},t))})]})]})})})}}};