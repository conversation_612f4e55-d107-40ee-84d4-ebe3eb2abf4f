"use strict";exports.id=1826,exports.ids=[1826],exports.modules={21637:(e,a,s)=>{s.d(a,{E:()=>n});var r=s(2569);s(29068);var t=s(70166),l=s(75958),i=s(75673);let d=(0,l.F)("inline-flex items-center justify-center rounded-md border px-2 py-0.5 text-xs font-medium w-fit whitespace-nowrap shrink-0 [&>svg]:size-3 gap-1 [&>svg]:pointer-events-none focus-visible:border-ring focus-visible:ring-ring/50 focus-visible:ring-[3px] aria-invalid:ring-destructive/20 dark:aria-invalid:ring-destructive/40 aria-invalid:border-destructive transition-[color,box-shadow] overflow-hidden",{variants:{variant:{default:"border-transparent bg-primary text-primary-foreground [a&]:hover:bg-primary/90",secondary:"border-transparent bg-secondary text-secondary-foreground [a&]:hover:bg-secondary/90",destructive:"border-transparent bg-destructive text-white [a&]:hover:bg-destructive/90 focus-visible:ring-destructive/20 dark:focus-visible:ring-destructive/40 dark:bg-destructive/60",outline:"text-foreground [a&]:hover:bg-accent [a&]:hover:text-accent-foreground"}},defaultVariants:{variant:"default"}});function n({className:e,variant:a,asChild:s=!1,...l}){let n=s?t.DX:"span";return(0,r.jsx)(n,{"data-slot":"badge",className:(0,i.cn)(d({variant:a}),e),...l})}},27084:(e,a,s)=>{s.d(a,{default:()=>u});var r=s(2569),t=s(68064),l=s(92622),i=s(21637),d=s(29068),n=s(42688),o=s(61865),c=s(62135),m=s(18230),x=s(13748);function u({section:e}){let[a,s]=(0,d.useState)(null),[u,h]=(0,d.useState)(new Set);if(e.disabled)return null;let f=async(e,a)=>{try{await navigator.clipboard.writeText(e),s(a),setTimeout(()=>s(null),2e3)}catch(e){console.error("Failed to copy prompt:",e)}},g=e=>{let a=new Set(u);a.has(e)?a.delete(e):a.add(e),h(a)};return(0,r.jsxs)("section",{className:"container py-16",children:[(0,r.jsxs)("div",{className:"mx-auto mb-12 text-center",children:[(0,r.jsx)("h2",{className:"mb-6 text-pretty text-3xl font-bold lg:text-4xl",children:e.title}),(0,r.jsx)("p",{className:"mb-4 max-w-xl text-muted-foreground lg:max-w-none lg:text-lg",children:e.description})]}),(0,r.jsx)("div",{className:"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6",children:e.items?.map((e,s)=>r.jsxs(t.Zp,{className:"overflow-hidden transition-all hover:shadow-lg dark:hover:shadow-primary/10 flex flex-col h-full",children:[e.image&&r.jsxs("div",{className:"relative aspect-[4/3] w-full overflow-hidden",children:[r.jsx(x.default,{src:e.image.src,alt:e.image.alt||e.title,fill:!0,className:"object-cover transition-transform duration-300 hover:scale-105"}),e.category&&r.jsx("div",{className:"absolute top-3 right-3",children:r.jsx(i.E,{variant:"outline",className:"bg-background/80 backdrop-blur-sm",children:e.category})})]}),r.jsxs(t.aR,{className:"pb-3",children:[r.jsx(t.ZB,{className:"text-lg font-semibold line-clamp-2",children:e.title}),e.description&&r.jsx("p",{className:"text-sm text-muted-foreground line-clamp-2 mt-1",children:e.description})]}),r.jsxs(t.Wu,{className:"flex-1 flex flex-col space-y-3",children:[e.tags&&e.tags.length>0&&r.jsxs("div",{className:"flex flex-wrap gap-1",children:[e.tags.slice(0,3).map((e,a)=>r.jsx(i.E,{variant:"secondary",className:"text-xs px-2 py-0.5",children:e},a)),e.tags.length>3&&r.jsxs(i.E,{variant:"secondary",className:"text-xs px-2 py-0.5",children:["+",e.tags.length-3]})]}),r.jsx("div",{className:"flex-1",children:u.has(s)&&r.jsx("div",{className:"mb-3",children:r.jsx("div",{className:"bg-muted/50 rounded-lg p-3 border border-border/50",children:r.jsx("p",{className:"text-xs font-mono leading-relaxed text-foreground/90 whitespace-pre-wrap break-words",children:e.prompt})})})}),r.jsxs("div",{className:"space-y-2",children:[r.jsx(l.$,{onClick:()=>g(s),variant:"outline",size:"sm",className:"w-full transition-all duration-200",children:u.has(s)?r.jsxs(r.Fragment,{children:[r.jsx(n.A,{className:"w-4 h-4 mr-2"}),"隐藏 Prompt"]}):r.jsxs(r.Fragment,{children:[r.jsx(o.A,{className:"w-4 h-4 mr-2"}),"显示 Prompt"]})}),u.has(s)&&r.jsx(l.$,{onClick:()=>f(e.prompt,s),variant:"default",size:"sm",className:"w-full transition-all duration-200",children:a===s?r.jsxs(r.Fragment,{children:[r.jsx(c.A,{className:"w-4 h-4 mr-2"}),"已复制"]}):r.jsxs(r.Fragment,{children:[r.jsx(m.A,{className:"w-4 h-4 mr-2"}),"复制 Prompt"]})})]})]})]},s))})]})}},68064:(e,a,s)=>{s.d(a,{BT:()=>n,Wu:()=>c,X9:()=>o,ZB:()=>d,Zp:()=>l,aR:()=>i,wL:()=>m});var r=s(2569);s(29068);var t=s(75673);function l({className:e,...a}){return(0,r.jsx)("div",{"data-slot":"card",className:(0,t.cn)("bg-card text-card-foreground flex flex-col gap-6 rounded-2xl border border-border/50 p-6 shadow-lg backdrop-blur-sm transition-all duration-200 hover:shadow-xl hover:scale-[1.01]",e),...a})}function i({className:e,...a}){return(0,r.jsx)("div",{"data-slot":"card-header",className:(0,t.cn)("@container/card-header grid auto-rows-min grid-rows-[auto_auto] items-start gap-2 has-data-[slot=card-action]:grid-cols-[1fr_auto] [.border-b]:pb-6",e),...a})}function d({className:e,...a}){return(0,r.jsx)("div",{"data-slot":"card-title",className:(0,t.cn)("leading-none font-semibold",e),...a})}function n({className:e,...a}){return(0,r.jsx)("div",{"data-slot":"card-description",className:(0,t.cn)("text-muted-foreground text-sm",e),...a})}function o({className:e,...a}){return(0,r.jsx)("div",{"data-slot":"card-action",className:(0,t.cn)("col-start-2 row-span-2 row-start-1 self-start justify-self-end",e),...a})}function c({className:e,...a}){return(0,r.jsx)("div",{"data-slot":"card-content",className:(0,t.cn)("px-6",e),...a})}function m({className:e,...a}){return(0,r.jsx)("div",{"data-slot":"card-footer",className:(0,t.cn)("flex items-center px-6 [.border-t]:pt-6",e),...a})}},73699:(e,a,s)=>{s.d(a,{default:()=>x});var r=s(2569),t=s(68064),l=s(92622),i=s(21637),d=s(29068),n=s(19405),o=s(62135),c=s(18230),m=s(13748);function x({section:e}){let[a,s]=(0,d.useState)(null);if(e.disabled)return null;let x=async(e,a)=>{try{await navigator.clipboard.writeText(e),s(a),setTimeout(()=>s(null),2e3)}catch(e){console.error("Failed to copy prompt:",e)}};return(0,r.jsxs)("section",{className:"container py-16",children:[(0,r.jsxs)("div",{className:"mx-auto mb-12 text-center",children:[(0,r.jsx)("h2",{className:"mb-6 text-pretty text-3xl font-bold lg:text-4xl",children:e.title}),(0,r.jsx)("p",{className:"mb-4 max-w-xl text-muted-foreground lg:max-w-none lg:text-lg",children:e.description})]}),(0,r.jsx)("div",{className:"grid grid-cols-1 gap-8 max-w-none",children:e.items?.map((e,s)=>r.jsxs(t.Zp,{className:"overflow-hidden transition-all hover:shadow-lg dark:hover:shadow-primary/10 flex flex-col max-w-4xl mx-auto w-full",children:[r.jsxs(t.aR,{className:"pb-4",children:[r.jsxs("div",{className:"flex items-start justify-between gap-2",children:[r.jsx(t.ZB,{className:"text-xl font-semibold line-clamp-2 flex-1",children:e.title}),e.category&&r.jsx(i.E,{variant:"outline",className:"shrink-0",children:e.category})]}),e.description&&r.jsx("p",{className:"text-sm text-muted-foreground line-clamp-2 mt-2",children:e.description})]}),r.jsxs(t.Wu,{className:"flex-1 flex flex-col space-y-6",children:[r.jsxs("div",{className:"space-y-4",children:[r.jsxs("div",{className:"block md:hidden space-y-4",children:[r.jsxs("div",{className:"space-y-2",children:[r.jsx("p",{className:"text-sm font-medium text-muted-foreground",children:"处理前"}),r.jsx("div",{className:"relative w-full overflow-hidden rounded-lg border border-border/50",children:r.jsx(m.default,{src:e.beforeImage.src,alt:e.beforeImage.alt,width:600,height:400,className:"w-full h-auto object-contain transition-transform duration-300 hover:scale-105"})})]}),r.jsx("div",{className:"flex justify-center",children:r.jsx(n.A,{className:"w-6 h-6 text-muted-foreground rotate-90"})}),r.jsxs("div",{className:"space-y-2",children:[r.jsx("p",{className:"text-sm font-medium text-muted-foreground",children:"处理后"}),r.jsx("div",{className:"relative w-full overflow-hidden rounded-lg border border-border/50",children:r.jsx(m.default,{src:e.afterImage.src,alt:e.afterImage.alt,width:600,height:400,className:"w-full h-auto object-contain transition-transform duration-300 hover:scale-105"})})]})]}),r.jsxs("div",{className:"hidden md:flex items-center gap-4",children:[r.jsxs("div",{className:"flex-1 space-y-2",children:[r.jsx("p",{className:"text-sm font-medium text-muted-foreground",children:"处理前"}),r.jsx("div",{className:"relative w-full overflow-hidden rounded-lg border border-border/50",children:r.jsx(m.default,{src:e.beforeImage.src,alt:e.beforeImage.alt,width:600,height:400,className:"w-full h-auto object-contain transition-transform duration-300 hover:scale-105"})})]}),r.jsx("div",{className:"flex-shrink-0",children:r.jsx(n.A,{className:"w-6 h-6 text-muted-foreground"})}),r.jsxs("div",{className:"flex-1 space-y-2",children:[r.jsx("p",{className:"text-sm font-medium text-muted-foreground",children:"处理后"}),r.jsx("div",{className:"relative w-full overflow-hidden rounded-lg border border-border/50",children:r.jsx(m.default,{src:e.afterImage.src,alt:e.afterImage.alt,width:600,height:400,className:"w-full h-auto object-contain transition-transform duration-300 hover:scale-105"})})]})]})]}),r.jsxs("div",{className:"space-y-3",children:[r.jsx("p",{className:"text-sm font-medium text-foreground",children:"使用的 Prompt"}),r.jsx("div",{className:"bg-muted/50 rounded-lg p-4 border border-border/50",children:r.jsx("p",{className:"text-sm font-mono leading-relaxed text-foreground/90 whitespace-pre-wrap break-words",children:e.prompt})})]}),e.tags&&e.tags.length>0&&r.jsxs("div",{className:"flex flex-wrap gap-2",children:[e.tags.slice(0,4).map((e,a)=>r.jsx(i.E,{variant:"secondary",className:"text-xs px-2 py-1",children:e},a)),e.tags.length>4&&r.jsxs(i.E,{variant:"secondary",className:"text-xs px-2 py-1",children:["+",e.tags.length-4]})]}),r.jsx(l.$,{onClick:()=>x(e.prompt,s),variant:"outline",size:"sm",className:"w-full transition-all duration-200 hover:bg-primary hover:text-primary-foreground",children:a===s?r.jsxs(r.Fragment,{children:[r.jsx(o.A,{className:"w-4 h-4 mr-2"}),"已复制"]}):r.jsxs(r.Fragment,{children:[r.jsx(c.A,{className:"w-4 h-4 mr-2"}),"复制 Prompt"]})})]})]},s))})]})}}};