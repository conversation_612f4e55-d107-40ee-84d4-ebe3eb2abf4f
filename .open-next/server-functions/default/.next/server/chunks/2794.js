exports.id=2794,exports.ids=[2794],exports.modules={2814:(e,t,r)=>{"use strict";r.d(t,{default:()=>a});var n=r(29037);let a=(0,n.registerClientReference)(function(){throw Error("Attempted to call the default export of \"/Users/<USER>/My-Project/fluxkrea_test/components/language/detector.tsx\" from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"/Users/<USER>/My-Project/fluxkrea_test/components/language/detector.tsx","default");(0,n.registerClientReference)(function(){throw Error("Attempted to call SimpleLanguageDetector() from the server but SimpleLanguageDetector is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"/Users/<USER>/My-Project/fluxkrea_test/components/language/detector.tsx","SimpleLanguageDetector"),(0,n.registerClientReference)(function(){throw Error("Attempted to call DebugLanguageDetector() from the server but DebugLanguageDetector is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"/Users/<USER>/My-Project/fluxkrea_test/components/language/detector.tsx","DebugLanguageDetector")},6637:(e,t,r)=>{"use strict";r.d(t,{AppContextProvider:()=>h,U:()=>u});var n=r(2569),a=r(29068),s=r(67534),o=r(99293),i=r(66802),l=r.n(i);r(19711);var d=r(54487);let c=(0,a.createContext)({}),u=()=>(0,a.useContext)(c),h=({children:e})=>{!function(){let{data:e,status:t}=(0,d.wV)();n.Fragment}();let{data:t}=(0,d.wV)(),[r,i]=(0,a.useState)(()=>"light"),[u,h]=(0,a.useState)(!1),[m,g]=(0,a.useState)(null),[f,p]=(0,a.useState)(!1),v=async function(){try{let e=await fetch("/api/get-user-info",{method:"POST"});if(!e.ok)throw Error("fetch user info failed with status: "+e.status);let{code:t,message:r,data:n}=await e.json();if(0!==t)throw Error(r);g(n),x(n)}catch(e){console.log("fetch user info failed")}},x=async e=>{try{if(e.invited_by){console.log("user already been invited",e.invited_by);return}let t=(0,s.pr)(o.wD.InviteCode);if(!t)return;let r=l()(e.created_at).unix(),n=l()().unix(),a=Number(n-r);if(a<=0||a>7200){console.log("user created more than 2 hours");return}console.log("update invite",t,e.uuid);let i={invite_code:t,user_uuid:e.uuid},d=await fetch("/api/update-invite",{method:"POST",headers:{"Content-Type":"application/json"},body:JSON.stringify(i)});if(!d.ok)throw Error("update invite failed with status: "+d.status);let{code:c,message:u,data:h}=await d.json();if(0!==c)throw Error(u);g(h),(0,s.np)(o.wD.InviteCode)}catch(e){console.log("update invite failed: ",e)}};return(0,a.useEffect)(()=>{t&&t.user&&v()},[t]),(0,n.jsx)(c.Provider,{value:{theme:r,setTheme:i,showSignModal:u,setShowSignModal:h,user:m,setUser:g,showFeedback:f,setShowFeedback:p},children:e})}},10895:(e,t,r)=>{Promise.resolve().then(r.bind(r,86859)),Promise.resolve().then(r.bind(r,2814)),Promise.resolve().then(r.bind(r,32868)),Promise.resolve().then(r.bind(r,37032)),Promise.resolve().then(r.bind(r,52262)),Promise.resolve().then(r.bind(r,77174))},14552:(e,t,r)=>{var n={"./en.json":[98611,8611],"./zh.json":[53798,3798]};function a(e){if(!r.o(n,e))return Promise.resolve().then(()=>{var t=Error("Cannot find module '"+e+"'");throw t.code="MODULE_NOT_FOUND",t});var t=n[e],a=t[0];return r.e(t[1]).then(()=>r.t(a,19))}a.keys=()=>Object.keys(n),a.id=14552,e.exports=a},18898:(e,t,r)=>{"use strict";r.d(t,{BZ:()=>o,aj:()=>i,eV:()=>l,k3:()=>d,xd:()=>s});var n=r(19115);let a={en:"en","en-US":"en","en-GB":"en","en-CA":"en","en-AU":"en",zh:"zh","zh-CN":"zh","zh-TW":"zh","zh-HK":"zh","zh-MO":"zh","zh-SG":"zh"};function s(){return null}function o(){for(let e of[]){let t=function(e){if(a[e])return a[e];let t=e.split("-")[0];return a[t]?a[t]:null}(e);if(t&&n.IB.includes(t))return t}return null}function i(e){let t=o();return t&&t!==e?t:null}function l(e){return({en:"English",zh:"中文"})[e]||e}function d(e){let t=s(),r=o(),l=i(e);return{browserLanguage:t,browserLanguages:[],supportedBrowserLang:r,currentLocale:e,suggestedLang:l,appSupportedLanguages:n.IB,languageMapping:a}}},19115:(e,t,r)=>{"use strict";r.d(t,{GB:()=>i,IB:()=>n,L$:()=>a,b:()=>o,q:()=>s,u7:()=>l});let n=["en","zh"],a={en:"English",zh:"中文"},s="en",o="as-needed",i=!1,l={en:{"privacy-policy":"/privacy-policy","terms-of-service":"/terms-of-service"}}},21307:(e,t,r)=>{"use strict";r.d(t,{lg:()=>n});let n=()=>Date.parse(new Date().toUTCString())/1e3},23766:()=>{},25039:(e,t,r)=>{"use strict";r.d(t,{cn:()=>s});var n=r(1832),a=r(22757);function s(...e){return(0,a.QP)((0,n.$)(e))}},25253:(e,t,r)=>{"use strict";r.d(t,{GB:()=>i,IB:()=>n,L$:()=>a,b:()=>o,q:()=>s,u7:()=>l});let n=["en","zh"],a={en:"English",zh:"中文"},s="en",o="as-needed",i=!1,l={en:{"privacy-policy":"/privacy-policy","terms-of-service":"/terms-of-service"}}},26062:(e,t,r)=>{Promise.resolve().then(r.t.bind(r,9350,23)),Promise.resolve().then(r.t.bind(r,20016,23)),Promise.resolve().then(r.t.bind(r,17220,23)),Promise.resolve().then(r.t.bind(r,69207,23)),Promise.resolve().then(r.t.bind(r,87743,23)),Promise.resolve().then(r.t.bind(r,41879,23)),Promise.resolve().then(r.t.bind(r,18567,23)),Promise.resolve().then(r.t.bind(r,32337,23))},32868:(e,t,r)=>{"use strict";r.d(t,{AppContextProvider:()=>a});var n=r(29037);(0,n.registerClientReference)(function(){throw Error("Attempted to call useAppContext() from the server but useAppContext is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"/Users/<USER>/My-Project/fluxkrea_test/contexts/app.tsx","useAppContext");let a=(0,n.registerClientReference)(function(){throw Error("Attempted to call AppContextProvider() from the server but AppContextProvider is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"/Users/<USER>/My-Project/fluxkrea_test/contexts/app.tsx","AppContextProvider")},45546:(e,t,r)=>{"use strict";r.d(t,{ThemeProvider:()=>z});var n=r(2569),a=r(85226);function s(){let e="G-DFL8TGPG03";return e?(0,n.jsx)(a.GoogleAnalytics,{gaId:e}):null}function o(){return null}function i(){return null}r(17474);var l=r(94446);function d(){let e="snteb2f8tj";return e?(0,n.jsx)(l.default,{id:"microsoft-clarity",strategy:"afterInteractive",children:`
    (function(c, l, a, r, i, t, y) {
      c[a] = c[a] || function() {
        (c[a].q = c[a].q || []).push(arguments)
      };
      t = l.createElement(r);
      t.async = 1;
      t.src = "https://www.clarity.ms/tag/" + i;
      y = l.getElementsByTagName(r)[0];
      y.parentNode.insertBefore(t, y);
    })(window, document, "clarity", "script", "${e}");
  `}):null}function c(){return(0,n.jsxs)(n.Fragment,{children:[(0,n.jsx)(o,{}),(0,n.jsx)(s,{}),(0,n.jsx)(i,{}),(0,n.jsx)(d,{})]})}r(99293);var u=r(29068),h=r(97946),m=r(14673),g=r(75673);function f({...e}){return(0,n.jsx)(m._s.Root,{"data-slot":"drawer",...e})}function p({...e}){return(0,n.jsx)(m._s.Portal,{"data-slot":"drawer-portal",...e})}function v({...e}){return(0,n.jsx)(m._s.Close,{"data-slot":"drawer-close",...e})}function x({className:e,...t}){return(0,n.jsx)(m._s.Overlay,{"data-slot":"drawer-overlay",className:(0,g.cn)("data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=open]:fade-in-0 fixed inset-0 z-50 bg-black/50",e),...t})}function b({className:e,children:t,...r}){return(0,n.jsxs)(p,{"data-slot":"drawer-portal",children:[(0,n.jsx)(x,{}),(0,n.jsxs)(m._s.Content,{"data-slot":"drawer-content",className:(0,g.cn)("group/drawer-content bg-background fixed z-50 flex h-auto flex-col","data-[vaul-drawer-direction=top]:inset-x-0 data-[vaul-drawer-direction=top]:top-0 data-[vaul-drawer-direction=top]:mb-24 data-[vaul-drawer-direction=top]:max-h-[80vh] data-[vaul-drawer-direction=top]:rounded-b-lg data-[vaul-drawer-direction=top]:border-b","data-[vaul-drawer-direction=bottom]:inset-x-0 data-[vaul-drawer-direction=bottom]:bottom-0 data-[vaul-drawer-direction=bottom]:mt-24 data-[vaul-drawer-direction=bottom]:max-h-[80vh] data-[vaul-drawer-direction=bottom]:rounded-t-lg data-[vaul-drawer-direction=bottom]:border-t","data-[vaul-drawer-direction=right]:inset-y-0 data-[vaul-drawer-direction=right]:right-0 data-[vaul-drawer-direction=right]:w-3/4 data-[vaul-drawer-direction=right]:border-l data-[vaul-drawer-direction=right]:sm:max-w-sm","data-[vaul-drawer-direction=left]:inset-y-0 data-[vaul-drawer-direction=left]:left-0 data-[vaul-drawer-direction=left]:w-3/4 data-[vaul-drawer-direction=left]:border-r data-[vaul-drawer-direction=left]:sm:max-w-sm",e),...r,children:[(0,n.jsx)("div",{className:"bg-muted mx-auto mt-4 hidden h-2 w-[100px] shrink-0 rounded-full group-data-[vaul-drawer-direction=bottom]/drawer-content:block"}),t]})]})}function w({className:e,...t}){return(0,n.jsx)("div",{"data-slot":"drawer-header",className:(0,g.cn)("flex flex-col gap-1.5 p-4",e),...t})}function j({className:e,...t}){return(0,n.jsx)("div",{"data-slot":"drawer-footer",className:(0,g.cn)("mt-auto flex flex-col gap-2 p-4",e),...t})}function y({className:e,...t}){return(0,n.jsx)(m._s.Title,{"data-slot":"drawer-title",className:(0,g.cn)("text-foreground font-semibold",e),...t})}function C({className:e,...t}){return(0,n.jsx)(m._s.Description,{"data-slot":"drawer-description",className:(0,g.cn)("text-muted-foreground text-sm",e),...t})}var N=r(44401),P=r(71569),k=r(92622),_=r(54487),L=r(6637),S=r(73054);function A(){let e=(0,S.c3)(),{showSignModal:t,setShowSignModal:r}=(0,L.U)(),[a,s]=u.useState(!1);return!function(e){let[t,r]=(0,u.useState)(!1);return t}(0)?(0,n.jsx)(f,{open:t,onOpenChange:r,children:(0,n.jsxs)(b,{children:[(0,n.jsxs)(w,{className:"text-left",children:[(0,n.jsx)(y,{children:e("sign_modal.sign_in_title")}),(0,n.jsx)(C,{children:e("sign_modal.sign_in_description")})]}),(0,n.jsx)(D,{className:"px-4"}),(0,n.jsx)(j,{className:"pt-4",children:(0,n.jsx)(v,{asChild:!0,children:(0,n.jsx)(k.$,{variant:"outline",children:e("sign_modal.cancel_title")})})})]})}):(0,n.jsx)(h.lG,{open:t,onOpenChange:r,children:(0,n.jsxs)(h.Cf,{className:"sm:max-w-[425px]",children:[(0,n.jsxs)(h.c7,{children:[(0,n.jsx)(h.L3,{children:e("sign_modal.sign_in_title")}),(0,n.jsx)(h.rr,{children:e("sign_modal.sign_in_description")})]}),(0,n.jsx)(D,{})]})})}function D({className:e}){let t=(0,S.c3)(),[r,a]=u.useState(!1),[s,o]=u.useState(!1),i=async()=>{a(!0);try{await (0,_.Jv)("google")}catch(e){console.error("Google登录失败:",e),a(!1)}};return(0,n.jsxs)("div",{className:(0,g.cn)("grid items-start gap-4",e),children:[(0,n.jsx)(k.$,{variant:"outline",className:"w-full flex items-center gap-2",onClick:i,disabled:r||s,children:r?(0,n.jsxs)(n.Fragment,{children:[(0,n.jsx)(P.A,{className:"w-4 h-4 animate-spin"}),t("sign_modal.google_signing_in")]}):(0,n.jsxs)(n.Fragment,{children:[(0,n.jsx)(N.UKz,{className:"w-4 h-4"}),t("sign_modal.google_sign_in")]})}),!1]})}var E=r(39365);let $=({...e})=>{let{theme:t}=(0,L.U)();return(0,n.jsx)(E.l$,{theme:t,className:"toaster group",style:{"--normal-bg":"var(--popover)","--normal-text":"var(--popover-foreground)","--normal-border":"var(--border)"},...e})};function z({children:e}){let{theme:t,setTheme:r}=(0,L.U)();return(0,n.jsxs)(n.Fragment,{children:[e,(0,n.jsx)($,{position:"top-center",richColors:!0}),(0,n.jsx)(A,{}),(0,n.jsx)(c,{})]})}r(67534)},53748:(e,t,r)=>{"use strict";r.d(t,{A:()=>d});var n=r(2569);r(29068);var a=r(73054),s=r(97946),o=r(92622),i=r(62254),l=r(18898);function d({open:e,currentLanguage:t,suggestedLanguage:r,onSwitch:d,onCancel:c,onClose:u}){let h=(0,a.c3)("language_switch"),m=(0,l.eV)(t),g=(0,l.eV)(r),f=h("description",{currentLanguage:m,suggestedLanguage:g}),p=h(`switch_button_${r}`,{suggestedLanguage:g}),v=h(`cancel_button_${t}`,{currentLanguage:m});return(0,n.jsx)(s.lG,{open:e,onOpenChange:u,children:(0,n.jsxs)(s.Cf,{className:"sm:max-w-md",children:[(0,n.jsxs)(s.c7,{children:[(0,n.jsxs)(s.L3,{className:"flex items-center gap-2",children:[(0,n.jsx)(i.aVW,{className:"text-xl text-primary"}),h("title")]}),(0,n.jsx)(s.rr,{className:"text-left",children:f})]}),(0,n.jsxs)(s.Es,{className:"flex-col sm:flex-row gap-2",children:[(0,n.jsx)(o.$,{variant:"outline",onClick:()=>{c(),u()},className:"w-full sm:w-auto",children:v}),(0,n.jsx)(o.$,{onClick:()=>{d(),u()},className:"w-full sm:w-auto",children:p})]})]})})}},63232:(e,t,r)=>{"use strict";r.d(t,{G:()=>f,default:()=>g});var n=r(2569),a=r(29068),s=r(14059),o=r(18898),i=r(70080),l=r(97946),d=r(92622),c=r(62254);function u({open:e,currentLanguage:t,suggestedLanguage:r,onSwitch:a,onCancel:s,onClose:i,texts:u}){let h=(0,o.eV)(t),m=(0,o.eV)(r),g=(e,t)=>"zh"===e?`切换到${t}`:`Switch to ${t}`,f=(e,t)=>"zh"===e?`保持${t}`:`Keep ${t}`,p={title:"Switch Language?",description:`We detected that your browser language is ${m}. Would you like to switch from ${h} to ${m}?`,switchButton:g(r,m),cancelButton:f(t,h)},v={title:"切换语言？",description:`我们检测到您的浏览器语言是${m}。您是否要从${h}切换到${m}？`,switchButton:g(r,m),cancelButton:f(t,h)},x=u||("zh"===t?v:p);return(0,n.jsx)(l.lG,{open:e,onOpenChange:i,children:(0,n.jsxs)(l.Cf,{className:"sm:max-w-md",children:[(0,n.jsxs)(l.c7,{children:[(0,n.jsxs)(l.L3,{className:"flex items-center gap-2",children:[(0,n.jsx)(c.aVW,{className:"text-xl text-primary"}),x.title]}),(0,n.jsx)(l.rr,{className:"text-left",children:x.description})]}),(0,n.jsxs)(l.Es,{className:"flex-col sm:flex-row gap-2",children:[(0,n.jsx)(d.$,{variant:"outline",onClick:()=>{s(),i()},className:"w-full sm:w-auto",children:x.cancelButton}),(0,n.jsx)(d.$,{onClick:()=>{a(),i()},className:"w-full sm:w-auto",children:x.switchButton})]})]})})}function h({open:e,currentLanguage:t,suggestedLanguage:r,onSwitch:a,onCancel:s,onClose:o}){return(0,n.jsx)(u,{open:e,currentLanguage:t,suggestedLanguage:r,onSwitch:a,onCancel:s,onClose:o})}var m=r(53748);function g({debug:e=!1,detectionDelay:t=1e3,onLanguageSwitch:r,useI18n:l=!0}){let d=(0,s.useParams)(),c=(0,s.useRouter)(),u=(0,s.usePathname)(),g=d.locale,[f,p]=(0,a.useState)(!1),[v,x]=(0,a.useState)(null),[b,w]=(0,a.useState)(!1),j=(e,t)=>{if(r)r(e,t);else{let r=u.replace(`/${e}`,`/${t}`);r.startsWith(`/${t}`)||(r=`/${t}${r}`),c.push(r)}},y=()=>{if(!v)return;let e=(0,o.aj)(g);e&&((0,i.l5)(g,v,e),j(g,v))},C=()=>{if(!v)return;let e=(0,o.aj)(g);e&&(0,i.OE)(g,v,e)};return e&&b?(0,n.jsxs)("div",{className:"fixed bottom-4 right-4 bg-black/80 text-white p-4 rounded-lg text-xs max-w-sm z-50",children:[(0,n.jsx)("h4",{className:"font-bold mb-2",children:"Language Detection Debug"}),(0,n.jsxs)("div",{className:"space-y-1",children:[(0,n.jsxs)("div",{children:["Current: ",g]}),(0,n.jsxs)("div",{children:["Suggested: ",v||"None"]}),(0,n.jsxs)("div",{children:["Show Dialog: ",f?"Yes":"No"]})]}),f&&(0,n.jsx)(h,{open:f,currentLanguage:g,suggestedLanguage:v,onSwitch:y,onCancel:C,onClose:()=>p(!1)})]}):(0,n.jsx)(n.Fragment,{children:f&&v&&(l?(0,n.jsx)(m.A,{open:f,currentLanguage:g,suggestedLanguage:v,onSwitch:y,onCancel:C,onClose:()=>p(!1)}):(0,n.jsx)(h,{open:f,currentLanguage:g,suggestedLanguage:v,onSwitch:y,onCancel:C,onClose:()=>p(!1)}))})}function f(){return(0,n.jsx)(g,{debug:!0,detectionDelay:500})}},65814:(e,t,r)=>{Promise.resolve().then(r.t.bind(r,79848,23)),Promise.resolve().then(r.t.bind(r,20038,23)),Promise.resolve().then(r.t.bind(r,66054,23)),Promise.resolve().then(r.t.bind(r,69737,23)),Promise.resolve().then(r.t.bind(r,54061,23)),Promise.resolve().then(r.t.bind(r,32245,23)),Promise.resolve().then(r.t.bind(r,60125,23)),Promise.resolve().then(r.t.bind(r,21767,23))},67534:(e,t,r)=>{"use strict";r.d(t,{PW:()=>s,np:()=>o,pr:()=>a});var n=r(21307);let a=e=>{let t=localStorage.getItem(e);if(!t)return null;let r=t.split(":");if(!r||r.length<2)return null;let a=Number(r[0]),s=(0,n.lg)();if(-1!==a&&a<s)return o(e),null;let i=r[0]+":";return t.replace(i,"")},s=(e,t,r)=>{localStorage.setItem(e,r+":"+t)},o=e=>{localStorage.removeItem(e)}},70080:(e,t,r)=>{"use strict";r.d(t,{A0:()=>l,DQ:()=>g,OE:()=>m,l5:()=>h,or:()=>c,rB:()=>u});var n=r(67534),a=r(99293),s=r(21307);function o(e,t=30){let r=-1===t?-1:(0,s.lg)()+86400*t;(0,n.PW)(a.wD.LanguagePreference,JSON.stringify(e),r)}function i(){let e=(0,n.pr)(a.wD.LanguagePreference);if(!e)return null;try{return JSON.parse(e)}catch(e){return console.error("Failed to parse language preference:",e),l(),null}}function l(){(0,n.np)(a.wD.LanguagePreference)}function d(e,t,r,o=7){let i={languagePair:`${e}-${t}`,timestamp:(0,s.lg)(),choice:r},l=(0,s.lg)()+86400*o;(0,n.PW)(a.wD.LanguageSwitchAsked,JSON.stringify(i),l)}function c(){(0,n.np)(a.wD.LanguageSwitchAsked)}function u(e,t){let r=i();if(r&&(r.selectedLanguage===e||r.declined&&r.detectedLanguage===t&&r.selectedLanguage===e))return!1;let s=function(e,t){let r=(0,n.pr)(a.wD.LanguageSwitchAsked);if(!r)return null;try{let n=JSON.parse(r),a=`${e}-${t}`;if(n.languagePair===a)return n;return null}catch(e){return console.error("Failed to parse language switch asked record:",e),null}}(e,t);return!s||(s.choice,!1)}function h(e,t,r){o({selectedLanguage:t,detectedLanguage:r,declined:!1,timestamp:(0,s.lg)()}),d(e,t,"accepted")}function m(e,t,r){o({selectedLanguage:e,detectedLanguage:r,declined:!0,timestamp:(0,s.lg)()}),d(e,t,"declined")}function g(){return{preference:i(),switchAsked:(0,n.pr)(a.wD.LanguageSwitchAsked),timestamp:(0,s.lg)()}}},70727:(e,t,r)=>{Promise.resolve().then(r.bind(r,72337)),Promise.resolve().then(r.bind(r,63232)),Promise.resolve().then(r.bind(r,6637)),Promise.resolve().then(r.bind(r,20518)),Promise.resolve().then(r.bind(r,23664)),Promise.resolve().then(r.bind(r,45546))},72337:(e,t,r)=>{"use strict";r.d(t,{NextAuthSessionProvider:()=>s});var n=r(2569),a=r(54487);function s({children:e}){return(0,n.jsx)(a.CP,{children:e})}},75673:(e,t,r)=>{"use strict";r.d(t,{cn:()=>s});var n=r(90974),a=r(70919);function s(...e){return(0,a.QP)((0,n.$)(e))}},77174:(e,t,r)=>{"use strict";r.d(t,{ThemeProvider:()=>n});let n=(0,r(29037).registerClientReference)(function(){throw Error("Attempted to call ThemeProvider() from the server but ThemeProvider is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"/Users/<USER>/My-Project/fluxkrea_test/providers/theme.tsx","ThemeProvider")},78933:(e,t,r)=>{"use strict";r.d(t,{DT:()=>s,N_:()=>o});var n=r(25253),a=r(5656);let s=(0,r(37398).A)({locales:n.IB,defaultLocale:n.q,localePrefix:n.b,pathnames:n.u7,localeDetection:n.GB}),{Link:o,redirect:i,usePathname:l,useRouter:d}=(0,a.A)(s)},82606:(e,t,r)=>{var n={"./en.json":[45989,5989],"./zh.json":[9776,9776]};function a(e){if(!r.o(n,e))return Promise.resolve().then(()=>{var t=Error("Cannot find module '"+e+"'");throw t.code="MODULE_NOT_FOUND",t});var t=n[e],a=t[0];return r.e(t[1]).then(()=>r.t(a,19))}a.keys=()=>Object.keys(n),a.id=82606,e.exports=a},83581:(e,t,r)=>{"use strict";r.r(t),r.d(t,{default:()=>g});var n=r(34411);r(23766);var a=r(76470),s=r(25253),o=r(32868),i=r(75916),l=r.n(i),d=r(86859),c=r(35927),u=r(77174),h=r(25039),m=r(2814);async function g({children:e,params:t}){let{locale:r}=await t,i=await (0,a.A)(),g="http://localhost:3000";return(0,n.jsxs)("html",{lang:r,suppressHydrationWarning:!0,children:[(0,n.jsxs)("head",{children:[(0,n.jsx)("meta",{name:"viewport",content:"width=device-width, initial-scale=1.0"}),"",(0,n.jsx)("link",{rel:"icon",href:"/favicon.ico"}),s.IB&&s.IB.map(e=>(0,n.jsx)("link",{rel:"alternate",hrefLang:e,href:`${g}${"en"===e?"":`/${e}`}/`},e)),(0,n.jsx)("link",{rel:"alternate",hrefLang:"x-default",href:g})]}),(0,n.jsx)("body",{className:(0,h.cn)("min-h-screen bg-background font-sans antialiased overflow-x-hidden",l().variable),children:(0,n.jsx)(c.A,{messages:i,children:(0,n.jsx)(d.NextAuthSessionProvider,{children:(0,n.jsx)(o.AppContextProvider,{children:(0,n.jsxs)(u.ThemeProvider,{children:[e,(0,n.jsx)(m.default,{})]})})})})})]})}},86859:(e,t,r)=>{"use strict";r.d(t,{NextAuthSessionProvider:()=>n});let n=(0,r(29037).registerClientReference)(function(){throw Error("Attempted to call NextAuthSessionProvider() from the server but NextAuthSessionProvider is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"/Users/<USER>/My-Project/fluxkrea_test/auth/session.tsx","NextAuthSessionProvider")},92622:(e,t,r)=>{"use strict";r.d(t,{$:()=>l,r:()=>i});var n=r(2569);r(29068);var a=r(70166),s=r(75958),o=r(75673);let i=(0,s.F)("inline-flex items-center justify-center gap-2 whitespace-nowrap rounded-lg text-sm font-medium transition-all duration-200 disabled:pointer-events-none disabled:opacity-50 [&_svg]:pointer-events-none [&_svg:not([class*='size-'])]:size-4 shrink-0 [&_svg]:shrink-0 outline-none focus-visible:border-ring focus-visible:ring-ring/50 focus-visible:ring-[3px] aria-invalid:ring-destructive/20 dark:aria-invalid:ring-destructive/40 aria-invalid:border-destructive",{variants:{variant:{default:"bg-gradient-to-r from-primary to-accent text-primary-foreground shadow-md hover:shadow-lg hover:scale-[1.02] active:scale-[0.98]",destructive:"bg-destructive text-destructive-foreground shadow-md hover:bg-destructive/90 hover:shadow-lg hover:scale-[1.02] active:scale-[0.98] focus-visible:ring-destructive/20 dark:focus-visible:ring-destructive/40",outline:"border-2 border-primary/20 bg-background/50 backdrop-blur-sm shadow-sm hover:bg-accent/50 hover:text-accent-foreground hover:border-primary/40 hover:shadow-md hover:scale-[1.02] active:scale-[0.98] dark:bg-input/30 dark:border-input dark:hover:bg-input/50",secondary:"bg-gradient-to-r from-secondary to-muted text-secondary-foreground shadow-sm hover:shadow-md hover:scale-[1.02] active:scale-[0.98]",ghost:"hover:bg-accent/50 hover:text-accent-foreground hover:scale-[1.02] active:scale-[0.98] dark:hover:bg-accent/30",link:"text-primary underline-offset-4 hover:underline hover:text-accent"},size:{default:"h-10 px-6 py-2 has-[>svg]:px-4",sm:"h-8 rounded-lg gap-1.5 px-4 has-[>svg]:px-3",lg:"h-12 rounded-lg px-8 has-[>svg]:px-6 text-base",icon:"size-10 rounded-full"}},defaultVariants:{variant:"default",size:"default"}});function l({className:e,variant:t,size:r,asChild:s=!1,...l}){let d=s?a.DX:"button";return(0,n.jsx)(d,{"data-slot":"button",className:(0,o.cn)(i({variant:t,size:r,className:e})),...l})}},94066:(e,t,r)=>{"use strict";r.d(t,{A:()=>s});var n=r(68099),a=r(78933);let s=(0,n.A)(async({requestLocale:e})=>{let t=await e;t&&a.DT.locales.includes(t)||(t=a.DT.defaultLocale),["zh-CN"].includes(t)&&(t="zh"),a.DT.locales.includes(t)||(t="en");try{let e=(await r(14552)(`./${t.toLowerCase()}.json`)).default,n={};try{let e=(await r(82606)(`./${t.toLowerCase()}.json`)).default;n["ai-dashboard"]=e}catch(e){try{let e=(await r.e(5989).then(r.t.bind(r,45989,19))).default;n["ai-dashboard"]=e}catch(e){}}return{locale:t,messages:{...e,...n}}}catch(e){return{locale:"en",messages:(await r.e(8611).then(r.t.bind(r,98611,19))).default}}})},97946:(e,t,r)=>{"use strict";r.d(t,{Cf:()=>u,Es:()=>m,L3:()=>g,c7:()=>h,lG:()=>l,rr:()=>f});var n=r(2569),a=r(29068),s=r(61224),o=r(49671),i=r(75673);let l=s.bL;s.l9;let d=s.ZL;s.bm;let c=a.forwardRef(({className:e,...t},r)=>(0,n.jsx)(s.hJ,{ref:r,className:(0,i.cn)("fixed inset-0 z-50 bg-black/80  data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=open]:fade-in-0",e),...t}));c.displayName=s.hJ.displayName;let u=a.forwardRef(({className:e,children:t,...r},a)=>(0,n.jsxs)(d,{children:[(0,n.jsx)(c,{}),(0,n.jsxs)(s.UC,{ref:a,className:(0,i.cn)("fixed left-[50%] top-[50%] z-50 grid w-full max-w-lg translate-x-[-50%] translate-y-[-50%] gap-4 border bg-background p-6 shadow-lg duration-200 data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=open]:fade-in-0 data-[state=closed]:zoom-out-95 data-[state=open]:zoom-in-95 data-[state=closed]:slide-out-to-left-1/2 data-[state=closed]:slide-out-to-top-[48%] data-[state=open]:slide-in-from-left-1/2 data-[state=open]:slide-in-from-top-[48%] sm:rounded-lg",e),...r,children:[t,(0,n.jsxs)(s.bm,{className:"absolute right-4 top-4 rounded-sm opacity-70 ring-offset-background transition-opacity hover:opacity-100 focus:outline-hidden focus:ring-2 focus:ring-ring focus:ring-offset-2 disabled:pointer-events-none data-[state=open]:bg-accent data-[state=open]:text-muted-foreground",children:[(0,n.jsx)(o.A,{className:"h-4 w-4"}),(0,n.jsx)("span",{className:"sr-only",children:"Close"})]})]})]}));u.displayName=s.UC.displayName;let h=({className:e,...t})=>(0,n.jsx)("div",{className:(0,i.cn)("flex flex-col space-y-1.5 text-center sm:text-left",e),...t});h.displayName="DialogHeader";let m=({className:e,...t})=>(0,n.jsx)("div",{className:(0,i.cn)("flex flex-col-reverse sm:flex-row sm:justify-end sm:space-x-2",e),...t});m.displayName="DialogFooter";let g=a.forwardRef(({className:e,...t},r)=>(0,n.jsx)(s.hE,{ref:r,className:(0,i.cn)("text-lg font-semibold leading-none tracking-tight",e),...t}));g.displayName=s.hE.displayName;let f=a.forwardRef(({className:e,...t},r)=>(0,n.jsx)(s.VY,{ref:r,className:(0,i.cn)("text-sm text-muted-foreground",e),...t}));f.displayName=s.VY.displayName},99293:(e,t,r)=>{"use strict";r.d(t,{wD:()=>n});let n={Theme:"THEME",InviteCode:"INVITE_CODE",LanguagePreference:"LANGUAGE_PREFERENCE",LanguageSwitchAsked:"LANGUAGE_SWITCH_ASKED"}}};