exports.id=995,exports.ids=[995],exports.modules={1647:(e,t,a)=>{"use strict";a.d(t,{default:()=>s});let s=(0,a(29037).registerClientReference)(function(){throw Error("Attempted to call the default export of \"/Users/<USER>/My-Project/fluxkrea_test/components/blocks/form/index.tsx\" from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"/Users/<USER>/My-Project/fluxkrea_test/components/blocks/form/index.tsx","default")},20822:(e,t,a)=>{"use strict";a.d(t,{J:()=>o});var s=a(2569);a(29068);var r=a(64463),n=a(75673);function o({className:e,...t}){return(0,s.jsx)(r.b,{"data-slot":"label",className:(0,n.cn)("flex items-center gap-2 text-sm leading-none font-medium select-none group-data-[disabled=true]:pointer-events-none group-data-[disabled=true]:opacity-50 peer-disabled:cursor-not-allowed peer-disabled:opacity-50",e),...t})}},32184:(e,t,a)=>{"use strict";a.d(t,{T:()=>o});var s=a(2569),r=a(29068),n=a(75673);let o=r.forwardRef(({className:e,...t},a)=>(0,s.jsx)("textarea",{className:(0,n.cn)("flex min-h-[80px] w-full rounded-md border border-input bg-background px-3 py-2 text-base ring-offset-background placeholder:text-muted-foreground focus-visible:outline-hidden focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:cursor-not-allowed disabled:opacity-50 md:text-sm",e),ref:a,...t}));o.displayName="Textarea"},72925:()=>{},75490:(e,t,a)=>{"use strict";a.d(t,{A:()=>B});var s=a(2569);a(72925);var r=a(72852),n=a(62872),o=a(27828),l=a(19502),i=a(21111);a(29068);var c=a(4332),d=a(72047),u=a(23052),g=a(78270),m=a(13167),b=a(62759),h=a(40999),p=a(12410),x=a(47429),f=a(27408),v=a(12355),y=a(21190),j=a(38087),N=a(25223),k=a(60106),A=a(21155),w=a(26695),C=a(66705),$=a(52778),H=a(63200),F=a(22520);function I(){let{editor:e}=(0,l.aA)();return e?(0,s.jsxs)("div",{className:"flex flex-wrap gap-1 p-2 border-b mb-4",children:[(0,s.jsx)("button",{type:"button",onClick:()=>e.chain().focus().toggleBold().run(),disabled:!e.can().chain().focus().toggleBold().run(),className:`p-2 rounded hover:bg-gray-100 ${e.isActive("bold")?"bg-gray-100":""}`,title:"Bold",children:(0,s.jsx)(c.A,{className:"w-4 h-4"})}),(0,s.jsx)("button",{type:"button",onClick:()=>e.chain().focus().toggleItalic().run(),disabled:!e.can().chain().focus().toggleItalic().run(),className:`p-2 rounded hover:bg-gray-100 ${e.isActive("italic")?"bg-gray-100":""}`,title:"Italic",children:(0,s.jsx)(d.A,{className:"w-4 h-4"})}),(0,s.jsx)("button",{type:"button",onClick:()=>e.chain().focus().toggleStrike().run(),disabled:!e.can().chain().focus().toggleStrike().run(),className:`p-2 rounded hover:bg-gray-100 ${e.isActive("strike")?"bg-gray-100":""}`,title:"Strike",children:(0,s.jsx)(u.A,{className:"w-4 h-4"})}),(0,s.jsx)("button",{type:"button",onClick:()=>e.chain().focus().toggleCode().run(),disabled:!e.can().chain().focus().toggleCode().run(),className:`p-2 rounded hover:bg-gray-100 ${e.isActive("code")?"bg-gray-100":""}`,title:"Code",children:(0,s.jsx)(g.A,{className:"w-4 h-4"})}),(0,s.jsx)("button",{type:"button",onClick:()=>e.chain().focus().unsetAllMarks().run(),className:"p-2 rounded hover:bg-gray-100",title:"Clear marks",children:(0,s.jsx)(m.A,{className:"w-4 h-4"})}),(0,s.jsx)("button",{type:"button",onClick:()=>e.chain().focus().setParagraph().run(),className:`p-2 rounded hover:bg-gray-100 ${e.isActive("paragraph")?"bg-gray-100":""}`,title:"Paragraph",children:(0,s.jsx)(b.A,{className:"w-4 h-4"})}),(0,s.jsx)("button",{type:"button",onClick:()=>e.chain().focus().toggleHeading({level:1}).run(),className:`p-2 rounded hover:bg-gray-100 ${e.isActive("heading",{level:1})?"bg-gray-100":""}`,title:"Heading 1",children:(0,s.jsx)(h.A,{className:"w-4 h-4"})}),(0,s.jsx)("button",{type:"button",onClick:()=>e.chain().focus().toggleHeading({level:2}).run(),className:`p-2 rounded hover:bg-gray-100 ${e.isActive("heading",{level:2})?"bg-gray-100":""}`,title:"Heading 2",children:(0,s.jsx)(p.A,{className:"w-4 h-4"})}),(0,s.jsx)("button",{type:"button",onClick:()=>e.chain().focus().toggleHeading({level:3}).run(),className:`p-2 rounded hover:bg-gray-100 ${e.isActive("heading",{level:3})?"bg-gray-100":""}`,title:"Heading 3",children:(0,s.jsx)(x.A,{className:"w-4 h-4"})}),(0,s.jsx)("button",{type:"button",onClick:()=>e.chain().focus().toggleHeading({level:4}).run(),className:`p-2 rounded hover:bg-gray-100 ${e.isActive("heading",{level:4})?"bg-gray-100":""}`,title:"Heading 4",children:(0,s.jsx)(f.A,{className:"w-4 h-4"})}),(0,s.jsx)("button",{type:"button",onClick:()=>e.chain().focus().toggleHeading({level:5}).run(),className:`p-2 rounded hover:bg-gray-100 ${e.isActive("heading",{level:5})?"bg-gray-100":""}`,title:"Heading 5",children:(0,s.jsx)(v.A,{className:"w-4 h-4"})}),(0,s.jsx)("button",{type:"button",onClick:()=>e.chain().focus().toggleHeading({level:6}).run(),className:`p-2 rounded hover:bg-gray-100 ${e.isActive("heading",{level:6})?"bg-gray-100":""}`,title:"Heading 6",children:(0,s.jsx)(y.A,{className:"w-4 h-4"})}),(0,s.jsx)("button",{type:"button",onClick:()=>e.chain().focus().toggleBulletList().run(),className:`p-2 rounded hover:bg-gray-100 ${e.isActive("bulletList")?"bg-gray-100":""}`,title:"Bullet list",children:(0,s.jsx)(j.A,{className:"w-4 h-4"})}),(0,s.jsx)("button",{type:"button",onClick:()=>e.chain().focus().toggleOrderedList().run(),className:`p-2 rounded hover:bg-gray-100 ${e.isActive("orderedList")?"bg-gray-100":""}`,title:"Ordered list",children:(0,s.jsx)(N.A,{className:"w-4 h-4"})}),(0,s.jsx)("button",{type:"button",onClick:()=>e.chain().focus().toggleCodeBlock().run(),className:`p-2 rounded hover:bg-gray-100 ${e.isActive("codeBlock")?"bg-gray-100":""}`,title:"Code block",children:(0,s.jsx)(k.A,{className:"w-4 h-4"})}),(0,s.jsx)("button",{type:"button",onClick:()=>e.chain().focus().toggleBlockquote().run(),className:`p-2 rounded hover:bg-gray-100 ${e.isActive("blockquote")?"bg-gray-100":""}`,title:"Blockquote",children:(0,s.jsx)(A.A,{className:"w-4 h-4"})}),(0,s.jsx)("button",{type:"button",onClick:()=>e.chain().focus().setHorizontalRule().run(),className:"p-2 rounded hover:bg-gray-100",title:"Horizontal rule",children:(0,s.jsx)(w.A,{className:"w-4 h-4"})}),(0,s.jsx)("button",{type:"button",onClick:()=>e.chain().focus().setHardBreak().run(),className:"p-2 rounded hover:bg-gray-100",title:"Hard break",children:(0,s.jsx)(C.A,{className:"w-4 h-4"})}),(0,s.jsx)("button",{type:"button",onClick:()=>e.chain().focus().undo().run(),disabled:!e.can().chain().focus().undo().run(),className:"p-2 rounded hover:bg-gray-100",title:"Undo",children:(0,s.jsx)($.A,{className:"w-4 h-4"})}),(0,s.jsx)("button",{type:"button",onClick:()=>e.chain().focus().redo().run(),disabled:!e.can().chain().focus().redo().run(),className:"p-2 rounded hover:bg-gray-100",title:"Redo",children:(0,s.jsx)(H.A,{className:"w-4 h-4"})}),(0,s.jsx)("button",{type:"button",onClick:()=>e.chain().focus().setColor("#958DF1").run(),className:`p-2 rounded hover:bg-gray-100 ${e.isActive("textStyle",{color:"#958DF1"})?"bg-gray-100":""}`,title:"Purple",children:(0,s.jsx)(F.A,{className:"w-4 h-4"})})]}):null}let R=[r.Q.configure({types:[o.A.name,n.A.name]}),o.A,i.A.configure({bulletList:{keepMarks:!0,keepAttributes:!1},orderedList:{keepMarks:!0,keepAttributes:!1}})];function B({value:e,onChange:t}){return(0,s.jsx)("div",{className:"border rounded-md bg-background",children:(0,s.jsx)(l.zz,{slotBefore:(0,s.jsx)(I,{}),extensions:R,content:e,onUpdate:({editor:e})=>{t(e.getHTML())}})})}},78085:(e,t,a)=>{"use strict";a.d(t,{default:()=>B});var s=a(2569),r=a(29068),n=a(70166),o=a(37459),l=a(75673),i=a(20822);let c=o.Op,d=r.createContext({}),u=({...e})=>(0,s.jsx)(d.Provider,{value:{name:e.name},children:(0,s.jsx)(o.xI,{...e})}),g=()=>{let e=r.useContext(d),t=r.useContext(m),{getFieldState:a,formState:s}=(0,o.xW)(),n=a(e.name,s);if(!e)throw Error("useFormField should be used within <FormField>");let{id:l}=t;return{id:l,name:e.name,formItemId:`${l}-form-item`,formDescriptionId:`${l}-form-item-description`,formMessageId:`${l}-form-item-message`,...n}},m=r.createContext({}),b=r.forwardRef(({className:e,...t},a)=>{let n=r.useId();return(0,s.jsx)(m.Provider,{value:{id:n},children:(0,s.jsx)("div",{ref:a,className:(0,l.cn)("space-y-2",e),...t})})});b.displayName="FormItem";let h=r.forwardRef(({className:e,...t},a)=>{let{error:r,formItemId:n}=g();return(0,s.jsx)(i.J,{ref:a,className:(0,l.cn)(r&&"text-destructive",e),htmlFor:n,...t})});h.displayName="FormLabel";let p=r.forwardRef(({...e},t)=>{let{error:a,formItemId:r,formDescriptionId:o,formMessageId:l}=g();return(0,s.jsx)(n.DX,{ref:t,id:r,"aria-describedby":a?`${o} ${l}`:`${o}`,"aria-invalid":!!a,...e})});p.displayName="FormControl";let x=r.forwardRef(({className:e,...t},a)=>{let{formDescriptionId:r}=g();return(0,s.jsx)("p",{ref:a,id:r,className:(0,l.cn)("text-sm text-muted-foreground",e),...t})});x.displayName="FormDescription";let f=r.forwardRef(({className:e,children:t,...a},r)=>{let{error:n,formMessageId:o}=g(),i=n?String(n?.message):t;return i?(0,s.jsx)("p",{ref:r,id:o,className:(0,l.cn)("text-sm font-medium text-destructive",e),...a,children:i}):null});f.displayName="FormMessage";var v=a(14078),y=a(92622),j=a(92227),N=a(3764),k=a(67412),A=a(94591),w=a(75490),C=a(32184),$=a(39365),H=a(37967),F=a(90198),I=a(99390);let R=e=>{let t={};return e.forEach(e=>{e.name&&(t[e.name]=function(e){let t=F.z.string();return e.validation?.required&&(t=t.min(1,{message:e.validation.message||`${e.title} is required`})),e.validation?.min&&(t=t.min(e.validation.min,{message:e.validation.message||`${e.title} must be at least ${e.validation.min} characters`})),e.validation?.max&&(t=t.max(e.validation.max,{message:e.validation.message||`${e.title} must be at most ${e.validation.max} characters`})),e.validation?.email&&(t=t.email({message:e.validation.message||`${e.title} must be a valid email`})),t}(e))}),F.z.object(t)};function B({fields:e,data:t,passby:a,submit:r,loading:n}){e||(e=[]);let l=(0,H.rd)(),i=R(e),d={};e.forEach(e=>{e.name&&(d[e.name]=t?.[e.name]||e.value||"")});let g=(0,o.mN)({resolver:(0,I.u)(i),defaultValues:d});async function m(e){if(r?.handler)try{let t=new FormData;Object.entries(e).forEach(([e,a])=>{t.append(e,a)});let s=await r.handler(t,a);if(!s)throw Error("No response received from server");s.message&&("success"===s.status?$.oR.success(s.message):$.oR.error(s.message)),s.redirect_url&&l.push(s.redirect_url)}catch(e){console.log("submit form error",e),$.oR.error(e.message||"submit form failed")}}return(0,s.jsx)(c,{...g,children:(0,s.jsxs)("form",{onSubmit:g.handleSubmit(m),className:"w-full md:w-1/2 lg:w-1/2 space-y-6 px-2 pb-8",children:[e.map((e,t)=>(0,s.jsx)(u,{control:g.control,name:e.name||"",render:({field:t})=>(0,s.jsxs)(b,{children:[(0,s.jsxs)(h,{children:[e.title,e.validation?.required&&(0,s.jsx)("span",{className:"text-red-500 ml-1",children:"*"})]}),(0,s.jsx)(p,{children:"textarea"===e.type?(0,s.jsx)(C.T,{...t,placeholder:e.placeholder,...e.attributes}):"select"===e.type?(0,s.jsxs)(v.l6,{onValueChange:t.onChange,defaultValue:t.value,...e.attributes,children:[(0,s.jsx)(v.bq,{className:"w-full bg-background rounded-md",children:(0,s.jsx)(v.yv,{placeholder:e.placeholder})}),(0,s.jsx)(v.gC,{className:"bg-background rounded-md",children:e.options?.map(e=>s.jsx(v.eb,{value:e.value,children:e.title},e.value))})]}):"markdown_editor"===e.type?(0,s.jsx)(A.A,{value:t.value,onChange:t.onChange}):"editor"===e.type||"richtext_editor"===e.type?(0,s.jsx)(w.A,{value:t.value,onChange:t.onChange}):(0,s.jsx)(N.p,{...t,type:e.type||"text",placeholder:e.placeholder,className:"bg-background rounded-md",...e.attributes})}),e.tip&&(0,s.jsx)(x,{dangerouslySetInnerHTML:{__html:e.tip}}),(0,s.jsx)(f,{})]})},t)),r?.button&&(0,s.jsxs)(y.$,{type:"submit",variant:r.button.variant,className:"flex items-center justify-center gap-2 font-semibold cursor-pointer",disabled:n,children:[n?(0,s.jsx)(k.A,{className:"mr-2 h-4 w-4 animate-spin"}):r.button.icon&&(0,s.jsx)(j.default,{name:r.button.icon,className:"size-4"}),r.button.title]})]})})}},94591:(e,t,a)=>{"use strict";a.d(t,{A:()=>n});var s=a(2569);a(29068);let r=(0,a(10394).default)(async()=>{},{loadableGenerated:{modules:["components/blocks/mdeditor/index.tsx -> @uiw/react-md-editor"]},ssr:!1});function n({value:e,onChange:t}){return(0,s.jsx)("div",{className:"w-full md:w-[800px]",children:(0,s.jsx)(r,{value:e,onChange:e=>t(e||""),height:600})})}}};