"use strict";exports.id=2686,exports.ids=[2686],exports.modules={14066:(e,t,a)=>{a.d(t,{default:()=>n});var r=a(2569),o=a(53467),s=a(39365);function n({text:e,children:t}){return(0,r.jsx)(o.<PERSON>oClipboard,{text:e,onCopy:()=>s.oR.success("Copied"),children:(0,r.jsx)("div",{className:"cursor-pointer",children:t})})}},15585:(e,t,a)=>{a.d(t,{A:()=>m});var r=a(34411),o=a(60050);function s({value:e,options:t,className:a}){return(0,r.jsx)("img",{src:e,alt:e,className:`w-10 h-10 rounded-full ${a}`})}var n=a(58243);function i({value:e,options:t,className:a}){return(0,r.jsx)(n.E,{variant:t?.variant??"secondary",className:a,children:e})}var l=a(22740),d=a.n(l);function c({value:e,options:t,className:a}){return(0,r.jsx)("div",{className:a,children:t?.format?d()(e).format(t?.format):d()(e).fromNow()})}var u=a(16976);function m({columns:e,data:t,emptyMessage:a}){return e||(e=[]),(0,r.jsxs)(o.Table,{className:"w-full",children:[(0,r.jsx)(o.TableHeader,{className:"",children:(0,r.jsx)(o.TableRow,{className:"rounded-md",children:e&&e.map((e,t)=>(0,r.jsx)(o.TableHead,{className:e.className,children:e.title},t))})}),(0,r.jsx)(o.TableBody,{children:t&&t.length>0?t.map((t,a)=>(0,r.jsx)(o.TableRow,{className:"h-16",children:e&&e.map((e,a)=>{let n=t[e.name],l=e.callback?e.callback(t):n,d=l;return"image"===e.type?d=(0,r.jsx)(s,{value:n,options:e.options,className:e.className}):"time"===e.type?d=(0,r.jsx)(c,{value:n,options:e.options,className:e.className}):"label"===e.type?d=(0,r.jsx)(i,{value:n,options:e.options,className:e.className}):"copy"===e.type&&n&&(d=(0,r.jsx)(u.default,{text:n,children:l})),(0,r.jsx)(o.TableCell,{className:e.className,children:d},a)})},a)):(0,r.jsx)(o.TableRow,{className:"",children:(0,r.jsx)(o.TableCell,{colSpan:e.length,children:(0,r.jsx)("div",{className:"flex w-full justify-center items-center py-8 text-muted-foreground",children:(0,r.jsx)("p",{children:a})})})})})]})}},16976:(e,t,a)=>{a.d(t,{default:()=>r});let r=(0,a(29037).registerClientReference)(function(){throw Error("Attempted to call the default export of \"/Users/<USER>/My-Project/fluxkrea_test/components/blocks/table/copy.tsx\" from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"/Users/<USER>/My-Project/fluxkrea_test/components/blocks/table/copy.tsx","default")},26820:(e,t,a)=>{a.r(t),a.d(t,{"00880263a376180560adeff1fdd2511a7543726d4a":()=>r.T});var r=a(39201)},34414:(e,t,a)=>{a.d(t,{A:()=>i});var r=a(34411),o=a(92172),s=a(57595),n=a(78933);function i({items:e}){return(0,r.jsx)("div",{className:"flex space-x-4 mb-8",children:e?.map((e,t)=>e.url?r.jsx(n.N_,{href:e.url,children:r.jsxs(o.$,{variant:e.variant,size:"sm",className:e.className,children:[e.icon&&r.jsx(s.default,{name:e.icon,className:"w-4 h-4 mr-1"}),e.title]})},t):r.jsxs(o.$,{variant:e.variant,size:"sm",className:e.className,children:[e.icon&&r.jsx(s.default,{name:e.icon,className:"w-4 h-4 mr-1"}),e.title]},t))})}},37967:(e,t,a)=>{a.d(t,{N_:()=>n,a8:()=>l,rd:()=>d});var r=a(19115),o=a(17345);let s=(0,a(64456).A)({locales:r.IB,defaultLocale:r.q,localePrefix:r.b,pathnames:r.u7,localeDetection:r.GB}),{Link:n,redirect:i,usePathname:l,useRouter:d}=(0,o.A)(s)},57595:(e,t,a)=>{a.r(t),a.d(t,{default:()=>r});let r=(0,a(29037).registerClientReference)(function(){throw Error("Attempted to call the default export of \"/Users/<USER>/My-Project/fluxkrea_test/components/icon/index.tsx\" from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"/Users/<USER>/My-Project/fluxkrea_test/components/icon/index.tsx","default")},58243:(e,t,a)=>{a.d(t,{E:()=>l});var r=a(34411);a(37582);var o=a(20392),s=a(38e3),n=a(25039);let i=(0,s.F)("inline-flex items-center justify-center rounded-md border px-2 py-0.5 text-xs font-medium w-fit whitespace-nowrap shrink-0 [&>svg]:size-3 gap-1 [&>svg]:pointer-events-none focus-visible:border-ring focus-visible:ring-ring/50 focus-visible:ring-[3px] aria-invalid:ring-destructive/20 dark:aria-invalid:ring-destructive/40 aria-invalid:border-destructive transition-[color,box-shadow] overflow-hidden",{variants:{variant:{default:"border-transparent bg-primary text-primary-foreground [a&]:hover:bg-primary/90",secondary:"border-transparent bg-secondary text-secondary-foreground [a&]:hover:bg-secondary/90",destructive:"border-transparent bg-destructive text-white [a&]:hover:bg-destructive/90 focus-visible:ring-destructive/20 dark:focus-visible:ring-destructive/40 dark:bg-destructive/60",outline:"text-foreground [a&]:hover:bg-accent [a&]:hover:text-accent-foreground"}},defaultVariants:{variant:"default"}});function l({className:e,variant:t,asChild:a=!1,...s}){let l=a?o.DX:"span";return(0,r.jsx)(l,{"data-slot":"badge",className:(0,n.cn)(i({variant:t}),e),...s})}},60050:(e,t,a)=>{a.d(t,{Table:()=>o,TableBody:()=>n,TableCell:()=>d,TableHead:()=>i,TableHeader:()=>s,TableRow:()=>l});var r=a(29037);let o=(0,r.registerClientReference)(function(){throw Error("Attempted to call Table() from the server but Table is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"/Users/<USER>/My-Project/fluxkrea_test/components/ui/table.tsx","Table"),s=(0,r.registerClientReference)(function(){throw Error("Attempted to call TableHeader() from the server but TableHeader is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"/Users/<USER>/My-Project/fluxkrea_test/components/ui/table.tsx","TableHeader"),n=(0,r.registerClientReference)(function(){throw Error("Attempted to call TableBody() from the server but TableBody is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"/Users/<USER>/My-Project/fluxkrea_test/components/ui/table.tsx","TableBody");(0,r.registerClientReference)(function(){throw Error("Attempted to call TableFooter() from the server but TableFooter is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"/Users/<USER>/My-Project/fluxkrea_test/components/ui/table.tsx","TableFooter");let i=(0,r.registerClientReference)(function(){throw Error("Attempted to call TableHead() from the server but TableHead is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"/Users/<USER>/My-Project/fluxkrea_test/components/ui/table.tsx","TableHead"),l=(0,r.registerClientReference)(function(){throw Error("Attempted to call TableRow() from the server but TableRow is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"/Users/<USER>/My-Project/fluxkrea_test/components/ui/table.tsx","TableRow"),d=(0,r.registerClientReference)(function(){throw Error("Attempted to call TableCell() from the server but TableCell is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"/Users/<USER>/My-Project/fluxkrea_test/components/ui/table.tsx","TableCell");(0,r.registerClientReference)(function(){throw Error("Attempted to call TableCaption() from the server but TableCaption is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"/Users/<USER>/My-Project/fluxkrea_test/components/ui/table.tsx","TableCaption")},65707:(e,t,a)=>{a.d(t,{A:()=>l});var r=a(34411),o=a(27419),s=a(15585),n=a(34414),i=a(18586);function l({...e}){return(0,r.jsxs)(r.Fragment,{children:[(0,r.jsx)(o.A,{crumb:e.crumb}),(0,r.jsxs)("div",{className:"w-full px-4 md:px-8 py-8",children:[(0,r.jsx)("h1",{className:"text-2xl font-medium mb-8",children:e.title}),e.description&&(0,r.jsx)("p",{className:"text-sm text-muted-foreground mb-8",children:e.description}),e.tip&&(0,r.jsx)("p",{className:"text-sm text-muted-foreground mb-8",children:e.tip.description||e.tip.title}),e.toolbar&&(0,r.jsx)(n.A,{items:e.toolbar.items}),(0,r.jsx)(i.Zp,{className:"overflow-x-auto px-6",children:(0,r.jsx)(s.A,{columns:e.columns??[],data:e.data??[]})})]})]})}},72568:(e,t,a)=>{a.d(t,{Table:()=>s,TableBody:()=>i,TableCell:()=>c,TableHead:()=>d,TableHeader:()=>n,TableRow:()=>l});var r=a(2569);a(29068);var o=a(75673);function s({className:e,...t}){return(0,r.jsx)("div",{"data-slot":"table-container",className:"relative w-full overflow-x-auto",children:(0,r.jsx)("table",{"data-slot":"table",className:(0,o.cn)("w-full caption-bottom text-sm",e),...t})})}function n({className:e,...t}){return(0,r.jsx)("thead",{"data-slot":"table-header",className:(0,o.cn)("[&_tr]:border-b",e),...t})}function i({className:e,...t}){return(0,r.jsx)("tbody",{"data-slot":"table-body",className:(0,o.cn)("[&_tr:last-child]:border-0",e),...t})}function l({className:e,...t}){return(0,r.jsx)("tr",{"data-slot":"table-row",className:(0,o.cn)("hover:bg-muted/50 data-[state=selected]:bg-muted border-b transition-colors",e),...t})}function d({className:e,...t}){return(0,r.jsx)("th",{"data-slot":"table-head",className:(0,o.cn)("text-foreground h-10 px-2 text-left align-middle font-medium whitespace-nowrap [&:has([role=checkbox])]:pr-0 [&>[role=checkbox]]:translate-y-[2px]",e),...t})}function c({className:e,...t}){return(0,r.jsx)("td",{"data-slot":"table-cell",className:(0,o.cn)("p-2 align-middle whitespace-nowrap [&:has([role=checkbox])]:pr-0 [&>[role=checkbox]]:translate-y-[2px]",e),...t})}},78341:(e,t,a)=>{a.d(t,{I:()=>d,SQ:()=>l,_2:()=>c,lp:()=>u,mB:()=>m,rI:()=>n,ty:()=>i});var r=a(2569);a(29068);var o=a(63636),s=a(75673);function n({...e}){return(0,r.jsx)(o.bL,{"data-slot":"dropdown-menu",...e})}function i({...e}){return(0,r.jsx)(o.l9,{"data-slot":"dropdown-menu-trigger",...e})}function l({className:e,sideOffset:t=4,...a}){return(0,r.jsx)(o.ZL,{children:(0,r.jsx)(o.UC,{"data-slot":"dropdown-menu-content",sideOffset:t,className:(0,s.cn)("bg-popover text-popover-foreground data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=open]:fade-in-0 data-[state=closed]:zoom-out-95 data-[state=open]:zoom-in-95 data-[side=bottom]:slide-in-from-top-2 data-[side=left]:slide-in-from-right-2 data-[side=right]:slide-in-from-left-2 data-[side=top]:slide-in-from-bottom-2 z-50 max-h-(--radix-dropdown-menu-content-available-height) min-w-[8rem] origin-(--radix-dropdown-menu-content-transform-origin) overflow-x-hidden overflow-y-auto rounded-md border p-1 shadow-md",e),...a})})}function d({...e}){return(0,r.jsx)(o.YJ,{"data-slot":"dropdown-menu-group",...e})}function c({className:e,inset:t,variant:a="default",...n}){return(0,r.jsx)(o.q7,{"data-slot":"dropdown-menu-item","data-inset":t,"data-variant":a,className:(0,s.cn)("focus:bg-accent focus:text-accent-foreground data-[variant=destructive]:text-destructive data-[variant=destructive]:focus:bg-destructive/10 dark:data-[variant=destructive]:focus:bg-destructive/20 data-[variant=destructive]:focus:text-destructive data-[variant=destructive]:*:[svg]:!text-destructive [&_svg:not([class*='text-'])]:text-muted-foreground relative flex cursor-default items-center gap-2 rounded-sm px-2 py-1.5 text-sm outline-hidden select-none data-[disabled]:pointer-events-none data-[disabled]:opacity-50 data-[inset]:pl-8 [&_svg]:pointer-events-none [&_svg]:shrink-0 [&_svg:not([class*='size-'])]:size-4",e),...n})}function u({className:e,inset:t,...a}){return(0,r.jsx)(o.JU,{"data-slot":"dropdown-menu-label","data-inset":t,className:(0,s.cn)("px-2 py-1.5 text-sm font-medium data-[inset]:pl-8",e),...a})}function m({className:e,...t}){return(0,r.jsx)(o.wv,{"data-slot":"dropdown-menu-separator",className:(0,s.cn)("bg-border -mx-1 my-1 h-px",e),...t})}},78701:(e,t,a)=>{a.d(t,{Avatar:()=>n,AvatarImage:()=>i,q:()=>l});var r=a(2569);a(29068);var o=a(57673),s=a(75673);function n({className:e,...t}){return(0,r.jsx)(o.bL,{"data-slot":"avatar",className:(0,s.cn)("relative flex size-8 shrink-0 overflow-hidden rounded-full",e),...t})}function i({className:e,...t}){return(0,r.jsx)(o._V,{"data-slot":"avatar-image",className:(0,s.cn)("aspect-square size-full object-cover",e),...t})}function l({className:e,...t}){return(0,r.jsx)(o.H4,{"data-slot":"avatar-fallback",className:(0,s.cn)("bg-muted flex size-full items-center justify-center rounded-full",e),...t})}},84601:(e,t,a)=>{a.d(t,{CG:()=>l,Fm:()=>m,Qs:()=>p,cj:()=>i,h:()=>u,qp:()=>f});var r=a(2569);a(29068);var o=a(61224),s=a(49671),n=a(75673);function i({...e}){return(0,r.jsx)(o.bL,{"data-slot":"sheet",...e})}function l({...e}){return(0,r.jsx)(o.l9,{"data-slot":"sheet-trigger",...e})}function d({...e}){return(0,r.jsx)(o.ZL,{"data-slot":"sheet-portal",...e})}function c({className:e,...t}){return(0,r.jsx)(o.hJ,{"data-slot":"sheet-overlay",className:(0,n.cn)("data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=open]:fade-in-0 fixed inset-0 z-50 bg-black/50",e),...t})}function u({className:e,children:t,side:a="right",...i}){return(0,r.jsxs)(d,{children:[(0,r.jsx)(c,{}),(0,r.jsxs)(o.UC,{"data-slot":"sheet-content",className:(0,n.cn)("bg-background data-[state=open]:animate-in data-[state=closed]:animate-out fixed z-50 flex flex-col gap-4 shadow-lg transition ease-in-out data-[state=closed]:duration-300 data-[state=open]:duration-500","right"===a&&"data-[state=closed]:slide-out-to-right data-[state=open]:slide-in-from-right inset-y-0 right-0 h-full w-3/4 border-l sm:max-w-sm","left"===a&&"data-[state=closed]:slide-out-to-left data-[state=open]:slide-in-from-left inset-y-0 left-0 h-full w-3/4 border-r sm:max-w-sm","top"===a&&"data-[state=closed]:slide-out-to-top data-[state=open]:slide-in-from-top inset-x-0 top-0 h-auto border-b","bottom"===a&&"data-[state=closed]:slide-out-to-bottom data-[state=open]:slide-in-from-bottom inset-x-0 bottom-0 h-auto border-t",e),...i,children:[t,(0,r.jsxs)(o.bm,{className:"ring-offset-background focus:ring-ring data-[state=open]:bg-secondary absolute top-4 right-4 rounded-xs opacity-70 transition-opacity hover:opacity-100 focus:ring-2 focus:ring-offset-2 focus:outline-hidden disabled:pointer-events-none",children:[(0,r.jsx)(s.A,{className:"size-4"}),(0,r.jsx)("span",{className:"sr-only",children:"Close"})]})]})]})}function m({className:e,...t}){return(0,r.jsx)("div",{"data-slot":"sheet-header",className:(0,n.cn)("flex flex-col gap-1.5 p-4",e),...t})}function f({className:e,...t}){return(0,r.jsx)(o.hE,{"data-slot":"sheet-title",className:(0,n.cn)("text-foreground font-semibold",e),...t})}function p({className:e,...t}){return(0,r.jsx)(o.VY,{"data-slot":"sheet-description",className:(0,n.cn)("text-muted-foreground text-sm",e),...t})}},92172:(e,t,a)=>{a.d(t,{$:()=>l});var r=a(34411);a(37582);var o=a(20392),s=a(38e3),n=a(25039);let i=(0,s.F)("inline-flex items-center justify-center gap-2 whitespace-nowrap rounded-lg text-sm font-medium transition-all duration-200 disabled:pointer-events-none disabled:opacity-50 [&_svg]:pointer-events-none [&_svg:not([class*='size-'])]:size-4 shrink-0 [&_svg]:shrink-0 outline-none focus-visible:border-ring focus-visible:ring-ring/50 focus-visible:ring-[3px] aria-invalid:ring-destructive/20 dark:aria-invalid:ring-destructive/40 aria-invalid:border-destructive",{variants:{variant:{default:"bg-gradient-to-r from-primary to-accent text-primary-foreground shadow-md hover:shadow-lg hover:scale-[1.02] active:scale-[0.98]",destructive:"bg-destructive text-destructive-foreground shadow-md hover:bg-destructive/90 hover:shadow-lg hover:scale-[1.02] active:scale-[0.98] focus-visible:ring-destructive/20 dark:focus-visible:ring-destructive/40",outline:"border-2 border-primary/20 bg-background/50 backdrop-blur-sm shadow-sm hover:bg-accent/50 hover:text-accent-foreground hover:border-primary/40 hover:shadow-md hover:scale-[1.02] active:scale-[0.98] dark:bg-input/30 dark:border-input dark:hover:bg-input/50",secondary:"bg-gradient-to-r from-secondary to-muted text-secondary-foreground shadow-sm hover:shadow-md hover:scale-[1.02] active:scale-[0.98]",ghost:"hover:bg-accent/50 hover:text-accent-foreground hover:scale-[1.02] active:scale-[0.98] dark:hover:bg-accent/30",link:"text-primary underline-offset-4 hover:underline hover:text-accent"},size:{default:"h-10 px-6 py-2 has-[>svg]:px-4",sm:"h-8 rounded-lg gap-1.5 px-4 has-[>svg]:px-3",lg:"h-12 rounded-lg px-8 has-[>svg]:px-6 text-base",icon:"size-10 rounded-full"}},defaultVariants:{variant:"default",size:"default"}});function l({className:e,variant:t,size:a,asChild:s=!1,...l}){let d=s?o.DX:"button";return(0,r.jsx)(d,{"data-slot":"button",className:(0,n.cn)(i({variant:t,size:a,className:e})),...l})}},92227:(e,t,a)=>{a.r(t),a.d(t,{default:()=>d});var r=a(2569),o=a(29068),s=a(47118),n=a(37650),i=a(59719);let l={RiChatSmile3Line:s.Bgv,RiImageLine:s.fsL,RiVideoLine:s.xi0,RiMicLine:s.RQr,RiMoneyDollarCircleLine:s.AN5,RiArrowRightUpLine:s.SJ3,RiFlashlightFill:s.bwM,RiEyeLine:s.tLq,RiCpuLine:s.y_v,RiUserSmileLine:s.VNl,RiFlashlightLine:s.uEe,RiStarLine:s.WN7,RiPaletteLine:s.LrS,RiRocketLine:s.QWc,RiVoiceprintLine:s.Dcp,RiExchangeLine:s.Lcj,RiTwitterXFill:s.ase,RiGithubFill:s.sAW,RiDiscordFill:s.r53,RiMailLine:s.R0Y,FaRegHeart:n.sOK,GoThumbsup:i.VZG,GoArrowUpRight:i.zny},d=(0,o.memo)(({name:e,className:t,onClick:a,...o})=>{let s=l[e];return s?(0,r.jsx)(s,{className:t,onClick:a,style:{cursor:a?"pointer":"default"},...o}):null})}};