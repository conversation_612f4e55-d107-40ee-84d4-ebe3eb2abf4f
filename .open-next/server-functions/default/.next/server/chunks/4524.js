exports.id=4524,exports.ids=[4524],exports.modules={4961:(e,t,r)=>{"use strict";function n(){return new Date().toISOString()}r.d(t,{MI:()=>a,iq:()=>n});let a=()=>{let e=new Date,t=new Date(e);return t.setFullYear(e.getFullYear()+1),t.toISOString()}},4995:(e,t,r)=>{"use strict";r.d(t,{A:()=>a});var n=r(5327);function a(){let e=process.env.SUPABASE_URL||"",t=process.env.SUPABASE_ANON_KEY||"";if(process.env.SUPABASE_SERVICE_ROLE_KEY&&(t=process.env.SUPABASE_SERVICE_ROLE_KEY),!e||!t)throw Error("Supabase URL or key is not set");return(0,n.UU)(e,t)}},29452:(e,t,r)=>{"use strict";r.d(t,{YJ:()=>i,ZK:()=>o,f1:()=>s});var n=r(19867),a=r(58246);function i(){return(0,a.A)()}function s(e){let t="ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789",r="",n=t.length;for(let a=0;a<e;a++)r+=t[Math.floor(Math.random()*n)];return r}function o(){return new n.F({workerId:1}).NextId().toString()}},35050:(e,t,r)=>{"use strict";r.d(t,{Gs:()=>s,HW:()=>i,Nh:()=>u,PD:()=>_,QZ:()=>l,XQ:()=>d,in:()=>w,lo:()=>c,pX:()=>o,y_:()=>f});var n=r(4961),a=r(4995);async function i(e){let t=(0,a.A)(),{data:r,error:n}=await t.from("users").insert(e);if(n)throw n;return r}async function s(e){let t=(0,a.A)(),{data:r,error:n}=await t.from("users").select("*").eq("email",e).limit(1).single();if(!n)return r}async function o(e){let t=(0,a.A)(),{data:r,error:n}=await t.from("users").select("*").eq("uuid",e).single();if(!n)return r}async function c(e=1,t=50){e<1&&(e=1),t<=0&&(t=50);let r=(e-1)*t,n=(0,a.A)(),{data:i,error:s}=await n.from("users").select("*").order("created_at",{ascending:!1}).range(r,r+t-1);if(!s)return i}async function d(e,t){let r=(0,a.A)(),i=(0,n.iq)(),{data:s,error:o}=await r.from("users").update({invite_code:t,updated_at:i}).eq("uuid",e);if(o)throw o;return s}async function u(e,t){let r=(0,a.A)(),i=(0,n.iq)(),{data:s,error:o}=await r.from("users").update({invited_by:t,updated_at:i}).eq("uuid",e);if(o)throw o;return s}async function l(e){let t=(0,a.A)(),{data:r,error:n}=await t.from("users").select("*").in("uuid",e);return n?[]:r}async function f(e){let t=(0,a.A)(),{data:r,error:n}=await t.from("users").select("*").eq("invite_code",e).single();if(!n)return r}async function _(){let e=(0,a.A)(),{data:t,error:r}=await e.from("users").select("count",{count:"exact"});if(!r)return t[0].count}async function w(e){let t=(0,a.A)().from("users").select("created_at").gte("created_at",e);t=t.order("created_at",{ascending:!0});let{data:r,error:n}=await t;if(n)return;let i=new Map;return r.forEach(e=>{let t=e.created_at.split("T")[0];i.set(t,(i.get(t)||0)+1)}),i}},40109:(e,t,r)=>{"use strict";r.d(t,{BJ:()=>d,El:()=>a,I5:()=>c,K7:()=>i,Kv:()=>o,PG:()=>u,Yc:()=>f,Yw:()=>s,jE:()=>l,nz:()=>_});var n=r(4995);async function a(e){let t=(0,n.A)(),{data:r,error:a}=await t.from("orders").insert(e);if(a)throw a;return r}async function i(e){let t=(0,n.A)(),{data:r,error:a}=await t.from("orders").select("*").eq("order_no",e).single();if(!a)return r}async function s(e){let t=(0,n.A)(),{data:r,error:a}=await t.from("orders").select("*").eq("user_uuid",e).eq("status","paid").order("created_at",{ascending:!0}).limit(1).single();if(!a)return r}async function o(e,t,r,a,i){let s=(0,n.A)(),{data:o,error:c}=await s.from("orders").update({status:t,paid_at:r,paid_detail:i,paid_email:a}).eq("order_no",e);if(c)throw c;return o}async function c(e,t,r){let a=(0,n.A)(),{data:i,error:s}=await a.from("orders").update({stripe_session_id:t,order_detail:r}).eq("order_no",e);if(s)throw s;return i}async function d(e){new Date().toISOString();let t=(0,n.A)(),{data:r,error:a}=await t.from("orders").select("*").eq("user_uuid",e).eq("status","paid").order("created_at",{ascending:!1});if(!a)return r}async function u(e){new Date().toISOString();let t=(0,n.A)(),{data:r,error:a}=await t.from("orders").select("*").eq("paid_email",e).eq("status","paid").order("created_at",{ascending:!1});if(!a)return r}async function l(e,t){let r=(0,n.A)(),{data:a,error:i}=await r.from("orders").select("*").eq("status","paid").order("created_at",{ascending:!1}).range((e-1)*t,e*t);if(!i)return a}async function f(){let e=(0,n.A)(),{data:t,error:r}=await e.from("orders").select("count",{count:"exact"}).eq("status","paid");if(!r)return t[0].count}async function _(e,t){let r=(0,n.A)().from("orders").select("created_at").gte("created_at",e);t&&(r=r.eq("status",t)),r=r.order("created_at",{ascending:!0});let{data:a,error:i}=await r;if(i)return;let s=new Map;return a.forEach(e=>{let t=e.created_at.split("T")[0];s.set(t,(s.get(t)||0)+1)}),s}},45590:()=>{},52367:()=>{},53928:(e,t,r)=>{"use strict";r.d(t,{Ni:()=>a,_3:()=>o,jR:()=>i,uo:()=>s});var n=r(4995);async function a(e){let t=(0,n.A)(),{data:r,error:a}=await t.from("credits").insert(e);if(a)throw a;return r}async function i(e){let t=(0,n.A)(),{data:r,error:a}=await t.from("credits").select("*").eq("order_no",e).limit(1).single();if(!a)return r}async function s(e){let t=new Date().toISOString(),r=(0,n.A)(),{data:a,error:i}=await r.from("credits").select("*").eq("user_uuid",e).gte("expired_at",t).order("expired_at",{ascending:!0});if(!i)return a}async function o(e,t=1,r=50){let a=(0,n.A)(),{data:i,error:s}=await a.from("credits").select("*").eq("user_uuid",e).order("created_at",{ascending:!1}).range((t-1)*r,t*r-1);if(!s)return i}},87830:(e,t,r)=>{"use strict";r.d(t,{H3:()=>o,OI:()=>f,Ty:()=>_,d_:()=>l,jL:()=>w,ll:()=>g,mP:()=>d,nX:()=>u,rN:()=>c});var n=r(53928),a=r(40109),i=r(4961),s=r(29452),o=function(e){return e.NewUser="new_user",e.OrderPay="order_pay",e.SystemAdd="system_add",e.Ping="ping",e.TextGeneration="text_generation",e.ImageGeneration="image_generation",e.VideoGeneration="video_generation",e.AIModelUsage="ai_model_usage",e}({}),c=function(e){return e[e.NewUserGet=100]="NewUserGet",e[e.PingCost=1]="PingCost",e[e.TextGenerationBase=5]="TextGenerationBase",e[e.ImageGenerationBase=50]="ImageGenerationBase",e[e.VideoGenerationBase=200]="VideoGenerationBase",e}({});async function d(e){let t={left_credits:0};try{await (0,a.Yw)(e)&&(t.is_recharged=!0);let r=await (0,n.uo)(e);return r&&r.forEach(e=>{t.left_credits+=e.credits}),t.left_credits<0&&(t.left_credits=0),t.left_credits>0&&(t.is_pro=!0),t}catch(e){return console.log("get user credits failed: ",e),t}}async function u({user_uuid:e,trans_type:t,credits:r}){try{let a="",o="",c=0,d=await (0,n.uo)(e);if(d)for(let e=0,t=d.length;e<t;e++){let t=d[e];if((c+=t.credits)>=r){a=t.order_no,o=t.expired_at||"";break}}let u={trans_no:(0,s.ZK)(),created_at:(0,i.iq)(),user_uuid:e,trans_type:t,credits:0-r,order_no:a,expired_at:o};await (0,n.Ni)(u)}catch(e){throw console.log("decrease credits failed: ",e),e}}async function l({user_uuid:e,trans_type:t,credits:r,expired_at:a,order_no:o}){try{let c={trans_no:(0,s.ZK)(),created_at:(0,i.iq)(),user_uuid:e,trans_type:t,credits:r,order_no:o||"",expired_at:a||""};await (0,n.Ni)(c)}catch(e){throw console.log("increase credits failed: ",e),e}}async function f(e){try{if(await (0,n.jR)(e.order_no))return;await l({user_uuid:e.user_uuid,trans_type:"order_pay",credits:e.credits,expired_at:e.expired_at,order_no:e.order_no})}catch(e){throw console.log("update credit for order failed: ",e),e}}async function _({user_uuid:e,model_id:t,request_id:r,credits:a,trans_type:o="ai_model_usage"}){try{let c="",d="",u=0,l=await (0,n.uo)(e);if(l)for(let e=0,t=l.length;e<t;e++){let t=l[e];if((u+=t.credits)>=a){c=t.order_no,d=t.expired_at||"";break}}if(u<a)throw Error("Insufficient credits");let f={trans_no:(0,s.ZK)(),created_at:(0,i.iq)(),user_uuid:e,trans_type:o,credits:0-a,order_no:c,expired_at:d,model_id:t,request_id:r};await (0,n.Ni)(f)}catch(e){throw console.log("decrease credits for AI model failed: ",e),e}}async function w({user_uuid:e,model_id:t,request_id:r,credits:a,original_trans_no:o}){try{let o={trans_no:(0,s.ZK)(),created_at:(0,i.iq)(),user_uuid:e,trans_type:"ai_model_refund",credits:a,order_no:"",expired_at:(0,i.MI)(),model_id:t,request_id:r};await (0,n.Ni)(o)}catch(e){throw console.log("refund credits for AI model failed: ",e),e}}async function g(e,t){try{return(await d(e)).left_credits>=t}catch(e){return console.log("check sufficient credits failed: ",e),!1}}}};