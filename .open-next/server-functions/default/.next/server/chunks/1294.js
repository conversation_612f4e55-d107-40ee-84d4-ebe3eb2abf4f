exports.id=1294,exports.ids=[1294],exports.modules={13373:()=>{},29042:(e,t,s)=>{"use strict";function r(e){return i(0,"ok",e||[])}function o(){return i(0,"ok")}function a(e){return i(-1,e)}function i(e,t,s){let r={code:e,message:t,data:s};return s&&(r.data=s),Response.json(r)}s.d(t,{DQ:()=>r,YS:()=>a,j7:()=>i,rn:()=>o})},60270:(e,t,s)=>{"use strict";s.d(t,{lC:()=>d,b:()=>c,om:()=>l,zQ:()=>u,kl:()=>m,Kp:()=>n,nv:()=>i,oY:()=>g,c9:()=>f,it:()=>p});var r=s(4995);function o(e,t="en",s="zh"){if(!e)return"";if("string"==typeof e)return e;if("object"==typeof e){if(e[t])return e[t];if(e[s])return e[s];let r=Object.values(e).filter(Boolean);if(r.length>0)return r[0]}return""}function a(e,t="en"){return{...e,model_name:o(e.model_name_i18n||e.model_name,t),description:o(e.description_i18n||e.description,t)}}async function i(e="en"){let t=(0,r.A)(),{data:s,error:o}=await t.from("ai_models").select("*").eq("is_active",!0).order("model_type",{ascending:!0}).order("credits_per_unit",{ascending:!0});if(o)throw o;return(s||[]).map(t=>a(t,e))}async function n(e,t="en"){let s=(0,r.A)(),{data:o,error:i}=await s.from("ai_models").select("*").eq("model_type",e).eq("is_active",!0).order("credits_per_unit",{ascending:!0});if(i)throw i;return(o||[]).map(e=>a(e,t))}async function l(e,t="en"){let s=(0,r.A)(),{data:o,error:i}=await s.from("ai_models").select("*").eq("model_id",e).eq("is_active",!0).single();return i?null:o?a(o,t):null}async function u(e,t="en"){let s=(0,r.A)(),{data:o,error:i}=await s.from("ai_models").select("*").eq("model_id",e).single();return i?null:o?a(o,t):null}async function d(e,t,s){let o=(0,r.A)(),{data:a,error:i}=await o.rpc("calculate_model_cost",{p_model_id:e,p_input_size:t,p_output_size:s||0});if(i)throw i;return a||1}async function c(e){let t=(0,r.A)(),{data:s,error:o}=await t.from("ai_model_usage").insert(e).select().single();if(o)throw o;return s}async function p(e,t){let s=(0,r.A)(),{data:o,error:a}=await s.from("ai_model_usage").update(t).eq("request_id",e).select().single();return a?null:o}async function m(e){let t=(0,r.A)(),{data:s,error:o}=await t.from("ai_model_usage").select("*").eq("request_id",e).single();return o?null:s}async function g(e,t=1,s=50){let o=(0,r.A)(),{data:a,error:i}=await o.from("ai_model_usage").select("*").eq("user_uuid",e).order("created_at",{ascending:!1}).range((t-1)*s,t*s-1);if(i)throw i;return a||[]}async function f(e){let t=(0,r.A)(),{data:s,error:o}=await t.from("user_credits_usage_stats").select("*").eq("user_uuid",e).order("total_credits_consumed",{ascending:!1});if(o)throw o;return s||[]}},94813:()=>{},99353:(e,t,s)=>{"use strict";s.d(t,{U:()=>l,g:()=>n});var r=s(29452),o=s(4961),a=s(60270);let i={baseURL:{overseas:"https://api.grsai.com",domestic:"https://grsai.dakka.com.cn"},retryAttempts:3,retryDelay:1e3};class n{constructor(e,t="overseas"){if(this.apiKey=e||process.env.GRSAI_APIKEY||"",this.baseURL=i.baseURL[t],!this.apiKey)throw Error("GRSAI API key is required")}async makeRequest(e,t,s={}){let r=`${this.baseURL}${e}`,o={method:"POST",headers:{"Content-Type":"application/json",Authorization:`Bearer ${this.apiKey}`},body:JSON.stringify(t)};console.log(`[GRSAI] Making request to: ${r}`),console.log("[GRSAI] Request data:",JSON.stringify(t,null,2));let a=0;for(;a<i.retryAttempts;)try{let e=await fetch(r,o);if(console.log(`[GRSAI] Response status: ${e.status}`),console.log("[GRSAI] Response headers:",Object.fromEntries(e.headers.entries())),!e.ok){let t=await e.text();throw console.log("[GRSAI] Error response:",t),Error(`HTTP ${e.status}: ${e.statusText} - ${t}`)}if(s.stream)return e;let t=e.headers.get("content-type");if(console.log(`[GRSAI] Content-Type: ${t}`),t&&t.includes("text/plain")){let t=await e.text();return console.log("[GRSAI] SSE response text:",t),this.parseSSEResponse(t)}let a=await e.json();return console.log("[GRSAI] JSON response:",JSON.stringify(a,null,2)),a}catch(e){if(console.log(`[GRSAI] Request attempt ${a+1} failed:`,e),++a>=i.retryAttempts)throw e;await new Promise(e=>setTimeout(e,i.retryDelay*a))}}parseSSEResponse(e){try{console.log(`[GRSAI] Parsing SSE response, total length: ${e.length}`);let t=e.split("\n"),s=null,r=0;for(let e of t)if(e.startsWith("data: ")&&"data: [DONE]"!==e)try{let t=e.substring(6);console.log(`[GRSAI] Parsing JSON line: ${t}`),s=JSON.parse(t),r++,console.log(`[GRSAI] Successfully parsed JSON data #${r}:`,s)}catch(t){console.log(`[GRSAI] Failed to parse JSON line: ${e}`,t)}return console.log(`[GRSAI] Found ${r} valid JSON data entries`),console.log("[GRSAI] Final parsed data:",s),s||{error:"No valid JSON data found in SSE response"}}catch(e){throw console.log("[GRSAI] SSE parsing error:",e),Error(`Failed to parse SSE response: ${e}`)}}async generateText(e){return await this.makeRequest("/v1/chat/completions",e,{stream:e.stream})}async generateImage(e){let t={...e,webHook:e.webHook||"-1"};return await this.makeRequest("/v1/draw/completions",t)}async generateFluxImage(e){let t={...e,webHook:e.webHook||"-1"};return await this.makeRequest("/v1/draw/flux",t)}async generateVideo(e){let t={...e,webHook:e.webHook||"-1"};return await this.makeRequest("/v1/video/veo",t)}async getResult(e){return await this.makeRequest("/v1/draw/result",{id:e})}}class l{constructor(){this.grsaiProvider=new n}async processRequest(e,t){let s=(0,r.YJ)(),i=await (0,a.om)(t.model);if(!i)throw Error(`Model ${t.model} not found`);let n=await this.estimateCost(t,i);await (0,a.b)({user_uuid:e,model_id:t.model,request_id:s,credits_consumed:n,status:"pending",request_params:t,started_at:(0,o.iq)()});try{let e;switch(t.type){case"text":e=await this.handleTextGeneration(t,s);break;case"image":e=await this.handleImageGeneration(t,s);break;case"video":e=await this.handleVideoGeneration(t,s);break;default:throw Error(`Unsupported request type: ${t.type}`)}return"pending"!==e.status&&await (0,a.it)(s,{status:"success"===e.status?"success":"failed",output_size:this.calculateOutputSize(e),response_data:e,completed_at:(0,o.iq)()}),e}catch(e){throw await (0,a.it)(s,{status:"failed",error_reason:"error",error_detail:e instanceof Error?e.message:"Unknown error",completed_at:(0,o.iq)()}),e}}async handleTextGeneration(e,t){let s;let r=e.options?.uploadedImages||[];if(r.length>0){let t=[{type:"text",text:e.prompt}];r.forEach(e=>{t.push({type:"image_url",image_url:{url:e}})}),s=e.options?.messages?.map(e=>({role:e.role,content:e.image_url?[{type:"text",text:e.content},{type:"image_url",image_url:{url:e.image_url}}]:e.content}))||[{role:"user",content:t}]}else s=e.options?.messages?.map(e=>({role:e.role,content:e.content}))||[{role:"user",content:e.prompt}];let o={model:e.model,messages:s,stream:e.options?.stream||!1,temperature:e.options?.temperature,max_tokens:e.options?.max_tokens},a=await this.grsaiProvider.generateText(o);return a instanceof ReadableStream?{id:t,type:"text",status:"pending",result:{text:""}}:{id:t,type:"text",status:"success",result:{text:a.choices[0]?.message?.content||""},usage:{input_tokens:this.estimateTokens(e.prompt),output_tokens:this.estimateTokens(a.choices[0]?.message?.content||""),total_tokens:0,credits_consumed:0}}}async handleImageGeneration(e,t){let s;console.log(`[AI Service] Starting image generation for model: ${e.model}`);let r=await (0,a.om)(e.model);if(!r)throw Error(`Model ${e.model} not found`);console.log("[AI Service] Model config:",r);let o=[...e.options?.referenceImages||[],...e.options?.uploadedImages||[]];if("/v1/draw/flux"===r.api_endpoint){let t={model:e.model,prompt:e.prompt,urls:o.length>0?o:void 0,seed:e.options?.seed,aspectRatio:e.options?.aspectRatio||e.options?.size,webHook:"-1",cdn:e.options?.cdn||"global"};console.log("[AI Service] Calling Flux API with request:",t),s=await this.grsaiProvider.generateFluxImage(t)}else{let t={model:e.model,prompt:e.prompt,size:e.options?.size,variants:e.options?.variants,urls:o.length>0?o:void 0,webHook:"-1",cdn:e.options?.cdn||"global"};console.log("[AI Service] Calling Image API with request:",t),s=await this.grsaiProvider.generateImage(t)}return(console.log("[AI Service] GRSAI response:",s),0===s.code&&s.data&&s.data.id)?(console.log(`[AI Service] Task created with ID: ${s.data.id}`),await (0,a.it)(t,{response_data:s,status:"pending"}),{id:t,type:"image",status:"pending",progress:0,usage:{credits_consumed:r.credits_per_unit}}):(console.log(`[AI Service] Direct result response, status: ${s.status}`),{id:t,type:"image",status:"succeeded"===s.status?"success":"failed"===s.status?"failed":"running",progress:s.progress||0,result:{images:"succeeded"===s.status&&s.url?[{url:s.url,width:s.width||1024,height:s.height||1024}]:void 0},error:"failed"===s.status?{reason:s.failure_reason||"error",detail:s.error||"Unknown error"}:void 0,usage:{credits_consumed:r.credits_per_unit}})}async handleVideoGeneration(e,t){let s=e.options?.uploadedImages?.[0]||e.options?.firstFrameUrl,r={model:e.model,prompt:e.prompt,firstFrameUrl:s,webHook:"-1",cdn:e.options?.cdn||"global"},o=await this.grsaiProvider.generateVideo(r),i=await (0,a.om)(e.model);return"code"in o&&"data"in o&&0===o.code&&o.data&&"object"==typeof o.data&&null!==o.data&&"id"in o.data?{id:t,type:"video",status:"pending",progress:0,usage:{credits_consumed:i?.credits_per_unit||0}}:{id:t,type:"video",status:"succeeded"===o.status?"success":"failed"===o.status?"failed":"running",progress:o.progress||0,result:{video:"succeeded"===o.status&&o.url?{url:o.url}:void 0},error:"failed"===o.status?{reason:o.failure_reason||"error",detail:o.error||"Unknown error"}:void 0,usage:{credits_consumed:i?.credits_per_unit||0}}}async estimateCost(e,t){switch(e.type){case"text":let s=this.estimateTokens(e.prompt),r=e.options?.max_tokens||1e3;return await (0,a.lC)(e.model,s,r);case"image":let o=e.options?.variants||1;return t.credits_per_unit*o;default:return t.credits_per_unit}}estimateTokens(e){return Math.ceil((e.match(/[\u4e00-\u9fff]/g)||[]).length+1.3*e.replace(/[\u4e00-\u9fff]/g,"").split(/\s+/).filter(e=>e.length>0).length)}calculateOutputSize(e){switch(e.type){case"text":return this.estimateTokens(e.result?.text||"");case"image":return e.result?.images?.length||0;case"video":return+!!e.result?.video;default:return 0}}}}};