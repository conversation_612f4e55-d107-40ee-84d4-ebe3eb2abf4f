"use strict";exports.id=2303,exports.ids=[2303],exports.modules={17345:(e,t,r)=>{r.d(t,{A:()=>p});var n=r(14059),o=r(29068),i=r.t(o,2),l=r(50894),a=i["use".trim()],u=r(47183),c=r(20518),s=r(81026),f=r(2569),d=r(61116);function p(e){let{Link:t,config:r,getPathname:i,...p}=function(e,t){var r,i,l;let d={...r=t||{},localePrefix:"object"==typeof(l=r.localePrefix)?l:{mode:l||"always"},localeCookie:!!((i=r.localeCookie)??1)&&{name:"NEXT_LOCALE",sameSite:"lax",..."object"==typeof i&&i},localeDetection:r.localeDetection??!0,alternateLinks:r.alternateLinks??!0},p=d.pathnames,h=(0,o.forwardRef)(function({href:t,locale:r,...n},o){let i,l;"object"==typeof t?(i=t.pathname,l=t.params):i=t;let s=(0,u._x)(t),h=e(),g=(0,u.yL)(h)?a(h):h,v=s?m({locale:r||g,href:null==p?i:{pathname:i,params:l},forcePrefix:null!=r||void 0}):i;return(0,f.jsx)(c.default,{ref:o,href:"object"==typeof t?{...t,pathname:v}:v,locale:r,localeCookie:d.localeCookie,...n})});function m(e){let t;let{forcePrefix:r,href:n,locale:o}=e;return null==p?"object"==typeof n?(t=n.pathname,n.query&&(t+=(0,s.Zn)(n.query))):t=n:t=(0,s.FP)({locale:o,...(0,s.TK)(n),pathnames:d.pathnames}),(0,s.x3)(t,o,d,r)}function g(e){return function(t,...r){return e(m(t),...r)}}return{config:d,Link:h,redirect:g(n.redirect),permanentRedirect:g(n.permanentRedirect),getPathname:m}}(l.Ym,e);return{...p,Link:t,usePathname:function(){let e=function(e){let t=(0,n.usePathname)(),r=(0,l.Ym)();return(0,o.useMemo)(()=>{if(!t)return t;let n=t,o=(0,u.XP)(r,e.localePrefix);if((0,u.wO)(o,t))n=(0,u.MY)(t,o);else if("as-needed"===e.localePrefix.mode&&e.localePrefix.prefixes){let e=(0,u.bL)(r);(0,u.wO)(e,t)&&(n=(0,u.MY)(t,e))}return n},[e.localePrefix,r,t])}(r),t=(0,l.Ym)();return(0,o.useMemo)(()=>e&&r.pathnames?(0,s.aM)(t,e,r.pathnames):e,[t,e])},useRouter:function(){let e=(0,n.useRouter)(),t=(0,l.Ym)(),a=(0,n.usePathname)();return(0,o.useMemo)(()=>{function n(e){return function(n,o){let{locale:l,...u}=o||{},c=[i({href:n,locale:l||t})];Object.keys(u).length>0&&c.push(u),(0,d.A)(r.localeCookie,a,t,l),e(...c)}}return{...e,push:n(e.push),replace:n(e.replace),prefetch:n(e.prefetch)}},[t,a,e])},getPathname:i}}},25481:(e,t,r)=>{r.d(t,{RG:()=>y,bL:()=>S,q7:()=>V});var n=r(29068),o=r(79208),i=r(63056),l=r(58028),a=r(4002),u=r(50826),c=r(85137),s=r(89553),f=r(83465),d=r(66505),p=r(2569),h="rovingFocusGroup.onEntryFocus",m={bubbles:!1,cancelable:!0},g="RovingFocusGroup",[v,w,C]=(0,i.N)(g),[x,y]=(0,a.A)(g,[C]),[b,M]=x(g),L=n.forwardRef((e,t)=>(0,p.jsx)(v.Provider,{scope:e.__scopeRovingFocusGroup,children:(0,p.jsx)(v.Slot,{scope:e.__scopeRovingFocusGroup,children:(0,p.jsx)(R,{...e,ref:t})})}));L.displayName=g;var R=n.forwardRef((e,t)=>{let{__scopeRovingFocusGroup:r,orientation:i,loop:a=!1,dir:u,currentTabStopId:v,defaultCurrentTabStopId:C,onCurrentTabStopIdChange:x,onEntryFocus:y,preventScrollOnEntryFocus:M=!1,...L}=e,R=n.useRef(null),H=(0,l.s)(t,R),k=(0,d.jH)(u),[A,S]=(0,f.i)({prop:v,defaultProp:C??null,onChange:x,caller:g}),[V,P]=n.useState(!1),D=(0,s.c)(y),E=w(r),T=n.useRef(!1),[O,Z]=n.useState(0);return n.useEffect(()=>{let e=R.current;if(e)return e.addEventListener(h,D),()=>e.removeEventListener(h,D)},[D]),(0,p.jsx)(b,{scope:r,orientation:i,dir:k,loop:a,currentTabStopId:A,onItemFocus:n.useCallback(e=>S(e),[S]),onItemShiftTab:n.useCallback(()=>P(!0),[]),onFocusableItemAdd:n.useCallback(()=>Z(e=>e+1),[]),onFocusableItemRemove:n.useCallback(()=>Z(e=>e-1),[]),children:(0,p.jsx)(c.sG.div,{tabIndex:V||0===O?-1:0,"data-orientation":i,...L,ref:H,style:{outline:"none",...e.style},onMouseDown:(0,o.m)(e.onMouseDown,()=>{T.current=!0}),onFocus:(0,o.m)(e.onFocus,e=>{let t=!T.current;if(e.target===e.currentTarget&&t&&!V){let t=new CustomEvent(h,m);if(e.currentTarget.dispatchEvent(t),!t.defaultPrevented){let e=E().filter(e=>e.focusable);j([e.find(e=>e.active),e.find(e=>e.id===A),...e].filter(Boolean).map(e=>e.ref.current),M)}}T.current=!1}),onBlur:(0,o.m)(e.onBlur,()=>P(!1))})})}),H="RovingFocusGroupItem",k=n.forwardRef((e,t)=>{let{__scopeRovingFocusGroup:r,focusable:i=!0,active:l=!1,tabStopId:a,children:s,...f}=e,d=(0,u.B)(),h=a||d,m=M(H,r),g=m.currentTabStopId===h,C=w(r),{onFocusableItemAdd:x,onFocusableItemRemove:y,currentTabStopId:b}=m;return n.useEffect(()=>{if(i)return x(),()=>y()},[i,x,y]),(0,p.jsx)(v.ItemSlot,{scope:r,id:h,focusable:i,active:l,children:(0,p.jsx)(c.sG.span,{tabIndex:g?0:-1,"data-orientation":m.orientation,...f,ref:t,onMouseDown:(0,o.m)(e.onMouseDown,e=>{i?m.onItemFocus(h):e.preventDefault()}),onFocus:(0,o.m)(e.onFocus,()=>m.onItemFocus(h)),onKeyDown:(0,o.m)(e.onKeyDown,e=>{if("Tab"===e.key&&e.shiftKey){m.onItemShiftTab();return}if(e.target!==e.currentTarget)return;let t=function(e,t,r){var n;let o=(n=e.key,"rtl"!==r?n:"ArrowLeft"===n?"ArrowRight":"ArrowRight"===n?"ArrowLeft":n);if(!("vertical"===t&&["ArrowLeft","ArrowRight"].includes(o))&&!("horizontal"===t&&["ArrowUp","ArrowDown"].includes(o)))return A[o]}(e,m.orientation,m.dir);if(void 0!==t){if(e.metaKey||e.ctrlKey||e.altKey||e.shiftKey)return;e.preventDefault();let r=C().filter(e=>e.focusable).map(e=>e.ref.current);if("last"===t)r.reverse();else if("prev"===t||"next"===t){"prev"===t&&r.reverse();let n=r.indexOf(e.currentTarget);r=m.loop?function(e,t){return e.map((r,n)=>e[(t+n)%e.length])}(r,n+1):r.slice(n+1)}setTimeout(()=>j(r))}}),children:"function"==typeof s?s({isCurrentTabStop:g,hasTabStop:null!=b}):s})})});k.displayName=H;var A={ArrowLeft:"prev",ArrowUp:"prev",ArrowRight:"next",ArrowDown:"next",PageUp:"first",Home:"first",PageDown:"last",End:"last"};function j(e,t=!1){let r=document.activeElement;for(let n of e)if(n===r||(n.focus({preventScroll:t}),document.activeElement!==r))return}var S=L,V=k},26798:(e,t,r)=>{var n=r(29068),o="function"==typeof Object.is?Object.is:function(e,t){return e===t&&(0!==e||1/e==1/t)||e!=e&&t!=t},i=n.useState,l=n.useEffect,a=n.useLayoutEffect,u=n.useDebugValue;function c(e){var t=e.getSnapshot;e=e.value;try{var r=t();return!o(e,r)}catch(e){return!0}}var s="undefined"==typeof window||void 0===window.document||void 0===window.document.createElement?function(e,t){return t()}:function(e,t){var r=t(),n=i({inst:{value:r,getSnapshot:t}}),o=n[0].inst,s=n[1];return a(function(){o.value=r,o.getSnapshot=t,c(o)&&s({inst:o})},[e,r,t]),l(function(){return c(o)&&s({inst:o}),e(function(){c(o)&&s({inst:o})})},[e]),u(r),r};t.useSyncExternalStore=void 0!==n.useSyncExternalStore?n.useSyncExternalStore:s},37650:(e,t,r)=>{r.d(t,{sOK:()=>o});var n=r(67920);function o(e){return(0,n.k5)({tag:"svg",attr:{viewBox:"0 0 512 512"},child:[{tag:"path",attr:{d:"M458.4 64.3C400.6 15.7 311.3 23 256 79.3 200.7 23 111.4 15.6 53.6 64.3-21.6 127.6-10.6 230.8 43 285.5l175.4 178.7c10 10.2 23.4 15.9 37.6 15.9 14.3 0 27.6-5.6 37.6-15.8L469 285.6c53.5-54.7 64.7-157.9-10.6-221.3zm-23.6 187.5L259.4 430.5c-2.4 2.4-4.4 2.4-6.8 0L77.2 251.8c-36.5-37.2-43.9-107.6 7.3-150.7 38.9-32.7 98.9-27.8 136.5 10.5l35 35.7 35-35.7c37.8-38.5 97.8-43.2 136.5-10.6 51.1 43.1 43.5 113.9 7.3 150.8z"},child:[]}]})(e)}},42509:(e,t,r)=>{r.d(t,{X:()=>i});var n=r(29068),o=r(29543);function i(e){let[t,r]=n.useState(void 0);return(0,o.N)(()=>{if(e){r({width:e.offsetWidth,height:e.offsetHeight});let t=new ResizeObserver(t=>{let n,o;if(!Array.isArray(t)||!t.length)return;let i=t[0];if("borderBoxSize"in i){let e=i.borderBoxSize,t=Array.isArray(e)?e[0]:e;n=t.inlineSize,o=t.blockSize}else n=e.offsetWidth,o=e.offsetHeight;r({width:n,height:o})});return t.observe(e,{box:"border-box"}),()=>t.unobserve(e)}r(void 0)},[e]),t}},44450:(e,t,r)=>{r.d(t,{Mz:()=>te,i3:()=>tr,UC:()=>tt,bL:()=>e3,Bk:()=>e$});var n=r(29068);let o=["top","right","bottom","left"],i=Math.min,l=Math.max,a=Math.round,u=Math.floor,c=e=>({x:e,y:e}),s={left:"right",right:"left",bottom:"top",top:"bottom"},f={start:"end",end:"start"};function d(e,t){return"function"==typeof e?e(t):e}function p(e){return e.split("-")[0]}function h(e){return e.split("-")[1]}function m(e){return"x"===e?"y":"x"}function g(e){return"y"===e?"height":"width"}let v=new Set(["top","bottom"]);function w(e){return v.has(p(e))?"y":"x"}function C(e){return e.replace(/start|end/g,e=>f[e])}let x=["left","right"],y=["right","left"],b=["top","bottom"],M=["bottom","top"];function L(e){return e.replace(/left|right|bottom|top/g,e=>s[e])}function R(e){return"number"!=typeof e?{top:0,right:0,bottom:0,left:0,...e}:{top:e,right:e,bottom:e,left:e}}function H(e){let{x:t,y:r,width:n,height:o}=e;return{width:n,height:o,top:r,left:t,right:t+n,bottom:r+o,x:t,y:r}}function k(e,t,r){let n,{reference:o,floating:i}=e,l=w(t),a=m(w(t)),u=g(a),c=p(t),s="y"===l,f=o.x+o.width/2-i.width/2,d=o.y+o.height/2-i.height/2,v=o[u]/2-i[u]/2;switch(c){case"top":n={x:f,y:o.y-i.height};break;case"bottom":n={x:f,y:o.y+o.height};break;case"right":n={x:o.x+o.width,y:d};break;case"left":n={x:o.x-i.width,y:d};break;default:n={x:o.x,y:o.y}}switch(h(t)){case"start":n[a]-=v*(r&&s?-1:1);break;case"end":n[a]+=v*(r&&s?-1:1)}return n}let A=async(e,t,r)=>{let{placement:n="bottom",strategy:o="absolute",middleware:i=[],platform:l}=r,a=i.filter(Boolean),u=await (null==l.isRTL?void 0:l.isRTL(t)),c=await l.getElementRects({reference:e,floating:t,strategy:o}),{x:s,y:f}=k(c,n,u),d=n,p={},h=0;for(let r=0;r<a.length;r++){let{name:i,fn:m}=a[r],{x:g,y:v,data:w,reset:C}=await m({x:s,y:f,initialPlacement:n,placement:d,strategy:o,middlewareData:p,rects:c,platform:l,elements:{reference:e,floating:t}});s=null!=g?g:s,f=null!=v?v:f,p={...p,[i]:{...p[i],...w}},C&&h<=50&&(h++,"object"==typeof C&&(C.placement&&(d=C.placement),C.rects&&(c=!0===C.rects?await l.getElementRects({reference:e,floating:t,strategy:o}):C.rects),{x:s,y:f}=k(c,d,u)),r=-1)}return{x:s,y:f,placement:d,strategy:o,middlewareData:p}};async function j(e,t){var r;void 0===t&&(t={});let{x:n,y:o,platform:i,rects:l,elements:a,strategy:u}=e,{boundary:c="clippingAncestors",rootBoundary:s="viewport",elementContext:f="floating",altBoundary:p=!1,padding:h=0}=d(t,e),m=R(h),g=a[p?"floating"===f?"reference":"floating":f],v=H(await i.getClippingRect({element:null==(r=await (null==i.isElement?void 0:i.isElement(g)))||r?g:g.contextElement||await (null==i.getDocumentElement?void 0:i.getDocumentElement(a.floating)),boundary:c,rootBoundary:s,strategy:u})),w="floating"===f?{x:n,y:o,width:l.floating.width,height:l.floating.height}:l.reference,C=await (null==i.getOffsetParent?void 0:i.getOffsetParent(a.floating)),x=await (null==i.isElement?void 0:i.isElement(C))&&await (null==i.getScale?void 0:i.getScale(C))||{x:1,y:1},y=H(i.convertOffsetParentRelativeRectToViewportRelativeRect?await i.convertOffsetParentRelativeRectToViewportRelativeRect({elements:a,rect:w,offsetParent:C,strategy:u}):w);return{top:(v.top-y.top+m.top)/x.y,bottom:(y.bottom-v.bottom+m.bottom)/x.y,left:(v.left-y.left+m.left)/x.x,right:(y.right-v.right+m.right)/x.x}}function S(e,t){return{top:e.top-t.height,right:e.right-t.width,bottom:e.bottom-t.height,left:e.left-t.width}}function V(e){return o.some(t=>e[t]>=0)}let P=new Set(["left","top"]);async function D(e,t){let{placement:r,platform:n,elements:o}=e,i=await (null==n.isRTL?void 0:n.isRTL(o.floating)),l=p(r),a=h(r),u="y"===w(r),c=P.has(l)?-1:1,s=i&&u?-1:1,f=d(t,e),{mainAxis:m,crossAxis:g,alignmentAxis:v}="number"==typeof f?{mainAxis:f,crossAxis:0,alignmentAxis:null}:{mainAxis:f.mainAxis||0,crossAxis:f.crossAxis||0,alignmentAxis:f.alignmentAxis};return a&&"number"==typeof v&&(g="end"===a?-1*v:v),u?{x:g*s,y:m*c}:{x:m*c,y:g*s}}function E(){return"undefined"!=typeof window}function T(e){return N(e)?(e.nodeName||"").toLowerCase():"#document"}function O(e){var t;return(null==e||null==(t=e.ownerDocument)?void 0:t.defaultView)||window}function Z(e){var t;return null==(t=(N(e)?e.ownerDocument:e.document)||window.document)?void 0:t.documentElement}function N(e){return!!E()&&(e instanceof Node||e instanceof O(e).Node)}function _(e){return!!E()&&(e instanceof Element||e instanceof O(e).Element)}function I(e){return!!E()&&(e instanceof HTMLElement||e instanceof O(e).HTMLElement)}function F(e){return!!E()&&"undefined"!=typeof ShadowRoot&&(e instanceof ShadowRoot||e instanceof O(e).ShadowRoot)}let B=new Set(["inline","contents"]);function G(e){let{overflow:t,overflowX:r,overflowY:n,display:o}=ee(e);return/auto|scroll|overlay|hidden|clip/.test(t+n+r)&&!B.has(o)}let K=new Set(["table","td","th"]),W=[":popover-open",":modal"];function z(e){return W.some(t=>{try{return e.matches(t)}catch(e){return!1}})}let $=["transform","translate","scale","rotate","perspective"],Y=["transform","translate","scale","rotate","perspective","filter"],U=["paint","layout","strict","content"];function X(e){let t=q(),r=_(e)?ee(e):e;return $.some(e=>!!r[e]&&"none"!==r[e])||!!r.containerType&&"normal"!==r.containerType||!t&&!!r.backdropFilter&&"none"!==r.backdropFilter||!t&&!!r.filter&&"none"!==r.filter||Y.some(e=>(r.willChange||"").includes(e))||U.some(e=>(r.contain||"").includes(e))}function q(){return"undefined"!=typeof CSS&&!!CSS.supports&&CSS.supports("-webkit-backdrop-filter","none")}let J=new Set(["html","body","#document"]);function Q(e){return J.has(T(e))}function ee(e){return O(e).getComputedStyle(e)}function et(e){return _(e)?{scrollLeft:e.scrollLeft,scrollTop:e.scrollTop}:{scrollLeft:e.scrollX,scrollTop:e.scrollY}}function er(e){if("html"===T(e))return e;let t=e.assignedSlot||e.parentNode||F(e)&&e.host||Z(e);return F(t)?t.host:t}function en(e,t,r){var n;void 0===t&&(t=[]),void 0===r&&(r=!0);let o=function e(t){let r=er(t);return Q(r)?t.ownerDocument?t.ownerDocument.body:t.body:I(r)&&G(r)?r:e(r)}(e),i=o===(null==(n=e.ownerDocument)?void 0:n.body),l=O(o);if(i){let e=eo(l);return t.concat(l,l.visualViewport||[],G(o)?o:[],e&&r?en(e):[])}return t.concat(o,en(o,[],r))}function eo(e){return e.parent&&Object.getPrototypeOf(e.parent)?e.frameElement:null}function ei(e){let t=ee(e),r=parseFloat(t.width)||0,n=parseFloat(t.height)||0,o=I(e),i=o?e.offsetWidth:r,l=o?e.offsetHeight:n,u=a(r)!==i||a(n)!==l;return u&&(r=i,n=l),{width:r,height:n,$:u}}function el(e){return _(e)?e:e.contextElement}function ea(e){let t=el(e);if(!I(t))return c(1);let r=t.getBoundingClientRect(),{width:n,height:o,$:i}=ei(t),l=(i?a(r.width):r.width)/n,u=(i?a(r.height):r.height)/o;return l&&Number.isFinite(l)||(l=1),u&&Number.isFinite(u)||(u=1),{x:l,y:u}}let eu=c(0);function ec(e){let t=O(e);return q()&&t.visualViewport?{x:t.visualViewport.offsetLeft,y:t.visualViewport.offsetTop}:eu}function es(e,t,r,n){var o;void 0===t&&(t=!1),void 0===r&&(r=!1);let i=e.getBoundingClientRect(),l=el(e),a=c(1);t&&(n?_(n)&&(a=ea(n)):a=ea(e));let u=(void 0===(o=r)&&(o=!1),n&&(!o||n===O(l))&&o)?ec(l):c(0),s=(i.left+u.x)/a.x,f=(i.top+u.y)/a.y,d=i.width/a.x,p=i.height/a.y;if(l){let e=O(l),t=n&&_(n)?O(n):n,r=e,o=eo(r);for(;o&&n&&t!==r;){let e=ea(o),t=o.getBoundingClientRect(),n=ee(o),i=t.left+(o.clientLeft+parseFloat(n.paddingLeft))*e.x,l=t.top+(o.clientTop+parseFloat(n.paddingTop))*e.y;s*=e.x,f*=e.y,d*=e.x,p*=e.y,s+=i,f+=l,o=eo(r=O(o))}}return H({width:d,height:p,x:s,y:f})}function ef(e,t){let r=et(e).scrollLeft;return t?t.left+r:es(Z(e)).left+r}function ed(e,t,r){void 0===r&&(r=!1);let n=e.getBoundingClientRect();return{x:n.left+t.scrollLeft-(r?0:ef(e,n)),y:n.top+t.scrollTop}}let ep=new Set(["absolute","fixed"]);function eh(e,t,r){let n;if("viewport"===t)n=function(e,t){let r=O(e),n=Z(e),o=r.visualViewport,i=n.clientWidth,l=n.clientHeight,a=0,u=0;if(o){i=o.width,l=o.height;let e=q();(!e||e&&"fixed"===t)&&(a=o.offsetLeft,u=o.offsetTop)}return{width:i,height:l,x:a,y:u}}(e,r);else if("document"===t)n=function(e){let t=Z(e),r=et(e),n=e.ownerDocument.body,o=l(t.scrollWidth,t.clientWidth,n.scrollWidth,n.clientWidth),i=l(t.scrollHeight,t.clientHeight,n.scrollHeight,n.clientHeight),a=-r.scrollLeft+ef(e),u=-r.scrollTop;return"rtl"===ee(n).direction&&(a+=l(t.clientWidth,n.clientWidth)-o),{width:o,height:i,x:a,y:u}}(Z(e));else if(_(t))n=function(e,t){let r=es(e,!0,"fixed"===t),n=r.top+e.clientTop,o=r.left+e.clientLeft,i=I(e)?ea(e):c(1),l=e.clientWidth*i.x,a=e.clientHeight*i.y;return{width:l,height:a,x:o*i.x,y:n*i.y}}(t,r);else{let r=ec(e);n={x:t.x-r.x,y:t.y-r.y,width:t.width,height:t.height}}return H(n)}function em(e){return"static"===ee(e).position}function eg(e,t){if(!I(e)||"fixed"===ee(e).position)return null;if(t)return t(e);let r=e.offsetParent;return Z(e)===r&&(r=r.ownerDocument.body),r}function ev(e,t){var r;let n=O(e);if(z(e))return n;if(!I(e)){let t=er(e);for(;t&&!Q(t);){if(_(t)&&!em(t))return t;t=er(t)}return n}let o=eg(e,t);for(;o&&(r=o,K.has(T(r)))&&em(o);)o=eg(o,t);return o&&Q(o)&&em(o)&&!X(o)?n:o||function(e){let t=er(e);for(;I(t)&&!Q(t);){if(X(t))return t;if(z(t))break;t=er(t)}return null}(e)||n}let ew=async function(e){let t=this.getOffsetParent||ev,r=this.getDimensions,n=await r(e.floating);return{reference:function(e,t,r){let n=I(t),o=Z(t),i="fixed"===r,l=es(e,!0,i,t),a={scrollLeft:0,scrollTop:0},u=c(0);if(n||!n&&!i){if(("body"!==T(t)||G(o))&&(a=et(t)),n){let e=es(t,!0,i,t);u.x=e.x+t.clientLeft,u.y=e.y+t.clientTop}else o&&(u.x=ef(o))}i&&!n&&o&&(u.x=ef(o));let s=!o||n||i?c(0):ed(o,a);return{x:l.left+a.scrollLeft-u.x-s.x,y:l.top+a.scrollTop-u.y-s.y,width:l.width,height:l.height}}(e.reference,await t(e.floating),e.strategy),floating:{x:0,y:0,width:n.width,height:n.height}}},eC={convertOffsetParentRelativeRectToViewportRelativeRect:function(e){let{elements:t,rect:r,offsetParent:n,strategy:o}=e,i="fixed"===o,l=Z(n),a=!!t&&z(t.floating);if(n===l||a&&i)return r;let u={scrollLeft:0,scrollTop:0},s=c(1),f=c(0),d=I(n);if((d||!d&&!i)&&(("body"!==T(n)||G(l))&&(u=et(n)),I(n))){let e=es(n);s=ea(n),f.x=e.x+n.clientLeft,f.y=e.y+n.clientTop}let p=!l||d||i?c(0):ed(l,u,!0);return{width:r.width*s.x,height:r.height*s.y,x:r.x*s.x-u.scrollLeft*s.x+f.x+p.x,y:r.y*s.y-u.scrollTop*s.y+f.y+p.y}},getDocumentElement:Z,getClippingRect:function(e){let{element:t,boundary:r,rootBoundary:n,strategy:o}=e,a=[..."clippingAncestors"===r?z(t)?[]:function(e,t){let r=t.get(e);if(r)return r;let n=en(e,[],!1).filter(e=>_(e)&&"body"!==T(e)),o=null,i="fixed"===ee(e).position,l=i?er(e):e;for(;_(l)&&!Q(l);){let t=ee(l),r=X(l);r||"fixed"!==t.position||(o=null),(i?!r&&!o:!r&&"static"===t.position&&!!o&&ep.has(o.position)||G(l)&&!r&&function e(t,r){let n=er(t);return!(n===r||!_(n)||Q(n))&&("fixed"===ee(n).position||e(n,r))}(e,l))?n=n.filter(e=>e!==l):o=t,l=er(l)}return t.set(e,n),n}(t,this._c):[].concat(r),n],u=a[0],c=a.reduce((e,r)=>{let n=eh(t,r,o);return e.top=l(n.top,e.top),e.right=i(n.right,e.right),e.bottom=i(n.bottom,e.bottom),e.left=l(n.left,e.left),e},eh(t,u,o));return{width:c.right-c.left,height:c.bottom-c.top,x:c.left,y:c.top}},getOffsetParent:ev,getElementRects:ew,getClientRects:function(e){return Array.from(e.getClientRects())},getDimensions:function(e){let{width:t,height:r}=ei(e);return{width:t,height:r}},getScale:ea,isElement:_,isRTL:function(e){return"rtl"===ee(e).direction}};function ex(e,t){return e.x===t.x&&e.y===t.y&&e.width===t.width&&e.height===t.height}let ey=e=>({name:"arrow",options:e,async fn(t){let{x:r,y:n,placement:o,rects:a,platform:u,elements:c,middlewareData:s}=t,{element:f,padding:p=0}=d(e,t)||{};if(null==f)return{};let v=R(p),C={x:r,y:n},x=m(w(o)),y=g(x),b=await u.getDimensions(f),M="y"===x,L=M?"clientHeight":"clientWidth",H=a.reference[y]+a.reference[x]-C[x]-a.floating[y],k=C[x]-a.reference[x],A=await (null==u.getOffsetParent?void 0:u.getOffsetParent(f)),j=A?A[L]:0;j&&await (null==u.isElement?void 0:u.isElement(A))||(j=c.floating[L]||a.floating[y]);let S=j/2-b[y]/2-1,V=i(v[M?"top":"left"],S),P=i(v[M?"bottom":"right"],S),D=j-b[y]-P,E=j/2-b[y]/2+(H/2-k/2),T=l(V,i(E,D)),O=!s.arrow&&null!=h(o)&&E!==T&&a.reference[y]/2-(E<V?V:P)-b[y]/2<0,Z=O?E<V?E-V:E-D:0;return{[x]:C[x]+Z,data:{[x]:T,centerOffset:E-T-Z,...O&&{alignmentOffset:Z}},reset:O}}}),eb=(e,t,r)=>{let n=new Map,o={platform:eC,...r},i={...o.platform,_c:n};return A(e,t,{...o,platform:i})};var eM=r(87277),eL="undefined"!=typeof document?n.useLayoutEffect:function(){};function eR(e,t){let r,n,o;if(e===t)return!0;if(typeof e!=typeof t)return!1;if("function"==typeof e&&e.toString()===t.toString())return!0;if(e&&t&&"object"==typeof e){if(Array.isArray(e)){if((r=e.length)!==t.length)return!1;for(n=r;0!=n--;)if(!eR(e[n],t[n]))return!1;return!0}if((r=(o=Object.keys(e)).length)!==Object.keys(t).length)return!1;for(n=r;0!=n--;)if(!({}).hasOwnProperty.call(t,o[n]))return!1;for(n=r;0!=n--;){let r=o[n];if(("_owner"!==r||!e.$$typeof)&&!eR(e[r],t[r]))return!1}return!0}return e!=e&&t!=t}function eH(e){return"undefined"==typeof window?1:(e.ownerDocument.defaultView||window).devicePixelRatio||1}function ek(e,t){let r=eH(e);return Math.round(t*r)/r}function eA(e){let t=n.useRef(e);return eL(()=>{t.current=e}),t}let ej=e=>({name:"arrow",options:e,fn(t){let{element:r,padding:n}="function"==typeof e?e(t):e;return r&&({}).hasOwnProperty.call(r,"current")?null!=r.current?ey({element:r.current,padding:n}).fn(t):{}:r?ey({element:r,padding:n}).fn(t):{}}}),eS=(e,t)=>({...function(e){return void 0===e&&(e=0),{name:"offset",options:e,async fn(t){var r,n;let{x:o,y:i,placement:l,middlewareData:a}=t,u=await D(t,e);return l===(null==(r=a.offset)?void 0:r.placement)&&null!=(n=a.arrow)&&n.alignmentOffset?{}:{x:o+u.x,y:i+u.y,data:{...u,placement:l}}}}}(e),options:[e,t]}),eV=(e,t)=>({...function(e){return void 0===e&&(e={}),{name:"shift",options:e,async fn(t){let{x:r,y:n,placement:o}=t,{mainAxis:a=!0,crossAxis:u=!1,limiter:c={fn:e=>{let{x:t,y:r}=e;return{x:t,y:r}}},...s}=d(e,t),f={x:r,y:n},h=await j(t,s),g=w(p(o)),v=m(g),C=f[v],x=f[g];if(a){let e="y"===v?"top":"left",t="y"===v?"bottom":"right",r=C+h[e],n=C-h[t];C=l(r,i(C,n))}if(u){let e="y"===g?"top":"left",t="y"===g?"bottom":"right",r=x+h[e],n=x-h[t];x=l(r,i(x,n))}let y=c.fn({...t,[v]:C,[g]:x});return{...y,data:{x:y.x-r,y:y.y-n,enabled:{[v]:a,[g]:u}}}}}}(e),options:[e,t]}),eP=(e,t)=>({...function(e){return void 0===e&&(e={}),{options:e,fn(t){let{x:r,y:n,placement:o,rects:i,middlewareData:l}=t,{offset:a=0,mainAxis:u=!0,crossAxis:c=!0}=d(e,t),s={x:r,y:n},f=w(o),h=m(f),g=s[h],v=s[f],C=d(a,t),x="number"==typeof C?{mainAxis:C,crossAxis:0}:{mainAxis:0,crossAxis:0,...C};if(u){let e="y"===h?"height":"width",t=i.reference[h]-i.floating[e]+x.mainAxis,r=i.reference[h]+i.reference[e]-x.mainAxis;g<t?g=t:g>r&&(g=r)}if(c){var y,b;let e="y"===h?"width":"height",t=P.has(p(o)),r=i.reference[f]-i.floating[e]+(t&&(null==(y=l.offset)?void 0:y[f])||0)+(t?0:x.crossAxis),n=i.reference[f]+i.reference[e]+(t?0:(null==(b=l.offset)?void 0:b[f])||0)-(t?x.crossAxis:0);v<r?v=r:v>n&&(v=n)}return{[h]:g,[f]:v}}}}(e),options:[e,t]}),eD=(e,t)=>({...function(e){return void 0===e&&(e={}),{name:"flip",options:e,async fn(t){var r,n,o,i,l;let{placement:a,middlewareData:u,rects:c,initialPlacement:s,platform:f,elements:v}=t,{mainAxis:R=!0,crossAxis:H=!0,fallbackPlacements:k,fallbackStrategy:A="bestFit",fallbackAxisSideDirection:S="none",flipAlignment:V=!0,...P}=d(e,t);if(null!=(r=u.arrow)&&r.alignmentOffset)return{};let D=p(a),E=w(s),T=p(s)===s,O=await (null==f.isRTL?void 0:f.isRTL(v.floating)),Z=k||(T||!V?[L(s)]:function(e){let t=L(e);return[C(e),t,C(t)]}(s)),N="none"!==S;!k&&N&&Z.push(...function(e,t,r,n){let o=h(e),i=function(e,t,r){switch(e){case"top":case"bottom":if(r)return t?y:x;return t?x:y;case"left":case"right":return t?b:M;default:return[]}}(p(e),"start"===r,n);return o&&(i=i.map(e=>e+"-"+o),t&&(i=i.concat(i.map(C)))),i}(s,V,S,O));let _=[s,...Z],I=await j(t,P),F=[],B=(null==(n=u.flip)?void 0:n.overflows)||[];if(R&&F.push(I[D]),H){let e=function(e,t,r){void 0===r&&(r=!1);let n=h(e),o=m(w(e)),i=g(o),l="x"===o?n===(r?"end":"start")?"right":"left":"start"===n?"bottom":"top";return t.reference[i]>t.floating[i]&&(l=L(l)),[l,L(l)]}(a,c,O);F.push(I[e[0]],I[e[1]])}if(B=[...B,{placement:a,overflows:F}],!F.every(e=>e<=0)){let e=((null==(o=u.flip)?void 0:o.index)||0)+1,t=_[e];if(t&&("alignment"!==H||E===w(t)||B.every(e=>e.overflows[0]>0&&w(e.placement)===E)))return{data:{index:e,overflows:B},reset:{placement:t}};let r=null==(i=B.filter(e=>e.overflows[0]<=0).sort((e,t)=>e.overflows[1]-t.overflows[1])[0])?void 0:i.placement;if(!r)switch(A){case"bestFit":{let e=null==(l=B.filter(e=>{if(N){let t=w(e.placement);return t===E||"y"===t}return!0}).map(e=>[e.placement,e.overflows.filter(e=>e>0).reduce((e,t)=>e+t,0)]).sort((e,t)=>e[1]-t[1])[0])?void 0:l[0];e&&(r=e);break}case"initialPlacement":r=s}if(a!==r)return{reset:{placement:r}}}return{}}}}(e),options:[e,t]}),eE=(e,t)=>({...function(e){return void 0===e&&(e={}),{name:"size",options:e,async fn(t){var r,n;let o,a;let{placement:u,rects:c,platform:s,elements:f}=t,{apply:m=()=>{},...g}=d(e,t),v=await j(t,g),C=p(u),x=h(u),y="y"===w(u),{width:b,height:M}=c.floating;"top"===C||"bottom"===C?(o=C,a=x===(await (null==s.isRTL?void 0:s.isRTL(f.floating))?"start":"end")?"left":"right"):(a=C,o="end"===x?"top":"bottom");let L=M-v.top-v.bottom,R=b-v.left-v.right,H=i(M-v[o],L),k=i(b-v[a],R),A=!t.middlewareData.shift,S=H,V=k;if(null!=(r=t.middlewareData.shift)&&r.enabled.x&&(V=R),null!=(n=t.middlewareData.shift)&&n.enabled.y&&(S=L),A&&!x){let e=l(v.left,0),t=l(v.right,0),r=l(v.top,0),n=l(v.bottom,0);y?V=b-2*(0!==e||0!==t?e+t:l(v.left,v.right)):S=M-2*(0!==r||0!==n?r+n:l(v.top,v.bottom))}await m({...t,availableWidth:V,availableHeight:S});let P=await s.getDimensions(f.floating);return b!==P.width||M!==P.height?{reset:{rects:!0}}:{}}}}(e),options:[e,t]}),eT=(e,t)=>({...function(e){return void 0===e&&(e={}),{name:"hide",options:e,async fn(t){let{rects:r}=t,{strategy:n="referenceHidden",...o}=d(e,t);switch(n){case"referenceHidden":{let e=S(await j(t,{...o,elementContext:"reference"}),r.reference);return{data:{referenceHiddenOffsets:e,referenceHidden:V(e)}}}case"escaped":{let e=S(await j(t,{...o,altBoundary:!0}),r.floating);return{data:{escapedOffsets:e,escaped:V(e)}}}default:return{}}}}}(e),options:[e,t]}),eO=(e,t)=>({...ej(e),options:[e,t]});var eZ=r(85137),eN=r(2569),e_=n.forwardRef((e,t)=>{let{children:r,width:n=10,height:o=5,...i}=e;return(0,eN.jsx)(eZ.sG.svg,{...i,ref:t,width:n,height:o,viewBox:"0 0 30 10",preserveAspectRatio:"none",children:e.asChild?r:(0,eN.jsx)("polygon",{points:"0,0 30,0 15,10"})})});e_.displayName="Arrow";var eI=r(58028),eF=r(4002),eB=r(89553),eG=r(29543),eK=r(42509),eW="Popper",[ez,e$]=(0,eF.A)(eW),[eY,eU]=ez(eW),eX=e=>{let{__scopePopper:t,children:r}=e,[o,i]=n.useState(null);return(0,eN.jsx)(eY,{scope:t,anchor:o,onAnchorChange:i,children:r})};eX.displayName=eW;var eq="PopperAnchor",eJ=n.forwardRef((e,t)=>{let{__scopePopper:r,virtualRef:o,...i}=e,l=eU(eq,r),a=n.useRef(null),u=(0,eI.s)(t,a);return n.useEffect(()=>{l.onAnchorChange(o?.current||a.current)}),o?null:(0,eN.jsx)(eZ.sG.div,{...i,ref:u})});eJ.displayName=eq;var eQ="PopperContent",[e1,e0]=ez(eQ),e2=n.forwardRef((e,t)=>{let{__scopePopper:r,side:o="bottom",sideOffset:a=0,align:c="center",alignOffset:s=0,arrowPadding:f=0,avoidCollisions:d=!0,collisionBoundary:p=[],collisionPadding:h=0,sticky:m="partial",hideWhenDetached:g=!1,updatePositionStrategy:v="optimized",onPlaced:w,...C}=e,x=eU(eQ,r),[y,b]=n.useState(null),M=(0,eI.s)(t,e=>b(e)),[L,R]=n.useState(null),H=(0,eK.X)(L),k=H?.width??0,A=H?.height??0,j="number"==typeof h?h:{top:0,right:0,bottom:0,left:0,...h},S=Array.isArray(p)?p:[p],V=S.length>0,P={padding:j,boundary:S.filter(e7),altBoundary:V},{refs:D,floatingStyles:E,placement:T,isPositioned:O,middlewareData:N}=function(e){void 0===e&&(e={});let{placement:t="bottom",strategy:r="absolute",middleware:o=[],platform:i,elements:{reference:l,floating:a}={},transform:u=!0,whileElementsMounted:c,open:s}=e,[f,d]=n.useState({x:0,y:0,strategy:r,placement:t,middlewareData:{},isPositioned:!1}),[p,h]=n.useState(o);eR(p,o)||h(o);let[m,g]=n.useState(null),[v,w]=n.useState(null),C=n.useCallback(e=>{e!==M.current&&(M.current=e,g(e))},[]),x=n.useCallback(e=>{e!==L.current&&(L.current=e,w(e))},[]),y=l||m,b=a||v,M=n.useRef(null),L=n.useRef(null),R=n.useRef(f),H=null!=c,k=eA(c),A=eA(i),j=eA(s),S=n.useCallback(()=>{if(!M.current||!L.current)return;let e={placement:t,strategy:r,middleware:p};A.current&&(e.platform=A.current),eb(M.current,L.current,e).then(e=>{let t={...e,isPositioned:!1!==j.current};V.current&&!eR(R.current,t)&&(R.current=t,eM.flushSync(()=>{d(t)}))})},[p,t,r,A,j]);eL(()=>{!1===s&&R.current.isPositioned&&(R.current.isPositioned=!1,d(e=>({...e,isPositioned:!1})))},[s]);let V=n.useRef(!1);eL(()=>(V.current=!0,()=>{V.current=!1}),[]),eL(()=>{if(y&&(M.current=y),b&&(L.current=b),y&&b){if(k.current)return k.current(y,b,S);S()}},[y,b,S,k,H]);let P=n.useMemo(()=>({reference:M,floating:L,setReference:C,setFloating:x}),[C,x]),D=n.useMemo(()=>({reference:y,floating:b}),[y,b]),E=n.useMemo(()=>{let e={position:r,left:0,top:0};if(!D.floating)return e;let t=ek(D.floating,f.x),n=ek(D.floating,f.y);return u?{...e,transform:"translate("+t+"px, "+n+"px)",...eH(D.floating)>=1.5&&{willChange:"transform"}}:{position:r,left:t,top:n}},[r,u,D.floating,f.x,f.y]);return n.useMemo(()=>({...f,update:S,refs:P,elements:D,floatingStyles:E}),[f,S,P,D,E])}({strategy:"fixed",placement:o+("center"!==c?"-"+c:""),whileElementsMounted:(...e)=>(function(e,t,r,n){let o;void 0===n&&(n={});let{ancestorScroll:a=!0,ancestorResize:c=!0,elementResize:s="function"==typeof ResizeObserver,layoutShift:f="function"==typeof IntersectionObserver,animationFrame:d=!1}=n,p=el(e),h=a||c?[...p?en(p):[],...en(t)]:[];h.forEach(e=>{a&&e.addEventListener("scroll",r,{passive:!0}),c&&e.addEventListener("resize",r)});let m=p&&f?function(e,t){let r,n=null,o=Z(e);function a(){var e;clearTimeout(r),null==(e=n)||e.disconnect(),n=null}return function c(s,f){void 0===s&&(s=!1),void 0===f&&(f=1),a();let d=e.getBoundingClientRect(),{left:p,top:h,width:m,height:g}=d;if(s||t(),!m||!g)return;let v=u(h),w=u(o.clientWidth-(p+m)),C={rootMargin:-v+"px "+-w+"px "+-u(o.clientHeight-(h+g))+"px "+-u(p)+"px",threshold:l(0,i(1,f))||1},x=!0;function y(t){let n=t[0].intersectionRatio;if(n!==f){if(!x)return c();n?c(!1,n):r=setTimeout(()=>{c(!1,1e-7)},1e3)}1!==n||ex(d,e.getBoundingClientRect())||c(),x=!1}try{n=new IntersectionObserver(y,{...C,root:o.ownerDocument})}catch(e){n=new IntersectionObserver(y,C)}n.observe(e)}(!0),a}(p,r):null,g=-1,v=null;s&&(v=new ResizeObserver(e=>{let[n]=e;n&&n.target===p&&v&&(v.unobserve(t),cancelAnimationFrame(g),g=requestAnimationFrame(()=>{var e;null==(e=v)||e.observe(t)})),r()}),p&&!d&&v.observe(p),v.observe(t));let w=d?es(e):null;return d&&function t(){let n=es(e);w&&!ex(w,n)&&r(),w=n,o=requestAnimationFrame(t)}(),r(),()=>{var e;h.forEach(e=>{a&&e.removeEventListener("scroll",r),c&&e.removeEventListener("resize",r)}),null==m||m(),null==(e=v)||e.disconnect(),v=null,d&&cancelAnimationFrame(o)}})(...e,{animationFrame:"always"===v}),elements:{reference:x.anchor},middleware:[eS({mainAxis:a+A,alignmentAxis:s}),d&&eV({mainAxis:!0,crossAxis:!1,limiter:"partial"===m?eP():void 0,...P}),d&&eD({...P}),eE({...P,apply:({elements:e,rects:t,availableWidth:r,availableHeight:n})=>{let{width:o,height:i}=t.reference,l=e.floating.style;l.setProperty("--radix-popper-available-width",`${r}px`),l.setProperty("--radix-popper-available-height",`${n}px`),l.setProperty("--radix-popper-anchor-width",`${o}px`),l.setProperty("--radix-popper-anchor-height",`${i}px`)}}),L&&eO({element:L,padding:f}),e8({arrowWidth:k,arrowHeight:A}),g&&eT({strategy:"referenceHidden",...P})]}),[_,I]=e6(T),F=(0,eB.c)(w);(0,eG.N)(()=>{O&&F?.()},[O,F]);let B=N.arrow?.x,G=N.arrow?.y,K=N.arrow?.centerOffset!==0,[W,z]=n.useState();return(0,eG.N)(()=>{y&&z(window.getComputedStyle(y).zIndex)},[y]),(0,eN.jsx)("div",{ref:D.setFloating,"data-radix-popper-content-wrapper":"",style:{...E,transform:O?E.transform:"translate(0, -200%)",minWidth:"max-content",zIndex:W,"--radix-popper-transform-origin":[N.transformOrigin?.x,N.transformOrigin?.y].join(" "),...N.hide?.referenceHidden&&{visibility:"hidden",pointerEvents:"none"}},dir:e.dir,children:(0,eN.jsx)(e1,{scope:r,placedSide:_,onArrowChange:R,arrowX:B,arrowY:G,shouldHideArrow:K,children:(0,eN.jsx)(eZ.sG.div,{"data-side":_,"data-align":I,...C,ref:M,style:{...C.style,animation:O?void 0:"none"}})})})});e2.displayName=eQ;var e5="PopperArrow",e4={top:"bottom",right:"left",bottom:"top",left:"right"},e9=n.forwardRef(function(e,t){let{__scopePopper:r,...n}=e,o=e0(e5,r),i=e4[o.placedSide];return(0,eN.jsx)("span",{ref:o.onArrowChange,style:{position:"absolute",left:o.arrowX,top:o.arrowY,[i]:0,transformOrigin:{top:"",right:"0 0",bottom:"center 0",left:"100% 0"}[o.placedSide],transform:{top:"translateY(100%)",right:"translateY(50%) rotate(90deg) translateX(-50%)",bottom:"rotate(180deg)",left:"translateY(50%) rotate(-90deg) translateX(50%)"}[o.placedSide],visibility:o.shouldHideArrow?"hidden":void 0},children:(0,eN.jsx)(e_,{...n,ref:t,style:{...n.style,display:"block"}})})});function e7(e){return null!==e}e9.displayName=e5;var e8=e=>({name:"transformOrigin",options:e,fn(t){let{placement:r,rects:n,middlewareData:o}=t,i=o.arrow?.centerOffset!==0,l=i?0:e.arrowWidth,a=i?0:e.arrowHeight,[u,c]=e6(r),s={start:"0%",center:"50%",end:"100%"}[c],f=(o.arrow?.x??0)+l/2,d=(o.arrow?.y??0)+a/2,p="",h="";return"bottom"===u?(p=i?s:`${f}px`,h=`${-a}px`):"top"===u?(p=i?s:`${f}px`,h=`${n.floating.height+a}px`):"right"===u?(p=`${-a}px`,h=i?s:`${d}px`):"left"===u&&(p=`${n.floating.width+a}px`,h=i?s:`${d}px`),{data:{x:p,y:h}}}});function e6(e){let[t,r="center"]=e.split("-");return[t,r]}var e3=eX,te=eJ,tt=e2,tr=e9},47118:(e,t,r)=>{r.d(t,{AN5:()=>s,Bgv:()=>l,Dcp:()=>w,Lcj:()=>c,LrS:()=>a,QWc:()=>h,R0Y:()=>i,RQr:()=>g,SJ3:()=>o,VNl:()=>y,WN7:()=>x,ase:()=>p,bwM:()=>b,fsL:()=>m,r53:()=>f,sAW:()=>d,tLq:()=>C,uEe:()=>M,xi0:()=>v,y_v:()=>u});var n=r(67920);function o(e){return(0,n.k5)({tag:"svg",attr:{viewBox:"0 0 24 24",fill:"currentColor"},child:[{tag:"path",attr:{d:"M16.0037 9.41421L7.39712 18.0208L5.98291 16.6066L14.5895 8H7.00373V6H18.0037V17H16.0037V9.41421Z"},child:[]}]})(e)}function i(e){return(0,n.k5)({tag:"svg",attr:{viewBox:"0 0 24 24",fill:"currentColor"},child:[{tag:"path",attr:{d:"M3 3H21C21.5523 3 22 3.44772 22 4V20C22 20.5523 21.5523 21 21 21H3C2.44772 21 2 20.5523 2 20V4C2 3.44772 2.44772 3 3 3ZM20 7.23792L12.0718 14.338L4 7.21594V19H20V7.23792ZM4.51146 5L12.0619 11.662L19.501 5H4.51146Z"},child:[]}]})(e)}function l(e){return(0,n.k5)({tag:"svg",attr:{viewBox:"0 0 24 24",fill:"currentColor"},child:[{tag:"path",attr:{d:"M2 12C2 6.47715 6.47715 2 12 2C17.5228 2 22 6.47715 22 12C22 17.5228 17.5228 22 12 22H2L4.92893 19.0711C3.11929 17.2614 2 14.7614 2 12ZM6.82843 20H12C16.4183 20 20 16.4183 20 12C20 7.58172 16.4183 4 12 4C7.58172 4 4 7.58172 4 12C4 14.1524 4.85124 16.1649 6.34315 17.6569L7.75736 19.0711L6.82843 20ZM8 13H16C16 15.2091 14.2091 17 12 17C9.79086 17 8 15.2091 8 13Z"},child:[]}]})(e)}function a(e){return(0,n.k5)({tag:"svg",attr:{viewBox:"0 0 24 24",fill:"currentColor"},child:[{tag:"path",attr:{d:"M12 2C17.5222 2 22 5.97778 22 10.8889C22 13.9556 19.5111 16.4444 16.4444 16.4444H14.4778C13.5556 16.4444 12.8111 17.1889 12.8111 18.1111C12.8111 18.5333 12.9778 18.9222 13.2333 19.2111C13.5 19.5111 13.6667 19.9 13.6667 20.3333C13.6667 21.2556 12.9 22 12 22C6.47778 22 2 17.5222 2 12C2 6.47778 6.47778 2 12 2ZM10.8111 18.1111C10.8111 16.0843 12.451 14.4444 14.4778 14.4444H16.4444C18.4065 14.4444 20 12.851 20 10.8889C20 7.1392 16.4677 4 12 4C7.58235 4 4 7.58235 4 12C4 16.19 7.2226 19.6285 11.324 19.9718C10.9948 19.4168 10.8111 18.7761 10.8111 18.1111ZM7.5 12C6.67157 12 6 11.3284 6 10.5C6 9.67157 6.67157 9 7.5 9C8.32843 9 9 9.67157 9 10.5C9 11.3284 8.32843 12 7.5 12ZM16.5 12C15.6716 12 15 11.3284 15 10.5C15 9.67157 15.6716 9 16.5 9C17.3284 9 18 9.67157 18 10.5C18 11.3284 17.3284 12 16.5 12ZM12 9C11.1716 9 10.5 8.32843 10.5 7.5C10.5 6.67157 11.1716 6 12 6C12.8284 6 13.5 6.67157 13.5 7.5C13.5 8.32843 12.8284 9 12 9Z"},child:[]}]})(e)}function u(e){return(0,n.k5)({tag:"svg",attr:{viewBox:"0 0 24 24",fill:"currentColor"},child:[{tag:"path",attr:{d:"M6 18H18V6H6V18ZM14 20H10V22H8V20H5C4.44772 20 4 19.5523 4 19V16H2V14H4V10H2V8H4V5C4 4.44772 4.44772 4 5 4H8V2H10V4H14V2H16V4H19C19.5523 4 20 4.44772 20 5V8H22V10H20V14H22V16H20V19C20 19.5523 19.5523 20 19 20H16V22H14V20ZM8 8H16V16H8V8Z"},child:[]}]})(e)}function c(e){return(0,n.k5)({tag:"svg",attr:{viewBox:"0 0 24 24",fill:"currentColor"},child:[{tag:"path",attr:{d:"M12.0049 22.0027C6.48204 22.0027 2.00488 17.5256 2.00488 12.0027C2.00488 6.4799 6.48204 2.00275 12.0049 2.00275C17.5277 2.00275 22.0049 6.4799 22.0049 12.0027C22.0049 17.5256 17.5277 22.0027 12.0049 22.0027ZM12.0049 20.0027C16.4232 20.0027 20.0049 16.421 20.0049 12.0027C20.0049 7.58447 16.4232 4.00275 12.0049 4.00275C7.5866 4.00275 4.00488 7.58447 4.00488 12.0027C4.00488 16.421 7.5866 20.0027 12.0049 20.0027ZM7.00488 13.0027H16.0049V15.0027H12.0049V18.0027L7.00488 13.0027ZM12.0049 9.00275V6.00275L17.0049 11.0027H8.00488V9.00275H12.0049Z"},child:[]}]})(e)}function s(e){return(0,n.k5)({tag:"svg",attr:{viewBox:"0 0 24 24",fill:"currentColor"},child:[{tag:"path",attr:{d:"M12.0049 22.0027C6.48204 22.0027 2.00488 17.5256 2.00488 12.0027C2.00488 6.4799 6.48204 2.00275 12.0049 2.00275C17.5277 2.00275 22.0049 6.4799 22.0049 12.0027C22.0049 17.5256 17.5277 22.0027 12.0049 22.0027ZM12.0049 20.0027C16.4232 20.0027 20.0049 16.421 20.0049 12.0027C20.0049 7.58447 16.4232 4.00275 12.0049 4.00275C7.5866 4.00275 4.00488 7.58447 4.00488 12.0027C4.00488 16.421 7.5866 20.0027 12.0049 20.0027ZM8.50488 14.0027H14.0049C14.281 14.0027 14.5049 13.7789 14.5049 13.5027C14.5049 13.2266 14.281 13.0027 14.0049 13.0027H10.0049C8.62417 13.0027 7.50488 11.8835 7.50488 10.5027C7.50488 9.12203 8.62417 8.00275 10.0049 8.00275H11.0049V6.00275H13.0049V8.00275H15.5049V10.0027H10.0049C9.72874 10.0027 9.50488 10.2266 9.50488 10.5027C9.50488 10.7789 9.72874 11.0027 10.0049 11.0027H14.0049C15.3856 11.0027 16.5049 12.122 16.5049 13.5027C16.5049 14.8835 15.3856 16.0027 14.0049 16.0027H13.0049V18.0027H11.0049V16.0027H8.50488V14.0027Z"},child:[]}]})(e)}function f(e){return(0,n.k5)({tag:"svg",attr:{viewBox:"0 0 24 24",fill:"currentColor"},child:[{tag:"path",attr:{d:"M19.3034 5.33716C17.9344 4.71103 16.4805 4.2547 14.9629 4C14.7719 4.32899 14.5596 4.77471 14.411 5.12492C12.7969 4.89144 11.1944 4.89144 9.60255 5.12492C9.45397 4.77471 9.2311 4.32899 9.05068 4C7.52251 4.2547 6.06861 4.71103 4.70915 5.33716C1.96053 9.39111 1.21766 13.3495 1.5891 17.2549C3.41443 18.5815 5.17612 19.388 6.90701 19.9187C7.33151 19.3456 7.71356 18.73 8.04255 18.0827C7.41641 17.8492 6.82211 17.5627 6.24904 17.2231C6.39762 17.117 6.5462 17.0003 6.68416 16.8835C10.1438 18.4648 13.8911 18.4648 17.3082 16.8835C17.4568 17.0003 17.5948 17.117 17.7434 17.2231C17.1703 17.5627 16.576 17.8492 15.9499 18.0827C16.2789 18.73 16.6609 19.3456 17.0854 19.9187C18.8152 19.388 20.5875 18.5815 22.4033 17.2549C22.8596 12.7341 21.6806 8.80747 19.3034 5.33716ZM8.5201 14.8459C7.48007 14.8459 6.63107 13.9014 6.63107 12.7447C6.63107 11.5879 7.45884 10.6434 8.5201 10.6434C9.57071 10.6434 10.4303 11.5879 10.4091 12.7447C10.4091 13.9014 9.57071 14.8459 8.5201 14.8459ZM15.4936 14.8459C14.4535 14.8459 13.6034 13.9014 13.6034 12.7447C13.6034 11.5879 14.4323 10.6434 15.4936 10.6434C16.5442 10.6434 17.4038 11.5879 17.3825 12.7447C17.3825 13.9014 16.5548 14.8459 15.4936 14.8459Z"},child:[]}]})(e)}function d(e){return(0,n.k5)({tag:"svg",attr:{viewBox:"0 0 24 24",fill:"currentColor"},child:[{tag:"path",attr:{d:"M12.001 2C6.47598 2 2.00098 6.475 2.00098 12C2.00098 16.425 4.86348 20.1625 8.83848 21.4875C9.33848 21.575 9.52598 21.275 9.52598 21.0125C9.52598 20.775 9.51348 19.9875 9.51348 19.15C7.00098 19.6125 6.35098 18.5375 6.15098 17.975C6.03848 17.6875 5.55098 16.8 5.12598 16.5625C4.77598 16.375 4.27598 15.9125 5.11348 15.9C5.90098 15.8875 6.46348 16.625 6.65098 16.925C7.55098 18.4375 8.98848 18.0125 9.56348 17.75C9.65098 17.1 9.91348 16.6625 10.201 16.4125C7.97598 16.1625 5.65098 15.3 5.65098 11.475C5.65098 10.3875 6.03848 9.4875 6.67598 8.7875C6.57598 8.5375 6.22598 7.5125 6.77598 6.1375C6.77598 6.1375 7.61348 5.875 9.52598 7.1625C10.326 6.9375 11.176 6.825 12.026 6.825C12.876 6.825 13.726 6.9375 14.526 7.1625C16.4385 5.8625 17.276 6.1375 17.276 6.1375C17.826 7.5125 17.476 8.5375 17.376 8.7875C18.0135 9.4875 18.401 10.375 18.401 11.475C18.401 15.3125 16.0635 16.1625 13.8385 16.4125C14.201 16.725 14.5135 17.325 14.5135 18.2625C14.5135 19.6 14.501 20.675 14.501 21.0125C14.501 21.275 14.6885 21.5875 15.1885 21.4875C19.259 20.1133 21.9999 16.2963 22.001 12C22.001 6.475 17.526 2 12.001 2Z"},child:[]}]})(e)}function p(e){return(0,n.k5)({tag:"svg",attr:{viewBox:"0 0 24 24",fill:"currentColor"},child:[{tag:"path",attr:{d:"M17.6874 3.0625L12.6907 8.77425L8.37045 3.0625H2.11328L9.58961 12.8387L2.50378 20.9375H5.53795L11.0068 14.6886L15.7863 20.9375H21.8885L14.095 10.6342L20.7198 3.0625H17.6874ZM16.6232 19.1225L5.65436 4.78217H7.45745L18.3034 19.1225H16.6232Z"},child:[]}]})(e)}function h(e){return(0,n.k5)({tag:"svg",attr:{viewBox:"0 0 24 24",fill:"currentColor"},child:[{tag:"path",attr:{d:"M4.99958 12.9999C4.99958 7.91198 7.90222 3.5636 11.9996 1.81799C16.0969 3.5636 18.9996 7.91198 18.9996 12.9999C18.9996 13.8229 18.9236 14.6264 18.779 15.4027L20.7194 17.2353C20.8845 17.3913 20.9238 17.6389 20.815 17.8383L18.3196 22.4133C18.1873 22.6557 17.8836 22.7451 17.6412 22.6128C17.5993 22.59 17.5608 22.5612 17.5271 22.5274L15.2925 20.2928C15.1049 20.1053 14.8506 19.9999 14.5854 19.9999H9.41379C9.14857 19.9999 8.89422 20.1053 8.70668 20.2928L6.47209 22.5274C6.27683 22.7227 5.96025 22.7227 5.76498 22.5274C5.73122 22.4937 5.70246 22.4552 5.67959 22.4133L3.18412 17.8383C3.07537 17.6389 3.11464 17.3913 3.27975 17.2353L5.22014 15.4027C5.07551 14.6264 4.99958 13.8229 4.99958 12.9999ZM6.47542 19.6957L7.29247 18.8786C7.85508 18.316 8.61814 17.9999 9.41379 17.9999H14.5854C15.381 17.9999 16.1441 18.316 16.7067 18.8786L17.5237 19.6957L18.5056 17.8955L17.4058 16.8568C16.9117 16.3901 16.6884 15.7045 16.8128 15.0364C16.9366 14.3722 16.9996 13.6911 16.9996 12.9999C16.9996 9.13037 15.0045 5.69965 11.9996 4.04033C8.99462 5.69965 6.99958 9.13037 6.99958 12.9999C6.99958 13.6911 7.06255 14.3722 7.18631 15.0364C7.31078 15.7045 7.08746 16.3901 6.59338 16.8568L5.49353 17.8955L6.47542 19.6957ZM11.9996 12.9999C10.895 12.9999 9.99958 12.1045 9.99958 10.9999C9.99958 9.89537 10.895 8.99994 11.9996 8.99994C13.1041 8.99994 13.9996 9.89537 13.9996 10.9999C13.9996 12.1045 13.1041 12.9999 11.9996 12.9999Z"},child:[]}]})(e)}function m(e){return(0,n.k5)({tag:"svg",attr:{viewBox:"0 0 24 24",fill:"currentColor"},child:[{tag:"path",attr:{d:"M2.9918 21C2.44405 21 2 20.5551 2 20.0066V3.9934C2 3.44476 2.45531 3 2.9918 3H21.0082C21.556 3 22 3.44495 22 3.9934V20.0066C22 20.5552 21.5447 21 21.0082 21H2.9918ZM20 15V5H4V19L14 9L20 15ZM20 17.8284L14 11.8284L6.82843 19H20V17.8284ZM8 11C6.89543 11 6 10.1046 6 9C6 7.89543 6.89543 7 8 7C9.10457 7 10 7.89543 10 9C10 10.1046 9.10457 11 8 11Z"},child:[]}]})(e)}function g(e){return(0,n.k5)({tag:"svg",attr:{viewBox:"0 0 24 24",fill:"currentColor"},child:[{tag:"path",attr:{d:"M11.9998 3C10.3429 3 8.99976 4.34315 8.99976 6V10C8.99976 11.6569 10.3429 13 11.9998 13C13.6566 13 14.9998 11.6569 14.9998 10V6C14.9998 4.34315 13.6566 3 11.9998 3ZM11.9998 1C14.7612 1 16.9998 3.23858 16.9998 6V10C16.9998 12.7614 14.7612 15 11.9998 15C9.23833 15 6.99976 12.7614 6.99976 10V6C6.99976 3.23858 9.23833 1 11.9998 1ZM3.05469 11H5.07065C5.55588 14.3923 8.47329 17 11.9998 17C15.5262 17 18.4436 14.3923 18.9289 11H20.9448C20.4837 15.1716 17.1714 18.4839 12.9998 18.9451V23H10.9998V18.9451C6.82814 18.4839 3.51584 15.1716 3.05469 11Z"},child:[]}]})(e)}function v(e){return(0,n.k5)({tag:"svg",attr:{viewBox:"0 0 24 24",fill:"currentColor"},child:[{tag:"path",attr:{d:"M3 3.9934C3 3.44476 3.44495 3 3.9934 3H20.0066C20.5552 3 21 3.44495 21 3.9934V20.0066C21 20.5552 20.5551 21 20.0066 21H3.9934C3.44476 21 3 20.5551 3 20.0066V3.9934ZM5 5V19H19V5H5ZM10.6219 8.41459L15.5008 11.6672C15.6846 11.7897 15.7343 12.0381 15.6117 12.2219C15.5824 12.2658 15.5447 12.3035 15.5008 12.3328L10.6219 15.5854C10.4381 15.708 10.1897 15.6583 10.0672 15.4745C10.0234 15.4088 10 15.3316 10 15.2526V8.74741C10 8.52649 10.1791 8.34741 10.4 8.34741C10.479 8.34741 10.5562 8.37078 10.6219 8.41459Z"},child:[]}]})(e)}function w(e){return(0,n.k5)({tag:"svg",attr:{viewBox:"0 0 24 24",fill:"currentColor"},child:[{tag:"path",attr:{d:"M5 7H7V17H5V7ZM1 10H3V14H1V10ZM9 2H11V20H9V2ZM13 4H15V22H13V4ZM17 7H19V17H17V7ZM21 10H23V14H21V10Z"},child:[]}]})(e)}function C(e){return(0,n.k5)({tag:"svg",attr:{viewBox:"0 0 24 24",fill:"currentColor"},child:[{tag:"path",attr:{d:"M12.0003 3C17.3924 3 21.8784 6.87976 22.8189 12C21.8784 17.1202 17.3924 21 12.0003 21C6.60812 21 2.12215 17.1202 1.18164 12C2.12215 6.87976 6.60812 3 12.0003 3ZM12.0003 19C16.2359 19 19.8603 16.052 20.7777 12C19.8603 7.94803 16.2359 5 12.0003 5C7.7646 5 4.14022 7.94803 3.22278 12C4.14022 16.052 7.7646 19 12.0003 19ZM12.0003 16.5C9.51498 16.5 7.50026 14.4853 7.50026 12C7.50026 9.51472 9.51498 7.5 12.0003 7.5C14.4855 7.5 16.5003 9.51472 16.5003 12C16.5003 14.4853 14.4855 16.5 12.0003 16.5ZM12.0003 14.5C13.381 14.5 14.5003 13.3807 14.5003 12C14.5003 10.6193 13.381 9.5 12.0003 9.5C10.6196 9.5 9.50026 10.6193 9.50026 12C9.50026 13.3807 10.6196 14.5 12.0003 14.5Z"},child:[]}]})(e)}function x(e){return(0,n.k5)({tag:"svg",attr:{viewBox:"0 0 24 24",fill:"currentColor"},child:[{tag:"path",attr:{d:"M12.0006 18.26L4.94715 22.2082L6.52248 14.2799L0.587891 8.7918L8.61493 7.84006L12.0006 0.5L15.3862 7.84006L23.4132 8.7918L17.4787 14.2799L19.054 22.2082L12.0006 18.26ZM12.0006 15.968L16.2473 18.3451L15.2988 13.5717L18.8719 10.2674L14.039 9.69434L12.0006 5.27502L9.96214 9.69434L5.12921 10.2674L8.70231 13.5717L7.75383 18.3451L12.0006 15.968Z"},child:[]}]})(e)}function y(e){return(0,n.k5)({tag:"svg",attr:{viewBox:"0 0 24 24",fill:"currentColor"},child:[{tag:"path",attr:{d:"M12 22C6.47715 22 2 17.5228 2 12C2 6.47715 6.47715 2 12 2C17.5228 2 22 6.47715 22 12C22 17.5228 17.5228 22 12 22ZM12 20C16.4183 20 20 16.4183 20 12C20 7.58172 16.4183 4 12 4C7.58172 4 4 7.58172 4 12C4 16.4183 7.58172 20 12 20ZM7 12H9C9 13.6569 10.3431 15 12 15C13.6569 15 15 13.6569 15 12H17C17 14.7614 14.7614 17 12 17C9.23858 17 7 14.7614 7 12Z"},child:[]}]})(e)}function b(e){return(0,n.k5)({tag:"svg",attr:{viewBox:"0 0 24 24",fill:"currentColor"},child:[{tag:"path",attr:{d:"M13 10H20L11 23V14H4L13 1V10Z"},child:[]}]})(e)}function M(e){return(0,n.k5)({tag:"svg",attr:{viewBox:"0 0 24 24",fill:"currentColor"},child:[{tag:"path",attr:{d:"M13 9H21L11 24V15H4L13 0V9ZM11 11V7.22063L7.53238 13H13V17.3944L17.263 11H11Z"},child:[]}]})(e)}},57673:(e,t,r)=>{r.d(t,{H4:()=>L,_V:()=>M,bL:()=>b});var n=r(29068),o=r(4002),i=r(89553),l=r(29543),a=r(85137),u=r(69517);function c(){return()=>{}}var s=r(2569),f="Avatar",[d,p]=(0,o.A)(f),[h,m]=d(f),g=n.forwardRef((e,t)=>{let{__scopeAvatar:r,...o}=e,[i,l]=n.useState("idle");return(0,s.jsx)(h,{scope:r,imageLoadingStatus:i,onImageLoadingStatusChange:l,children:(0,s.jsx)(a.sG.span,{...o,ref:t})})});g.displayName=f;var v="AvatarImage",w=n.forwardRef((e,t)=>{let{__scopeAvatar:r,src:o,onLoadingStatusChange:f=()=>{},...d}=e,p=m(v,r),h=function(e,{referrerPolicy:t,crossOrigin:r}){let o=(0,u.useSyncExternalStore)(c,()=>!0,()=>!1),i=n.useRef(null),a=o?(i.current||(i.current=new window.Image),i.current):null,[s,f]=n.useState(()=>y(a,e));return(0,l.N)(()=>{f(y(a,e))},[a,e]),(0,l.N)(()=>{let e=e=>()=>{f(e)};if(!a)return;let n=e("loaded"),o=e("error");return a.addEventListener("load",n),a.addEventListener("error",o),t&&(a.referrerPolicy=t),"string"==typeof r&&(a.crossOrigin=r),()=>{a.removeEventListener("load",n),a.removeEventListener("error",o)}},[a,r,t]),s}(o,d),g=(0,i.c)(e=>{f(e),p.onImageLoadingStatusChange(e)});return(0,l.N)(()=>{"idle"!==h&&g(h)},[h,g]),"loaded"===h?(0,s.jsx)(a.sG.img,{...d,ref:t,src:o}):null});w.displayName=v;var C="AvatarFallback",x=n.forwardRef((e,t)=>{let{__scopeAvatar:r,delayMs:o,...i}=e,l=m(C,r),[u,c]=n.useState(void 0===o);return n.useEffect(()=>{if(void 0!==o){let e=window.setTimeout(()=>c(!0),o);return()=>window.clearTimeout(e)}},[o]),u&&"loaded"!==l.imageLoadingStatus?(0,s.jsx)(a.sG.span,{...i,ref:t}):null});function y(e,t){return e?t?(e.src!==t&&(e.src=t),e.complete&&e.naturalWidth>0?"loaded":"loading"):"error":"idle"}x.displayName=C;var b=g,M=w,L=x},59719:(e,t,r)=>{r.d(t,{VZG:()=>i,zny:()=>o});var n=r(67920);function o(e){return(0,n.k5)({tag:"svg",attr:{viewBox:"0 0 24 24"},child:[{tag:"path",attr:{d:"M18.25 15.5a.75.75 0 0 1-.75-.75V7.56L7.28 17.78a.749.749 0 0 1-1.275-.326.749.749 0 0 1 .215-.734L16.44 6.5H9.25a.75.75 0 0 1 0-1.5h9a.75.75 0 0 1 .75.75v9a.75.75 0 0 1-.75.75Z"},child:[]}]})(e)}function i(e){return(0,n.k5)({tag:"svg",attr:{viewBox:"0 0 24 24"},child:[{tag:"path",attr:{d:"M12.596 2.043c1.075.076 2.059.281 2.743.956.698.688.92 1.696.92 2.941 0 .432-.057.955-.117 1.438-.026.2-.051.392-.076.572l-.056.429h2.05c.752 0 1.446.108 2.036.404.612.306 1.062.787 1.355 1.431.551 1.214.542 3.008.223 5.394l-.051.39c-.134 1.01-.248 1.872-.396 2.58-.166.795-.394 1.496-.816 2.05-.89 1.168-2.395 1.372-4.583 1.372-2.331 0-4.08-.418-5.544-.824l-.602-.17c-1.023-.29-1.852-.526-2.69-.586A1.75 1.75 0 0 1 5.25 22h-1.5A1.75 1.75 0 0 1 2 20.25V9.75C2 8.784 2.784 8 3.75 8h1.5a1.75 1.75 0 0 1 1.746 1.633 1.85 1.85 0 0 0 .523-.131c.961-.415 2.774-1.534 2.774-4.2V4.249c0-1.22 1.002-2.298 2.303-2.206ZM7 18.918c1.059.064 2.079.355 3.118.652l.568.16c1.406.39 3.006.77 5.142.77 2.277 0 3.004-.274 3.39-.781.216-.283.388-.718.54-1.448.136-.65.242-1.45.379-2.477l.05-.384c.32-2.4.253-3.795-.102-4.575-.16-.352-.375-.568-.66-.711-.305-.153-.74-.245-1.365-.245h-2.37c-.681 0-1.293-.57-1.211-1.328.026-.243.065-.537.105-.834l.07-.527c.06-.482.105-.921.105-1.25 0-1.125-.213-1.617-.473-1.873-.275-.27-.774-.455-1.795-.528-.351-.024-.698.274-.698.71v1.053c0 3.55-2.488 5.063-3.68 5.577-.372.16-.754.232-1.113.26ZM3.75 20.5h1.5a.25.25 0 0 0 .25-.25V9.75a.25.25 0 0 0-.25-.25h-1.5a.25.25 0 0 0-.25.25v10.5c0 .138.112.25.25.25Z"},child:[]}]})(e)}},63056:(e,t,r)=>{r.d(t,{N:()=>u});var n=r(29068),o=r(4002),i=r(58028),l=r(70166),a=r(2569);function u(e){let t=e+"CollectionProvider",[r,u]=(0,o.A)(t),[c,s]=r(t,{collectionRef:{current:null},itemMap:new Map}),f=e=>{let{scope:t,children:r}=e,o=n.useRef(null),i=n.useRef(new Map).current;return(0,a.jsx)(c,{scope:t,itemMap:i,collectionRef:o,children:r})};f.displayName=t;let d=e+"CollectionSlot",p=(0,l.TL)(d),h=n.forwardRef((e,t)=>{let{scope:r,children:n}=e,o=s(d,r),l=(0,i.s)(t,o.collectionRef);return(0,a.jsx)(p,{ref:l,children:n})});h.displayName=d;let m=e+"CollectionItemSlot",g="data-radix-collection-item",v=(0,l.TL)(m),w=n.forwardRef((e,t)=>{let{scope:r,children:o,...l}=e,u=n.useRef(null),c=(0,i.s)(t,u),f=s(m,r);return n.useEffect(()=>(f.itemMap.set(u,{ref:u,...l}),()=>void f.itemMap.delete(u))),(0,a.jsx)(v,{[g]:"",ref:c,children:o})});return w.displayName=m,[{Provider:f,Slot:h,ItemSlot:w},function(t){let r=s(e+"CollectionConsumer",t);return n.useCallback(()=>{let e=r.collectionRef.current;if(!e)return[];let t=Array.from(e.querySelectorAll(`[${g}]`));return Array.from(r.itemMap.values()).sort((e,r)=>t.indexOf(e.ref.current)-t.indexOf(r.ref.current))},[r.collectionRef,r.itemMap])},u]}var c=new WeakMap;function s(e,t){if("at"in Array.prototype)return Array.prototype.at.call(e,t);let r=function(e,t){let r=e.length,n=f(t),o=n>=0?n:r+n;return o<0||o>=r?-1:o}(e,t);return -1===r?void 0:e[r]}function f(e){return e!=e||0===e?0:Math.trunc(e)}},63636:(e,t,r)=>{r.d(t,{UC:()=>eU,YJ:()=>eX,q7:()=>eJ,JU:()=>eq,ZL:()=>eY,bL:()=>ez,wv:()=>eQ,l9:()=>e$});var n=r(29068),o=r(79208),i=r(58028),l=r(4002),a=r(83465),u=r(85137),c=r(63056),s=r(66505),f=r(28900),d=r(88372),p=r(84920),h=r(50826),m=r(44450),g=r(89062),v=r(50703),w=r(25481),C=r(70166),x=r(89553),y=r(72777),b=r(30202),M=r(2569),L=["Enter"," "],R=["ArrowUp","PageDown","End"],H=["ArrowDown","PageUp","Home",...R],k={ltr:[...L,"ArrowRight"],rtl:[...L,"ArrowLeft"]},A={ltr:["ArrowLeft"],rtl:["ArrowRight"]},j="Menu",[S,V,P]=(0,c.N)(j),[D,E]=(0,l.A)(j,[P,m.Bk,w.RG]),T=(0,m.Bk)(),O=(0,w.RG)(),[Z,N]=D(j),[_,I]=D(j),F=e=>{let{__scopeMenu:t,open:r=!1,children:o,dir:i,onOpenChange:l,modal:a=!0}=e,u=T(t),[c,f]=n.useState(null),d=n.useRef(!1),p=(0,x.c)(l),h=(0,s.jH)(i);return n.useEffect(()=>{let e=()=>{d.current=!0,document.addEventListener("pointerdown",t,{capture:!0,once:!0}),document.addEventListener("pointermove",t,{capture:!0,once:!0})},t=()=>d.current=!1;return document.addEventListener("keydown",e,{capture:!0}),()=>{document.removeEventListener("keydown",e,{capture:!0}),document.removeEventListener("pointerdown",t,{capture:!0}),document.removeEventListener("pointermove",t,{capture:!0})}},[]),(0,M.jsx)(m.bL,{...u,children:(0,M.jsx)(Z,{scope:t,open:r,onOpenChange:p,content:c,onContentChange:f,children:(0,M.jsx)(_,{scope:t,onClose:n.useCallback(()=>p(!1),[p]),isUsingKeyboardRef:d,dir:h,modal:a,children:o})})})};F.displayName=j;var B=n.forwardRef((e,t)=>{let{__scopeMenu:r,...n}=e,o=T(r);return(0,M.jsx)(m.Mz,{...o,...n,ref:t})});B.displayName="MenuAnchor";var G="MenuPortal",[K,W]=D(G,{forceMount:void 0}),z=e=>{let{__scopeMenu:t,forceMount:r,children:n,container:o}=e,i=N(G,t);return(0,M.jsx)(K,{scope:t,forceMount:r,children:(0,M.jsx)(v.C,{present:r||i.open,children:(0,M.jsx)(g.Z,{asChild:!0,container:o,children:n})})})};z.displayName=G;var $="MenuContent",[Y,U]=D($),X=n.forwardRef((e,t)=>{let r=W($,e.__scopeMenu),{forceMount:n=r.forceMount,...o}=e,i=N($,e.__scopeMenu),l=I($,e.__scopeMenu);return(0,M.jsx)(S.Provider,{scope:e.__scopeMenu,children:(0,M.jsx)(v.C,{present:n||i.open,children:(0,M.jsx)(S.Slot,{scope:e.__scopeMenu,children:l.modal?(0,M.jsx)(q,{...o,ref:t}):(0,M.jsx)(J,{...o,ref:t})})})})}),q=n.forwardRef((e,t)=>{let r=N($,e.__scopeMenu),l=n.useRef(null),a=(0,i.s)(t,l);return n.useEffect(()=>{let e=l.current;if(e)return(0,y.Eq)(e)},[]),(0,M.jsx)(ee,{...e,ref:a,trapFocus:r.open,disableOutsidePointerEvents:r.open,disableOutsideScroll:!0,onFocusOutside:(0,o.m)(e.onFocusOutside,e=>e.preventDefault(),{checkForDefaultPrevented:!1}),onDismiss:()=>r.onOpenChange(!1)})}),J=n.forwardRef((e,t)=>{let r=N($,e.__scopeMenu);return(0,M.jsx)(ee,{...e,ref:t,trapFocus:!1,disableOutsidePointerEvents:!1,disableOutsideScroll:!1,onDismiss:()=>r.onOpenChange(!1)})}),Q=(0,C.TL)("MenuContent.ScrollLock"),ee=n.forwardRef((e,t)=>{let{__scopeMenu:r,loop:l=!1,trapFocus:a,onOpenAutoFocus:u,onCloseAutoFocus:c,disableOutsidePointerEvents:s,onEntryFocus:h,onEscapeKeyDown:g,onPointerDownOutside:v,onFocusOutside:C,onInteractOutside:x,onDismiss:y,disableOutsideScroll:L,...k}=e,A=N($,r),j=I($,r),S=T(r),P=O(r),D=V(r),[E,Z]=n.useState(null),_=n.useRef(null),F=(0,i.s)(t,_,A.onContentChange),B=n.useRef(0),G=n.useRef(""),K=n.useRef(0),W=n.useRef(null),z=n.useRef("right"),U=n.useRef(0),X=L?b.A:n.Fragment,q=e=>{let t=G.current+e,r=D().filter(e=>!e.disabled),n=document.activeElement,o=r.find(e=>e.ref.current===n)?.textValue,i=function(e,t,r){var n;let o=t.length>1&&Array.from(t).every(e=>e===t[0])?t[0]:t,i=(n=Math.max(r?e.indexOf(r):-1,0),e.map((t,r)=>e[(n+r)%e.length]));1===o.length&&(i=i.filter(e=>e!==r));let l=i.find(e=>e.toLowerCase().startsWith(o.toLowerCase()));return l!==r?l:void 0}(r.map(e=>e.textValue),t,o),l=r.find(e=>e.textValue===i)?.ref.current;(function e(t){G.current=t,window.clearTimeout(B.current),""!==t&&(B.current=window.setTimeout(()=>e(""),1e3))})(t),l&&setTimeout(()=>l.focus())};n.useEffect(()=>()=>window.clearTimeout(B.current),[]),(0,d.Oh)();let J=n.useCallback(e=>z.current===W.current?.side&&function(e,t){return!!t&&function(e,t){let{x:r,y:n}=e,o=!1;for(let e=0,i=t.length-1;e<t.length;i=e++){let l=t[e],a=t[i],u=l.x,c=l.y,s=a.x,f=a.y;c>n!=f>n&&r<(s-u)*(n-c)/(f-c)+u&&(o=!o)}return o}({x:e.clientX,y:e.clientY},t)}(e,W.current?.area),[]);return(0,M.jsx)(Y,{scope:r,searchRef:G,onItemEnter:n.useCallback(e=>{J(e)&&e.preventDefault()},[J]),onItemLeave:n.useCallback(e=>{J(e)||(_.current?.focus(),Z(null))},[J]),onTriggerLeave:n.useCallback(e=>{J(e)&&e.preventDefault()},[J]),pointerGraceTimerRef:K,onPointerGraceIntentChange:n.useCallback(e=>{W.current=e},[]),children:(0,M.jsx)(X,{...L?{as:Q,allowPinchZoom:!0}:void 0,children:(0,M.jsx)(p.n,{asChild:!0,trapped:a,onMountAutoFocus:(0,o.m)(u,e=>{e.preventDefault(),_.current?.focus({preventScroll:!0})}),onUnmountAutoFocus:c,children:(0,M.jsx)(f.qW,{asChild:!0,disableOutsidePointerEvents:s,onEscapeKeyDown:g,onPointerDownOutside:v,onFocusOutside:C,onInteractOutside:x,onDismiss:y,children:(0,M.jsx)(w.bL,{asChild:!0,...P,dir:j.dir,orientation:"vertical",loop:l,currentTabStopId:E,onCurrentTabStopIdChange:Z,onEntryFocus:(0,o.m)(h,e=>{j.isUsingKeyboardRef.current||e.preventDefault()}),preventScrollOnEntryFocus:!0,children:(0,M.jsx)(m.UC,{role:"menu","aria-orientation":"vertical","data-state":eH(A.open),"data-radix-menu-content":"",dir:j.dir,...S,...k,ref:F,style:{outline:"none",...k.style},onKeyDown:(0,o.m)(k.onKeyDown,e=>{let t=e.target.closest("[data-radix-menu-content]")===e.currentTarget,r=e.ctrlKey||e.altKey||e.metaKey,n=1===e.key.length;t&&("Tab"===e.key&&e.preventDefault(),!r&&n&&q(e.key));let o=_.current;if(e.target!==o||!H.includes(e.key))return;e.preventDefault();let i=D().filter(e=>!e.disabled).map(e=>e.ref.current);R.includes(e.key)&&i.reverse(),function(e){let t=document.activeElement;for(let r of e)if(r===t||(r.focus(),document.activeElement!==t))return}(i)}),onBlur:(0,o.m)(e.onBlur,e=>{e.currentTarget.contains(e.target)||(window.clearTimeout(B.current),G.current="")}),onPointerMove:(0,o.m)(e.onPointerMove,ej(e=>{let t=e.target,r=U.current!==e.clientX;e.currentTarget.contains(t)&&r&&(z.current=e.clientX>U.current?"right":"left",U.current=e.clientX)}))})})})})})})});X.displayName=$;var et=n.forwardRef((e,t)=>{let{__scopeMenu:r,...n}=e;return(0,M.jsx)(u.sG.div,{role:"group",...n,ref:t})});et.displayName="MenuGroup";var er=n.forwardRef((e,t)=>{let{__scopeMenu:r,...n}=e;return(0,M.jsx)(u.sG.div,{...n,ref:t})});er.displayName="MenuLabel";var en="MenuItem",eo="menu.itemSelect",ei=n.forwardRef((e,t)=>{let{disabled:r=!1,onSelect:l,...a}=e,c=n.useRef(null),s=I(en,e.__scopeMenu),f=U(en,e.__scopeMenu),d=(0,i.s)(t,c),p=n.useRef(!1);return(0,M.jsx)(el,{...a,ref:d,disabled:r,onClick:(0,o.m)(e.onClick,()=>{let e=c.current;if(!r&&e){let t=new CustomEvent(eo,{bubbles:!0,cancelable:!0});e.addEventListener(eo,e=>l?.(e),{once:!0}),(0,u.hO)(e,t),t.defaultPrevented?p.current=!1:s.onClose()}}),onPointerDown:t=>{e.onPointerDown?.(t),p.current=!0},onPointerUp:(0,o.m)(e.onPointerUp,e=>{p.current||e.currentTarget?.click()}),onKeyDown:(0,o.m)(e.onKeyDown,e=>{let t=""!==f.searchRef.current;!r&&(!t||" "!==e.key)&&L.includes(e.key)&&(e.currentTarget.click(),e.preventDefault())})})});ei.displayName=en;var el=n.forwardRef((e,t)=>{let{__scopeMenu:r,disabled:l=!1,textValue:a,...c}=e,s=U(en,r),f=O(r),d=n.useRef(null),p=(0,i.s)(t,d),[h,m]=n.useState(!1),[g,v]=n.useState("");return n.useEffect(()=>{let e=d.current;e&&v((e.textContent??"").trim())},[c.children]),(0,M.jsx)(S.ItemSlot,{scope:r,disabled:l,textValue:a??g,children:(0,M.jsx)(w.q7,{asChild:!0,...f,focusable:!l,children:(0,M.jsx)(u.sG.div,{role:"menuitem","data-highlighted":h?"":void 0,"aria-disabled":l||void 0,"data-disabled":l?"":void 0,...c,ref:p,onPointerMove:(0,o.m)(e.onPointerMove,ej(e=>{l?s.onItemLeave(e):(s.onItemEnter(e),e.defaultPrevented||e.currentTarget.focus({preventScroll:!0}))})),onPointerLeave:(0,o.m)(e.onPointerLeave,ej(e=>s.onItemLeave(e))),onFocus:(0,o.m)(e.onFocus,()=>m(!0)),onBlur:(0,o.m)(e.onBlur,()=>m(!1))})})})}),ea=n.forwardRef((e,t)=>{let{checked:r=!1,onCheckedChange:n,...i}=e;return(0,M.jsx)(em,{scope:e.__scopeMenu,checked:r,children:(0,M.jsx)(ei,{role:"menuitemcheckbox","aria-checked":ek(r)?"mixed":r,...i,ref:t,"data-state":eA(r),onSelect:(0,o.m)(i.onSelect,()=>n?.(!!ek(r)||!r),{checkForDefaultPrevented:!1})})})});ea.displayName="MenuCheckboxItem";var eu="MenuRadioGroup",[ec,es]=D(eu,{value:void 0,onValueChange:()=>{}}),ef=n.forwardRef((e,t)=>{let{value:r,onValueChange:n,...o}=e,i=(0,x.c)(n);return(0,M.jsx)(ec,{scope:e.__scopeMenu,value:r,onValueChange:i,children:(0,M.jsx)(et,{...o,ref:t})})});ef.displayName=eu;var ed="MenuRadioItem",ep=n.forwardRef((e,t)=>{let{value:r,...n}=e,i=es(ed,e.__scopeMenu),l=r===i.value;return(0,M.jsx)(em,{scope:e.__scopeMenu,checked:l,children:(0,M.jsx)(ei,{role:"menuitemradio","aria-checked":l,...n,ref:t,"data-state":eA(l),onSelect:(0,o.m)(n.onSelect,()=>i.onValueChange?.(r),{checkForDefaultPrevented:!1})})})});ep.displayName=ed;var eh="MenuItemIndicator",[em,eg]=D(eh,{checked:!1}),ev=n.forwardRef((e,t)=>{let{__scopeMenu:r,forceMount:n,...o}=e,i=eg(eh,r);return(0,M.jsx)(v.C,{present:n||ek(i.checked)||!0===i.checked,children:(0,M.jsx)(u.sG.span,{...o,ref:t,"data-state":eA(i.checked)})})});ev.displayName=eh;var ew=n.forwardRef((e,t)=>{let{__scopeMenu:r,...n}=e;return(0,M.jsx)(u.sG.div,{role:"separator","aria-orientation":"horizontal",...n,ref:t})});ew.displayName="MenuSeparator";var eC=n.forwardRef((e,t)=>{let{__scopeMenu:r,...n}=e,o=T(r);return(0,M.jsx)(m.i3,{...o,...n,ref:t})});eC.displayName="MenuArrow";var[ex,ey]=D("MenuSub"),eb="MenuSubTrigger",eM=n.forwardRef((e,t)=>{let r=N(eb,e.__scopeMenu),l=I(eb,e.__scopeMenu),a=ey(eb,e.__scopeMenu),u=U(eb,e.__scopeMenu),c=n.useRef(null),{pointerGraceTimerRef:s,onPointerGraceIntentChange:f}=u,d={__scopeMenu:e.__scopeMenu},p=n.useCallback(()=>{c.current&&window.clearTimeout(c.current),c.current=null},[]);return n.useEffect(()=>p,[p]),n.useEffect(()=>{let e=s.current;return()=>{window.clearTimeout(e),f(null)}},[s,f]),(0,M.jsx)(B,{asChild:!0,...d,children:(0,M.jsx)(el,{id:a.triggerId,"aria-haspopup":"menu","aria-expanded":r.open,"aria-controls":a.contentId,"data-state":eH(r.open),...e,ref:(0,i.t)(t,a.onTriggerChange),onClick:t=>{e.onClick?.(t),e.disabled||t.defaultPrevented||(t.currentTarget.focus(),r.open||r.onOpenChange(!0))},onPointerMove:(0,o.m)(e.onPointerMove,ej(t=>{u.onItemEnter(t),t.defaultPrevented||e.disabled||r.open||c.current||(u.onPointerGraceIntentChange(null),c.current=window.setTimeout(()=>{r.onOpenChange(!0),p()},100))})),onPointerLeave:(0,o.m)(e.onPointerLeave,ej(e=>{p();let t=r.content?.getBoundingClientRect();if(t){let n=r.content?.dataset.side,o="right"===n,i=t[o?"left":"right"],l=t[o?"right":"left"];u.onPointerGraceIntentChange({area:[{x:e.clientX+(o?-5:5),y:e.clientY},{x:i,y:t.top},{x:l,y:t.top},{x:l,y:t.bottom},{x:i,y:t.bottom}],side:n}),window.clearTimeout(s.current),s.current=window.setTimeout(()=>u.onPointerGraceIntentChange(null),300)}else{if(u.onTriggerLeave(e),e.defaultPrevented)return;u.onPointerGraceIntentChange(null)}})),onKeyDown:(0,o.m)(e.onKeyDown,t=>{let n=""!==u.searchRef.current;!e.disabled&&(!n||" "!==t.key)&&k[l.dir].includes(t.key)&&(r.onOpenChange(!0),r.content?.focus(),t.preventDefault())})})})});eM.displayName=eb;var eL="MenuSubContent",eR=n.forwardRef((e,t)=>{let r=W($,e.__scopeMenu),{forceMount:l=r.forceMount,...a}=e,u=N($,e.__scopeMenu),c=I($,e.__scopeMenu),s=ey(eL,e.__scopeMenu),f=n.useRef(null),d=(0,i.s)(t,f);return(0,M.jsx)(S.Provider,{scope:e.__scopeMenu,children:(0,M.jsx)(v.C,{present:l||u.open,children:(0,M.jsx)(S.Slot,{scope:e.__scopeMenu,children:(0,M.jsx)(ee,{id:s.contentId,"aria-labelledby":s.triggerId,...a,ref:d,align:"start",side:"rtl"===c.dir?"left":"right",disableOutsidePointerEvents:!1,disableOutsideScroll:!1,trapFocus:!1,onOpenAutoFocus:e=>{c.isUsingKeyboardRef.current&&f.current?.focus(),e.preventDefault()},onCloseAutoFocus:e=>e.preventDefault(),onFocusOutside:(0,o.m)(e.onFocusOutside,e=>{e.target!==s.trigger&&u.onOpenChange(!1)}),onEscapeKeyDown:(0,o.m)(e.onEscapeKeyDown,e=>{c.onClose(),e.preventDefault()}),onKeyDown:(0,o.m)(e.onKeyDown,e=>{let t=e.currentTarget.contains(e.target),r=A[c.dir].includes(e.key);t&&r&&(u.onOpenChange(!1),s.trigger?.focus(),e.preventDefault())})})})})})});function eH(e){return e?"open":"closed"}function ek(e){return"indeterminate"===e}function eA(e){return ek(e)?"indeterminate":e?"checked":"unchecked"}function ej(e){return t=>"mouse"===t.pointerType?e(t):void 0}eR.displayName=eL;var eS="DropdownMenu",[eV,eP]=(0,l.A)(eS,[E]),eD=E(),[eE,eT]=eV(eS),eO=e=>{let{__scopeDropdownMenu:t,children:r,dir:o,open:i,defaultOpen:l,onOpenChange:u,modal:c=!0}=e,s=eD(t),f=n.useRef(null),[d,p]=(0,a.i)({prop:i,defaultProp:l??!1,onChange:u,caller:eS});return(0,M.jsx)(eE,{scope:t,triggerId:(0,h.B)(),triggerRef:f,contentId:(0,h.B)(),open:d,onOpenChange:p,onOpenToggle:n.useCallback(()=>p(e=>!e),[p]),modal:c,children:(0,M.jsx)(F,{...s,open:d,onOpenChange:p,dir:o,modal:c,children:r})})};eO.displayName=eS;var eZ="DropdownMenuTrigger",eN=n.forwardRef((e,t)=>{let{__scopeDropdownMenu:r,disabled:n=!1,...l}=e,a=eT(eZ,r),c=eD(r);return(0,M.jsx)(B,{asChild:!0,...c,children:(0,M.jsx)(u.sG.button,{type:"button",id:a.triggerId,"aria-haspopup":"menu","aria-expanded":a.open,"aria-controls":a.open?a.contentId:void 0,"data-state":a.open?"open":"closed","data-disabled":n?"":void 0,disabled:n,...l,ref:(0,i.t)(t,a.triggerRef),onPointerDown:(0,o.m)(e.onPointerDown,e=>{n||0!==e.button||!1!==e.ctrlKey||(a.onOpenToggle(),a.open||e.preventDefault())}),onKeyDown:(0,o.m)(e.onKeyDown,e=>{!n&&(["Enter"," "].includes(e.key)&&a.onOpenToggle(),"ArrowDown"===e.key&&a.onOpenChange(!0),["Enter"," ","ArrowDown"].includes(e.key)&&e.preventDefault())})})})});eN.displayName=eZ;var e_=e=>{let{__scopeDropdownMenu:t,...r}=e,n=eD(t);return(0,M.jsx)(z,{...n,...r})};e_.displayName="DropdownMenuPortal";var eI="DropdownMenuContent",eF=n.forwardRef((e,t)=>{let{__scopeDropdownMenu:r,...i}=e,l=eT(eI,r),a=eD(r),u=n.useRef(!1);return(0,M.jsx)(X,{id:l.contentId,"aria-labelledby":l.triggerId,...a,...i,ref:t,onCloseAutoFocus:(0,o.m)(e.onCloseAutoFocus,e=>{u.current||l.triggerRef.current?.focus(),u.current=!1,e.preventDefault()}),onInteractOutside:(0,o.m)(e.onInteractOutside,e=>{let t=e.detail.originalEvent,r=0===t.button&&!0===t.ctrlKey,n=2===t.button||r;(!l.modal||n)&&(u.current=!0)}),style:{...e.style,"--radix-dropdown-menu-content-transform-origin":"var(--radix-popper-transform-origin)","--radix-dropdown-menu-content-available-width":"var(--radix-popper-available-width)","--radix-dropdown-menu-content-available-height":"var(--radix-popper-available-height)","--radix-dropdown-menu-trigger-width":"var(--radix-popper-anchor-width)","--radix-dropdown-menu-trigger-height":"var(--radix-popper-anchor-height)"}})});eF.displayName=eI;var eB=n.forwardRef((e,t)=>{let{__scopeDropdownMenu:r,...n}=e,o=eD(r);return(0,M.jsx)(et,{...o,...n,ref:t})});eB.displayName="DropdownMenuGroup";var eG=n.forwardRef((e,t)=>{let{__scopeDropdownMenu:r,...n}=e,o=eD(r);return(0,M.jsx)(er,{...o,...n,ref:t})});eG.displayName="DropdownMenuLabel";var eK=n.forwardRef((e,t)=>{let{__scopeDropdownMenu:r,...n}=e,o=eD(r);return(0,M.jsx)(ei,{...o,...n,ref:t})});eK.displayName="DropdownMenuItem",n.forwardRef((e,t)=>{let{__scopeDropdownMenu:r,...n}=e,o=eD(r);return(0,M.jsx)(ea,{...o,...n,ref:t})}).displayName="DropdownMenuCheckboxItem",n.forwardRef((e,t)=>{let{__scopeDropdownMenu:r,...n}=e,o=eD(r);return(0,M.jsx)(ef,{...o,...n,ref:t})}).displayName="DropdownMenuRadioGroup",n.forwardRef((e,t)=>{let{__scopeDropdownMenu:r,...n}=e,o=eD(r);return(0,M.jsx)(ep,{...o,...n,ref:t})}).displayName="DropdownMenuRadioItem",n.forwardRef((e,t)=>{let{__scopeDropdownMenu:r,...n}=e,o=eD(r);return(0,M.jsx)(ev,{...o,...n,ref:t})}).displayName="DropdownMenuItemIndicator";var eW=n.forwardRef((e,t)=>{let{__scopeDropdownMenu:r,...n}=e,o=eD(r);return(0,M.jsx)(ew,{...o,...n,ref:t})});eW.displayName="DropdownMenuSeparator",n.forwardRef((e,t)=>{let{__scopeDropdownMenu:r,...n}=e,o=eD(r);return(0,M.jsx)(eC,{...o,...n,ref:t})}).displayName="DropdownMenuArrow",n.forwardRef((e,t)=>{let{__scopeDropdownMenu:r,...n}=e,o=eD(r);return(0,M.jsx)(eM,{...o,...n,ref:t})}).displayName="DropdownMenuSubTrigger",n.forwardRef((e,t)=>{let{__scopeDropdownMenu:r,...n}=e,o=eD(r);return(0,M.jsx)(eR,{...o,...n,ref:t,style:{...e.style,"--radix-dropdown-menu-content-transform-origin":"var(--radix-popper-transform-origin)","--radix-dropdown-menu-content-available-width":"var(--radix-popper-available-width)","--radix-dropdown-menu-content-available-height":"var(--radix-popper-available-height)","--radix-dropdown-menu-trigger-width":"var(--radix-popper-anchor-width)","--radix-dropdown-menu-trigger-height":"var(--radix-popper-anchor-height)"}})}).displayName="DropdownMenuSubContent";var ez=eO,e$=eN,eY=e_,eU=eF,eX=eB,eq=eG,eJ=eK,eQ=eW},64456:(e,t,r)=>{r.d(t,{A:()=>n});function n(e){return e}},66505:(e,t,r)=>{r.d(t,{jH:()=>i});var n=r(29068);r(2569);var o=n.createContext(void 0);function i(e){let t=n.useContext(o);return e||t||"ltr"}},69517:(e,t,r)=>{e.exports=r(26798)},83233:(e,t,r)=>{r.d(t,{Qg:()=>l,bL:()=>u});var n=r(29068),o=r(85137),i=r(2569),l=Object.freeze({position:"absolute",border:0,width:1,height:1,padding:0,margin:-1,overflow:"hidden",clip:"rect(0, 0, 0, 0)",whiteSpace:"nowrap",wordWrap:"normal"}),a=n.forwardRef((e,t)=>(0,i.jsx)(o.sG.span,{...e,ref:t,style:{...l,...e.style}}));a.displayName="VisuallyHidden";var u=a}};