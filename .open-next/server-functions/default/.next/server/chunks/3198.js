"use strict";exports.id=3198,exports.ids=[3198],exports.modules={10817:s=>{s.exports=JSON.parse('{"showcase":{"name":"showcase","title":"使用 ShipAny 构建的 AI SaaS 创业项目","description":"易于使用，快速发布。","items":[{"title":"ThinkAny","description":"AI 搜索引擎","url":"https://thinkany.ai","target":"_blank","image":{"src":"/imgs/showcases/7.png"}},{"title":"HeyBeauty","description":"AI 虚拟试妆","url":"https://heybeauty.ai","target":"_blank","image":{"src":"/imgs/showcases/5.png"}},{"title":"AI Wallpaper","description":"AI 壁纸生成器","url":"https://aiwallpaper.shop","target":"_blank","image":{"src":"/imgs/showcases/1.png"}},{"title":"AI Cover","description":"AI 封面生成器","url":"https://aicover.design","target":"_blank","image":{"src":"/imgs/showcases/2.png"}},{"title":"GPTs Works","description":"GPTs 目录","url":"https://gpts.works","target":"_blank","image":{"src":"/imgs/showcases/3.png"}},{"title":"Melodisco","description":"AI 音乐播放器","url":"https://melodis.co","target":"_blank","image":{"src":"/imgs/showcases/4.png"}},{"title":"Pagen","description":"AI 落地页生成器","url":"https://pagen.so","target":"_blank","image":{"src":"/imgs/showcases/6.png"}},{"title":"SoraFM","description":"AI 视频生成器","url":"https://sorafm.trys.ai","target":"_blank","image":{"src":"/imgs/showcases/8.png"}},{"title":"PodLM","description":"AI 播客生成器","url":"https://podlm.ai","target":"_blank","image":{"src":"/imgs/showcases/9.png"}}]}}')}};