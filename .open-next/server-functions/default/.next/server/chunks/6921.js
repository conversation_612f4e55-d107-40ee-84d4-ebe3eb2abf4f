exports.id=6921,exports.ids=[6921],exports.modules={13373:()=>{},19867:(t,e,i)=>{"use strict";e.F=void 0;let r=i(37198);Object.defineProperty(e,"F",{enumerable:!0,get:function(){return r.Snow<PERSON>lakeIdv1}})},30122:(t,e,i)=>{"use strict";i.d(e,{J:()=>h});var r=i(87830),n=i(40109),s=i(4961),o=i(80310),a=i(37947),u=i(35050);async function d(t){try{let e=await (0,u.pX)(t.user_uuid);if(e&&e.uuid&&e.invited_by&&e.invited_by!==e.uuid){if(await (0,o.N$)(t.order_no))return;await (0,o.Rt)({user_uuid:e.uuid,invited_by:e.invited_by,created_at:(0,s.iq)(),status:a.OJ.Completed,paid_order_no:t.order_no,paid_amount:t.amount,reward_percent:a.oR.Paied,reward_amount:a.h1.Paied})}}catch(t){throw console.log("update affiliate for order failed: ",t),t}}async function h(t){try{if(!t||!t.metadata||!t.metadata.order_no||"paid"!==t.payment_status)throw Error("invalid session");let e=t.metadata.order_no,i=t.customer_details?.email||t.customer_email||"",o=JSON.stringify(t),a=await (0,n.K7)(e);if(!a||"created"!==a.status)throw Error("invalid order");let u=(0,s.iq)();await (0,n.Kv)(e,"paid",u,i,o),a.user_uuid&&(a.credits>0&&await (0,r.OI)(a),await d(a)),console.log("handle order session successed: ",e,u,i,o)}catch(t){throw console.log("handle order session failed: ",t),t}}},37198:(t,e)=>{"use strict";Object.defineProperty(e,"__esModule",{value:!0}),e.SnowflakeIdv1=void 0;class i{constructor(t){if(void 0===t.workerId)throw Error("lost WorkerId");(!t.baseTime||t.baseTime<0)&&(t.baseTime=15778368e5),(!t.workerIdBitLength||t.workerIdBitLength<0)&&(t.workerIdBitLength=6),(!t.seqBitLength||t.seqBitLength<0)&&(t.seqBitLength=6),(void 0==t.maxSeqNumber||t.maxSeqNumber<=0)&&(t.maxSeqNumber=63),(void 0==t.minSeqNumber||t.minSeqNumber<0)&&(t.minSeqNumber=5),(void 0==t.topOverCostCount||t.topOverCostCount<0)&&(t.topOverCostCount=2e3),2!==t.method?t.method=1:t.method=2,this.Method=BigInt(t.method),this.BaseTime=BigInt(t.baseTime),this.WorkerId=BigInt(t.workerId),this.WorkerIdBitLength=BigInt(t.workerIdBitLength),this.SeqBitLength=BigInt(t.seqBitLength),this.MaxSeqNumber=BigInt(t.maxSeqNumber),this.MinSeqNumber=BigInt(t.minSeqNumber),this.TopOverCostCount=BigInt(t.topOverCostCount);let e=this.WorkerIdBitLength+this.SeqBitLength,i=this.MinSeqNumber;this._TimestampShift=e,this._CurrentSeqNumber=i,this._LastTimeTick=BigInt(0),this._TurnBackTimeTick=BigInt(0),this._TurnBackIndex=0,this._IsOverCost=!1,this._OverCostCountInOneTerm=0}BeginOverCostAction(t){}EndOverCostAction(t){}BeginTurnBackAction(t){}EndTurnBackAction(t){}NextOverCostId(){let t=this.GetCurrentTimeTick();return t>this._LastTimeTick?(this.EndOverCostAction(t),this._LastTimeTick=t,this._CurrentSeqNumber=this.MinSeqNumber,this._IsOverCost=!1,this._OverCostCountInOneTerm=0):this._OverCostCountInOneTerm>=this.TopOverCostCount?(this.EndOverCostAction(t),this._LastTimeTick=this.GetNextTimeTick(),this._CurrentSeqNumber=this.MinSeqNumber,this._IsOverCost=!1,this._OverCostCountInOneTerm=0):this._CurrentSeqNumber>this.MaxSeqNumber&&(this._LastTimeTick++,this._CurrentSeqNumber=this.MinSeqNumber,this._IsOverCost=!0,this._OverCostCountInOneTerm++),this.CalcId(this._LastTimeTick)}NextNormalId(){let t=this.GetCurrentTimeTick();return t<this._LastTimeTick?(this._TurnBackTimeTick<1&&(this._TurnBackTimeTick=this._LastTimeTick-BigInt(1),this._TurnBackIndex++,this._TurnBackIndex>4&&(this._TurnBackIndex=1),this.BeginTurnBackAction(this._TurnBackTimeTick)),this.CalcTurnBackId(this._TurnBackTimeTick)):((this._TurnBackTimeTick>0&&(this.EndTurnBackAction(this._TurnBackTimeTick),this._TurnBackTimeTick=BigInt(0)),t>this._LastTimeTick)?(this._LastTimeTick=t,this._CurrentSeqNumber=this.MinSeqNumber):this._CurrentSeqNumber>this.MaxSeqNumber&&(this.BeginOverCostAction(t),this._LastTimeTick++,this._CurrentSeqNumber=this.MinSeqNumber,this._IsOverCost=!0,this._OverCostCountInOneTerm=1),this.CalcId(this._LastTimeTick))}CalcId(t){let e=BigInt(t<<this._TimestampShift)+BigInt(this.WorkerId<<this.SeqBitLength)+BigInt(this._CurrentSeqNumber);return this._CurrentSeqNumber++,e}CalcTurnBackId(t){let e=BigInt(t<<this._TimestampShift)+BigInt(this.WorkerId<<this.SeqBitLength)+BigInt(this._TurnBackIndex);return this._TurnBackTimeTick--,e}GetCurrentTimeTick(){return BigInt(new Date().valueOf())-this.BaseTime}GetNextTimeTick(){let t=this.GetCurrentTimeTick();for(;t<=this._LastTimeTick;)t=this.GetCurrentTimeTick();return t}NextNumber(){if(this._IsOverCost){let t=this.NextOverCostId();if(t>=9007199254740992n)throw Error(`${t.toString()} over max of Number 9007199254740992`);return parseInt(t.toString())}{let t=this.NextNormalId();if(t>=9007199254740992n)throw Error(`${t.toString()} over max of Number 9007199254740992`);return parseInt(t.toString())}}NextId(){if(this._IsOverCost){let t=this.NextOverCostId();return t>=9007199254740992n?t:parseInt(t.toString())}{let t=this.NextNormalId();return t>=9007199254740992n?t:parseInt(t.toString())}}NextBigId(){return this._IsOverCost?this.NextOverCostId():this.NextNormalId()}}e.SnowflakeIdv1=i},37947:(t,e,i)=>{"use strict";i.d(e,{OJ:()=>r,h1:()=>s,oR:()=>n});let r={Pending:"pending",Completed:"completed"},n={Invited:0,Paied:20},s={Invited:0,Paied:5e3}},58246:(t,e,i)=>{"use strict";i.d(e,{A:()=>u});var r=i(55511);let n={randomUUID:r.randomUUID},s=new Uint8Array(256),o=s.length,a=[];for(let t=0;t<256;++t)a.push((t+256).toString(16).slice(1));let u=function(t,e,i){if(n.randomUUID&&!e&&!t)return n.randomUUID();let u=(t=t||{}).random??t.rng?.()??(o>s.length-16&&((0,r.randomFillSync)(s),o=0),s.slice(o,o+=16));if(u.length<16)throw Error("Random bytes length must be >= 16");if(u[6]=15&u[6]|64,u[8]=63&u[8]|128,e){if((i=i||0)<0||i+16>e.length)throw RangeError(`UUID byte range ${i}:${i+15} is out of buffer bounds`);for(let t=0;t<16;++t)e[i+t]=u[t];return e}return function(t,e=0){return(a[t[e+0]]+a[t[e+1]]+a[t[e+2]]+a[t[e+3]]+"-"+a[t[e+4]]+a[t[e+5]]+"-"+a[t[e+6]]+a[t[e+7]]+"-"+a[t[e+8]]+a[t[e+9]]+"-"+a[t[e+10]]+a[t[e+11]]+a[t[e+12]]+a[t[e+13]]+a[t[e+14]]+a[t[e+15]]).toLowerCase()}(u)}},80310:(t,e,i)=>{"use strict";i.d(e,{A:()=>a,Ex:()=>o,N$:()=>u,Rt:()=>s});var r=i(4995),n=i(35050);async function s(t){let e=(0,r.A)(),{data:i,error:n}=await e.from("affiliates").insert({user_uuid:t.user_uuid,invited_by:t.invited_by,created_at:t.created_at,status:t.status,paid_order_no:t.paid_order_no,paid_amount:t.paid_amount,reward_percent:t.reward_percent,reward_amount:t.reward_amount});if(n)throw n;return i}async function o(t,e=1,i=50){let s=(0,r.A)(),{data:a,error:u}=await s.from("affiliates").select("*").eq("invited_by",t).order("created_at",{ascending:!1}).range((e-1)*i,e*i);if(u)return console.error("Error fetching user invites:",u),[];if(!a||0===a.length)return;let d=Array.from(new Set(a.map(t=>t.user_uuid))),h=await (0,n.QZ)(d);return a.map(t=>{let e=h.find(e=>e.uuid===t.user_uuid);return{...t,user:e}})}async function a(t){let e=(0,r.A)(),{data:i,error:n}=await e.from("affiliates").select("*").eq("invited_by",t),s={total_invited:0,total_paid:0,total_reward:0};if(n)return s;let o=new Set,a=new Set;return i.forEach(t=>{o.add(t.user_uuid),t.paid_amount>0&&(a.add(t.user_uuid),s.total_reward+=t.reward_amount)}),s.total_invited=o.size,s.total_paid=a.size,s}async function u(t){let e=(0,r.A)(),{data:i,error:n}=await e.from("affiliates").select("*").eq("paid_order_no",t).single();if(!n)return i}},94813:()=>{}};