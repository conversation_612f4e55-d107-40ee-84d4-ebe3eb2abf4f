exports.id=6158,exports.ids=[6158],exports.modules={996:e=>{class t extends Error{constructor(e,t,r){super(e),this.name="ApiError",this.request=t,this.response=r}}e.exports=t},2808:e=>{e.exports={current:async function(){return(await this.request("/account",{method:"GET"})).json()}}},3183:e=>{e.exports={get:async function(e){return(await this.request(`/collections/${e}`,{method:"GET"})).json()},list:async function(){return(await this.request("/collections",{method:"GET"})).json()}}},4001:e=>{e.exports={create:async function(e,t={}){let r,i;let s=new FormData;if(e instanceof Blob)r=e.name||`blob_${Date.now()}`,i=e;else if(Buffer.isBuffer(e))r=`buffer_${Date.now()}`,i=new Blob([new Uint8Array(e)],{type:"application/octet-stream",name:r});else throw Error("Invalid file argument, must be a Blob, File or Buffer");return s.append("content",i,r),s.append("metadata",new Blob([JSON.stringify(t)],{type:"application/json"})),(await this.request("/files",{method:"POST",data:s,headers:{"Content-Type":"multipart/form-data"}})).json()},list:async function(){return(await this.request("/files",{method:"GET"})).json()},get:async function(e){return(await this.request(`/files/${e}`,{method:"GET"})).json()},delete:async function(e){return 204===(await this.request(`/files/${e}`,{method:"DELETE"})).status}}},6158:(e,t,r)=>{let i=r(996),s=r(65743),{createReadableStream:n,createFileOutput:a}=r(30116),{transform:o,withAutomaticRetries:c,validateWebhook:l,parseProgressFromLogs:u,streamAsyncIterator:d}=r(51936),h=r(2808),f=r(3183),p=r(91454),m=r(4001),w=r(80660),y=r(68826),b=r(33652),g=r(81049),v=r(69542),E=r(33696);class j{constructor(e={}){this.auth=e.auth||("undefined"!=typeof process?process.env.REPLICATE_API_TOKEN:null),this.userAgent=e.userAgent||`replicate-javascript/${E.version}`,this.baseUrl=e.baseUrl||"https://api.replicate.com/v1",this.fetch=e.fetch||globalThis.fetch,this.fileEncodingStrategy=e.fileEncodingStrategy||"default",this.useFileOutput=!1!==e.useFileOutput,this.accounts={current:h.current.bind(this)},this.collections={list:f.list.bind(this),get:f.get.bind(this)},this.deployments={get:p.get.bind(this),create:p.create.bind(this),update:p.update.bind(this),delete:p.delete.bind(this),list:p.list.bind(this),predictions:{create:p.predictions.create.bind(this)}},this.files={create:m.create.bind(this),get:m.get.bind(this),list:m.list.bind(this),delete:m.delete.bind(this)},this.hardware={list:w.list.bind(this)},this.models={get:y.get.bind(this),list:y.list.bind(this),create:y.create.bind(this),versions:{list:y.versions.list.bind(this),get:y.versions.get.bind(this)},search:y.search.bind(this)},this.predictions={create:b.create.bind(this),get:b.get.bind(this),cancel:b.cancel.bind(this),list:b.list.bind(this)},this.trainings={create:g.create.bind(this),get:g.get.bind(this),cancel:g.cancel.bind(this),list:g.list.bind(this)},this.webhooks={default:{secret:{get:v.default.secret.get.bind(this)}}}}async run(e,t,r){let i;let{wait:n={mode:"block"},signal:c,...l}=t,u=s.parse(e);if(u.version)i=await this.predictions.create({...l,version:u.version,wait:"block"===n.mode&&(n.timeout??!0)});else if(u.owner&&u.name)i=await this.predictions.create({...l,model:`${u.owner}/${u.name}`,wait:"block"===n.mode&&(n.timeout??!0)});else throw Error("Invalid model version identifier");if(r&&r(i),("block"!==n.mode||"starting"===i.status)&&(i=await this.wait(i,{interval:"poll"===n.mode?n.interval:void 0},async e=>(r&&r(e),!!c&&!!c.aborted))),c&&c.aborted&&(i=await this.predictions.cancel(i.id)),r&&r(i),"failed"===i.status)throw Error(`Prediction failed: ${i.error}`);return o(i.output,e=>"string"==typeof e&&(e.startsWith("https:")||e.startsWith("data:"))&&this.useFileOutput?a({url:e,fetch:this.fetch}):e)}async request(e,t){let r,s;let{auth:n,baseUrl:a,userAgent:o}=this;r=e instanceof URL?e:new URL(e.startsWith("/")?e.slice(1):e,a.endsWith("/")?a:`${a}/`);let{method:l="GET",params:u={},data:d}=t;for(let[e,t]of Object.entries(u))r.searchParams.append(e,t);let h={"Content-Type":"application/json","User-Agent":o};if(n&&(h.Authorization=`Bearer ${n}`),t.headers)for(let[e,r]of Object.entries(t.headers))h[e]=r;d instanceof FormData?(s=d,delete h["Content-Type"]):d&&(s=JSON.stringify(d));let f={method:l,headers:h,body:s},p="GET"===l?e=>429===e.status||e.status>=500:e=>429===e.status,m=this.fetch,w=await c(async()=>m(r,f),{shouldRetry:p});if(!w.ok){let e=new Request(r,f),t=await w.text();throw new i(`Request to ${r} failed with status ${w.status} ${w.statusText}: ${t}.`,e,w)}return w}async *stream(e,t){let r;let{wait:i,signal:a,...o}=t,c=s.parse(e);if(c.version)r=await this.predictions.create({...o,version:c.version});else if(c.owner&&c.name)r=await this.predictions.create({...o,model:`${c.owner}/${c.name}`});else throw Error("Invalid model version identifier");if(r.urls&&r.urls.stream){let e=n({url:r.urls.stream,fetch:this.fetch,...a?{options:{signal:a}}:{}});yield*d(e)}else throw Error("Prediction does not support streaming")}async *paginate(e){let t=await e();yield t.results,t.next&&(yield*this.paginate(()=>this.request(t.next,{method:"GET"}).then(e=>e.json())))}async wait(e,t,r){let{id:i}=e;if(!i)throw Error("Invalid prediction");if("succeeded"===e.status||"failed"===e.status||"canceled"===e.status)return e;let s=e=>new Promise(t=>setTimeout(t,e)),n=t&&t.interval||500,a=await this.predictions.get(i);for(;"succeeded"!==a.status&&"failed"!==a.status&&"canceled"!==a.status&&(!r||await r(a)!==!0);)await s(n),a=await this.predictions.get(e.id);if("failed"===a.status)throw Error(`Prediction failed: ${a.error}`);return a}}e.exports=j,e.exports.validateWebhook=l,e.exports.parseProgressFromLogs=u},28896:e=>{var t=Object.defineProperty,r=Object.getOwnPropertyDescriptor,i=Object.getOwnPropertyNames,s=Object.prototype.hasOwnProperty,n={};((e,r)=>{for(var i in r)t(e,i,{get:r[i],enumerable:!0})})(n,{TextDecoderStream:()=>l}),e.exports=((e,n,a,o)=>{if(n&&"object"==typeof n||"function"==typeof n)for(let a of i(n))s.call(e,a)||void 0===a||t(e,a,{get:()=>n[a],enumerable:!(o=r(n,a))||o.enumerable});return e})(t({},"__esModule",{value:!0}),n);var a=Symbol("decDecoder"),o=Symbol("decTransform"),c=class{constructor(e){this.decoder_=e}transform(e,t){if(!(e instanceof ArrayBuffer||ArrayBuffer.isView(e)))throw TypeError("Input data must be a BufferSource");let r=this.decoder_.decode(e,{stream:!0});0!==r.length&&t.enqueue(r)}flush(e){let t=this.decoder_.decode();0!==t.length&&e.enqueue(t)}},l=class{constructor(e,t){let r=new TextDecoder(e||"utf-8",t||{});this[a]=r,this[o]=new TransformStream(new c(r))}get encoding(){return this[a].encoding}get fatal(){return this[a].fatal}get ignoreBOM(){return this[a].ignoreBOM}get readable(){return this[o].readable}get writable(){return this[o].writable}};Symbol("encEncoder"),Symbol("encTransform")},30116:(e,t,r)=>{let i=r(996),{streamAsyncIterator:s}=r(51936),{EventSourceParserStream:n}=r(88009),{TextDecoderStream:a}=void 0===globalThis.TextDecoderStream?r(28896):globalThis;class o{constructor(e,t,r,i){this.event=e,this.data=t,this.id=r,this.retry=i}toString(){return"output"===this.event?this.data:""}}function c({url:e,fetch:t}){let r="application/octet-stream";class n extends ReadableStream{async blob(){let e=[];for await(let t of this)e.push(t);return new Blob(e,{type:r})}url(){return new URL(e)}toString(){return e}}return new n({async start(n){let a=await t(e);if(!a.ok){let t=await a.text(),r=new Request(e,init);n.error(new i(`Request to ${e} failed with status ${a.status}: ${t}`,r,a))}a.headers.get("Content-Type")&&(r=a.headers.get("Content-Type"));try{for await(let e of s(a.body))n.enqueue(e);n.close()}catch(e){n.error(e)}}})}e.exports={createFileOutput:c,createReadableStream:function({url:e,fetch:t,options:r={}}){let{useFileOutput:l=!0,headers:u={},...d}=r;return new ReadableStream({async start(r){let h={...d,headers:{...u,Accept:"text/event-stream"}},f=await t(e,h);if(!f.ok){let t=await f.text(),s=new Request(e,h);r.error(new i(`Request to ${e} failed with status ${f.status}: ${t}`,s,f))}for await(let e of s(f.body.pipeThrough(new a).pipeThrough(new n))){if("error"===e.event){r.error(Error(e.data));break}let i=e.data;if(l&&"string"==typeof i&&(i.startsWith("https:")||i.startsWith("data:"))&&(i=c({data:i,fetch:t})),r.enqueue(new o(e.event,i,e.id)),"done"===e.event)break}r.close()}})},ServerSentEvent:o}},33652:(e,t,r)=>{let{transformFileInputs:i}=r(51936);e.exports={create:async function(e){let t;let{model:r,version:s,input:n,wait:a,...o}=e;if(o.webhook)try{new URL(o.webhook)}catch(e){throw Error("Invalid webhook URL")}let c={};if(a){if("number"==typeof a){let e=Math.max(1,Math.ceil(Number(a))||1);c.Prefer=`wait=${e}`}else c.Prefer="wait"}if(s)t=await this.request("/predictions",{method:"POST",headers:c,data:{...o,input:await i(this,n,this.fileEncodingStrategy),version:s}});else if(r)t=await this.request(`/models/${r}/predictions`,{method:"POST",headers:c,data:{...o,input:await i(this,n,this.fileEncodingStrategy)}});else throw Error("Either model or version must be specified");return t.json()},get:async function(e){return(await this.request(`/predictions/${e}`,{method:"GET"})).json()},cancel:async function(e){return(await this.request(`/predictions/${e}/cancel`,{method:"POST"})).json()},list:async function(){return(await this.request("/predictions",{method:"GET"})).json()}}},33696:e=>{"use strict";e.exports=JSON.parse('{"name":"replicate","version":"1.0.1","description":"JavaScript client for Replicate","repository":"github:replicate/replicate-javascript","homepage":"https://github.com/replicate/replicate-javascript#readme","bugs":"https://github.com/replicate/replicate-javascript/issues","license":"Apache-2.0","main":"index.js","type":"commonjs","types":"index.d.ts","files":["CONTRIBUTING.md","LICENSE","README.md","index.d.ts","index.js","lib/**/*.js","vendor/**/*","package.json"],"engines":{"node":">=18.0.0","npm":">=7.19.0","git":">=2.11.0","yarn":">=1.7.0"},"scripts":{"check":"tsc","format":"biome format . --write","lint-biome":"biome lint .","lint-publint":"publint","lint":"npm run lint-biome && npm run lint-publint","test":"jest"},"optionalDependencies":{"readable-stream":">=4.0.0"},"devDependencies":{"@biomejs/biome":"^1.4.1","@types/jest":"^29.5.3","@typescript-eslint/eslint-plugin":"^5.56.0","cross-fetch":"^3.1.5","jest":"^29.7.0","nock":"^14.0.0-beta.6","publint":"^0.2.7","ts-jest":"^29.1.0","typescript":"^5.0.2"}}')},51936:(e,t,r)=>{let i=r(996),{create:s}=r(4001);async function n(e,t){var i;let s=new TextEncoder,n=globalThis.crypto;void 0===n&&(n=r(29345).call(null,"node:crypto").webcrypto);let o=await n.subtle.importKey("raw",(i=e,Uint8Array.from(atob(i),e=>e.codePointAt(0))),{name:"HMAC",hash:"SHA-256"},!1,["sign"]);return a(await n.subtle.sign("HMAC",o,s.encode(t)))}function a(e){return btoa(String.fromCharCode.apply(null,new Uint8Array(e)))}async function o(e,t){return await l(t,async t=>t instanceof Blob||t instanceof Buffer?(await s.call(e,t)).urls.get:t)}async function c(e){let t=0;return await l(e,async e=>{let r,i;if(e instanceof Blob)r=await e.arrayBuffer(),i=e.type;else{if(!u(e))return e;r=e}if((t+=r.byteLength)>1e7)throw Error(`Combined filesize of prediction ${t} bytes exceeds 10mb limit for inline encoding, please provide URLs instead`);let s=a(r);return i=i||"application/octet-stream",`data:${i};base64,${s}`})}async function l(e,t){if(Array.isArray(e)){let r=[];for(let i of e){let e=await l(i,t);r.push(e)}return r}if(function(e){if("object"!=typeof e||null===e||"[object Object]"!==String(e))return!1;let t=Object.getPrototypeOf(e);if(null===t)return!0;let r=Object.prototype.hasOwnProperty.call(t,"constructor")&&t.constructor;return"function"==typeof r&&r instanceof r&&Function.prototype.toString.call(r)===Function.prototype.toString.call(Object)}(e)){let r={};for(let i of Object.keys(e))r[i]=await l(e[i],t);return r}return await t(e)}function u(e){return e instanceof Int8Array||e instanceof Int16Array||e instanceof Int32Array||e instanceof Uint8Array||e instanceof Uint8ClampedArray||e instanceof Uint16Array||e instanceof Uint32Array||e instanceof Float32Array||e instanceof Float64Array}e.exports={transform:l,transformFileInputs:async function e(e,t,r){switch(r){case"data-uri":return await c(e,t);case"upload":return await o(e,t);case"default":try{return await o(e,t)}catch(e){if(e instanceof i&&e.response.status>=400&&e.response.status<500)throw e;return await c(t)}default:throw Error(`Unexpected file upload strategy: ${r}`)}},validateWebhook:async function e(e,t){let{id:r,timestamp:i,body:s,signature:a}=e,o=t||e.secret;if(e&&e.headers&&e.body&&("function"==typeof e.headers.get?(r=e.headers.get("webhook-id"),i=e.headers.get("webhook-timestamp"),a=e.headers.get("webhook-signature")):(r=e.headers["webhook-id"],i=e.headers["webhook-timestamp"],a=e.headers["webhook-signature"]),s=e.body),s instanceof ReadableStream||s.readable)try{s=await new Response(s).text()}catch(e){throw Error(`Error reading body: ${e.message}`)}else if(u(s))s=await new Blob([s]).text();else if("object"==typeof s)s=JSON.stringify(s);else if("string"!=typeof s)throw Error("Invalid body type");if(!r||!i||!a)throw Error("Missing required webhook headers");if(!s)throw Error("Missing required body");if(!o)throw Error("Missing required secret");let c=`${r}.${i}.${s}`,l=await n(o.split("_").pop(),c);return a.split(" ").map(e=>e.split(",")[1]).some(e=>e===l)},withAutomaticRetries:async function e(t,r={}){let s=r.shouldRetry||(()=>!1),n=r.maxRetries||5,a=r.interval||500,o=r.jitter||100,c=e=>new Promise(t=>setTimeout(t,e)),l=0;do{let e=a*2**l+Math.random()*o;try{let e=await t();if(e.ok||!s(e))return e}catch(t){if(t instanceof i){let r=t.response.headers.get("Retry-After");if(r){if(Number.isInteger(r))e=1e3*r;else{let t=new Date(r);Number.isNaN(t.getTime())||(e=t.getTime()-new Date().getTime())}}}}Number.isInteger(n)&&n>0&&(Number.isInteger(e)&&e>0&&await c(a*2**(r.maxRetries-n)),l+=1)}while(l<n);return t()},parseProgressFromLogs:function(e){let t="object"==typeof e&&e.logs?e.logs:e;if(!t||"string"!=typeof t)return null;let r=/^\s*(\d+)%\s*\|.+?\|\s*(\d+)\/(\d+)/;for(let e of t.split("\n").reverse()){let t=e.match(r);if(t&&4===t.length)return{percentage:parseInt(t[1],10)/100,current:parseInt(t[2],10),total:parseInt(t[3],10)}}return null},streamAsyncIterator:async function*(e){let t=e.getReader();try{for(;;){let{done:e,value:r}=await t.read();if(e)return;yield r}}finally{t.releaseLock()}}}},65743:e=>{class t{constructor(e,t,r=null){this.owner=e,this.name=t,this.version=r}static parse(e){let r=e.match(/^(?<owner>[^/]+)\/(?<name>[^/:]+)(:(?<version>.+))?$/);if(!r)throw Error(`Invalid reference to model version: ${e}. Expected format: owner/name or owner/name:version`);let{owner:i,name:s,version:n}=r.groups;return new t(i,s,n)}}e.exports=t},68826:e=>{e.exports={get:async function(e,t){return(await this.request(`/models/${e}/${t}`,{method:"GET"})).json()},list:async function(){return(await this.request("/models",{method:"GET"})).json()},create:async function(e,t,r){let i={owner:e,name:t,...r};return(await this.request("/models",{method:"POST",data:i})).json()},versions:{list:async function(e,t){return(await this.request(`/models/${e}/${t}/versions`,{method:"GET"})).json()},get:async function(e,t,r){return(await this.request(`/models/${e}/${t}/versions/${r}`,{method:"GET"})).json()}},search:async function(e){return(await this.request("/models",{method:"QUERY",headers:{"Content-Type":"text/plain"},data:e})).json()}}},69542:e=>{e.exports={default:{secret:{get:async function(){return(await this.request("/webhooks/default/secret",{method:"GET"})).json()}}}}},80660:e=>{e.exports={list:async function(){return(await this.request("/hardware",{method:"GET"})).json()}}},81049:e=>{e.exports={create:async function(e,t,r,i){let{...s}=i;if(s.webhook)try{new URL(s.webhook)}catch(e){throw Error("Invalid webhook URL")}return(await this.request(`/models/${e}/${t}/versions/${r}/trainings`,{method:"POST",data:s})).json()},get:async function(e){return(await this.request(`/trainings/${e}`,{method:"GET"})).json()},cancel:async function(e){return(await this.request(`/trainings/${e}/cancel`,{method:"POST"})).json()},list:async function(){return(await this.request("/trainings",{method:"GET"})).json()}}},88009:e=>{var t=Object.defineProperty,r=Object.getOwnPropertyDescriptor,i=Object.getOwnPropertyNames,s=Object.prototype.hasOwnProperty,n={};((e,r)=>{for(var i in r)t(e,i,{get:r[i],enumerable:!0})})(n,{EventSourceParserStream:()=>o}),e.exports=((e,n,a,o)=>{if(n&&"object"==typeof n||"function"==typeof n)for(let a of i(n))s.call(e,a)||void 0===a||t(e,a,{get:()=>n[a],enumerable:!(o=r(n,a))||o.enumerable});return e})(t({},"__esModule",{value:!0}),n);var a=[239,187,191],o=class extends TransformStream{constructor(){let e;super({start(t){e=function(e){let t,r,i,s,n,o,c;return l(),{feed:function(l){var u;r=r?r+l:l,t&&(u=r,a.every((e,t)=>u.charCodeAt(t)===e))&&(r=r.slice(a.length)),t=!1;let d=r.length,h=0,f=!1;for(;h<d;){let t;f&&("\n"===r[h]&&++h,f=!1);let a=-1,l=s;for(let e=i;a<0&&e<d;++e)":"===(t=r[e])&&l<0?l=e-h:"\r"===t?(f=!0,a=e-h):"\n"===t&&(a=e-h);if(a<0){i=d-h,s=l;break}i=0,s=-1,function(t,r,i,s){if(0===s){c.length>0&&(e({type:"event",id:n,event:o||void 0,data:c.slice(0,-1)}),c="",n=void 0),o=void 0;return}let a=i<0,l=t.slice(r,r+(a?s:i)),u=0;u=a?s:" "===t[r+i+1]?i+2:i+1;let d=r+u,h=s-u,f=t.slice(d,d+h).toString();if("data"===l)c+=f?"".concat(f,"\n"):"\n";else if("event"===l)o=f;else if("id"!==l||f.includes("\0")){if("retry"===l){let t=parseInt(f,10);Number.isNaN(t)||e({type:"reconnect-interval",value:t})}}else n=f}(r,h,l,a),h+=a+1}h===d?r="":h>0&&(r=r.slice(h))},reset:l};function l(){t=!0,r="",i=0,s=-1,n=void 0,o=void 0,c=""}}(e=>{"event"===e.type&&t.enqueue(e)})},transform(t){e.feed(t)}})}}},91454:(e,t,r)=>{let{transformFileInputs:i}=r(51936);e.exports={predictions:{create:async function(e,t,r){let{input:s,wait:n,...a}=r;if(a.webhook)try{new URL(a.webhook)}catch(e){throw Error("Invalid webhook URL")}let o={};if(n){if("number"==typeof n){let e=Math.max(1,Math.ceil(Number(n))||1);o.Prefer=`wait=${e}`}else o.Prefer="wait"}return(await this.request(`/deployments/${e}/${t}/predictions`,{method:"POST",headers:o,data:{...a,input:await i(this,s,this.fileEncodingStrategy)}})).json()}},get:async function(e,t){return(await this.request(`/deployments/${e}/${t}`,{method:"GET"})).json()},create:async function(e){return(await this.request("/deployments",{method:"POST",data:e})).json()},update:async function(e,t,r){return(await this.request(`/deployments/${e}/${t}`,{method:"PATCH",data:r})).json()},list:async function(){return(await this.request("/deployments",{method:"GET"})).json()},delete:async function(e,t){return 204===(await this.request(`/deployments/${e}/${t}`,{method:"DELETE"})).status}}}};