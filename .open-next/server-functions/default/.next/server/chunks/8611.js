"use strict";exports.id=8611,exports.ids=[8611],exports.modules={98611:e=>{e.exports=JSON.parse('{"user":{"sign_in":"Sign In","sign_out":"Sign Out","credits":"Credits","api_keys":"API Keys","my_orders":"My Orders","user_center":"User Center","admin_system":"Admin System"},"language_switch":{"title":"Switch Language?","description":"We detected that you might prefer to use {suggestedLanguage}. Would you like to switch to {suggestedLanguage}?","switch_button_en":"Switch to {suggestedLanguage}","switch_button_zh":"切换到{suggestedLanguage}","cancel_button_en":"Keep {currentLanguage}","cancel_button_zh":"保持{currentLanguage}"},"sign_modal":{"sign_in_title":"Sign In","sign_in_description":"Sign in to your account","sign_up_title":"Sign Up","sign_up_description":"Create an account","email_title":"Email","email_placeholder":"Input your email here","password_title":"Password","password_placeholder":"Input your password here","forgot_password":"Forgot password?","or":"Or","continue":"Continue","no_account":"Don\'t have an account?","email_sign_in":"Sign in with Email","google_sign_in":"Sign in with Google","google_signing_in":"Redirecting to Google...","github_sign_in":"Sign in with GitHub","github_signing_in":"Redirecting to GitHub...","close_title":"Close","cancel_title":"Cancel"},"my_orders":{"title":"My Orders","description":"orders paid","no_orders":"No orders found","tip":"","activate_order":"Activate Order","actived":"Activated","join_discord":"Join Discord","read_docs":"Read Docs","table":{"order_no":"Order No","email":"Email","product_name":"Product Name","amount":"Amount","paid_at":"Paid At","github_username":"GitHub Username","status":"Status"}},"my_credits":{"title":"My Credits","left_tip":"left credits: {left_credits}","no_credits":"No credits records","recharge":"Recharge","table":{"trans_no":"Trans No","trans_type":"Trans Type","credits":"Credits","updated_at":"Updated At","status":"Status"}},"api_keys":{"title":"API Keys","tip":"Please keep your apikey safe to avoid leaks","no_api_keys":"No API Keys","create_api_key":"Create API Key","table":{"name":"Name","key":"Key","created_at":"Created At"},"form":{"name":"Name","name_placeholder":"API Key Name","submit":"Submit"}},"blog":{"title":"Blog","description":"News, resources, and updates about us","read_more_text":"Read More"},"my_invites":{"title":"My Invites","description":"View your invite records","no_invites":"No invite records found","my_invite_link":"My Invite Link","edit_invite_link":"Edit Invite Link","copy_invite_link":"Copy Invite Link","invite_code":"Invite Code","invite_tip":"Invite 1 friend to order, reward $50.","invite_balance":"Invite Reward Balance","total_invite_count":"Total Invite Count","total_paid_count":"Total Paid Count","total_award_amount":"Total Award Amount","update_invite_code":"Set Invite Code","update_invite_code_tip":"Input your custom invite code","update_invite_button":"Save","no_orders":"You can\'t invite others before you order","no_affiliates":"You\'re not allowed to invite others, please contact us to apply for permission.","table":{"invite_time":"Invite Time","invite_user":"Invite User","status":"Status","reward_percent":"Reward Percent","reward_amount":"Reward Amount","pending":"Pending","completed":"Completed"}},"feedback":{"title":"Feedback","description":"We\'d love to hear what went well or how we can improve the product experience.","submit":"Submit","loading":"Submitting...","contact_tip":"Other ways to contact us","rating_tip":"How do you feel about our product?","placeholder":"Leave your words here..."}}')}};