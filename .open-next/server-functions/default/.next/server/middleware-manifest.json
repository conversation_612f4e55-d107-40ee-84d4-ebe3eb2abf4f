{"version": 3, "middleware": {"/": {"files": ["server/edge-runtime-webpack.js", "server/middleware.js"], "name": "middleware", "page": "/", "matchers": [{"regexp": "^(?:\\/(_next\\/data\\/[^/]{1,}))?(?:\\/(\\/?index|\\/?index\\.json))?[\\/#\\?]?$", "originalSource": "/"}, {"regexp": "^(?:\\/(_next\\/data\\/[^/]{1,}))?(?:\\/(en|en-US|zh|zh-CN|zh-TW|zh-HK|zh-MO|ja|ko|ru|fr|de|ar|es|it))(?:\\/((?:[^\\/#\\?]+?)(?:\\/(?:[^\\/#\\?]+?))*))?(\\.json)?[\\/#\\?]?$", "originalSource": "/(en|en-US|zh|zh-CN|zh-TW|zh-HK|zh-MO|ja|ko|ru|fr|de|ar|es|it)/:path*"}, {"regexp": "^(?:\\/(_next\\/data\\/[^/]{1,}))?(?:\\/((?!privacy-policy|terms-of-service|api\\/|_next|_vercel|.*\\..*).*))(\\.json)?[\\/#\\?]?$", "originalSource": "/((?!privacy-policy|terms-of-service|api/|_next|_vercel|.*\\..*).*)"}], "wasm": [], "assets": [], "env": {"__NEXT_BUILD_ID": "Dp_EjZ4J5q76S6vVMjk6b", "NEXT_SERVER_ACTIONS_ENCRYPTION_KEY": "dv7dWAk9T2M+/LsPh/t8l/c14E4NhTVpbZj8PiBGmPM=", "__NEXT_PREVIEW_MODE_ID": "c2027882c03a4983b4749d09c6c1f7e9", "__NEXT_PREVIEW_MODE_ENCRYPTION_KEY": "42fdd71e0a5281b50892e0064e5ef9e6d4cf49e469c61025a7c86b42cbe5a493", "__NEXT_PREVIEW_MODE_SIGNING_KEY": "08a7f1554a462498b85a7a6446d435d859bc06db442d0cf9ee90ef1a8a1ef9e5"}}}, "functions": {}, "sortedMiddleware": ["/"]}