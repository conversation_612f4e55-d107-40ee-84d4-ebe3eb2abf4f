{"version": 3, "middleware": {"/": {"files": ["server/edge-runtime-webpack.js", "server/middleware.js"], "name": "middleware", "page": "/", "matchers": [{"regexp": "^(?:\\/(_next\\/data\\/[^/]{1,}))?(?:\\/(\\/?index|\\/?index\\.json))?[\\/#\\?]?$", "originalSource": "/"}, {"regexp": "^(?:\\/(_next\\/data\\/[^/]{1,}))?(?:\\/(en|en-US|zh|zh-CN|zh-TW|zh-HK|zh-MO|ja|ko|ru|fr|de|ar|es|it))(?:\\/((?:[^\\/#\\?]+?)(?:\\/(?:[^\\/#\\?]+?))*))?(\\.json)?[\\/#\\?]?$", "originalSource": "/(en|en-US|zh|zh-CN|zh-TW|zh-HK|zh-MO|ja|ko|ru|fr|de|ar|es|it)/:path*"}, {"regexp": "^(?:\\/(_next\\/data\\/[^/]{1,}))?(?:\\/((?!privacy-policy|terms-of-service|api\\/|_next|_vercel|.*\\..*).*))(\\.json)?[\\/#\\?]?$", "originalSource": "/((?!privacy-policy|terms-of-service|api/|_next|_vercel|.*\\..*).*)"}], "wasm": [], "assets": [], "env": {"__NEXT_BUILD_ID": "Mz8aZrvyX7kD7MiUo0vt-", "NEXT_SERVER_ACTIONS_ENCRYPTION_KEY": "dv7dWAk9T2M+/LsPh/t8l/c14E4NhTVpbZj8PiBGmPM=", "__NEXT_PREVIEW_MODE_ID": "b6df6c7e242f66da2465f32b44b4b823", "__NEXT_PREVIEW_MODE_ENCRYPTION_KEY": "c658297acf58b549c99ee8123ce2c2c631f0c7405a1212853b92510eecb51ccc", "__NEXT_PREVIEW_MODE_SIGNING_KEY": "6ca5e98c538450654b41ef9ff4a74ef129a3630d90f53866c39e8a45634c883d"}}}, "functions": {}, "sortedMiddleware": ["/"]}