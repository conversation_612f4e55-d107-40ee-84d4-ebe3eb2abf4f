{"name": "qqai-template-one", "version": "2.0.0", "type": "module", "private": true, "scripts": {"dev": "cross-env NODE_NO_WARNINGS=1 next dev", "build": "next build", "start": "NODE_NO_WARNINGS=1 next start", "lint": "next lint", "deploy": "opennextjs-cloudflare build && opennextjs-cloudflare deploy", "preview": "opennextjs-cloudflare build && opennextjs-cloudflare preview", "cf-typegen": "wrangler types --env-interface CloudflareEnv ./cloudflare-env.d.ts", "test": "jest", "test:watch": "jest --watch", "test:coverage": "jest --coverage", "analyze": "ANALYZE=true pnpm build", "docker:build": "docker build -f Dockerfile -t shipany-template-one:latest ."}, "dependencies": {"@ai-sdk/deepseek": "^0.1.11", "@ai-sdk/openai": "^1.1.13", "@ai-sdk/openai-compatible": "^0.0.17", "@ai-sdk/provider": "^1.0.8", "@ai-sdk/provider-utils": "^2.0.7", "@ai-sdk/replicate": "^0.1.10", "@aws-sdk/client-s3": "^3.740.0", "@aws-sdk/lib-storage": "^3.740.0", "@hookform/resolvers": "^3.10.0", "@mdx-js/loader": "^3.1.0", "@mdx-js/react": "^3.1.0", "@next/mdx": "^15.1.3", "@next/third-parties": "^15.1.2", "@opennextjs/cloudflare": "^1.6.3", "@openpanel/nextjs": "^1.0.7", "@openrouter/ai-sdk-provider": "^0.0.6", "@radix-ui/react-accordion": "^1.2.2", "@radix-ui/react-avatar": "^1.1.2", "@radix-ui/react-checkbox": "^1.2.3", "@radix-ui/react-collapsible": "^1.1.2", "@radix-ui/react-dialog": "^1.1.4", "@radix-ui/react-dropdown-menu": "^2.1.4", "@radix-ui/react-label": "^2.1.1", "@radix-ui/react-navigation-menu": "^1.2.3", "@radix-ui/react-progress": "^1.1.7", "@radix-ui/react-radio-group": "^1.2.2", "@radix-ui/react-select": "^2.1.4", "@radix-ui/react-separator": "^1.1.1", "@radix-ui/react-slot": "^1.1.1", "@radix-ui/react-switch": "^1.1.2", "@radix-ui/react-tabs": "^1.1.2", "@radix-ui/react-toggle": "^1.1.6", "@radix-ui/react-toggle-group": "^1.1.7", "@radix-ui/react-tooltip": "^1.1.6", "@stripe/stripe-js": "^5.4.0", "@supabase/supabase-js": "^2.47.10", "@tabler/icons-react": "^3.31.0", "@tiptap/extension-color": "^2.11.7", "@tiptap/extension-list-item": "^2.11.7", "@tiptap/extension-text-style": "^2.11.7", "@tiptap/react": "^2.11.7", "@tiptap/starter-kit": "^2.11.7", "@types/mdx": "^2.0.13", "@uiw/react-md-editor": "^4.0.5", "ai": "^4.1.64", "class-variance-authority": "^0.7.1", "clsx": "^2.1.1", "embla-carousel-auto-scroll": "^8.5.1", "embla-carousel-fade": "^8.5.1", "embla-carousel-react": "^8.5.1", "eslint-config-next": "^15.4.5", "google-one-tap": "^1.0.6", "lucide-react": "^0.439.0", "moment": "^2.30.1", "next": "15.3.5", "next-auth": "5.0.0-beta.25", "next-intl": "^4.1.0", "next-themes": "^0.4.4", "react": "^19.0.0", "react-copy-to-clipboard": "^5.1.0", "react-dom": "^19.0.0", "react-hook-form": "^7.54.2", "react-icons": "^5.4.0", "recharts": "^2.15.3", "replicate": "^1.0.1", "simple-flakeid": "^0.0.5", "sonner": "^1.7.1", "stripe": "^17.5.0", "tailwind-merge": "^2.6.0", "tailwindcss-animate": "^1.0.7", "uuid": "^11.0.3", "vaul": "^1.1.2", "zod": "^3.24.1"}, "devDependencies": {"@eslint/eslintrc": "^3.3.1", "@next/bundle-analyzer": "^15.1.3", "@tailwindcss/postcss": "^4.1.4", "@testing-library/jest-dom": "^6.6.3", "@testing-library/react": "^16.3.0", "@testing-library/user-event": "^14.6.1", "@types/jest": "^30.0.0", "@types/markdown-it": "^14.1.2", "@types/node": "^20.17.10", "@types/react": "^18.3.18", "@types/react-copy-to-clipboard": "^5.0.7", "@types/react-dom": "^18.3.5", "@types/uuid": "^10.0.0", "cross-env": "^7.0.3", "jest": "^30.0.4", "jest-environment-jsdom": "^30.0.4", "postcss": "^8.4.49", "tailwindcss": "^4.1.4", "typescript": "^5.7.2", "vercel": "39.1.1", "wrangler": "^4.27.0"}}