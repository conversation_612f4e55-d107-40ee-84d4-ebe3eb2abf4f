(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[9143],{709:(e,t,a)=>{"use strict";a.d(t,{I:()=>d,SQ:()=>o,_2:()=>c,lp:()=>u,mB:()=>m,rI:()=>s,ty:()=>l});var r=a(16261);a(95361);var i=a(89812),n=a(61063);function s(e){let{...t}=e;return(0,r.jsx)(i.bL,{"data-slot":"dropdown-menu",...t})}function l(e){let{...t}=e;return(0,r.jsx)(i.l9,{"data-slot":"dropdown-menu-trigger",...t})}function o(e){let{className:t,sideOffset:a=4,...s}=e;return(0,r.jsx)(i.ZL,{children:(0,r.jsx)(i.UC,{"data-slot":"dropdown-menu-content",sideOffset:a,className:(0,n.cn)("bg-popover text-popover-foreground data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=open]:fade-in-0 data-[state=closed]:zoom-out-95 data-[state=open]:zoom-in-95 data-[side=bottom]:slide-in-from-top-2 data-[side=left]:slide-in-from-right-2 data-[side=right]:slide-in-from-left-2 data-[side=top]:slide-in-from-bottom-2 z-50 max-h-(--radix-dropdown-menu-content-available-height) min-w-[8rem] origin-(--radix-dropdown-menu-content-transform-origin) overflow-x-hidden overflow-y-auto rounded-md border p-1 shadow-md",t),...s})})}function d(e){let{...t}=e;return(0,r.jsx)(i.YJ,{"data-slot":"dropdown-menu-group",...t})}function c(e){let{className:t,inset:a,variant:s="default",...l}=e;return(0,r.jsx)(i.q7,{"data-slot":"dropdown-menu-item","data-inset":a,"data-variant":s,className:(0,n.cn)("focus:bg-accent focus:text-accent-foreground data-[variant=destructive]:text-destructive data-[variant=destructive]:focus:bg-destructive/10 dark:data-[variant=destructive]:focus:bg-destructive/20 data-[variant=destructive]:focus:text-destructive data-[variant=destructive]:*:[svg]:!text-destructive [&_svg:not([class*='text-'])]:text-muted-foreground relative flex cursor-default items-center gap-2 rounded-sm px-2 py-1.5 text-sm outline-hidden select-none data-[disabled]:pointer-events-none data-[disabled]:opacity-50 data-[inset]:pl-8 [&_svg]:pointer-events-none [&_svg]:shrink-0 [&_svg:not([class*='size-'])]:size-4",t),...l})}function u(e){let{className:t,inset:a,...s}=e;return(0,r.jsx)(i.JU,{"data-slot":"dropdown-menu-label","data-inset":a,className:(0,n.cn)("px-2 py-1.5 text-sm font-medium data-[inset]:pl-8",t),...s})}function m(e){let{className:t,...a}=e;return(0,r.jsx)(i.wv,{"data-slot":"dropdown-menu-separator",className:(0,n.cn)("bg-border -mx-1 my-1 h-px",t),...a})}},5399:(e,t,a)=>{"use strict";a.d(t,{Avatar:()=>s,AvatarImage:()=>l,q:()=>o});var r=a(16261);a(95361);var i=a(28806),n=a(61063);function s(e){let{className:t,...a}=e;return(0,r.jsx)(i.bL,{"data-slot":"avatar",className:(0,n.cn)("relative flex size-8 shrink-0 overflow-hidden rounded-full",t),...a})}function l(e){let{className:t,...a}=e;return(0,r.jsx)(i._V,{"data-slot":"avatar-image",className:(0,n.cn)("aspect-square size-full object-cover",t),...a})}function o(e){let{className:t,...a}=e;return(0,r.jsx)(i.H4,{"data-slot":"avatar-fallback",className:(0,n.cn)("bg-muted flex size-full items-center justify-center rounded-full",t),...a})}},17184:(e,t,a)=>{"use strict";a.d(t,{PW:()=>n,np:()=>s,pr:()=>i});var r=a(63843);let i=e=>{let t=localStorage.getItem(e);if(!t)return null;let a=t.split(":");if(!a||a.length<2)return null;let i=Number(a[0]),n=(0,r.lg)();if(-1!==i&&i<n)return s(e),null;let l=a[0]+":";return t.replace(l,"")},n=(e,t,a)=>{localStorage.setItem(e,a+":"+t)},s=e=>{localStorage.removeItem(e)}},21608:(e,t,a)=>{"use strict";a.d(t,{AppContextProvider:()=>h,U:()=>x});var r=a(16261),i=a(95361),n=a(17184),s=a(88999),l=a(96576),o=a.n(l),d=a(5657),c=a.n(d),u=a(80372);let m=(0,i.createContext)({}),x=()=>(0,i.useContext)(m),h=e=>{let{children:t}=e;!function(){let{data:e,status:t}=(0,u.wV)(),a=async function(){c()({client_id:"1011821969948-i0orej6mbfqon33v3klabnto7g49mvrh.apps.googleusercontent.com",auto_select:!1,cancel_on_tap_outside:!1,context:"signin"},e=>{console.log("onetap login ok",e),n(e.credential)})},n=async function(e){console.log("signIn ok",await (0,u.Jv)("google-one-tap",{credential:e,redirect:!1}))};(0,i.useEffect)(()=>{if("unauthenticated"===t){a();let e=setInterval(()=>{a()},3e3);return()=>{clearInterval(e)}}},[t]),r.Fragment}();let{data:a}=(0,u.wV)(),[l,d]=(0,i.useState)(()=>"light"),[x,h]=(0,i.useState)(!1),[f,v]=(0,i.useState)(null),[g,p]=(0,i.useState)(!1),j=async function(){try{let e=await fetch("/api/get-user-info",{method:"POST"});if(!e.ok)throw Error("fetch user info failed with status: "+e.status);let{code:t,message:a,data:r}=await e.json();if(0!==t)throw Error(a);v(r),N(r)}catch(e){console.log("fetch user info failed")}},N=async e=>{try{if(e.invited_by){console.log("user already been invited",e.invited_by);return}let t=(0,n.pr)(s.wD.InviteCode);if(!t)return;let a=o()(e.created_at).unix(),r=o()().unix(),i=Number(r-a);if(i<=0||i>7200){console.log("user created more than 2 hours");return}console.log("update invite",t,e.uuid);let l={invite_code:t,user_uuid:e.uuid},d=await fetch("/api/update-invite",{method:"POST",headers:{"Content-Type":"application/json"},body:JSON.stringify(l)});if(!d.ok)throw Error("update invite failed with status: "+d.status);let{code:c,message:u,data:m}=await d.json();if(0!==c)throw Error(u);v(m),(0,n.np)(s.wD.InviteCode)}catch(e){console.log("update invite failed: ",e)}};return(0,i.useEffect)(()=>{a&&a.user&&j()},[a]),(0,r.jsx)(m.Provider,{value:{theme:l,setTheme:d,showSignModal:x,setShowSignModal:h,user:f,setUser:v,showFeedback:g,setShowFeedback:p},children:t})}},22532:(e,t,a)=>{"use strict";a.d(t,{default:()=>_});var r=a(16261),i=a(95361),n=a(85598),s=a(62935),l=a(40528),o=a(89799);function d(e){var t;let{nav:a}=e,i=(0,o.usePathname)();return(0,r.jsx)(n.Cn,{children:(0,r.jsx)(n.rQ,{className:"flex flex-col gap-2 mt-4",children:(0,r.jsx)(n.wZ,{children:null==a?void 0:null===(t=a.items)||void 0===t?void 0:t.map(e=>(0,r.jsx)(n.FX,{children:(0,r.jsx)(n.Uj,{tooltip:e.title,className:"".concat(e.is_active||i.endsWith(e.url)?"bg-primary text-primary-foreground hover:bg-primary/90 hover:text-primary-foreground active:bg-primary/90 active:text-primary-foreground min-w-8 duration-200 ease-linear":""),children:e.url?(0,r.jsxs)(s.N_,{href:e.url,target:e.target,className:"w-full flex items-center gap-2 cursor-pointer",children:[e.icon&&(0,r.jsx)(l.default,{name:e.icon}),(0,r.jsx)("span",{children:e.title})]}):(0,r.jsxs)(r.Fragment,{children:[e.icon&&(0,r.jsx)(l.default,{name:e.icon}),(0,r.jsx)("span",{children:e.title})]})})},e.title))})})})}var c=a(35654),u=a(5399),m=a(87966),x=a(84438),h=a(709),f=a(82888),v=a(80372),g=a(21608),p=a(8624);function j(e){var t;let{account:a}=e,o=(0,p.c3)(),{user:d,setShowSignModal:c}=(0,g.U)(),{isMobile:j,open:N}=(0,n.cL)();return(0,r.jsx)(r.Fragment,{children:d?(0,r.jsxs)(n.wZ,{className:"gap-4",children:[!N&&(0,r.jsx)(n.FX,{children:(0,r.jsx)(n.Uj,{className:"cursor-pointer",asChild:!0,children:(0,r.jsx)(n.SidebarTrigger,{})})}),(0,r.jsx)(n.FX,{children:(0,r.jsxs)(h.rI,{children:[(0,r.jsx)(h.ty,{asChild:!0,children:(0,r.jsxs)(n.Uj,{size:"lg",className:"data-[state=open]:bg-sidebar-accent data-[state=open]:text-sidebar-accent-foreground",children:[(0,r.jsxs)(u.Avatar,{className:"h-8 w-8 rounded-lg",children:[(0,r.jsx)(u.AvatarImage,{src:null==d?void 0:d.avatar_url,alt:null==d?void 0:d.nickname}),(0,r.jsx)(u.q,{className:"rounded-lg",children:"CN"})]}),(0,r.jsxs)("div",{className:"grid flex-1 text-left text-sm leading-tight",children:[(0,r.jsx)("span",{className:"truncate font-semibold",children:d.nickname}),(0,r.jsx)("span",{className:"truncate text-xs",children:d.email})]}),(0,r.jsx)(m.A,{className:"ml-auto size-4"})]})}),(0,r.jsxs)(h.SQ,{className:"w-(--radix-dropdown-menu-trigger-width) min-w-56 rounded-lg bg-background",side:j?"bottom":"right",align:"end",sideOffset:4,children:[(0,r.jsx)(h.lp,{className:"p-0 font-normal",children:(0,r.jsxs)("div",{className:"flex items-center gap-2 px-1 py-1.5 text-left text-sm",children:[(0,r.jsxs)(u.Avatar,{className:"h-8 w-8 rounded-lg",children:[(0,r.jsx)(u.AvatarImage,{src:null==d?void 0:d.avatar_url,alt:null==d?void 0:d.nickname}),(0,r.jsx)(u.q,{className:"rounded-lg",children:"CN"})]}),(0,r.jsxs)("div",{className:"grid flex-1 text-left text-sm leading-tight",children:[(0,r.jsx)("span",{className:"truncate font-semibold",children:null==d?void 0:d.nickname}),(0,r.jsx)("span",{className:"truncate text-xs",children:null==d?void 0:d.email})]})]})}),(0,r.jsx)(h.mB,{}),(0,r.jsxs)(h.I,{children:[null==a?void 0:null===(t=a.items)||void 0===t?void 0:t.map((e,t)=>(0,r.jsxs)(i.Fragment,{children:[(0,r.jsx)(h._2,{className:"cursor-pointer",children:(0,r.jsxs)(s.N_,{href:e.url,target:e.target,className:"w-full flex items-center gap-2",children:[e.icon&&(0,r.jsx)(l.default,{name:e.icon}),e.title]})}),(0,r.jsx)(h.mB,{})]},t)),(0,r.jsxs)(h._2,{className:"cursor-pointer",onClick:()=>(0,v.CI)(),children:[(0,r.jsx)(x.A,{}),o("user.sign_out")]})]})]})]})})]}):(0,r.jsx)(r.Fragment,{children:N?(0,r.jsx)("div",{className:"flex justify-center items-center h-full px-4 py-4",children:(0,r.jsx)(f.$,{className:"w-full",onClick:()=>c(!0),children:o("user.sign_in")})}):(0,r.jsx)(n.wZ,{children:(0,r.jsx)(n.FX,{children:(0,r.jsx)(n.Uj,{className:"cursor-pointer",asChild:!0,children:(0,r.jsx)(n.SidebarTrigger,{})})})})})})}function N(e){var t;let{social:a}=e,{open:i}=(0,n.cL)();return(0,r.jsx)(r.Fragment,{children:i?(0,r.jsx)("div",{className:"w-full flex items-center justify-center mx-auto gap-x-4 px-4 py-4 border-t border-gray-200",children:null==a?void 0:null===(t=a.items)||void 0===t?void 0:t.map((e,t)=>(0,r.jsx)("div",{className:"cursor-pointer hover:text-primary",children:(0,r.jsx)(s.N_,{href:e.url,target:e.target||"_self",className:"cursor-pointer",children:e.icon&&(0,r.jsx)(l.default,{name:e.icon,className:"text-xl"})})},t))}):null})}var b=a(56600);function w(e){var t;let{library:a}=e,{isMobile:i}=(0,n.cL)(),o=(0,s.a8)();return(0,r.jsxs)(n.Cn,{className:"group-data-[collapsible=icon]:hidden",children:[(0,r.jsx)(n.jj,{children:a.title}),(0,r.jsxs)(n.wZ,{children:[null===(t=a.items)||void 0===t?void 0:t.map((e,t)=>(0,r.jsxs)(n.FX,{children:[(0,r.jsx)(n.Uj,{tooltip:e.title,className:"".concat(e.is_active||o.endsWith(e.url)?"bg-primary text-primary-foreground hover:bg-primary/90 hover:text-primary-foreground active:bg-primary/90 active:text-primary-foreground min-w-8 duration-200 ease-linear":""),children:(0,r.jsxs)(s.N_,{href:e.url||"",target:e.target,className:"w-full flex items-center gap-2 cursor-pointer",children:[e.icon&&(0,r.jsx)(l.default,{name:e.icon}),(0,r.jsx)("span",{children:e.title})]})}),(0,r.jsx)(h.rI,{})]},t)),a.more&&(0,r.jsx)(n.FX,{children:(0,r.jsxs)(n.Uj,{className:"text-sidebar-foreground/70",children:[(0,r.jsx)(b.A,{className:"text-sidebar-foreground/70"}),(0,r.jsx)("span",{children:a.more.title})]})})]})]})}function y(e){var t;let{nav:a,...i}=e;return(0,r.jsx)(n.Cn,{...i,children:(0,r.jsx)(n.rQ,{children:(0,r.jsx)(n.wZ,{children:null===(t=a.items)||void 0===t?void 0:t.map((e,t)=>(0,r.jsx)(n.FX,{children:(0,r.jsx)(n.Uj,{asChild:!0,children:(0,r.jsxs)(s.N_,{href:e.url,target:e.target,children:[e.icon&&(0,r.jsx)(l.default,{name:e.icon}),(0,r.jsx)("span",{children:e.title})]})})},t))})})})}function _(e){var t,a,i,l,o,u;let{sidebar:m,...x}=e;return(0,r.jsxs)(n.Bx,{collapsible:"offcanvas",...x,children:[(0,r.jsx)(n.Gh,{children:(0,r.jsx)(n.wZ,{children:(0,r.jsx)(n.FX,{children:(0,r.jsx)(n.Uj,{asChild:!0,className:"data-[slot=sidebar-menu-button]:!p-1.5",children:(0,r.jsxs)(s.N_,{href:null===(t=m.brand)||void 0===t?void 0:t.url,className:"flex items-center gap-2",children:[(null===(a=m.brand)||void 0===a?void 0:a.logo)&&(0,r.jsx)(c.default,{src:null===(l=m.brand)||void 0===l?void 0:null===(i=l.logo)||void 0===i?void 0:i.src,alt:null===(o=m.brand)||void 0===o?void 0:o.title,width:28,height:28,className:"rounded-full"}),(0,r.jsx)("span",{className:"text-base font-semibold",children:null===(u=m.brand)||void 0===u?void 0:u.title})]})})})})}),(0,r.jsxs)(n.Yv,{children:[m.nav&&(0,r.jsx)(d,{nav:m.nav}),m.library&&(0,r.jsx)(w,{library:m.library}),m.bottomNav&&(0,r.jsx)(y,{nav:m.bottomNav,className:"mt-auto"})]}),(0,r.jsxs)(n.CG,{children:[(0,r.jsx)(j,{account:m.account}),(null==m?void 0:m.social)&&(0,r.jsx)(N,{social:m.social})]})]})}},33667:(e,t,a)=>{Promise.resolve().then(a.bind(a,22532)),Promise.resolve().then(a.bind(a,85598))},40528:(e,t,a)=>{"use strict";a.r(t),a.d(t,{default:()=>d});var r=a(16261),i=a(95361),n=a(93694),s=a(98842),l=a(69507);let o={RiChatSmile3Line:n.Bgv,RiImageLine:n.fsL,RiVideoLine:n.xi0,RiMicLine:n.RQr,RiMoneyDollarCircleLine:n.AN5,RiArrowRightUpLine:n.SJ3,RiFlashlightFill:n.bwM,RiEyeLine:n.tLq,RiCpuLine:n.y_v,RiUserSmileLine:n.VNl,RiFlashlightLine:n.uEe,RiStarLine:n.WN7,RiPaletteLine:n.LrS,RiRocketLine:n.QWc,RiVoiceprintLine:n.Dcp,RiExchangeLine:n.Lcj,RiTwitterXFill:n.ase,RiGithubFill:n.sAW,RiDiscordFill:n.r53,RiMailLine:n.R0Y,FaRegHeart:s.sOK,GoThumbsup:l.VZG,GoArrowUpRight:l.zny},d=(0,i.memo)(e=>{let{name:t,className:a,onClick:i,...n}=e,s=o[t];return s?(0,r.jsx)(s,{className:a,onClick:i,style:{cursor:i?"pointer":"default"},...n}):null})},55777:(e,t,a)=>{"use strict";a.d(t,{GB:()=>l,IB:()=>r,L$:()=>i,b:()=>s,q:()=>n,u7:()=>o});let r=["en","zh"],i={en:"English",zh:"中文"},n="en",s="as-needed",l=!1,o={en:{"privacy-policy":"/privacy-policy","terms-of-service":"/terms-of-service"}}},62935:(e,t,a)=>{"use strict";a.d(t,{N_:()=>s,a8:()=>o,rd:()=>d});var r=a(55777),i=a(39754);let n=(0,a(31068).A)({locales:r.IB,defaultLocale:r.q,localePrefix:r.b,pathnames:r.u7,localeDetection:r.GB}),{Link:s,redirect:l,usePathname:o,useRouter:d}=(0,i.A)(n)},63843:(e,t,a)=>{"use strict";a.d(t,{lg:()=>r});let r=()=>Date.parse(new Date().toUTCString())/1e3},88999:(e,t,a)=>{"use strict";a.d(t,{wD:()=>r});let r={Theme:"THEME",InviteCode:"INVITE_CODE",LanguagePreference:"LANGUAGE_PREFERENCE",LanguageSwitchAsked:"LANGUAGE_SWITCH_ASKED"}}},e=>{var t=t=>e(e.s=t);e.O(0,[9242,2912,8901,8548,2117,6002,2482,789,7486,35,5998,5325,6032,5654,9812,7440,5598,9110,8886,7358],()=>t(33667)),_N_E=e.O()}]);