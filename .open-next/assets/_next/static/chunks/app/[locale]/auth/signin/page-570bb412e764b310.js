(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[4867],{1316:(e,r,t)=>{"use strict";t.d(r,{A:()=>l});var a=t(95361);let n=e=>e.replace(/([a-z0-9])([A-Z])/g,"$1-$2").toLowerCase(),s=function(){for(var e=arguments.length,r=Array(e),t=0;t<e;t++)r[t]=arguments[t];return r.filter((e,r,t)=>!!e&&t.indexOf(e)===r).join(" ")};var i={xmlns:"http://www.w3.org/2000/svg",width:24,height:24,viewBox:"0 0 24 24",fill:"none",stroke:"currentColor",strokeWidth:2,strokeLinecap:"round",strokeLinejoin:"round"};let o=(0,a.forwardRef)((e,r)=>{let{color:t="currentColor",size:n=24,strokeWidth:o=2,absoluteStrokeWidth:l,className:d="",children:c,iconNode:u,...v}=e;return(0,a.createElement)("svg",{ref:r,...i,width:n,height:n,stroke:t,strokeWidth:l?24*Number(o)/Number(n):o,className:s("lucide",d),...v},[...u.map(e=>{let[r,t]=e;return(0,a.createElement)(r,t)}),...Array.isArray(c)?c:[c]])}),l=(e,r)=>{let t=(0,a.forwardRef)((t,i)=>{let{className:l,...d}=t;return(0,a.createElement)(o,{ref:i,iconNode:r,className:s("lucide-".concat(n(e)),l),...d})});return t.displayName="".concat(e),t}},8624:(e,r,t)=>{"use strict";t.d(r,{c3:()=>s});var a=t(6002);function n(e,r){return(...e)=>{try{return r(...e)}catch{throw Error(void 0)}}}let s=n(0,a.c3);n(0,a.kc)},13466:(e,r,t)=>{"use strict";t.d(r,{BT:()=>l,Wu:()=>c,X9:()=>d,ZB:()=>o,Zp:()=>s,aR:()=>i,wL:()=>u});var a=t(16261);t(95361);var n=t(61063);function s(e){let{className:r,...t}=e;return(0,a.jsx)("div",{"data-slot":"card",className:(0,n.cn)("bg-card text-card-foreground flex flex-col gap-6 rounded-2xl border border-border/50 p-6 shadow-lg backdrop-blur-sm transition-all duration-200 hover:shadow-xl hover:scale-[1.01]",r),...t})}function i(e){let{className:r,...t}=e;return(0,a.jsx)("div",{"data-slot":"card-header",className:(0,n.cn)("@container/card-header grid auto-rows-min grid-rows-[auto_auto] items-start gap-2 has-data-[slot=card-action]:grid-cols-[1fr_auto] [.border-b]:pb-6",r),...t})}function o(e){let{className:r,...t}=e;return(0,a.jsx)("div",{"data-slot":"card-title",className:(0,n.cn)("leading-none font-semibold",r),...t})}function l(e){let{className:r,...t}=e;return(0,a.jsx)("div",{"data-slot":"card-description",className:(0,n.cn)("text-muted-foreground text-sm",r),...t})}function d(e){let{className:r,...t}=e;return(0,a.jsx)("div",{"data-slot":"card-action",className:(0,n.cn)("col-start-2 row-span-2 row-start-1 self-start justify-self-end",r),...t})}function c(e){let{className:r,...t}=e;return(0,a.jsx)("div",{"data-slot":"card-content",className:(0,n.cn)("px-6",r),...t})}function u(e){let{className:r,...t}=e;return(0,a.jsx)("div",{"data-slot":"card-footer",className:(0,n.cn)("flex items-center px-6 [.border-t]:pt-6",r),...t})}},15755:(e,r,t)=>{"use strict";t.d(r,{A:()=>a});let a=(0,t(1316).A)("LoaderCircle",[["path",{d:"M21 12a9 9 0 1 1-6.219-8.56",key:"13zald"}]])},20604:(e,r,t)=>{"use strict";t.d(r,{F:()=>i});var a=t(87786);let n=e=>"boolean"==typeof e?`${e}`:0===e?"0":e,s=a.$,i=(e,r)=>t=>{var a;if((null==r?void 0:r.variants)==null)return s(e,null==t?void 0:t.class,null==t?void 0:t.className);let{variants:i,defaultVariants:o}=r,l=Object.keys(i).map(e=>{let r=null==t?void 0:t[e],a=null==o?void 0:o[e];if(null===r)return null;let s=n(r)||n(a);return i[e][s]}),d=t&&Object.entries(t).reduce((e,r)=>{let[t,a]=r;return void 0===a||(e[t]=a),e},{});return s(e,l,null==r?void 0:null===(a=r.compoundVariants)||void 0===a?void 0:a.reduce((e,r)=>{let{class:t,className:a,...n}=r;return Object.entries(n).every(e=>{let[r,t]=e;return Array.isArray(t)?t.includes({...o,...d}[r]):({...o,...d})[r]===t})?[...e,t,a]:e},[]),null==t?void 0:t.class,null==t?void 0:t.className)}},41765:(e,r,t)=>{Promise.resolve().then(t.bind(t,89753))},43083:(e,r,t)=>{"use strict";t.d(r,{b:()=>o});var a=t(95361),n=t(72637),s=t(16261),i=a.forwardRef((e,r)=>(0,s.jsx)(n.sG.label,{...e,ref:r,onMouseDown:r=>{var t;r.target.closest("button, input, select, textarea")||(null===(t=e.onMouseDown)||void 0===t||t.call(e,r),!r.defaultPrevented&&r.detail>1&&r.preventDefault())}}));i.displayName="Label";var o=i},61063:(e,r,t)=>{"use strict";t.d(r,{cn:()=>s});var a=t(87786),n=t(34835);function s(){for(var e=arguments.length,r=Array(e),t=0;t<e;t++)r[t]=arguments[t];return(0,n.QP)((0,a.$)(r))}},66786:(e,r,t)=>{"use strict";t.d(r,{J:()=>i});var a=t(16261);t(95361);var n=t(43083),s=t(61063);function i(e){let{className:r,...t}=e;return(0,a.jsx)(n.b,{"data-slot":"label",className:(0,s.cn)("flex items-center gap-2 text-sm leading-none font-medium select-none group-data-[disabled=true]:pointer-events-none group-data-[disabled=true]:opacity-50 peer-disabled:cursor-not-allowed peer-disabled:opacity-50",r),...t})}},72637:(e,r,t)=>{"use strict";t.d(r,{hO:()=>l,sG:()=>o});var a=t(95361),n=t(32196),s=t(76012),i=t(16261),o=["a","button","div","form","h2","h3","img","input","label","li","nav","ol","p","select","span","svg","ul"].reduce((e,r)=>{let t=(0,s.TL)(`Primitive.${r}`),n=a.forwardRef((e,a)=>{let{asChild:n,...s}=e;return"undefined"!=typeof window&&(window[Symbol.for("radix-ui")]=!0),(0,i.jsx)(n?t:r,{...s,ref:a})});return n.displayName=`Primitive.${r}`,{...e,[r]:n}},{});function l(e,r){e&&n.flushSync(()=>e.dispatchEvent(r))}},81828:(e,r,t)=>{"use strict";t.d(r,{p:()=>s});var a=t(16261);t(95361);var n=t(61063);function s(e){let{className:r,type:t,...s}=e;return(0,a.jsx)("input",{type:t,"data-slot":"input",className:(0,n.cn)("file:text-foreground placeholder:text-muted-foreground selection:bg-primary selection:text-primary-foreground dark:bg-input/30 border-input flex h-9 w-full min-w-0 rounded-md border bg-transparent px-3 py-1 text-base shadow-xs transition-[color,box-shadow] outline-none file:inline-flex file:h-7 file:border-0 file:bg-transparent file:text-sm file:font-medium disabled:pointer-events-none disabled:cursor-not-allowed disabled:opacity-50 md:text-sm","focus-visible:border-ring focus-visible:ring-ring/50 focus-visible:ring-[3px]","aria-invalid:ring-destructive/20 dark:aria-invalid:ring-destructive/40 aria-invalid:border-destructive",r),...s})}},82888:(e,r,t)=>{"use strict";t.d(r,{$:()=>l,r:()=>o});var a=t(16261);t(95361);var n=t(76012),s=t(20604),i=t(61063);let o=(0,s.F)("inline-flex items-center justify-center gap-2 whitespace-nowrap rounded-lg text-sm font-medium transition-all duration-200 disabled:pointer-events-none disabled:opacity-50 [&_svg]:pointer-events-none [&_svg:not([class*='size-'])]:size-4 shrink-0 [&_svg]:shrink-0 outline-none focus-visible:border-ring focus-visible:ring-ring/50 focus-visible:ring-[3px] aria-invalid:ring-destructive/20 dark:aria-invalid:ring-destructive/40 aria-invalid:border-destructive",{variants:{variant:{default:"bg-gradient-to-r from-primary to-accent text-primary-foreground shadow-md hover:shadow-lg hover:scale-[1.02] active:scale-[0.98]",destructive:"bg-destructive text-destructive-foreground shadow-md hover:bg-destructive/90 hover:shadow-lg hover:scale-[1.02] active:scale-[0.98] focus-visible:ring-destructive/20 dark:focus-visible:ring-destructive/40",outline:"border-2 border-primary/20 bg-background/50 backdrop-blur-sm shadow-sm hover:bg-accent/50 hover:text-accent-foreground hover:border-primary/40 hover:shadow-md hover:scale-[1.02] active:scale-[0.98] dark:bg-input/30 dark:border-input dark:hover:bg-input/50",secondary:"bg-gradient-to-r from-secondary to-muted text-secondary-foreground shadow-sm hover:shadow-md hover:scale-[1.02] active:scale-[0.98]",ghost:"hover:bg-accent/50 hover:text-accent-foreground hover:scale-[1.02] active:scale-[0.98] dark:hover:bg-accent/30",link:"text-primary underline-offset-4 hover:underline hover:text-accent"},size:{default:"h-10 px-6 py-2 has-[>svg]:px-4",sm:"h-8 rounded-lg gap-1.5 px-4 has-[>svg]:px-3",lg:"h-12 rounded-lg px-8 has-[>svg]:px-6 text-base",icon:"size-10 rounded-full"}},defaultVariants:{variant:"default",size:"default"}});function l(e){let{className:r,variant:t,size:s,asChild:l=!1,...d}=e,c=l?n.DX:"button";return(0,a.jsx)(c,{"data-slot":"button",className:(0,i.cn)(o({variant:t,size:s,className:r})),...d})}},89753:(e,r,t)=>{"use strict";t.d(r,{default:()=>v});var a=t(16261),n=t(95361),s=t(13466),i=t(71569),o=t(15755),l=t(82888);t(81828),t(66786);var d=t(61063),c=t(80372),u=t(8624);function v(e){let{className:r,...t}=e,v=(0,u.c3)(),[f,g]=n.useState(!1),[m,h]=n.useState(!1),p=async()=>{g(!0);try{await (0,c.Jv)("google")}catch(e){console.error("Google登录失败:",e),g(!1)}};return(0,a.jsxs)("div",{className:(0,d.cn)("flex flex-col gap-6",r),...t,children:[(0,a.jsxs)(s.Zp,{children:[(0,a.jsxs)(s.aR,{className:"text-center",children:[(0,a.jsx)(s.ZB,{className:"text-xl",children:v("sign_modal.sign_in_title")}),(0,a.jsx)(s.BT,{children:v("sign_modal.sign_in_description")})]}),(0,a.jsx)(s.Wu,{children:(0,a.jsxs)("div",{className:"grid gap-6",children:[(0,a.jsxs)("div",{className:"flex flex-col gap-4",children:[(0,a.jsx)(l.$,{variant:"outline",className:"w-full",onClick:p,disabled:f||m,children:f?(0,a.jsxs)(a.Fragment,{children:[(0,a.jsx)(o.A,{className:"w-4 h-4 animate-spin mr-2"}),v("sign_modal.google_signing_in")]}):(0,a.jsxs)(a.Fragment,{children:[(0,a.jsx)(i.UKz,{className:"w-4 h-4 mr-2"}),v("sign_modal.google_sign_in")]})}),!1]}),!1]})})]}),(0,a.jsxs)("div",{className:"text-balance text-center text-xs text-muted-foreground [&_a]:underline [&_a]:underline-offset-4 hover:[&_a]:text-primary  ",children:["By clicking continue, you agree to our"," ",(0,a.jsx)("a",{href:"/terms-of-service",target:"_blank",children:"Terms of Service"})," ","and"," ",(0,a.jsx)("a",{href:"/privacy-policy",target:"_blank",children:"Privacy Policy"}),"."]})]})}}},e=>{var r=r=>e(e.s=r);e.O(0,[830,2117,6002,5325,9110,8886,7358],()=>r(41765)),_N_E=e.O()}]);