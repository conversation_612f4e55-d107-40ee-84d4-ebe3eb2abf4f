(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[2314],{1316:(e,t,r)=>{"use strict";r.d(t,{A:()=>n});var a=r(95361);let i=e=>e.replace(/([a-z0-9])([A-Z])/g,"$1-$2").toLowerCase(),s=function(){for(var e=arguments.length,t=Array(e),r=0;r<e;r++)t[r]=arguments[r];return t.filter((e,t,r)=>!!e&&r.indexOf(e)===t).join(" ")};var l={xmlns:"http://www.w3.org/2000/svg",width:24,height:24,viewBox:"0 0 24 24",fill:"none",stroke:"currentColor",strokeWidth:2,strokeLinecap:"round",strokeLinejoin:"round"};let o=(0,a.forwardRef)((e,t)=>{let{color:r="currentColor",size:i=24,strokeWidth:o=2,absoluteStrokeWidth:n,className:c="",children:d,iconNode:h,...m}=e;return(0,a.createElement)("svg",{ref:t,...l,width:i,height:i,stroke:r,strokeWidth:n?24*Number(o)/Number(i):o,className:s("lucide",c),...m},[...h.map(e=>{let[t,r]=e;return(0,a.createElement)(t,r)}),...Array.isArray(d)?d:[d]])}),n=(e,t)=>{let r=(0,a.forwardRef)((r,l)=>{let{className:n,...c}=r;return(0,a.createElement)(o,{ref:l,iconNode:t,className:s("lucide-".concat(i(e)),n),...c})});return r.displayName="".concat(e),r}},8275:(e,t,r)=>{"use strict";r.d(t,{A:()=>a});let a=(0,r(1316).A)("ArrowRight",[["path",{d:"M5 12h14",key:"1ays0h"}],["path",{d:"m12 5 7 7-7 7",key:"xquz4c"}]])},17002:(e,t,r)=>{"use strict";r.d(t,{A:()=>a});let a=(0,r(1316).A)("Copy",[["rect",{width:"14",height:"14",x:"8",y:"8",rx:"2",ry:"2",key:"17jyea"}],["path",{d:"M4 16c-1.1 0-2-.9-2-2V4c0-1.1.9-2 2-2h10c1.1 0 2 .9 2 2",key:"zix9uf"}]])},18409:(e,t,r)=>{"use strict";r.d(t,{A:()=>a});let a=(0,r(1316).A)("ChevronDown",[["path",{d:"m6 9 6 6 6-6",key:"qrunsl"}]])},20604:(e,t,r)=>{"use strict";r.d(t,{F:()=>l});var a=r(87786);let i=e=>"boolean"==typeof e?`${e}`:0===e?"0":e,s=a.$,l=(e,t)=>r=>{var a;if((null==t?void 0:t.variants)==null)return s(e,null==r?void 0:r.class,null==r?void 0:r.className);let{variants:l,defaultVariants:o}=t,n=Object.keys(l).map(e=>{let t=null==r?void 0:r[e],a=null==o?void 0:o[e];if(null===t)return null;let s=i(t)||i(a);return l[e][s]}),c=r&&Object.entries(r).reduce((e,t)=>{let[r,a]=t;return void 0===a||(e[r]=a),e},{});return s(e,n,null==t?void 0:null===(a=t.compoundVariants)||void 0===a?void 0:a.reduce((e,t)=>{let{class:r,className:a,...i}=t;return Object.entries(i).every(e=>{let[t,r]=e;return Array.isArray(r)?r.includes({...o,...c}[t]):({...o,...c})[t]===r})?[...e,r,a]:e},[]),null==r?void 0:r.class,null==r?void 0:r.className)}},25716:(e,t,r)=>{"use strict";r.d(t,{A:()=>a});let a=(0,r(1316).A)("ChevronUp",[["path",{d:"m18 15-6-6-6 6",key:"153udz"}]])},69283:(e,t,r)=>{Promise.resolve().then(r.bind(r,85259))},69397:(e,t,r)=>{"use strict";r.d(t,{A:()=>a});let a=(0,r(1316).A)("Check",[["path",{d:"M20 6 9 17l-5-5",key:"1gmf2c"}]])},85259:(e,t,r)=>{"use strict";r.d(t,{default:()=>l});var a=r(16261);r(13466),r(35654),r(12482);var i=r(78774),s=r(657);function l(){return(0,a.jsxs)("div",{className:"min-h-screen bg-background",children:[(0,a.jsx)("div",{className:"container py-16",children:(0,a.jsxs)("div",{className:"text-center mb-16",children:[(0,a.jsx)("h1",{className:"text-4xl font-bold mb-4 bg-gradient-to-r from-primary to-accent bg-clip-text text-transparent",children:"Showcase 组件演示"}),(0,a.jsx)("p",{className:"text-lg text-muted-foreground max-w-2xl mx-auto",children:"展示 PromptShowcase 和 ComparisonShowcase 两个组件的实际效果"})]})}),(0,a.jsx)(i.default,{section:{title:"FLUX & KREA AI 创作 Prompt 精选",description:"精心调试的高质量 Prompt，助您在 FLUX 和 KREA 平台创作出令人惊艳的 AI 作品",items:[{title:"超现实主义人像",description:"创造具有梦幻色彩和超现实元素的人物肖像",prompt:"Surreal portrait of a person with flowing liquid hair that transforms into cosmic nebula, ethereal lighting, dreamlike atmosphere, hyperrealistic details, vibrant colors blending into abstract patterns, professional photography style, 8K ultra-detailed",image:{src:"/imgs/showcases/flux-krea-showcase-2-1.webp",alt:"超现实主义人像作品"},category:"人像创作",tags:["超现实","梦幻","宇宙","液体"]},{title:"科幻场景设计",prompt:"Futuristic sci-fi environment with holographic interfaces, neon-lit corridors, advanced technology panels, atmospheric fog, dramatic lighting with blue and purple tones, cyberpunk aesthetic, highly detailed architecture, cinematic composition",image:{src:"/imgs/showcases/flux-krea-showcase-2-2.webp",alt:"科幻场景设计作品"}},{title:"抽象艺术创作",description:"充满创意的抽象艺术风格图像",prompt:"Abstract digital art with flowing geometric shapes, gradient colors transitioning from warm to cool tones, dynamic composition with depth and movement, modern artistic style, high contrast, professional digital artwork, 4K resolution",image:{src:"/imgs/showcases/flux-krea-showcase-2-3.webp",alt:"抽象艺术创作作品"},category:"抽象艺术",tags:["抽象","几何","渐变","动态"]},{title:"自然风光摄影",description:"壮观的自然景观和风光摄影效果",prompt:"Breathtaking landscape photography of mountain peaks during golden hour, dramatic clouds, perfect lighting, ultra-wide angle shot, professional nature photography, vivid colors, high dynamic range, crystal clear details, award-winning composition",image:{src:"/imgs/showcases/flux-krea-showcase-1-1.webp",alt:"自然风光摄影作品"},category:"风光摄影",tags:["山峰","黄金时刻","自然","广角"]},{title:"时尚人物摄影",description:"高端时尚杂志风格的人物摄影",prompt:"High-fashion portrait photography, model wearing avant-garde clothing, studio lighting setup, clean background, professional makeup and styling, editorial magazine quality, sharp focus, luxury aesthetic, commercial photography style",image:{src:"/imgs/showcases/flux-krea-showcase-1-2.webp",alt:"时尚人物摄影作品"},category:"时尚摄影",tags:["时尚","杂志","工作室","高端"]},{title:"建筑空间设计",description:"现代建筑和室内空间的设计概念",prompt:"Modern architectural interior design, minimalist aesthetic, natural lighting through large windows, clean lines and geometric forms, neutral color palette, high-end materials, professional architectural photography, ultra-detailed",image:{src:"/imgs/showcases/flux-krea-showcase-1-3.webp",alt:"建筑空间设计作品"},category:"建筑设计",tags:["现代","极简","室内","几何"]}]}}),(0,a.jsx)("div",{className:"container py-8",children:(0,a.jsx)("div",{className:"border-t border-border/50"})}),(0,a.jsx)(s.default,{section:{title:"FLUX & KREA AI 图像处理前后对比",description:"展示 FLUX 和 KREA AI 强大的图像处理能力，从普通照片到专业级作品的华丽转变",items:[{title:"艺术风格转换效果",description:"将普通图像转换为具有艺术感的创作作品",prompt:"Transform this image with artistic enhancement: apply cinematic lighting, enhance colors and contrast, add artistic depth and atmosphere, maintain natural proportions while creating a more visually striking and professional appearance. Style: photorealistic with artistic flair, high detail, dramatic lighting.",beforeImage:{src:"/imgs/showcases/flux-krea-showcase-3-1-1.jpg",alt:"AI处理前的原始图像"},afterImage:{src:"/imgs/showcases/flux-krea-showcase-3-1-2.jpg",alt:"AI艺术风格转换后的图像"},category:"艺术转换",tags:["风格转换","艺术增强","色彩优化","专业级"]}]}}),(0,a.jsx)("div",{className:"container py-16",children:(0,a.jsx)("div",{className:"text-center",children:(0,a.jsxs)("div",{className:"bg-muted/50 rounded-lg p-8 max-w-4xl mx-auto",children:[(0,a.jsx)("h3",{className:"text-xl font-semibold mb-4",children:"组件特性说明"}),(0,a.jsxs)("div",{className:"grid md:grid-cols-2 gap-6 text-sm text-muted-foreground",children:[(0,a.jsxs)("div",{children:[(0,a.jsx)("h4",{className:"font-medium text-foreground mb-2",children:"PromptShowcase"}),(0,a.jsxs)("ul",{className:"space-y-1 text-left",children:[(0,a.jsx)("li",{children:"• 支持 Prompt 一键复制"}),(0,a.jsx)("li",{children:"• 响应式网格布局"}),(0,a.jsx)("li",{children:"• 分类标签和多标签支持"}),(0,a.jsx)("li",{children:"• 复制状态反馈"})]})]}),(0,a.jsxs)("div",{children:[(0,a.jsx)("h4",{className:"font-medium text-foreground mb-2",children:"ComparisonShowcase"}),(0,a.jsxs)("ul",{className:"space-y-1 text-left",children:[(0,a.jsx)("li",{children:"• 前后图片对比展示"}),(0,a.jsx)("li",{children:"• 移动端垂直布局适配"}),(0,a.jsx)("li",{children:"• 图片悬停缩放效果"}),(0,a.jsx)("li",{children:"• Prompt 展示和复制"})]})]})]})]})})})]})}}},e=>{var t=t=>e(e.s=t);e.O(0,[2117,2482,5654,2790,9110,8886,7358],()=>t(69283)),_N_E=e.O()}]);