(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[9240],{17184:(e,t,r)=>{"use strict";r.d(t,{PW:()=>a,np:()=>s,pr:()=>u});var n=r(63843);let u=e=>{let t=localStorage.getItem(e);if(!t)return null;let r=t.split(":");if(!r||r.length<2)return null;let u=Number(r[0]),a=(0,n.lg)();if(-1!==u&&u<a)return s(e),null;let c=r[0]+":";return t.replace(c,"")},a=(e,t,r)=>{localStorage.setItem(e,r+":"+t)},s=e=>{localStorage.removeItem(e)}},49958:(e,t,r)=>{Promise.resolve().then(r.bind(r,58543))},58543:(e,t,r)=>{"use strict";r.r(t),r.d(t,{default:()=>i});var n=r(16261),u=r(88999),a=r(17184),s=r(63843),c=r(95361),l=r(89799);function i(){let e=(0,l.useParams)().code;return(0,c.useEffect)(()=>{let t=(0,s.lg)()+2592e3;(0,a.PW)(u.wD.InviteCode,e,t),console.log("cache invite code",e,t),window.location.href="/"},[e]),(0,n.jsx)("div",{className:"w-screen h-screen flex items-center justify-center",children:"loading..."})}},63843:(e,t,r)=>{"use strict";r.d(t,{lg:()=>n});let n=()=>Date.parse(new Date().toUTCString())/1e3},88999:(e,t,r)=>{"use strict";r.d(t,{wD:()=>n});let n={Theme:"THEME",InviteCode:"INVITE_CODE",LanguagePreference:"LANGUAGE_PREFERENCE",LanguageSwitchAsked:"LANGUAGE_SWITCH_ASKED"}},89799:(e,t,r)=>{"use strict";var n=r(8911);r.o(n,"permanentRedirect")&&r.d(t,{permanentRedirect:function(){return n.permanentRedirect}}),r.o(n,"redirect")&&r.d(t,{redirect:function(){return n.redirect}}),r.o(n,"useParams")&&r.d(t,{useParams:function(){return n.useParams}}),r.o(n,"usePathname")&&r.d(t,{usePathname:function(){return n.usePathname}}),r.o(n,"useRouter")&&r.d(t,{useRouter:function(){return n.useRouter}})}},e=>{var t=t=>e(e.s=t);e.O(0,[9110,8886,7358],()=>t(49958)),_N_E=e.O()}]);