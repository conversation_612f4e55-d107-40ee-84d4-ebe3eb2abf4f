(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[3951],{6656:(e,t,a)=>{Promise.resolve().then(a.bind(a,13860)),Promise.resolve().then(a.bind(a,23018)),Promise.resolve().then(a.bind(a,63076)),Promise.resolve().then(a.bind(a,3932)),Promise.resolve().then(a.t.bind(a,12482,23))},13466:(e,t,a)=>{"use strict";a.d(t,{BT:()=>l,Wu:()=>c,X9:()=>d,ZB:()=>o,Zp:()=>s,aR:()=>n,wL:()=>u});var r=a(16261);a(95361);var i=a(61063);function s(e){let{className:t,...a}=e;return(0,r.jsx)("div",{"data-slot":"card",className:(0,i.cn)("bg-card text-card-foreground flex flex-col gap-6 rounded-2xl border border-border/50 p-6 shadow-lg backdrop-blur-sm transition-all duration-200 hover:shadow-xl hover:scale-[1.01]",t),...a})}function n(e){let{className:t,...a}=e;return(0,r.jsx)("div",{"data-slot":"card-header",className:(0,i.cn)("@container/card-header grid auto-rows-min grid-rows-[auto_auto] items-start gap-2 has-data-[slot=card-action]:grid-cols-[1fr_auto] [.border-b]:pb-6",t),...a})}function o(e){let{className:t,...a}=e;return(0,r.jsx)("div",{"data-slot":"card-title",className:(0,i.cn)("leading-none font-semibold",t),...a})}function l(e){let{className:t,...a}=e;return(0,r.jsx)("div",{"data-slot":"card-description",className:(0,i.cn)("text-muted-foreground text-sm",t),...a})}function d(e){let{className:t,...a}=e;return(0,r.jsx)("div",{"data-slot":"card-action",className:(0,i.cn)("col-start-2 row-span-2 row-start-1 self-start justify-self-end",t),...a})}function c(e){let{className:t,...a}=e;return(0,r.jsx)("div",{"data-slot":"card-content",className:(0,i.cn)("px-6",t),...a})}function u(e){let{className:t,...a}=e;return(0,r.jsx)("div",{"data-slot":"card-footer",className:(0,i.cn)("flex items-center px-6 [.border-t]:pt-6",t),...a})}},13860:(e,t,a)=>{"use strict";a.d(t,{default:()=>n});var r=a(16261),i=a(29131),s=a(31399);function n(e){let{text:t,children:a}=e;return(0,r.jsx)(i.CopyToClipboard,{text:t,onCopy:()=>s.oR.success("Copied"),children:(0,r.jsx)("div",{className:"cursor-pointer",children:a})})}},17184:(e,t,a)=>{"use strict";a.d(t,{PW:()=>s,np:()=>n,pr:()=>i});var r=a(63843);let i=e=>{let t=localStorage.getItem(e);if(!t)return null;let a=t.split(":");if(!a||a.length<2)return null;let i=Number(a[0]),s=(0,r.lg)();if(-1!==i&&i<s)return n(e),null;let o=a[0]+":";return t.replace(o,"")},s=(e,t,a)=>{localStorage.setItem(e,a+":"+t)},n=e=>{localStorage.removeItem(e)}},21608:(e,t,a)=>{"use strict";a.d(t,{AppContextProvider:()=>p,U:()=>m});var r=a(16261),i=a(95361),s=a(17184),n=a(88999),o=a(96576),l=a.n(o),d=a(5657),c=a.n(d),u=a(80372);let f=(0,i.createContext)({}),m=()=>(0,i.useContext)(f),p=e=>{let{children:t}=e;!function(){let{data:e,status:t}=(0,u.wV)(),a=async function(){c()({client_id:"1011821969948-i0orej6mbfqon33v3klabnto7g49mvrh.apps.googleusercontent.com",auto_select:!1,cancel_on_tap_outside:!1,context:"signin"},e=>{console.log("onetap login ok",e),s(e.credential)})},s=async function(e){console.log("signIn ok",await (0,u.Jv)("google-one-tap",{credential:e,redirect:!1}))};(0,i.useEffect)(()=>{if("unauthenticated"===t){a();let e=setInterval(()=>{a()},3e3);return()=>{clearInterval(e)}}},[t]),r.Fragment}();let{data:a}=(0,u.wV)(),[o,d]=(0,i.useState)(()=>"light"),[m,p]=(0,i.useState)(!1),[x,v]=(0,i.useState)(null),[h,g]=(0,i.useState)(!1),b=async function(){try{let e=await fetch("/api/get-user-info",{method:"POST"});if(!e.ok)throw Error("fetch user info failed with status: "+e.status);let{code:t,message:a,data:r}=await e.json();if(0!==t)throw Error(a);v(r),y(r)}catch(e){console.log("fetch user info failed")}},y=async e=>{try{if(e.invited_by){console.log("user already been invited",e.invited_by);return}let t=(0,s.pr)(n.wD.InviteCode);if(!t)return;let a=l()(e.created_at).unix(),r=l()().unix(),i=Number(r-a);if(i<=0||i>7200){console.log("user created more than 2 hours");return}console.log("update invite",t,e.uuid);let o={invite_code:t,user_uuid:e.uuid},d=await fetch("/api/update-invite",{method:"POST",headers:{"Content-Type":"application/json"},body:JSON.stringify(o)});if(!d.ok)throw Error("update invite failed with status: "+d.status);let{code:c,message:u,data:f}=await d.json();if(0!==c)throw Error(u);v(f),(0,s.np)(n.wD.InviteCode)}catch(e){console.log("update invite failed: ",e)}};return(0,i.useEffect)(()=>{a&&a.user&&b()},[a]),(0,r.jsx)(f.Provider,{value:{theme:o,setTheme:d,showSignModal:m,setShowSignModal:p,user:x,setUser:v,showFeedback:h,setShowFeedback:g},children:t})}},23018:(e,t,a)=>{"use strict";a.d(t,{default:()=>x});var r=a(16261),i=a(13466),s=a(95361),n=a(82888),o=a(29131),l=a(40528),d=a(62184),c=a(81828),u=a(8624);function f(e){let{open:t,setOpen:a,username:i,initInviteCode:o,updateInviteCode:l,loading:f}=e,m=(0,u.c3)(),[p,x]=(0,s.useState)(o);return(0,r.jsx)(d.lG,{open:t,onOpenChange:a,children:(0,r.jsxs)(d.Cf,{className:"sm:max-w-[425px]",children:[(0,r.jsxs)(d.c7,{children:[(0,r.jsx)(d.L3,{children:m("my_invites.update_invite_code")}),(0,r.jsx)(d.rr,{children:m("my_invites.update_invite_code_tip")})]}),(0,r.jsx)("div",{className:"grid gap-4 py-4",children:(0,r.jsx)("div",{className:"grid grid-cols-1 items-center gap-4",children:(0,r.jsx)(c.p,{placeholder:"".concat(i),value:p,onChange:e=>x(e.target.value),className:"w-full"})})}),(0,r.jsx)(d.Es,{children:(0,r.jsx)(n.$,{onClick:()=>l(p),disabled:f,children:m("my_invites.update_invite_button")})})]})})}var m=a(31399),p=a(21608);function x(e){let{summary:t}=e,a=(0,u.c3)(),[d,c]=(0,s.useState)(!1),{user:x,setUser:v}=(0,p.U)(),[h,g]=(0,s.useState)(!1),b=async function(e){try{if(!(e=e.trim())){m.oR.error("invite code is required");return}g(!0);let t={invite_code:e},a=await fetch("/api/update-invite-code",{method:"POST",headers:{"Content-Type":"application/json"},body:JSON.stringify(t)});if(!a.ok)throw Error("update invite code faild with status "+a.status);let{code:r,message:i,data:s}=await a.json();if(0!==r){m.oR.error(i);return}v(s),m.oR.success("set invite code success"),c(!1)}catch(e){console.log("update invite code failed",e),m.oR.error("set invite code failed")}finally{g(!1)}};return(0,s.useEffect)(()=>{g(!1)},[]),(0,r.jsxs)("div",{className:"flex flex-wrap gap-6",children:[(0,r.jsxs)(i.Zp,{className:"flex-1 p-6",children:[(0,r.jsx)("h2",{className:"text-sm text-gray-500 mb-4",children:a("my_invites.invite_code")}),x&&x.uuid&&(0,r.jsxs)("div",{className:"flex items-center justify-between mb-4",children:[(0,r.jsx)(f,{open:d,setOpen:c,username:x.nickname,initInviteCode:x.invite_code,updateInviteCode:b,loading:h}),(0,r.jsxs)("div",{className:"flex items-center gap-2",children:[(0,r.jsx)("span",{className:"text-3xl font-bold",children:x.invite_code||"NOT SET"}),(0,r.jsx)(l.default,{name:"RiEditLine",className:"text-primary text-xl cursor-pointer",onClick:()=>c(!0)})]}),x.invite_code&&(0,r.jsx)(o.CopyToClipboard,{text:"".concat("http://localhost:3000","/i/").concat(null==x?void 0:x.invite_code),onCopy:()=>m.oR.success("copied"),children:(0,r.jsx)(n.$,{size:"sm",children:a("my_invites.copy_invite_link")})})]}),(0,r.jsx)("p",{className:"text-sm text-gray-500",children:a("my_invites.invite_tip")})]}),(0,r.jsxs)(i.Zp,{className:"flex-1 p-6",children:[(0,r.jsx)("div",{className:"flex justify-between items-end mb-8",children:(0,r.jsxs)("div",{children:[(0,r.jsx)("h2",{className:"text-sm text-gray-500 mb-4",children:a("my_invites.invite_balance")}),(0,r.jsxs)("p",{className:"text-4xl font-bold",children:["$",t.total_reward/100]})]})}),(0,r.jsxs)("div",{className:"grid grid-cols-4 gap-4",children:[(0,r.jsxs)("div",{children:[(0,r.jsx)("p",{className:"text-2xl font-bold",children:t.total_invited}),(0,r.jsx)("p",{className:"text-sm text-gray-500",children:a("my_invites.total_invite_count")})]}),(0,r.jsxs)("div",{children:[(0,r.jsx)("p",{className:"text-2xl font-bold",children:t.total_paid}),(0,r.jsx)("p",{className:"text-sm text-gray-500",children:a("my_invites.total_paid_count")})]}),(0,r.jsxs)("div",{children:[(0,r.jsxs)("p",{className:"text-2xl font-bold",children:["$",t.total_reward/100]}),(0,r.jsx)("p",{className:"text-sm text-gray-500",children:a("my_invites.total_award_amount")})]})]})]})]})}},40528:(e,t,a)=>{"use strict";a.r(t),a.d(t,{default:()=>d});var r=a(16261),i=a(95361),s=a(93694),n=a(98842),o=a(69507);let l={RiChatSmile3Line:s.Bgv,RiImageLine:s.fsL,RiVideoLine:s.xi0,RiMicLine:s.RQr,RiMoneyDollarCircleLine:s.AN5,RiArrowRightUpLine:s.SJ3,RiFlashlightFill:s.bwM,RiEyeLine:s.tLq,RiCpuLine:s.y_v,RiUserSmileLine:s.VNl,RiFlashlightLine:s.uEe,RiStarLine:s.WN7,RiPaletteLine:s.LrS,RiRocketLine:s.QWc,RiVoiceprintLine:s.Dcp,RiExchangeLine:s.Lcj,RiTwitterXFill:s.ase,RiGithubFill:s.sAW,RiDiscordFill:s.r53,RiMailLine:s.R0Y,FaRegHeart:n.sOK,GoThumbsup:o.VZG,GoArrowUpRight:o.zny},d=(0,i.memo)(e=>{let{name:t,className:a,onClick:i,...s}=e,n=l[t];return n?(0,r.jsx)(n,{className:a,onClick:i,style:{cursor:i?"pointer":"default"},...s}):null})},61063:(e,t,a)=>{"use strict";a.d(t,{cn:()=>s});var r=a(87786),i=a(34835);function s(){for(var e=arguments.length,t=Array(e),a=0;a<e;a++)t[a]=arguments[a];return(0,i.QP)((0,r.$)(t))}},62184:(e,t,a)=>{"use strict";a.d(t,{Cf:()=>u,Es:()=>m,L3:()=>p,c7:()=>f,lG:()=>l,rr:()=>x});var r=a(16261),i=a(95361),s=a(21494),n=a(87605),o=a(61063);let l=s.bL;s.l9;let d=s.ZL;s.bm;let c=i.forwardRef((e,t)=>{let{className:a,...i}=e;return(0,r.jsx)(s.hJ,{ref:t,className:(0,o.cn)("fixed inset-0 z-50 bg-black/80  data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=open]:fade-in-0",a),...i})});c.displayName=s.hJ.displayName;let u=i.forwardRef((e,t)=>{let{className:a,children:i,...l}=e;return(0,r.jsxs)(d,{children:[(0,r.jsx)(c,{}),(0,r.jsxs)(s.UC,{ref:t,className:(0,o.cn)("fixed left-[50%] top-[50%] z-50 grid w-full max-w-lg translate-x-[-50%] translate-y-[-50%] gap-4 border bg-background p-6 shadow-lg duration-200 data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=open]:fade-in-0 data-[state=closed]:zoom-out-95 data-[state=open]:zoom-in-95 data-[state=closed]:slide-out-to-left-1/2 data-[state=closed]:slide-out-to-top-[48%] data-[state=open]:slide-in-from-left-1/2 data-[state=open]:slide-in-from-top-[48%] sm:rounded-lg",a),...l,children:[i,(0,r.jsxs)(s.bm,{className:"absolute right-4 top-4 rounded-sm opacity-70 ring-offset-background transition-opacity hover:opacity-100 focus:outline-hidden focus:ring-2 focus:ring-ring focus:ring-offset-2 disabled:pointer-events-none data-[state=open]:bg-accent data-[state=open]:text-muted-foreground",children:[(0,r.jsx)(n.A,{className:"h-4 w-4"}),(0,r.jsx)("span",{className:"sr-only",children:"Close"})]})]})]})});u.displayName=s.UC.displayName;let f=e=>{let{className:t,...a}=e;return(0,r.jsx)("div",{className:(0,o.cn)("flex flex-col space-y-1.5 text-center sm:text-left",t),...a})};f.displayName="DialogHeader";let m=e=>{let{className:t,...a}=e;return(0,r.jsx)("div",{className:(0,o.cn)("flex flex-col-reverse sm:flex-row sm:justify-end sm:space-x-2",t),...a})};m.displayName="DialogFooter";let p=i.forwardRef((e,t)=>{let{className:a,...i}=e;return(0,r.jsx)(s.hE,{ref:t,className:(0,o.cn)("text-lg font-semibold leading-none tracking-tight",a),...i})});p.displayName=s.hE.displayName;let x=i.forwardRef((e,t)=>{let{className:a,...i}=e;return(0,r.jsx)(s.VY,{ref:t,className:(0,o.cn)("text-sm text-muted-foreground",a),...i})});x.displayName=s.VY.displayName},63076:(e,t,a)=>{"use strict";a.d(t,{Table:()=>s,TableBody:()=>o,TableCell:()=>c,TableHead:()=>d,TableHeader:()=>n,TableRow:()=>l});var r=a(16261);a(95361);var i=a(61063);function s(e){let{className:t,...a}=e;return(0,r.jsx)("div",{"data-slot":"table-container",className:"relative w-full overflow-x-auto",children:(0,r.jsx)("table",{"data-slot":"table",className:(0,i.cn)("w-full caption-bottom text-sm",t),...a})})}function n(e){let{className:t,...a}=e;return(0,r.jsx)("thead",{"data-slot":"table-header",className:(0,i.cn)("[&_tr]:border-b",t),...a})}function o(e){let{className:t,...a}=e;return(0,r.jsx)("tbody",{"data-slot":"table-body",className:(0,i.cn)("[&_tr:last-child]:border-0",t),...a})}function l(e){let{className:t,...a}=e;return(0,r.jsx)("tr",{"data-slot":"table-row",className:(0,i.cn)("hover:bg-muted/50 data-[state=selected]:bg-muted border-b transition-colors",t),...a})}function d(e){let{className:t,...a}=e;return(0,r.jsx)("th",{"data-slot":"table-head",className:(0,i.cn)("text-foreground h-10 px-2 text-left align-middle font-medium whitespace-nowrap [&:has([role=checkbox])]:pr-0 [&>[role=checkbox]]:translate-y-[2px]",t),...a})}function c(e){let{className:t,...a}=e;return(0,r.jsx)("td",{"data-slot":"table-cell",className:(0,i.cn)("p-2 align-middle whitespace-nowrap [&:has([role=checkbox])]:pr-0 [&>[role=checkbox]]:translate-y-[2px]",t),...a})}},63843:(e,t,a)=>{"use strict";a.d(t,{lg:()=>r});let r=()=>Date.parse(new Date().toUTCString())/1e3},81828:(e,t,a)=>{"use strict";a.d(t,{p:()=>s});var r=a(16261);a(95361);var i=a(61063);function s(e){let{className:t,type:a,...s}=e;return(0,r.jsx)("input",{type:a,"data-slot":"input",className:(0,i.cn)("file:text-foreground placeholder:text-muted-foreground selection:bg-primary selection:text-primary-foreground dark:bg-input/30 border-input flex h-9 w-full min-w-0 rounded-md border bg-transparent px-3 py-1 text-base shadow-xs transition-[color,box-shadow] outline-none file:inline-flex file:h-7 file:border-0 file:bg-transparent file:text-sm file:font-medium disabled:pointer-events-none disabled:cursor-not-allowed disabled:opacity-50 md:text-sm","focus-visible:border-ring focus-visible:ring-ring/50 focus-visible:ring-[3px]","aria-invalid:ring-destructive/20 dark:aria-invalid:ring-destructive/40 aria-invalid:border-destructive",t),...s})}},82888:(e,t,a)=>{"use strict";a.d(t,{$:()=>l,r:()=>o});var r=a(16261);a(95361);var i=a(76012),s=a(20604),n=a(61063);let o=(0,s.F)("inline-flex items-center justify-center gap-2 whitespace-nowrap rounded-lg text-sm font-medium transition-all duration-200 disabled:pointer-events-none disabled:opacity-50 [&_svg]:pointer-events-none [&_svg:not([class*='size-'])]:size-4 shrink-0 [&_svg]:shrink-0 outline-none focus-visible:border-ring focus-visible:ring-ring/50 focus-visible:ring-[3px] aria-invalid:ring-destructive/20 dark:aria-invalid:ring-destructive/40 aria-invalid:border-destructive",{variants:{variant:{default:"bg-gradient-to-r from-primary to-accent text-primary-foreground shadow-md hover:shadow-lg hover:scale-[1.02] active:scale-[0.98]",destructive:"bg-destructive text-destructive-foreground shadow-md hover:bg-destructive/90 hover:shadow-lg hover:scale-[1.02] active:scale-[0.98] focus-visible:ring-destructive/20 dark:focus-visible:ring-destructive/40",outline:"border-2 border-primary/20 bg-background/50 backdrop-blur-sm shadow-sm hover:bg-accent/50 hover:text-accent-foreground hover:border-primary/40 hover:shadow-md hover:scale-[1.02] active:scale-[0.98] dark:bg-input/30 dark:border-input dark:hover:bg-input/50",secondary:"bg-gradient-to-r from-secondary to-muted text-secondary-foreground shadow-sm hover:shadow-md hover:scale-[1.02] active:scale-[0.98]",ghost:"hover:bg-accent/50 hover:text-accent-foreground hover:scale-[1.02] active:scale-[0.98] dark:hover:bg-accent/30",link:"text-primary underline-offset-4 hover:underline hover:text-accent"},size:{default:"h-10 px-6 py-2 has-[>svg]:px-4",sm:"h-8 rounded-lg gap-1.5 px-4 has-[>svg]:px-3",lg:"h-12 rounded-lg px-8 has-[>svg]:px-6 text-base",icon:"size-10 rounded-full"}},defaultVariants:{variant:"default",size:"default"}});function l(e){let{className:t,variant:a,size:s,asChild:l=!1,...d}=e,c=l?i.DX:"button";return(0,r.jsx)(c,{"data-slot":"button",className:(0,n.cn)(o({variant:a,size:s,className:t})),...d})}},88999:(e,t,a)=>{"use strict";a.d(t,{wD:()=>r});let r={Theme:"THEME",InviteCode:"INVITE_CODE",LanguagePreference:"LANGUAGE_PREFERENCE",LanguageSwitchAsked:"LANGUAGE_SWITCH_ASKED"}}},e=>{var t=t=>e(e.s=t);e.O(0,[9242,2912,8901,8548,2117,6002,2482,789,7486,1399,5325,2130,9110,8886,7358],()=>t(6656)),_N_E=e.O()}]);