"use strict";(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[1204],{2005:(t,e,n)=>{n.d(e,{A:()=>D});var r=n(95361);function i(t){return"[object Object]"===Object.prototype.toString.call(t)||Array.isArray(t)}function o(t,e){let n=Object.keys(t),r=Object.keys(e);return n.length===r.length&&JSON.stringify(Object.keys(t.breakpoints||{}))===JSON.stringify(Object.keys(e.breakpoints||{}))&&n.every(n=>{let r=t[n],u=e[n];return"function"==typeof r?`${r}`==`${u}`:i(r)&&i(u)?o(r,u):r===u})}function u(t){return t.concat().sort((t,e)=>t.name>e.name?1:-1).map(t=>t.options)}function a(t){return"number"==typeof t}function c(t){return"string"==typeof t}function l(t){return"boolean"==typeof t}function s(t){return"[object Object]"===Object.prototype.toString.call(t)}function f(t){return Math.abs(t)}function d(t){return Math.sign(t)}function p(t){return y(t).map(Number)}function g(t){return t[m(t)]}function m(t){return Math.max(0,t.length-1)}function h(t,e=0){return Array.from(Array(t),(t,n)=>e+n)}function y(t){return Object.keys(t)}function b(t,e){return void 0!==e.MouseEvent&&t instanceof e.MouseEvent}function v(){let t=[],e={add:function(n,r,i,o={passive:!0}){let u;return"addEventListener"in n?(n.addEventListener(r,i,o),u=()=>n.removeEventListener(r,i,o)):(n.addListener(i),u=()=>n.removeListener(i)),t.push(u),e},clear:function(){t=t.filter(t=>t())}};return e}function x(t=0,e=0){let n=f(t-e);function r(n){return n<t||n>e}return{length:n,max:e,min:t,constrain:function(n){return r(n)?n<t?t:e:n},reachedAny:r,reachedMax:function(t){return t>e},reachedMin:function(e){return e<t},removeOffset:function(t){return n?t-n*Math.ceil((t-e)/n):t}}}function w(t){let e=t;function n(t){return a(t)?t:t.get()}return{get:function(){return e},set:function(t){e=n(t)},add:function(t){e+=n(t)},subtract:function(t){e-=n(t)}}}function E(t,e){let n="x"===t.scroll?function(t){return`translate3d(${t}px,0px,0px)`}:function(t){return`translate3d(0px,${t}px,0px)`},r=e.style,i=null,o=!1;return{clear:function(){o||(r.transform="",e.getAttribute("style")||e.removeAttribute("style"))},to:function(e){if(o)return;let u=Math.round(100*t.direction(e))/100;u!==i&&(r.transform=n(u),i=u)},toggleActive:function(t){o=!t}}}let S={align:"center",axis:"x",container:null,slides:null,containScroll:"trimSnaps",direction:"ltr",slidesToScroll:1,inViewThreshold:0,breakpoints:{},dragFree:!1,dragThreshold:10,loop:!1,skipSnaps:!1,duration:25,startIndex:0,active:!0,watchDrag:!0,watchResize:!0,watchSlides:!0,watchFocus:!0};function A(t,e,n){let r,i,o,u,D;let M=t.ownerDocument,k=M.defaultView,L=function(t){function e(t,e){return function t(e,n){return[e,n].reduce((e,n)=>(y(n).forEach(r=>{let i=e[r],o=n[r],u=s(i)&&s(o);e[r]=u?t(i,o):o}),e),{})}(t,e||{})}return{mergeOptions:e,optionsAtMedia:function(n){let r=n.breakpoints||{},i=y(r).filter(e=>t.matchMedia(e).matches).map(t=>r[t]).reduce((t,n)=>e(t,n),{});return e(n,i)},optionsMediaQueries:function(e){return e.map(t=>y(t.breakpoints||{})).reduce((t,e)=>t.concat(e),[]).map(t.matchMedia)}}}(k),I=(D=[],{init:function(t,e){return(D=e.filter(({options:t})=>!1!==L.optionsAtMedia(t).active)).forEach(e=>e.init(t,L)),e.reduce((t,e)=>Object.assign(t,{[e.name]:e}),{})},destroy:function(){D=D.filter(t=>t.destroy())}}),O=v(),F=function(){let t,e={},n={init:function(e){t=e},emit:function(r){return(e[r]||[]).forEach(e=>e(t,r)),n},off:function(t,r){return e[t]=(e[t]||[]).filter(t=>t!==r),n},on:function(t,r){return e[t]=(e[t]||[]).concat([r]),n},clear:function(){e={}}};return n}(),{mergeOptions:j,optionsAtMedia:T,optionsMediaQueries:N}=L,{on:C,off:P,emit:R}=F,V=!1,z=j(S,A.globalOptions),H=j(z),B=[];function q(e,n){!V&&(H=T(z=j(z,e)),B=n||B,function(){let{container:e,slides:n}=H;o=(c(e)?t.querySelector(e):e)||t.children[0];let r=c(n)?o.querySelectorAll(n):n;u=[].slice.call(r||o.children)}(),r=function e(n){let r=function(t,e,n,r,i,o,u){let s,S;let{align:A,axis:D,direction:M,startIndex:k,loop:L,duration:I,dragFree:O,dragThreshold:F,inViewThreshold:j,slidesToScroll:T,skipSnaps:N,containScroll:C,watchResize:P,watchSlides:R,watchDrag:V,watchFocus:z}=o,H={measure:function(t){let{offsetTop:e,offsetLeft:n,offsetWidth:r,offsetHeight:i}=t;return{top:e,right:n+r,bottom:e+i,left:n,width:r,height:i}}},B=H.measure(e),q=n.map(H.measure),U=function(t,e){let n="rtl"===e,r="y"===t,i=!r&&n?-1:1;return{scroll:r?"y":"x",cross:r?"x":"y",startEdge:r?"top":n?"right":"left",endEdge:r?"bottom":n?"left":"right",measureSize:function(t){let{height:e,width:n}=t;return r?e:n},direction:function(t){return t*i}}}(D,M),$=U.measureSize(B),G={measure:function(t){return t/100*$}},_=function(t,e){let n={start:function(){return 0},center:function(t){return(e-t)/2},end:function(t){return e-t}};return{measure:function(r,i){return c(t)?n[t](r):t(e,r,i)}}}(A,$),K=!L&&!!C,{slideSizes:J,slideSizesWithGaps:X,startGap:Q,endGap:Y}=function(t,e,n,r,i,o){let{measureSize:u,startEdge:a,endEdge:c}=t,l=n[0]&&i,s=function(){if(!l)return 0;let t=n[0];return f(e[a]-t[a])}(),d=l?parseFloat(o.getComputedStyle(g(r)).getPropertyValue(`margin-${c}`)):0,p=n.map(u),h=n.map((t,e,n)=>{let r=e===m(n);return e?r?p[e]+d:n[e+1][a]-t[a]:p[e]+s}).map(f);return{slideSizes:p,slideSizesWithGaps:h,startGap:s,endGap:d}}(U,B,q,n,L||!!C,i),W=function(t,e,n,r,i,o,u,c,l){let{startEdge:s,endEdge:d,direction:h}=t,y=a(n);return{groupSlides:function(t){return y?p(t).filter(t=>t%n==0).map(e=>t.slice(e,e+n)):t.length?p(t).reduce((n,a,l)=>{let p=g(n)||0,y=a===m(t),b=i[s]-o[p][s],v=i[s]-o[a][d],x=r||0!==p?0:h(u),w=f(v-(!r&&y?h(c):0)-(b+x));return l&&w>e+2&&n.push(a),y&&n.push(t.length),n},[]).map((e,n,r)=>{let i=Math.max(r[n-1]||0);return t.slice(i,e)}):[]}}}(U,$,T,L,B,q,Q,Y,0),{snaps:Z,snapsAligned:tt}=function(t,e,n,r,i){let{startEdge:o,endEdge:u}=t,{groupSlides:a}=i,c=a(r).map(t=>g(t)[u]-t[0][o]).map(f).map(e.measure),l=r.map(t=>n[o]-t[o]).map(t=>-f(t)),s=a(l).map(t=>t[0]).map((t,e)=>t+c[e]);return{snaps:l,snapsAligned:s}}(U,_,B,q,W),te=-g(Z)+g(X),{snapsContained:tn,scrollContainLimit:tr}=function(t,e,n,r,i){let o=x(-e+t,0),u=n.map((t,e)=>{let{min:r,max:i}=o,u=o.constrain(t),a=e===m(n);return e?a||function(t,e){return 1>=f(t-e)}(r,u)?r:function(t,e){return 1>=f(t-e)}(i,u)?i:u:i}).map(t=>parseFloat(t.toFixed(3))),a=function(){let t=u[0],e=g(u);return x(u.lastIndexOf(t),u.indexOf(e)+1)}();function c(t,e){return 1>=f(t-e)}return{snapsContained:function(){if(e<=t+2)return[o.max];if("keepSnaps"===r)return u;let{min:n,max:i}=a;return u.slice(n,i)}(),scrollContainLimit:a}}($,te,tt,C,0),ti=K?tn:tt,{limit:to}=function(t,e,n){let r=e[0];return{limit:x(n?r-t:g(e),r)}}(te,ti,L),tu=function t(e,n,r){let{constrain:i}=x(0,e),o=e+1,u=a(n);function a(t){return r?f((o+t)%o):i(t)}function c(){return t(e,u,r)}let l={get:function(){return u},set:function(t){return u=a(t),l},add:function(t){return c().set(u+t)},clone:c};return l}(m(ti),k,L),ta=tu.clone(),tc=p(n),tl=({dragHandler:t,scrollBody:e,scrollBounds:n,options:{loop:r}})=>{r||n.constrain(t.pointerDown()),e.seek()},ts=({scrollBody:t,translate:e,location:n,offsetLocation:r,previousLocation:i,scrollLooper:o,slideLooper:u,dragHandler:a,animation:c,eventHandler:l,scrollBounds:s,options:{loop:f}},d)=>{let p=t.settled(),g=!s.shouldConstrain(),m=f?p:p&&g,h=m&&!a.pointerDown();h&&c.stop();let y=n.get()*d+i.get()*(1-d);r.set(y),f&&(o.loop(t.direction()),u.loop()),e.to(r.get()),h&&l.emit("settle"),m||l.emit("scroll")},tf=function(t,e,n,r){let i=v(),o=1e3/60,u=null,a=0,c=0;function l(t){if(!c)return;u||(u=t,n(),n());let i=t-u;for(u=t,a+=i;a>=o;)n(),a-=o;r(a/o),c&&(c=e.requestAnimationFrame(l))}function s(){e.cancelAnimationFrame(c),u=null,a=0,c=0}return{init:function(){i.add(t,"visibilitychange",()=>{t.hidden&&(u=null,a=0)})},destroy:function(){s(),i.clear()},start:function(){c||(c=e.requestAnimationFrame(l))},stop:s,update:n,render:r}}(r,i,()=>tl(tD),t=>ts(tD,t)),td=ti[tu.get()],tp=w(td),tg=w(td),tm=w(td),th=w(td),ty=function(t,e,n,r,i,o){let u=0,a=0,c=i,l=.68,s=t.get(),p=0;function g(t){return c=t,h}function m(t){return l=t,h}let h={direction:function(){return a},duration:function(){return c},velocity:function(){return u},seek:function(){let e=r.get()-t.get(),i=0;return c?(n.set(t),u+=e/c,u*=l,s+=u,t.add(u),i=s-p):(u=0,n.set(r),t.set(r),i=e),a=d(i),p=s,h},settled:function(){return .001>f(r.get()-e.get())},useBaseFriction:function(){return m(.68)},useBaseDuration:function(){return g(i)},useFriction:m,useDuration:g};return h}(tp,tm,tg,th,I,.68),tb=function(t,e,n,r,i){let{reachedAny:o,removeOffset:u,constrain:a}=r;function c(t){return t.concat().sort((t,e)=>f(t)-f(e))[0]}function l(e,r){let i=[e,e+n,e-n];if(!t)return e;if(!r)return c(i);let o=i.filter(t=>d(t)===r);return o.length?c(o):g(i)-n}return{byDistance:function(n,r){let c=i.get()+n,{index:s,distance:d}=function(n){let r=t?u(n):a(n),{index:i}=e.map((t,e)=>({diff:l(t-r,0),index:e})).sort((t,e)=>f(t.diff)-f(e.diff))[0];return{index:i,distance:r}}(c),p=!t&&o(c);if(!r||p)return{index:s,distance:n};let g=n+l(e[s]-d,0);return{index:s,distance:g}},byIndex:function(t,n){let r=l(e[t]-i.get(),n);return{index:t,distance:r}},shortcut:l}}(L,ti,te,to,th),tv=function(t,e,n,r,i,o,u){function a(i){let a=i.distance,c=i.index!==e.get();o.add(a),a&&(r.duration()?t.start():(t.update(),t.render(1),t.update())),c&&(n.set(e.get()),e.set(i.index),u.emit("select"))}return{distance:function(t,e){a(i.byDistance(t,e))},index:function(t,n){let r=e.clone().set(t);a(i.byIndex(r.get(),n))}}}(tf,tu,ta,ty,tb,th,u),tx=function(t){let{max:e,length:n}=t;return{get:function(t){return n?-((t-e)/n):0}}}(to),tw=v(),tE=function(t,e,n,r){let i;let o={},u=null,a=null,c=!1;return{init:function(){i=new IntersectionObserver(t=>{c||(t.forEach(t=>{o[e.indexOf(t.target)]=t}),u=null,a=null,n.emit("slidesInView"))},{root:t.parentElement,threshold:r}),e.forEach(t=>i.observe(t))},destroy:function(){i&&i.disconnect(),c=!0},get:function(t=!0){if(t&&u)return u;if(!t&&a)return a;let e=y(o).reduce((e,n)=>{let r=parseInt(n),{isIntersecting:i}=o[r];return(t&&i||!t&&!i)&&e.push(r),e},[]);return t&&(u=e),t||(a=e),e}}}(e,n,u,j),{slideRegistry:tS}=function(t,e,n,r,i,o){let{groupSlides:u}=i,{min:a,max:c}=r;return{slideRegistry:function(){let r=u(o);return 1===n.length?[o]:t&&"keepSnaps"!==e?r.slice(a,c).map((t,e,n)=>{let r=e===m(n);return e?r?h(m(o)-g(n)[0]+1,g(n)[0]):t:h(g(n[0])+1)}):r}()}}(K,C,ti,tr,W,tc),tA=function(t,e,n,r,i,o,u,c){let s={passive:!0,capture:!0},f=0;function d(t){"Tab"===t.code&&(f=new Date().getTime())}return{init:function(p){c&&(o.add(document,"keydown",d,!1),e.forEach((e,d)=>{o.add(e,"focus",e=>{(l(c)||c(p,e))&&function(e){if(new Date().getTime()-f>10)return;u.emit("slideFocusStart"),t.scrollLeft=0;let o=n.findIndex(t=>t.includes(e));a(o)&&(i.useDuration(0),r.index(o,0),u.emit("slideFocus"))}(d)},s)}))}}}(t,n,tS,tv,ty,tw,u,z),tD={ownerDocument:r,ownerWindow:i,eventHandler:u,containerRect:B,slideRects:q,animation:tf,axis:U,dragHandler:function(t,e,n,r,i,o,u,a,c,s,p,g,m,h,y,w,E,S,A){let{cross:D,direction:M}=t,k=["INPUT","SELECT","TEXTAREA"],L={passive:!1},I=v(),O=v(),F=x(50,225).constrain(h.measure(20)),j={mouse:300,touch:400},T={mouse:500,touch:600},N=y?43:25,C=!1,P=0,R=0,V=!1,z=!1,H=!1,B=!1;function q(t){if(!b(t,r)&&t.touches.length>=2)return U(t);let e=o.readPoint(t),n=o.readPoint(t,D),u=f(e-P),c=f(n-R);if(!z&&!B&&(!t.cancelable||!(z=u>c)))return U(t);let l=o.pointerMove(t);u>w&&(H=!0),s.useFriction(.3).useDuration(.75),a.start(),i.add(M(l)),t.preventDefault()}function U(t){let e=p.byDistance(0,!1).index!==g.get(),n=o.pointerUp(t)*(y?T:j)[B?"mouse":"touch"],r=function(t,e){let n=g.add(-1*d(t)),r=p.byDistance(t,!y).distance;return y||f(t)<F?r:E&&e?.5*r:p.byIndex(n.get(),0).distance}(M(n),e),i=function(t,e){var n,r;if(0===t||0===e||f(t)<=f(e))return 0;let i=(n=f(t),r=f(e),f(n-r));return f(i/t)}(n,r);z=!1,V=!1,O.clear(),s.useDuration(N-10*i).useFriction(.68+i/50),c.distance(r,!y),B=!1,m.emit("pointerUp")}function $(t){H&&(t.stopPropagation(),t.preventDefault(),H=!1)}return{init:function(t){A&&I.add(e,"dragstart",t=>t.preventDefault(),L).add(e,"touchmove",()=>void 0,L).add(e,"touchend",()=>void 0).add(e,"touchstart",a).add(e,"mousedown",a).add(e,"touchcancel",U).add(e,"contextmenu",U).add(e,"click",$,!0);function a(a){(l(A)||A(t,a))&&function(t){let a=b(t,r);B=a,H=y&&a&&!t.buttons&&C,C=f(i.get()-u.get())>=2,(!a||0===t.button)&&!function(t){let e=t.nodeName||"";return k.includes(e)}(t.target)&&(V=!0,o.pointerDown(t),s.useFriction(0).useDuration(0),i.set(u),function(){let t=B?n:e;O.add(t,"touchmove",q,L).add(t,"touchend",U).add(t,"mousemove",q,L).add(t,"mouseup",U)}(),P=o.readPoint(t),R=o.readPoint(t,D),m.emit("pointerDown"))}(a)}},destroy:function(){I.clear(),O.clear()},pointerDown:function(){return V}}}(U,t,r,i,th,function(t,e){let n,r;function i(t){return t.timeStamp}function o(n,r){let i=r||t.scroll,o=`client${"x"===i?"X":"Y"}`;return(b(n,e)?n:n.touches[0])[o]}return{pointerDown:function(t){return n=t,r=t,o(t)},pointerMove:function(t){let e=o(t)-o(r),u=i(t)-i(n)>170;return r=t,u&&(n=t),e},pointerUp:function(t){if(!n||!r)return 0;let e=o(r)-o(n),u=i(t)-i(n),a=i(t)-i(r)>170,c=e/u;return u&&!a&&f(c)>.1?c:0},readPoint:o}}(U,i),tp,tf,tv,ty,tb,tu,u,G,O,F,N,0,V),eventStore:tw,percentOfView:G,index:tu,indexPrevious:ta,limit:to,location:tp,offsetLocation:tm,previousLocation:tg,options:o,resizeHandler:function(t,e,n,r,i,o,u){let a,c;let s=[t].concat(r),d=[],p=!1;function g(t){return i.measureSize(u.measure(t))}return{init:function(i){o&&(c=g(t),d=r.map(g),a=new ResizeObserver(n=>{(l(o)||o(i,n))&&function(n){for(let o of n){if(p)return;let n=o.target===t,u=r.indexOf(o.target),a=n?c:d[u];if(f(g(n?t:r[u])-a)>=.5){i.reInit(),e.emit("resize");break}}}(n)}),n.requestAnimationFrame(()=>{s.forEach(t=>a.observe(t))}))},destroy:function(){p=!0,a&&a.disconnect()}}}(e,u,i,n,U,P,H),scrollBody:ty,scrollBounds:function(t,e,n,r,i){let o=i.measure(10),u=i.measure(50),a=x(.1,.99),c=!1;function l(){return!!(!c&&t.reachedAny(n.get())&&t.reachedAny(e.get()))}return{shouldConstrain:l,constrain:function(i){if(!l())return;let c=t.reachedMin(e.get())?"min":"max",s=f(t[c]-e.get()),d=n.get()-e.get(),p=a.constrain(s/u);n.subtract(d*p),!i&&f(d)<o&&(n.set(t.constrain(n.get())),r.useDuration(25).useBaseFriction())},toggleActive:function(t){c=!t}}}(to,tm,th,ty,G),scrollLooper:function(t,e,n,r){let{reachedMin:i,reachedMax:o}=x(e.min+.1,e.max+.1);return{loop:function(e){if(!(1===e?o(n.get()):-1===e&&i(n.get())))return;let u=-1*e*t;r.forEach(t=>t.add(u))}}}(te,to,tm,[tp,tm,tg,th]),scrollProgress:tx,scrollSnapList:ti.map(tx.get),scrollSnaps:ti,scrollTarget:tb,scrollTo:tv,slideLooper:function(t,e,n,r,i,o,u,a,c){let l=p(i),s=p(i).reverse(),f=m(g(s,u[0]),n,!1).concat(m(g(l,e-u[0]-1),-n,!0));function d(t,e){return t.reduce((t,e)=>t-i[e],e)}function g(t,e){return t.reduce((t,n)=>d(t,e)>0?t.concat([n]):t,[])}function m(i,u,l){let s=o.map((t,n)=>({start:t-r[n]+.5+u,end:t+e-.5+u}));return i.map(e=>{let r=l?0:-n,i=l?n:0,o=s[e][l?"end":"start"];return{index:e,loopPoint:o,slideLocation:w(-1),translate:E(t,c[e]),target:()=>a.get()>o?r:i}})}return{canLoop:function(){return f.every(({index:t})=>.1>=d(l.filter(e=>e!==t),e))},clear:function(){f.forEach(t=>t.translate.clear())},loop:function(){f.forEach(t=>{let{target:e,translate:n,slideLocation:r}=t,i=e();i!==r.get()&&(n.to(i),r.set(i))})},loopPoints:f}}(U,$,te,J,X,Z,ti,tm,n),slideFocus:tA,slidesHandler:(S=!1,{init:function(t){R&&(s=new MutationObserver(e=>{!S&&(l(R)||R(t,e))&&function(e){for(let n of e)if("childList"===n.type){t.reInit(),u.emit("slidesChanged");break}}(e)})).observe(e,{childList:!0})},destroy:function(){s&&s.disconnect(),S=!0}}),slidesInView:tE,slideIndexes:tc,slideRegistry:tS,slidesToScroll:W,target:th,translate:E(U,e)};return tD}(t,o,u,M,k,n,F);return n.loop&&!r.slideLooper.canLoop()?e(Object.assign({},n,{loop:!1})):r}(H),N([z,...B.map(({options:t})=>t)]).forEach(t=>O.add(t,"change",U)),H.active&&(r.translate.to(r.location.get()),r.animation.init(),r.slidesInView.init(),r.slideFocus.init(K),r.eventHandler.init(K),r.resizeHandler.init(K),r.slidesHandler.init(K),r.options.loop&&r.slideLooper.loop(),o.offsetParent&&u.length&&r.dragHandler.init(K),i=I.init(K,B)))}function U(t,e){let n=_();$(),q(j({startIndex:n},t),e),F.emit("reInit")}function $(){r.dragHandler.destroy(),r.eventStore.clear(),r.translate.clear(),r.slideLooper.clear(),r.resizeHandler.destroy(),r.slidesHandler.destroy(),r.slidesInView.destroy(),r.animation.destroy(),I.destroy(),O.clear()}function G(t,e,n){H.active&&!V&&(r.scrollBody.useBaseFriction().useDuration(!0===e?0:H.duration),r.scrollTo.index(t,n||0))}function _(){return r.index.get()}let K={canScrollNext:function(){return r.index.add(1).get()!==_()},canScrollPrev:function(){return r.index.add(-1).get()!==_()},containerNode:function(){return o},internalEngine:function(){return r},destroy:function(){V||(V=!0,O.clear(),$(),F.emit("destroy"),F.clear())},off:P,on:C,emit:R,plugins:function(){return i},previousScrollSnap:function(){return r.indexPrevious.get()},reInit:U,rootNode:function(){return t},scrollNext:function(t){G(r.index.add(1).get(),t,-1)},scrollPrev:function(t){G(r.index.add(-1).get(),t,1)},scrollProgress:function(){return r.scrollProgress.get(r.offsetLocation.get())},scrollSnapList:function(){return r.scrollSnapList},scrollTo:G,selectedScrollSnap:_,slideNodes:function(){return u},slidesInView:function(){return r.slidesInView.get()},slidesNotInView:function(){return r.slidesInView.get(!1)}};return q(e,n),setTimeout(()=>F.emit("init"),0),K}function D(t={},e=[]){let n=(0,r.useRef)(t),i=(0,r.useRef)(e),[a,c]=(0,r.useState)(),[l,s]=(0,r.useState)(),f=(0,r.useCallback)(()=>{a&&a.reInit(n.current,i.current)},[a]);return(0,r.useEffect)(()=>{o(n.current,t)||(n.current=t,f())},[t,f]),(0,r.useEffect)(()=>{!function(t,e){if(t.length!==e.length)return!1;let n=u(t),r=u(e);return n.every((t,e)=>o(t,r[e]))}(i.current,e)&&(i.current=e,f())},[e,f]),(0,r.useEffect)(()=>{if("undefined"!=typeof window&&window.document&&window.document.createElement&&l){A.globalOptions=D.globalOptions;let t=A(l,n.current,i.current);return c(t),()=>t.destroy()}c(void 0)},[l,c]),[s,a]}A.globalOptions=void 0,D.globalOptions=void 0},8275:(t,e,n)=>{n.d(e,{A:()=>r});let r=(0,n(1316).A)("ArrowRight",[["path",{d:"M5 12h14",key:"1ays0h"}],["path",{d:"m12 5 7 7-7 7",key:"xquz4c"}]])},17002:(t,e,n)=>{n.d(e,{A:()=>r});let r=(0,n(1316).A)("Copy",[["rect",{width:"14",height:"14",x:"8",y:"8",rx:"2",ry:"2",key:"17jyea"}],["path",{d:"M4 16c-1.1 0-2-.9-2-2V4c0-1.1.9-2 2-2h10c1.1 0 2 .9 2 2",key:"zix9uf"}]])},22338:(t,e,n)=>{n.d(e,{A:()=>r});let r=(0,n(1316).A)("ArrowLeft",[["path",{d:"m12 19-7-7 7-7",key:"1l729n"}],["path",{d:"M19 12H5",key:"x3x0zl"}]])},58070:(t,e,n)=>{n.d(e,{B8:()=>I,Tabs:()=>v,TabsList:()=>w,TabsTrigger:()=>S,UC:()=>F,bL:()=>L,l9:()=>O});var r=n(95361),i=n(75030),o=n(22408),u=n(88295),a=n(12177),c=n(72637),l=n(37651),s=n(4471),f=n(25747),d=n(16261),p="Tabs",[g,m]=(0,o.A)(p,[u.RG]),h=(0,u.RG)(),[y,b]=g(p),v=r.forwardRef((t,e)=>{let{__scopeTabs:n,value:r,onValueChange:i,defaultValue:o,orientation:u="horizontal",dir:a,activationMode:g="automatic",...m}=t,h=(0,l.jH)(a),[b,v]=(0,s.i)({prop:r,onChange:i,defaultProp:null!=o?o:"",caller:p});return(0,d.jsx)(y,{scope:n,baseId:(0,f.B)(),value:b,onValueChange:v,orientation:u,dir:h,activationMode:g,children:(0,d.jsx)(c.sG.div,{dir:h,"data-orientation":u,...m,ref:e})})});v.displayName=p;var x="TabsList",w=r.forwardRef((t,e)=>{let{__scopeTabs:n,loop:r=!0,...i}=t,o=b(x,n),a=h(n);return(0,d.jsx)(u.bL,{asChild:!0,...a,orientation:o.orientation,dir:o.dir,loop:r,children:(0,d.jsx)(c.sG.div,{role:"tablist","aria-orientation":o.orientation,...i,ref:e})})});w.displayName=x;var E="TabsTrigger",S=r.forwardRef((t,e)=>{let{__scopeTabs:n,value:r,disabled:o=!1,...a}=t,l=b(E,n),s=h(n),f=M(l.baseId,r),p=k(l.baseId,r),g=r===l.value;return(0,d.jsx)(u.q7,{asChild:!0,...s,focusable:!o,active:g,children:(0,d.jsx)(c.sG.button,{type:"button",role:"tab","aria-selected":g,"aria-controls":p,"data-state":g?"active":"inactive","data-disabled":o?"":void 0,disabled:o,id:f,...a,ref:e,onMouseDown:(0,i.m)(t.onMouseDown,t=>{o||0!==t.button||!1!==t.ctrlKey?t.preventDefault():l.onValueChange(r)}),onKeyDown:(0,i.m)(t.onKeyDown,t=>{[" ","Enter"].includes(t.key)&&l.onValueChange(r)}),onFocus:(0,i.m)(t.onFocus,()=>{let t="manual"!==l.activationMode;g||o||!t||l.onValueChange(r)})})})});S.displayName=E;var A="TabsContent",D=r.forwardRef((t,e)=>{let{__scopeTabs:n,value:i,forceMount:o,children:u,...l}=t,s=b(A,n),f=M(s.baseId,i),p=k(s.baseId,i),g=i===s.value,m=r.useRef(g);return r.useEffect(()=>{let t=requestAnimationFrame(()=>m.current=!1);return()=>cancelAnimationFrame(t)},[]),(0,d.jsx)(a.C,{present:o||g,children:n=>{let{present:r}=n;return(0,d.jsx)(c.sG.div,{"data-state":g?"active":"inactive","data-orientation":s.orientation,role:"tabpanel","aria-labelledby":f,hidden:!r,id:p,tabIndex:0,...l,ref:e,style:{...t.style,animationDuration:m.current?"0s":void 0},children:r&&u})}})});function M(t,e){return"".concat(t,"-trigger-").concat(e)}function k(t,e){return"".concat(t,"-content-").concat(e)}D.displayName=A;var L=v,I=w,O=S,F=D},98763:(t,e,n)=>{function r(t,e,n){return Math.min(Math.max(t,e),n)}function i(t){return"number"==typeof t&&!isNaN(t)}function o(t={}){let e,n,u,a;let c=[],l=0,s=0,f=0,d=!1;function p(){y(e.selectedScrollSnap(),1)}function g(){d=!1}function m(){d=!1,l=0,s=0}function h(){let t=e.internalEngine().scrollBody.duration();s=+!t,d=!0,t||p()}function y(t,n){e.scrollSnapList().forEach((i,o)=>{let u=Math.abs(n),a=c[o],s=o===t,p=r(s?a+u:a-u,0,1);c[o]=p;let g=s&&d,m=e.previousScrollSnap();g&&(c[m]=1-p),s&&function(t,n){let{index:r,dragHandler:i,scrollSnaps:o}=e.internalEngine(),u=i.pointerDown(),a=1/(o.length-1),c=t,s=u?e.selectedScrollSnap():e.previousScrollSnap();if(u&&c===s){let t=-1*Math.sign(l);c=s,s=r.clone().set(s).add(t).get()}f=s*a+(c-s)*a*n}(t,p),function(t){let n=e.internalEngine().slideRegistry[t],{scrollSnaps:r,containerRect:i}=e.internalEngine(),o=c[t];n.forEach(n=>{let u=e.slideNodes()[n].style,a=parseFloat(o.toFixed(2)),c=a>0,l=function(t){let{axis:n}=e.internalEngine(),r=n.scroll.toUpperCase();return`translate${r}(${n.direction(t)}px)`}(c?r[t]:i.width+2);c&&(u.transform=l),u.opacity=a.toString(),u.pointerEvents=o>.5?"auto":"none",c||(u.transform=l)})}(o)})}function b(){let{dragHandler:t,index:n,scrollBody:r}=e.internalEngine(),i=e.selectedScrollSnap();if(!t.pointerDown())return i;let o=Math.sign(r.velocity()),u=Math.sign(l),a=n.clone().set(i).add(-1*o).get();return o&&u?u===o?a:i:null}function v(){let{target:t,location:r}=e.internalEngine(),o=Math.abs(t.get()-r.get())>=1,u=b(),a=!i(u);return!function(t){let{dragHandler:r,scrollBody:o}=t.internalEngine(),u=r.pointerDown(),a=o.velocity(),f=o.duration(),d=b(),p=!i(d);if(u){if(!a)return;l+=a,s=Math.abs(a/n),function(t){let{scrollSnaps:n,location:r,target:o}=e.internalEngine();i(t)&&!(c[t]<.5)&&(r.set(n[t]),o.set(r))}(d)}if(!u){if(!f||p)return;s+=(1-c[d])/f,s*=.68}p||y(d,s)}(e),!a&&!o&&c[u]>.999}function x(){return f}return{name:"fade",options:t,init:function(t){let i=(e=t).selectedScrollSnap(),{scrollBody:o,containerRect:l,axis:s}=e.internalEngine();n=r(.75*s.measureSize(l),200,500),d=!1,c=e.scrollSnapList().map((t,e)=>+(e===i)),u=o.settled,a=e.scrollProgress,o.settled=v,e.scrollProgress=x,e.on("select",h).on("slideFocus",p).on("pointerDown",m).on("pointerUp",g),function(){let{translate:t,slideLooper:n}=e.internalEngine();t.clear(),t.toggleActive(!1),n.loopPoints.forEach(({translate:t})=>{t.clear(),t.toggleActive(!1)})}(),p()},destroy:function(){let{scrollBody:t}=e.internalEngine();t.settled=u,e.scrollProgress=a,e.off("select",h).off("slideFocus",p).off("pointerDown",m).off("pointerUp",g),e.slideNodes().forEach(t=>{let e=t.style;e.opacity="",e.transform="",e.pointerEvents="",t.getAttribute("style")||t.removeAttribute("style")})}}}n.d(e,{A:()=>o}),o.globalOptions=void 0}}]);