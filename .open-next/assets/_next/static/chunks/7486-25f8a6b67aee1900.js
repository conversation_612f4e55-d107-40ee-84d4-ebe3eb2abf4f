"use strict";(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[7486],{10730:(e,t,n)=>{n.d(t,{qW:()=>f});var r,o=n(95361),a=n(75030),i=n(72637),u=n(94286),c=n(21751),l=n(16261),s="dismissableLayer.update",d=o.createContext({layers:new Set,layersWithOutsidePointerEventsDisabled:new Set,branches:new Set}),f=o.forwardRef((e,t)=>{var n,f;let{disableOutsidePointerEvents:h=!1,onEscapeKeyDown:p,onPointerDownOutside:E,onFocusOutside:g,onInteractOutside:y,onDismiss:b,...w}=e,C=o.useContext(d),[S,L]=o.useState(null),k=null!==(f=null==S?void 0:S.ownerDocument)&&void 0!==f?f:null===(n=globalThis)||void 0===n?void 0:n.document,[,T]=o.useState({}),N=(0,u.s)(t,e=>L(e)),R=Array.from(C.layers),[A]=[...C.layersWithOutsidePointerEventsDisabled].slice(-1),M=R.indexOf(A),P=S?R.indexOf(S):-1,x=C.layersWithOutsidePointerEventsDisabled.size>0,D=P>=M,W=function(e){var t;let n=arguments.length>1&&void 0!==arguments[1]?arguments[1]:null===(t=globalThis)||void 0===t?void 0:t.document,r=(0,c.c)(e),a=o.useRef(!1),i=o.useRef(()=>{});return o.useEffect(()=>{let e=e=>{if(e.target&&!a.current){let t=function(){m("dismissableLayer.pointerDownOutside",r,o,{discrete:!0})},o={originalEvent:e};"touch"===e.pointerType?(n.removeEventListener("click",i.current),i.current=t,n.addEventListener("click",i.current,{once:!0})):t()}else n.removeEventListener("click",i.current);a.current=!1},t=window.setTimeout(()=>{n.addEventListener("pointerdown",e)},0);return()=>{window.clearTimeout(t),n.removeEventListener("pointerdown",e),n.removeEventListener("click",i.current)}},[n,r]),{onPointerDownCapture:()=>a.current=!0}}(e=>{let t=e.target,n=[...C.branches].some(e=>e.contains(t));!D||n||(null==E||E(e),null==y||y(e),e.defaultPrevented||null==b||b())},k),O=function(e){var t;let n=arguments.length>1&&void 0!==arguments[1]?arguments[1]:null===(t=globalThis)||void 0===t?void 0:t.document,r=(0,c.c)(e),a=o.useRef(!1);return o.useEffect(()=>{let e=e=>{e.target&&!a.current&&m("dismissableLayer.focusOutside",r,{originalEvent:e},{discrete:!1})};return n.addEventListener("focusin",e),()=>n.removeEventListener("focusin",e)},[n,r]),{onFocusCapture:()=>a.current=!0,onBlurCapture:()=>a.current=!1}}(e=>{let t=e.target;[...C.branches].some(e=>e.contains(t))||(null==g||g(e),null==y||y(e),e.defaultPrevented||null==b||b())},k);return!function(e,t=globalThis?.document){let n=(0,c.c)(e);o.useEffect(()=>{let e=e=>{"Escape"===e.key&&n(e)};return t.addEventListener("keydown",e,{capture:!0}),()=>t.removeEventListener("keydown",e,{capture:!0})},[n,t])}(e=>{P===C.layers.size-1&&(null==p||p(e),!e.defaultPrevented&&b&&(e.preventDefault(),b()))},k),o.useEffect(()=>{if(S)return h&&(0===C.layersWithOutsidePointerEventsDisabled.size&&(r=k.body.style.pointerEvents,k.body.style.pointerEvents="none"),C.layersWithOutsidePointerEventsDisabled.add(S)),C.layers.add(S),v(),()=>{h&&1===C.layersWithOutsidePointerEventsDisabled.size&&(k.body.style.pointerEvents=r)}},[S,k,h,C]),o.useEffect(()=>()=>{S&&(C.layers.delete(S),C.layersWithOutsidePointerEventsDisabled.delete(S),v())},[S,C]),o.useEffect(()=>{let e=()=>T({});return document.addEventListener(s,e),()=>document.removeEventListener(s,e)},[]),(0,l.jsx)(i.sG.div,{...w,ref:N,style:{pointerEvents:x?D?"auto":"none":void 0,...e.style},onFocusCapture:(0,a.m)(e.onFocusCapture,O.onFocusCapture),onBlurCapture:(0,a.m)(e.onBlurCapture,O.onBlurCapture),onPointerDownCapture:(0,a.m)(e.onPointerDownCapture,W.onPointerDownCapture)})});function v(){let e=new CustomEvent(s);document.dispatchEvent(e)}function m(e,t,n,r){let{discrete:o}=r,a=n.originalEvent.target,u=new CustomEvent(e,{bubbles:!1,cancelable:!0,detail:n});t&&a.addEventListener(e,t,{once:!0}),o?(0,i.hO)(a,u):a.dispatchEvent(u)}f.displayName="DismissableLayer",o.forwardRef((e,t)=>{let n=o.useContext(d),r=o.useRef(null),a=(0,u.s)(t,r);return o.useEffect(()=>{let e=r.current;if(e)return n.branches.add(e),()=>{n.branches.delete(e)}},[n.branches]),(0,l.jsx)(i.sG.div,{...e,ref:a})}).displayName="DismissableLayerBranch"},57750:(e,t,n)=>{n.d(t,{Oh:()=>a});var r=n(95361),o=0;function a(){r.useEffect(()=>{var e,t;let n=document.querySelectorAll("[data-radix-focus-guard]");return document.body.insertAdjacentElement("afterbegin",null!==(e=n[0])&&void 0!==e?e:i()),document.body.insertAdjacentElement("beforeend",null!==(t=n[1])&&void 0!==t?t:i()),o++,()=>{1===o&&document.querySelectorAll("[data-radix-focus-guard]").forEach(e=>e.remove()),o--}},[])}function i(){let e=document.createElement("span");return e.setAttribute("data-radix-focus-guard",""),e.tabIndex=0,e.style.outline="none",e.style.opacity="0",e.style.position="fixed",e.style.pointerEvents="none",e}},70200:(e,t,n)=>{n.d(t,{Z:()=>c});var r=n(95361),o=n(32196),a=n(72637),i=n(53765),u=n(16261),c=r.forwardRef((e,t)=>{var n,c;let{container:l,...s}=e,[d,f]=r.useState(!1);(0,i.N)(()=>f(!0),[]);let v=l||d&&(null===(c=globalThis)||void 0===c?void 0:null===(n=c.document)||void 0===n?void 0:n.body);return v?o.createPortal((0,u.jsx)(a.sG.div,{...s,ref:t}),v):null});c.displayName="Portal"},70908:(e,t,n)=>{n.d(t,{n:()=>d});var r=n(95361),o=n(94286),a=n(72637),i=n(21751),u=n(16261),c="focusScope.autoFocusOnMount",l="focusScope.autoFocusOnUnmount",s={bubbles:!1,cancelable:!0},d=r.forwardRef((e,t)=>{let{loop:n=!1,trapped:d=!1,onMountAutoFocus:p,onUnmountAutoFocus:E,...g}=e,[y,b]=r.useState(null),w=(0,i.c)(p),C=(0,i.c)(E),S=r.useRef(null),L=(0,o.s)(t,e=>b(e)),k=r.useRef({paused:!1,pause(){this.paused=!0},resume(){this.paused=!1}}).current;r.useEffect(()=>{if(d){let e=function(e){if(k.paused||!y)return;let t=e.target;y.contains(t)?S.current=t:m(S.current,{select:!0})},t=function(e){if(k.paused||!y)return;let t=e.relatedTarget;null===t||y.contains(t)||m(S.current,{select:!0})};document.addEventListener("focusin",e),document.addEventListener("focusout",t);let n=new MutationObserver(function(e){if(document.activeElement===document.body)for(let t of e)t.removedNodes.length>0&&m(y)});return y&&n.observe(y,{childList:!0,subtree:!0}),()=>{document.removeEventListener("focusin",e),document.removeEventListener("focusout",t),n.disconnect()}}},[d,y,k.paused]),r.useEffect(()=>{if(y){h.add(k);let e=document.activeElement;if(!y.contains(e)){let t=new CustomEvent(c,s);y.addEventListener(c,w),y.dispatchEvent(t),t.defaultPrevented||(function(e){let{select:t=!1}=arguments.length>1&&void 0!==arguments[1]?arguments[1]:{},n=document.activeElement;for(let r of e)if(m(r,{select:t}),document.activeElement!==n)return}(f(y).filter(e=>"A"!==e.tagName),{select:!0}),document.activeElement===e&&m(y))}return()=>{y.removeEventListener(c,w),setTimeout(()=>{let t=new CustomEvent(l,s);y.addEventListener(l,C),y.dispatchEvent(t),t.defaultPrevented||m(null!=e?e:document.body,{select:!0}),y.removeEventListener(l,C),h.remove(k)},0)}}},[y,w,C,k]);let T=r.useCallback(e=>{if(!n&&!d||k.paused)return;let t="Tab"===e.key&&!e.altKey&&!e.ctrlKey&&!e.metaKey,r=document.activeElement;if(t&&r){let t=e.currentTarget,[o,a]=function(e){let t=f(e);return[v(t,e),v(t.reverse(),e)]}(t);o&&a?e.shiftKey||r!==a?e.shiftKey&&r===o&&(e.preventDefault(),n&&m(a,{select:!0})):(e.preventDefault(),n&&m(o,{select:!0})):r===t&&e.preventDefault()}},[n,d,k.paused]);return(0,u.jsx)(a.sG.div,{tabIndex:-1,...g,ref:L,onKeyDown:T})});function f(e){let t=[],n=document.createTreeWalker(e,NodeFilter.SHOW_ELEMENT,{acceptNode:e=>{let t="INPUT"===e.tagName&&"hidden"===e.type;return e.disabled||e.hidden||t?NodeFilter.FILTER_SKIP:e.tabIndex>=0?NodeFilter.FILTER_ACCEPT:NodeFilter.FILTER_SKIP}});for(;n.nextNode();)t.push(n.currentNode);return t}function v(e,t){for(let n of e)if(!function(e,t){let{upTo:n}=t;if("hidden"===getComputedStyle(e).visibility)return!0;for(;e&&(void 0===n||e!==n);){if("none"===getComputedStyle(e).display)return!0;e=e.parentElement}return!1}(n,{upTo:t}))return n}function m(e){let{select:t=!1}=arguments.length>1&&void 0!==arguments[1]?arguments[1]:{};if(e&&e.focus){var n;let r=document.activeElement;e.focus({preventScroll:!0}),e!==r&&(n=e)instanceof HTMLInputElement&&"select"in n&&t&&e.select()}}d.displayName="FocusScope";var h=function(){let e=[];return{add(t){let n=e[0];t!==n&&(null==n||n.pause()),(e=p(e,t)).unshift(t)},remove(t){var n;null===(n=(e=p(e,t))[0])||void 0===n||n.resume()}}}();function p(e,t){let n=[...e],r=n.indexOf(t);return -1!==r&&n.splice(r,1),n}},91783:(e,t,n)=>{n.d(t,{Eq:()=>s});var r=function(e){return"undefined"==typeof document?null:(Array.isArray(e)?e[0]:e).ownerDocument.body},o=new WeakMap,a=new WeakMap,i={},u=0,c=function(e){return e&&(e.host||c(e.parentNode))},l=function(e,t,n,r){var l=(Array.isArray(e)?e:[e]).map(function(e){if(t.contains(e))return e;var n=c(e);return n&&t.contains(n)?n:(console.error("aria-hidden",e,"in not contained inside",t,". Doing nothing"),null)}).filter(function(e){return!!e});i[n]||(i[n]=new WeakMap);var s=i[n],d=[],f=new Set,v=new Set(l),m=function(e){!(!e||f.has(e))&&(f.add(e),m(e.parentNode))};l.forEach(m);var h=function(e){!(!e||v.has(e))&&Array.prototype.forEach.call(e.children,function(e){if(f.has(e))h(e);else try{var t=e.getAttribute(r),i=null!==t&&"false"!==t,u=(o.get(e)||0)+1,c=(s.get(e)||0)+1;o.set(e,u),s.set(e,c),d.push(e),1===u&&i&&a.set(e,!0),1===c&&e.setAttribute(n,"true"),i||e.setAttribute(r,"true")}catch(t){console.error("aria-hidden: cannot operate on ",e,t)}})};return h(t),f.clear(),u++,function(){d.forEach(function(e){var t=o.get(e)-1,i=s.get(e)-1;o.set(e,t),s.set(e,i),t||(a.has(e)||e.removeAttribute(r),a.delete(e)),i||e.removeAttribute(n)}),--u||(o=new WeakMap,o=new WeakMap,a=new WeakMap,i={})}},s=function(e,t,n){void 0===n&&(n="data-aria-hidden");var o=Array.from(Array.isArray(e)?e:[e]),a=t||r(e);return a?(o.push.apply(o,Array.from(a.querySelectorAll("[aria-live], script"))),l(o,a,n,"aria-hidden")):function(){return null}}},99126:(e,t,n)=>{n.d(t,{A:()=>z});var r,o=n(37910),a=n(95361),i="right-scroll-bar-position",u="width-before-scroll-bar";function c(e,t){return"function"==typeof e?e(t):e&&(e.current=t),e}var l="undefined"!=typeof window?a.useLayoutEffect:a.useEffect,s=new WeakMap;function d(e){return e}var f=function(e){void 0===e&&(e={});var t,n,r,a,i=(t=null,void 0===n&&(n=d),r=[],a=!1,{read:function(){if(a)throw Error("Sidecar: could not `read` from an `assigned` medium. `read` could be used only with `useMedium`.");return r.length?r[r.length-1]:null},useMedium:function(e){var t=n(e,a);return r.push(t),function(){r=r.filter(function(e){return e!==t})}},assignSyncMedium:function(e){for(a=!0;r.length;){var t=r;r=[],t.forEach(e)}r={push:function(t){return e(t)},filter:function(){return r}}},assignMedium:function(e){a=!0;var t=[];if(r.length){var n=r;r=[],n.forEach(e),t=r}var o=function(){var n=t;t=[],n.forEach(e)},i=function(){return Promise.resolve().then(o)};i(),r={push:function(e){t.push(e),i()},filter:function(e){return t=t.filter(e),r}}}});return i.options=(0,o.Cl)({async:!0,ssr:!1},e),i}(),v=function(){},m=a.forwardRef(function(e,t){var n,r,i,u,d=a.useRef(null),m=a.useState({onScrollCapture:v,onWheelCapture:v,onTouchMoveCapture:v}),h=m[0],p=m[1],E=e.forwardProps,g=e.children,y=e.className,b=e.removeScrollBar,w=e.enabled,C=e.shards,S=e.sideCar,L=e.noRelative,k=e.noIsolation,T=e.inert,N=e.allowPinchZoom,R=e.as,A=e.gapMode,M=(0,o.Tt)(e,["forwardProps","children","className","removeScrollBar","enabled","shards","sideCar","noRelative","noIsolation","inert","allowPinchZoom","as","gapMode"]),P=(n=[d,t],r=function(e){return n.forEach(function(t){return c(t,e)})},(i=(0,a.useState)(function(){return{value:null,callback:r,facade:{get current(){return i.value},set current(value){var e=i.value;e!==value&&(i.value=value,i.callback(value,e))}}}})[0]).callback=r,u=i.facade,l(function(){var e=s.get(u);if(e){var t=new Set(e),r=new Set(n),o=u.current;t.forEach(function(e){r.has(e)||c(e,null)}),r.forEach(function(e){t.has(e)||c(e,o)})}s.set(u,n)},[n]),u),x=(0,o.Cl)((0,o.Cl)({},M),h);return a.createElement(a.Fragment,null,w&&a.createElement(S,{sideCar:f,removeScrollBar:b,shards:C,noRelative:L,noIsolation:k,inert:T,setCallbacks:p,allowPinchZoom:!!N,lockRef:d,gapMode:A}),E?a.cloneElement(a.Children.only(g),(0,o.Cl)((0,o.Cl)({},x),{ref:P})):a.createElement(void 0===R?"div":R,(0,o.Cl)({},x,{className:y,ref:P}),g))});m.defaultProps={enabled:!0,removeScrollBar:!0,inert:!1},m.classNames={fullWidth:u,zeroRight:i};var h=function(e){var t=e.sideCar,n=(0,o.Tt)(e,["sideCar"]);if(!t)throw Error("Sidecar: please provide `sideCar` property to import the right car");var r=t.read();if(!r)throw Error("Sidecar medium not found");return a.createElement(r,(0,o.Cl)({},n))};h.isSideCarExport=!0;var p=function(){var e=0,t=null;return{add:function(o){if(0==e&&(t=function(){if(!document)return null;var e=document.createElement("style");e.type="text/css";var t=r||n.nc;return t&&e.setAttribute("nonce",t),e}())){var a,i;(a=t).styleSheet?a.styleSheet.cssText=o:a.appendChild(document.createTextNode(o)),i=t,(document.head||document.getElementsByTagName("head")[0]).appendChild(i)}e++},remove:function(){--e||!t||(t.parentNode&&t.parentNode.removeChild(t),t=null)}}},E=function(){var e=p();return function(t,n){a.useEffect(function(){return e.add(t),function(){e.remove()}},[t&&n])}},g=function(){var e=E();return function(t){return e(t.styles,t.dynamic),null}},y={left:0,top:0,right:0,gap:0},b=function(e){return parseInt(e||"",10)||0},w=function(e){var t=window.getComputedStyle(document.body),n=t["padding"===e?"paddingLeft":"marginLeft"],r=t["padding"===e?"paddingTop":"marginTop"],o=t["padding"===e?"paddingRight":"marginRight"];return[b(n),b(r),b(o)]},C=function(e){if(void 0===e&&(e="margin"),"undefined"==typeof window)return y;var t=w(e),n=document.documentElement.clientWidth,r=window.innerWidth;return{left:t[0],top:t[1],right:t[2],gap:Math.max(0,r-n+t[2]-t[0])}},S=g(),L="data-scroll-locked",k=function(e,t,n,r){var o=e.left,a=e.top,c=e.right,l=e.gap;return void 0===n&&(n="margin"),"\n  .".concat("with-scroll-bars-hidden"," {\n   overflow: hidden ").concat(r,";\n   padding-right: ").concat(l,"px ").concat(r,";\n  }\n  body[").concat(L,"] {\n    overflow: hidden ").concat(r,";\n    overscroll-behavior: contain;\n    ").concat([t&&"position: relative ".concat(r,";"),"margin"===n&&"\n    padding-left: ".concat(o,"px;\n    padding-top: ").concat(a,"px;\n    padding-right: ").concat(c,"px;\n    margin-left:0;\n    margin-top:0;\n    margin-right: ").concat(l,"px ").concat(r,";\n    "),"padding"===n&&"padding-right: ".concat(l,"px ").concat(r,";")].filter(Boolean).join(""),"\n  }\n  \n  .").concat(i," {\n    right: ").concat(l,"px ").concat(r,";\n  }\n  \n  .").concat(u," {\n    margin-right: ").concat(l,"px ").concat(r,";\n  }\n  \n  .").concat(i," .").concat(i," {\n    right: 0 ").concat(r,";\n  }\n  \n  .").concat(u," .").concat(u," {\n    margin-right: 0 ").concat(r,";\n  }\n  \n  body[").concat(L,"] {\n    ").concat("--removed-body-scroll-bar-size",": ").concat(l,"px;\n  }\n")},T=function(){var e=parseInt(document.body.getAttribute(L)||"0",10);return isFinite(e)?e:0},N=function(){a.useEffect(function(){return document.body.setAttribute(L,(T()+1).toString()),function(){var e=T()-1;e<=0?document.body.removeAttribute(L):document.body.setAttribute(L,e.toString())}},[])},R=function(e){var t=e.noRelative,n=e.noImportant,r=e.gapMode,o=void 0===r?"margin":r;N();var i=a.useMemo(function(){return C(o)},[o]);return a.createElement(S,{styles:k(i,!t,o,n?"":"!important")})},A=!1;if("undefined"!=typeof window)try{var M=Object.defineProperty({},"passive",{get:function(){return A=!0,!0}});window.addEventListener("test",M,M),window.removeEventListener("test",M,M)}catch(e){A=!1}var P=!!A&&{passive:!1},x=function(e,t){if(!(e instanceof Element))return!1;var n=window.getComputedStyle(e);return"hidden"!==n[t]&&(n.overflowY!==n.overflowX||"TEXTAREA"===e.tagName||"visible"!==n[t])},D=function(e,t){var n=t.ownerDocument,r=t;do{if("undefined"!=typeof ShadowRoot&&r instanceof ShadowRoot&&(r=r.host),W(e,r)){var o=O(e,r);if(o[1]>o[2])return!0}r=r.parentNode}while(r&&r!==n.body);return!1},W=function(e,t){return"v"===e?x(t,"overflowY"):x(t,"overflowX")},O=function(e,t){return"v"===e?[t.scrollTop,t.scrollHeight,t.clientHeight]:[t.scrollLeft,t.scrollWidth,t.clientWidth]},F=function(e,t,n,r,o){var a,i=(a=window.getComputedStyle(t).direction,"h"===e&&"rtl"===a?-1:1),u=i*r,c=n.target,l=t.contains(c),s=!1,d=u>0,f=0,v=0;do{if(!c)break;var m=O(e,c),h=m[0],p=m[1]-m[2]-i*h;(h||p)&&W(e,c)&&(f+=p,v+=h);var E=c.parentNode;c=E&&E.nodeType===Node.DOCUMENT_FRAGMENT_NODE?E.host:E}while(!l&&c!==document.body||l&&(t.contains(c)||t===c));return d&&(o&&1>Math.abs(f)||!o&&u>f)?s=!0:!d&&(o&&1>Math.abs(v)||!o&&-u>v)&&(s=!0),s},I=function(e){return"changedTouches"in e?[e.changedTouches[0].clientX,e.changedTouches[0].clientY]:[0,0]},B=function(e){return[e.deltaX,e.deltaY]},_=function(e){return e&&"current"in e?e.current:e},K=0,j=[];let X=(f.useMedium(function(e){var t=a.useRef([]),n=a.useRef([0,0]),r=a.useRef(),i=a.useState(K++)[0],u=a.useState(g)[0],c=a.useRef(e);a.useEffect(function(){c.current=e},[e]),a.useEffect(function(){if(e.inert){document.body.classList.add("block-interactivity-".concat(i));var t=(0,o.fX)([e.lockRef.current],(e.shards||[]).map(_),!0).filter(Boolean);return t.forEach(function(e){return e.classList.add("allow-interactivity-".concat(i))}),function(){document.body.classList.remove("block-interactivity-".concat(i)),t.forEach(function(e){return e.classList.remove("allow-interactivity-".concat(i))})}}},[e.inert,e.lockRef.current,e.shards]);var l=a.useCallback(function(e,t){if("touches"in e&&2===e.touches.length||"wheel"===e.type&&e.ctrlKey)return!c.current.allowPinchZoom;var o,a=I(e),i=n.current,u="deltaX"in e?e.deltaX:i[0]-a[0],l="deltaY"in e?e.deltaY:i[1]-a[1],s=e.target,d=Math.abs(u)>Math.abs(l)?"h":"v";if("touches"in e&&"h"===d&&"range"===s.type)return!1;var f=D(d,s);if(!f)return!0;if(f?o=d:(o="v"===d?"h":"v",f=D(d,s)),!f)return!1;if(!r.current&&"changedTouches"in e&&(u||l)&&(r.current=o),!o)return!0;var v=r.current||o;return F(v,t,e,"h"===v?u:l,!0)},[]),s=a.useCallback(function(e){if(j.length&&j[j.length-1]===u){var n="deltaY"in e?B(e):I(e),r=t.current.filter(function(t){var r;return t.name===e.type&&(t.target===e.target||e.target===t.shadowParent)&&(r=t.delta)[0]===n[0]&&r[1]===n[1]})[0];if(r&&r.should){e.cancelable&&e.preventDefault();return}if(!r){var o=(c.current.shards||[]).map(_).filter(Boolean).filter(function(t){return t.contains(e.target)});(o.length>0?l(e,o[0]):!c.current.noIsolation)&&e.cancelable&&e.preventDefault()}}},[]),d=a.useCallback(function(e,n,r,o){var a={name:e,delta:n,target:r,should:o,shadowParent:function(e){for(var t=null;null!==e;)e instanceof ShadowRoot&&(t=e.host,e=e.host),e=e.parentNode;return t}(r)};t.current.push(a),setTimeout(function(){t.current=t.current.filter(function(e){return e!==a})},1)},[]),f=a.useCallback(function(e){n.current=I(e),r.current=void 0},[]),v=a.useCallback(function(t){d(t.type,B(t),t.target,l(t,e.lockRef.current))},[]),m=a.useCallback(function(t){d(t.type,I(t),t.target,l(t,e.lockRef.current))},[]);a.useEffect(function(){return j.push(u),e.setCallbacks({onScrollCapture:v,onWheelCapture:v,onTouchMoveCapture:m}),document.addEventListener("wheel",s,P),document.addEventListener("touchmove",s,P),document.addEventListener("touchstart",f,P),function(){j=j.filter(function(e){return e!==u}),document.removeEventListener("wheel",s,P),document.removeEventListener("touchmove",s,P),document.removeEventListener("touchstart",f,P)}},[]);var h=e.removeScrollBar,p=e.inert;return a.createElement(a.Fragment,null,p?a.createElement(u,{styles:"\n  .block-interactivity-".concat(i," {pointer-events: none;}\n  .allow-interactivity-").concat(i," {pointer-events: all;}\n")}):null,h?a.createElement(R,{noRelative:e.noRelative,gapMode:e.gapMode}):null)}),h);var Y=a.forwardRef(function(e,t){return a.createElement(m,(0,o.Cl)({},e,{ref:t,sideCar:X}))});Y.classNames=m.classNames;let z=Y}}]);