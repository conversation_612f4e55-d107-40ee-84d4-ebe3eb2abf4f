"use strict";(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[4040],{13466:(e,t,a)=>{a.d(t,{BT:()=>d,Wu:()=>c,X9:()=>i,ZB:()=>o,Zp:()=>n,aR:()=>l,wL:()=>u});var r=a(16261);a(95361);var s=a(61063);function n(e){let{className:t,...a}=e;return(0,r.jsx)("div",{"data-slot":"card",className:(0,s.cn)("bg-card text-card-foreground flex flex-col gap-6 rounded-2xl border border-border/50 p-6 shadow-lg backdrop-blur-sm transition-all duration-200 hover:shadow-xl hover:scale-[1.01]",t),...a})}function l(e){let{className:t,...a}=e;return(0,r.jsx)("div",{"data-slot":"card-header",className:(0,s.cn)("@container/card-header grid auto-rows-min grid-rows-[auto_auto] items-start gap-2 has-data-[slot=card-action]:grid-cols-[1fr_auto] [.border-b]:pb-6",t),...a})}function o(e){let{className:t,...a}=e;return(0,r.jsx)("div",{"data-slot":"card-title",className:(0,s.cn)("leading-none font-semibold",t),...a})}function d(e){let{className:t,...a}=e;return(0,r.jsx)("div",{"data-slot":"card-description",className:(0,s.cn)("text-muted-foreground text-sm",t),...a})}function i(e){let{className:t,...a}=e;return(0,r.jsx)("div",{"data-slot":"card-action",className:(0,s.cn)("col-start-2 row-span-2 row-start-1 self-start justify-self-end",t),...a})}function c(e){let{className:t,...a}=e;return(0,r.jsx)("div",{"data-slot":"card-content",className:(0,s.cn)("px-6",t),...a})}function u(e){let{className:t,...a}=e;return(0,r.jsx)("div",{"data-slot":"card-footer",className:(0,s.cn)("flex items-center px-6 [.border-t]:pt-6",t),...a})}},34384:(e,t,a)=>{a.d(t,{bq:()=>u,eb:()=>m,gC:()=>x,l6:()=>i,yv:()=>c});var r=a(16261);a(95361);var s=a(36202),n=a(18409),l=a(69397),o=a(25716),d=a(61063);function i(e){let{...t}=e;return(0,r.jsx)(s.bL,{"data-slot":"select",...t})}function c(e){let{...t}=e;return(0,r.jsx)(s.WT,{"data-slot":"select-value",...t})}function u(e){let{className:t,size:a="default",children:l,...o}=e;return(0,r.jsxs)(s.l9,{"data-slot":"select-trigger","data-size":a,className:(0,d.cn)("border-input data-[placeholder]:text-muted-foreground [&_svg:not([class*='text-'])]:text-muted-foreground focus-visible:border-ring focus-visible:ring-ring/50 aria-invalid:ring-destructive/20 dark:aria-invalid:ring-destructive/40 aria-invalid:border-destructive dark:bg-input/30 dark:hover:bg-input/50 flex w-fit items-center justify-between gap-2 rounded-md border bg-transparent px-3 py-2 text-sm whitespace-nowrap shadow-xs transition-[color,box-shadow] outline-none focus-visible:ring-[3px] disabled:cursor-not-allowed disabled:opacity-50 data-[size=default]:h-9 data-[size=sm]:h-8 *:data-[slot=select-value]:line-clamp-1 *:data-[slot=select-value]:flex *:data-[slot=select-value]:items-center *:data-[slot=select-value]:gap-2 [&_svg]:pointer-events-none [&_svg]:shrink-0 [&_svg:not([class*='size-'])]:size-4",t),...o,children:[l,(0,r.jsx)(s.In,{asChild:!0,children:(0,r.jsx)(n.A,{className:"size-4 opacity-50"})})]})}function x(e){let{className:t,children:a,position:n="popper",...l}=e;return(0,r.jsx)(s.ZL,{children:(0,r.jsxs)(s.UC,{"data-slot":"select-content",className:(0,d.cn)("bg-popover text-popover-foreground data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=open]:fade-in-0 data-[state=closed]:zoom-out-95 data-[state=open]:zoom-in-95 data-[side=bottom]:slide-in-from-top-2 data-[side=left]:slide-in-from-right-2 data-[side=right]:slide-in-from-left-2 data-[side=top]:slide-in-from-bottom-2 relative z-50 max-h-(--radix-select-content-available-height) min-w-[8rem] origin-(--radix-select-content-transform-origin) overflow-x-hidden overflow-y-auto rounded-md border shadow-md","popper"===n&&"data-[side=bottom]:translate-y-1 data-[side=left]:-translate-x-1 data-[side=right]:translate-x-1 data-[side=top]:-translate-y-1",t),position:n,...l,children:[(0,r.jsx)(f,{}),(0,r.jsx)(s.LM,{className:(0,d.cn)("p-1","popper"===n&&"h-[var(--radix-select-trigger-height)] w-full min-w-[var(--radix-select-trigger-width)] scroll-my-1"),children:a}),(0,r.jsx)(h,{})]})})}function m(e){let{className:t,children:a,...n}=e;return(0,r.jsxs)(s.q7,{"data-slot":"select-item",className:(0,d.cn)("focus:bg-accent focus:text-accent-foreground [&_svg:not([class*='text-'])]:text-muted-foreground relative flex w-full cursor-default items-center gap-2 rounded-sm py-1.5 pr-8 pl-2 text-sm outline-hidden select-none data-[disabled]:pointer-events-none data-[disabled]:opacity-50 [&_svg]:pointer-events-none [&_svg]:shrink-0 [&_svg:not([class*='size-'])]:size-4 *:[span]:last:flex *:[span]:last:items-center *:[span]:last:gap-2",t),...n,children:[(0,r.jsx)("span",{className:"absolute right-2 flex size-3.5 items-center justify-center",children:(0,r.jsx)(s.VF,{children:(0,r.jsx)(l.A,{className:"size-4"})})}),(0,r.jsx)(s.p4,{children:a})]})}function f(e){let{className:t,...a}=e;return(0,r.jsx)(s.PP,{"data-slot":"select-scroll-up-button",className:(0,d.cn)("flex cursor-default items-center justify-center py-1",t),...a,children:(0,r.jsx)(o.A,{className:"size-4"})})}function h(e){let{className:t,...a}=e;return(0,r.jsx)(s.wn,{"data-slot":"select-scroll-down-button",className:(0,d.cn)("flex cursor-default items-center justify-center py-1",t),...a,children:(0,r.jsx)(n.A,{className:"size-4"})})}},54040:(e,t,a)=>{a.d(t,{default:()=>C});var r=a(16261),s=a(95361),n=a(57193),l=a(75375),o=a(11896),d=a(77038),i=a(63911),c=a(13466),u=a(84893),x=a(99274),m=a(91032),f=a(61063);let h={light:"",dark:".dark"},p=s.createContext(null);function v(e){let{id:t,className:a,children:n,config:l,...o}=e,d=s.useId(),i="chart-".concat(t||d.replace(/:/g,""));return(0,r.jsx)(p.Provider,{value:{config:l},children:(0,r.jsxs)("div",{"data-slot":"chart","data-chart":i,className:(0,f.cn)("[&_.recharts-cartesian-axis-tick_text]:fill-muted-foreground [&_.recharts-cartesian-grid_line[stroke='#ccc']]:stroke-border/50 [&_.recharts-curve.recharts-tooltip-cursor]:stroke-border [&_.recharts-polar-grid_[stroke='#ccc']]:stroke-border [&_.recharts-radial-bar-background-sector]:fill-muted [&_.recharts-rectangle.recharts-tooltip-cursor]:fill-muted [&_.recharts-reference-line_[stroke='#ccc']]:stroke-border flex aspect-video justify-center text-xs [&_.recharts-dot[stroke='#fff']]:stroke-transparent [&_.recharts-layer]:outline-hidden [&_.recharts-sector]:outline-hidden [&_.recharts-sector[stroke='#fff']]:stroke-transparent [&_.recharts-surface]:outline-hidden",a),...o,children:[(0,r.jsx)(g,{id:i,config:l}),(0,r.jsx)(u.u,{children:n})]})})}let g=e=>{let{id:t,config:a}=e,s=Object.entries(a).filter(e=>{let[,t]=e;return t.theme||t.color});return s.length?(0,r.jsx)("style",{dangerouslySetInnerHTML:{__html:Object.entries(h).map(e=>{let[a,r]=e;return"\n".concat(r," [data-chart=").concat(t,"] {\n").concat(s.map(e=>{var t;let[r,s]=e,n=(null===(t=s.theme)||void 0===t?void 0:t[a])||s.color;return n?"  --color-".concat(r,": ").concat(n,";"):null}).join("\n"),"\n}\n")}).join("\n")}}):null},b=x.m;function j(e){let{active:t,payload:a,className:n,indicator:l="dot",hideLabel:o=!1,hideIndicator:d=!1,label:i,labelFormatter:c,labelClassName:u,formatter:x,color:m,nameKey:h,labelKey:v}=e,{config:g}=function(){let e=s.useContext(p);if(!e)throw Error("useChart must be used within a <ChartContainer />");return e}(),b=s.useMemo(()=>{var e;if(o||!(null==a?void 0:a.length))return null;let[t]=a,s="".concat(v||(null==t?void 0:t.dataKey)||(null==t?void 0:t.name)||"value"),n=y(g,t,s),l=v||"string"!=typeof i?null==n?void 0:n.label:(null===(e=g[i])||void 0===e?void 0:e.label)||i;return c?(0,r.jsx)("div",{className:(0,f.cn)("font-medium",u),children:c(l,a)}):l?(0,r.jsx)("div",{className:(0,f.cn)("font-medium",u),children:l}):null},[i,c,a,o,u,g,v]);if(!t||!(null==a?void 0:a.length))return null;let j=1===a.length&&"dot"!==l;return(0,r.jsxs)("div",{className:(0,f.cn)("border-border/50 bg-background grid min-w-[8rem] items-start gap-1.5 rounded-lg border px-2.5 py-1.5 text-xs shadow-xl",n),children:[j?null:b,(0,r.jsx)("div",{className:"grid gap-1.5",children:a.map((e,t)=>{let a="".concat(h||e.name||e.dataKey||"value"),s=y(g,e,a),n=m||e.payload.fill||e.color;return(0,r.jsx)("div",{className:(0,f.cn)("[&>svg]:text-muted-foreground flex w-full flex-wrap items-stretch gap-2 [&>svg]:h-2.5 [&>svg]:w-2.5","dot"===l&&"items-center"),children:x&&(null==e?void 0:e.value)!==void 0&&e.name?x(e.value,e.name,e,t,e.payload):(0,r.jsxs)(r.Fragment,{children:[(null==s?void 0:s.icon)?(0,r.jsx)(s.icon,{}):!d&&(0,r.jsx)("div",{className:(0,f.cn)("shrink-0 rounded-[2px] border-(--color-border) bg-(--color-bg)",{"h-2.5 w-2.5":"dot"===l,"w-1":"line"===l,"w-0 border-[1.5px] border-dashed bg-transparent":"dashed"===l,"my-0.5":j&&"dashed"===l}),style:{"--color-bg":n,"--color-border":n}}),(0,r.jsxs)("div",{className:(0,f.cn)("flex flex-1 justify-between leading-none",j?"items-end":"items-center"),children:[(0,r.jsxs)("div",{className:"grid gap-1.5",children:[j?b:null,(0,r.jsx)("span",{className:"text-muted-foreground",children:(null==s?void 0:s.label)||e.name})]}),e.value&&(0,r.jsx)("span",{className:"text-foreground font-mono font-medium tabular-nums",children:e.value.toLocaleString()})]})]})},e.dataKey)})})]})}function y(e,t,a){if("object"!=typeof t||null===t)return;let r="payload"in t&&"object"==typeof t.payload&&null!==t.payload?t.payload:void 0,s=a;return a in t&&"string"==typeof t[a]?s=t[a]:r&&a in r&&"string"==typeof r[a]&&(s=r[a]),s in e?e[s]:e[a]}m.s;var w=a(34384),k=a(39439);let N=(0,a(20604).F)("inline-flex items-center justify-center gap-2 rounded-md text-sm font-medium hover:bg-muted hover:text-muted-foreground disabled:pointer-events-none disabled:opacity-50 data-[state=on]:bg-accent data-[state=on]:text-accent-foreground [&_svg]:pointer-events-none [&_svg:not([class*='size-'])]:size-4 [&_svg]:shrink-0 focus-visible:border-ring focus-visible:ring-ring/50 focus-visible:ring-[3px] outline-none transition-[color,box-shadow] aria-invalid:ring-destructive/20 dark:aria-invalid:ring-destructive/40 aria-invalid:border-destructive whitespace-nowrap",{variants:{variant:{default:"bg-transparent",outline:"border border-input bg-transparent shadow-xs hover:bg-accent hover:text-accent-foreground"},size:{default:"h-9 px-2 min-w-9",sm:"h-8 px-1.5 min-w-8",lg:"h-10 px-2.5 min-w-10"}},defaultVariants:{variant:"default",size:"default"}}),_=s.createContext({size:"default",variant:"default"});function z(e){let{className:t,variant:a,size:s,children:n,...l}=e;return(0,r.jsx)(k.bL,{"data-slot":"toggle-group","data-variant":a,"data-size":s,className:(0,f.cn)("group/toggle-group flex w-fit items-center rounded-md data-[variant=outline]:shadow-xs",t),...l,children:(0,r.jsx)(_.Provider,{value:{variant:a,size:s},children:n})})}function L(e){let{className:t,children:a,variant:n,size:l,...o}=e,d=s.useContext(_);return(0,r.jsx)(k.q7,{"data-slot":"toggle-group-item","data-variant":d.variant||n,"data-size":d.size||l,className:(0,f.cn)(N({variant:d.variant||n,size:d.size||l}),"min-w-0 flex-1 shrink-0 rounded-none shadow-none first:rounded-l-md last:rounded-r-md focus:z-10 focus-visible:z-10 data-[variant=outline]:border-l-0 data-[variant=outline]:first:border-l",t),...o,children:a})}function C(e){let{data:t,fields:a,title:u,description:x,defaultTimeRange:m="90d"}=e,f=(0,i.a)(),[h,p]=s.useState(m);s.useEffect(()=>{f&&p("7d")},[f]);let g=a.reduce((e,t)=>(e[t.key]={label:t.label,color:t.color||"var(--primary)"},e),{}),y=t.filter(e=>{let a=new Date(e.date),r=new Date(t[t.length-1].date),s=90;"30d"===h?s=30:"7d"===h&&(s=7);let n=new Date(r);return n.setDate(n.getDate()-s),a>=n});return(0,r.jsxs)(c.Zp,{className:"@container/card",children:[(0,r.jsxs)(c.aR,{children:[(0,r.jsx)(c.ZB,{children:u}),(0,r.jsxs)(c.BT,{children:[(0,r.jsx)("span",{className:"hidden @[540px]/card:block",children:x}),(0,r.jsx)("span",{className:"@[540px]/card:hidden",children:x})]}),(0,r.jsxs)(c.X9,{children:[(0,r.jsxs)(z,{type:"single",value:h,onValueChange:e=>p(e),variant:"outline",className:"hidden *:data-[slot=toggle-group-item]:!px-4 @[767px]/card:flex",children:[(0,r.jsx)(L,{value:"90d",children:"Last 3 months"}),(0,r.jsx)(L,{value:"30d",children:"Last 30 days"}),(0,r.jsx)(L,{value:"7d",children:"Last 7 days"})]}),(0,r.jsxs)(w.l6,{value:h,onValueChange:e=>p(e),children:[(0,r.jsx)(w.bq,{className:"flex w-40 **:data-[slot=select-value]:block **:data-[slot=select-value]:truncate @[767px]/card:hidden",size:"sm","aria-label":"Select a value",children:(0,r.jsx)(w.yv,{placeholder:"Last 3 months"})}),(0,r.jsxs)(w.gC,{className:"rounded-xl",children:[(0,r.jsx)(w.eb,{value:"90d",className:"rounded-lg",children:"Last 3 months"}),(0,r.jsx)(w.eb,{value:"30d",className:"rounded-lg",children:"Last 30 days"}),(0,r.jsx)(w.eb,{value:"7d",className:"rounded-lg",children:"Last 7 days"})]})]})]})]}),(0,r.jsx)(c.Wu,{className:"px-2 pt-4 sm:px-6 sm:pt-6",children:(0,r.jsx)(v,{config:g,className:"aspect-auto h-[250px] w-full",children:(0,r.jsxs)(n.Q,{data:y,children:[(0,r.jsx)("defs",{children:a.map(e=>(0,r.jsxs)("linearGradient",{id:"fill-".concat(e.key),x1:"0",y1:"0",x2:"0",y2:"1",children:[(0,r.jsx)("stop",{offset:"5%",stopColor:e.color||"var(--primary)",stopOpacity:1}),(0,r.jsx)("stop",{offset:"95%",stopColor:e.color||"var(--primary)",stopOpacity:.1})]},"fill-".concat(e.key)))}),(0,r.jsx)(l.d,{vertical:!1}),(0,r.jsx)(o.W,{dataKey:"date",tickLine:!1,axisLine:!1,tickMargin:8,minTickGap:32,tickFormatter:e=>new Date(e).toLocaleDateString("en-US",{month:"short",day:"numeric"})}),(0,r.jsx)(b,{cursor:!1,defaultIndex:f?-1:10,content:(0,r.jsx)(j,{labelFormatter:e=>new Date(e).toLocaleDateString("en-US",{month:"short",day:"numeric"}),indicator:"dot"})}),a.map(e=>(0,r.jsx)(d.G,{dataKey:e.key,type:"natural",fill:"url(#fill-".concat(e.key,")"),stroke:e.color||"var(--primary)",stackId:"a"},e.key))]})})})]})}}}]);