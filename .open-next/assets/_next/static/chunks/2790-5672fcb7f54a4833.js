"use strict";(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[2790],{657:(e,r,a)=>{a.d(r,{default:()=>x});var s=a(16261),t=a(13466),l=a(82888),i=a(60025),n=a(95361),d=a(8275),o=a(69397),c=a(17002),m=a(35654);function x(e){var r;let{section:a}=e,[x,u]=(0,n.useState)(null);if(a.disabled)return null;let h=async(e,r)=>{try{await navigator.clipboard.writeText(e),u(r),setTimeout(()=>u(null),2e3)}catch(e){console.error("Failed to copy prompt:",e)}};return(0,s.jsxs)("section",{className:"container py-16",children:[(0,s.jsxs)("div",{className:"mx-auto mb-12 text-center",children:[(0,s.jsx)("h2",{className:"mb-6 text-pretty text-3xl font-bold lg:text-4xl",children:a.title}),(0,s.jsx)("p",{className:"mb-4 max-w-xl text-muted-foreground lg:max-w-none lg:text-lg",children:a.description})]}),(0,s.jsx)("div",{className:"grid grid-cols-1 gap-8 max-w-none",children:null===(r=a.items)||void 0===r?void 0:r.map((e,r)=>(0,s.jsxs)(t.Zp,{className:"overflow-hidden transition-all hover:shadow-lg dark:hover:shadow-primary/10 flex flex-col max-w-4xl mx-auto w-full",children:[(0,s.jsxs)(t.aR,{className:"pb-4",children:[(0,s.jsxs)("div",{className:"flex items-start justify-between gap-2",children:[(0,s.jsx)(t.ZB,{className:"text-xl font-semibold line-clamp-2 flex-1",children:e.title}),e.category&&(0,s.jsx)(i.E,{variant:"outline",className:"shrink-0",children:e.category})]}),e.description&&(0,s.jsx)("p",{className:"text-sm text-muted-foreground line-clamp-2 mt-2",children:e.description})]}),(0,s.jsxs)(t.Wu,{className:"flex-1 flex flex-col space-y-6",children:[(0,s.jsxs)("div",{className:"space-y-4",children:[(0,s.jsxs)("div",{className:"block md:hidden space-y-4",children:[(0,s.jsxs)("div",{className:"space-y-2",children:[(0,s.jsx)("p",{className:"text-sm font-medium text-muted-foreground",children:"处理前"}),(0,s.jsx)("div",{className:"relative w-full overflow-hidden rounded-lg border border-border/50",children:(0,s.jsx)(m.default,{src:e.beforeImage.src,alt:e.beforeImage.alt,width:600,height:400,className:"w-full h-auto object-contain transition-transform duration-300 hover:scale-105"})})]}),(0,s.jsx)("div",{className:"flex justify-center",children:(0,s.jsx)(d.A,{className:"w-6 h-6 text-muted-foreground rotate-90"})}),(0,s.jsxs)("div",{className:"space-y-2",children:[(0,s.jsx)("p",{className:"text-sm font-medium text-muted-foreground",children:"处理后"}),(0,s.jsx)("div",{className:"relative w-full overflow-hidden rounded-lg border border-border/50",children:(0,s.jsx)(m.default,{src:e.afterImage.src,alt:e.afterImage.alt,width:600,height:400,className:"w-full h-auto object-contain transition-transform duration-300 hover:scale-105"})})]})]}),(0,s.jsxs)("div",{className:"hidden md:flex items-center gap-4",children:[(0,s.jsxs)("div",{className:"flex-1 space-y-2",children:[(0,s.jsx)("p",{className:"text-sm font-medium text-muted-foreground",children:"处理前"}),(0,s.jsx)("div",{className:"relative w-full overflow-hidden rounded-lg border border-border/50",children:(0,s.jsx)(m.default,{src:e.beforeImage.src,alt:e.beforeImage.alt,width:600,height:400,className:"w-full h-auto object-contain transition-transform duration-300 hover:scale-105"})})]}),(0,s.jsx)("div",{className:"flex-shrink-0",children:(0,s.jsx)(d.A,{className:"w-6 h-6 text-muted-foreground"})}),(0,s.jsxs)("div",{className:"flex-1 space-y-2",children:[(0,s.jsx)("p",{className:"text-sm font-medium text-muted-foreground",children:"处理后"}),(0,s.jsx)("div",{className:"relative w-full overflow-hidden rounded-lg border border-border/50",children:(0,s.jsx)(m.default,{src:e.afterImage.src,alt:e.afterImage.alt,width:600,height:400,className:"w-full h-auto object-contain transition-transform duration-300 hover:scale-105"})})]})]})]}),(0,s.jsxs)("div",{className:"space-y-3",children:[(0,s.jsx)("p",{className:"text-sm font-medium text-foreground",children:"使用的 Prompt"}),(0,s.jsx)("div",{className:"bg-muted/50 rounded-lg p-4 border border-border/50",children:(0,s.jsx)("p",{className:"text-sm font-mono leading-relaxed text-foreground/90 whitespace-pre-wrap break-words",children:e.prompt})})]}),e.tags&&e.tags.length>0&&(0,s.jsxs)("div",{className:"flex flex-wrap gap-2",children:[e.tags.slice(0,4).map((e,r)=>(0,s.jsx)(i.E,{variant:"secondary",className:"text-xs px-2 py-1",children:e},r)),e.tags.length>4&&(0,s.jsxs)(i.E,{variant:"secondary",className:"text-xs px-2 py-1",children:["+",e.tags.length-4]})]}),(0,s.jsx)(l.$,{onClick:()=>h(e.prompt,r),variant:"outline",size:"sm",className:"w-full transition-all duration-200 hover:bg-primary hover:text-primary-foreground",children:x===r?(0,s.jsxs)(s.Fragment,{children:[(0,s.jsx)(o.A,{className:"w-4 h-4 mr-2"}),"已复制"]}):(0,s.jsxs)(s.Fragment,{children:[(0,s.jsx)(c.A,{className:"w-4 h-4 mr-2"}),"复制 Prompt"]})})]})]},r))})]})}},13466:(e,r,a)=>{a.d(r,{BT:()=>d,Wu:()=>c,X9:()=>o,ZB:()=>n,Zp:()=>l,aR:()=>i,wL:()=>m});var s=a(16261);a(95361);var t=a(61063);function l(e){let{className:r,...a}=e;return(0,s.jsx)("div",{"data-slot":"card",className:(0,t.cn)("bg-card text-card-foreground flex flex-col gap-6 rounded-2xl border border-border/50 p-6 shadow-lg backdrop-blur-sm transition-all duration-200 hover:shadow-xl hover:scale-[1.01]",r),...a})}function i(e){let{className:r,...a}=e;return(0,s.jsx)("div",{"data-slot":"card-header",className:(0,t.cn)("@container/card-header grid auto-rows-min grid-rows-[auto_auto] items-start gap-2 has-data-[slot=card-action]:grid-cols-[1fr_auto] [.border-b]:pb-6",r),...a})}function n(e){let{className:r,...a}=e;return(0,s.jsx)("div",{"data-slot":"card-title",className:(0,t.cn)("leading-none font-semibold",r),...a})}function d(e){let{className:r,...a}=e;return(0,s.jsx)("div",{"data-slot":"card-description",className:(0,t.cn)("text-muted-foreground text-sm",r),...a})}function o(e){let{className:r,...a}=e;return(0,s.jsx)("div",{"data-slot":"card-action",className:(0,t.cn)("col-start-2 row-span-2 row-start-1 self-start justify-self-end",r),...a})}function c(e){let{className:r,...a}=e;return(0,s.jsx)("div",{"data-slot":"card-content",className:(0,t.cn)("px-6",r),...a})}function m(e){let{className:r,...a}=e;return(0,s.jsx)("div",{"data-slot":"card-footer",className:(0,t.cn)("flex items-center px-6 [.border-t]:pt-6",r),...a})}},60025:(e,r,a)=>{a.d(r,{E:()=>d});var s=a(16261);a(95361);var t=a(76012),l=a(20604),i=a(61063);let n=(0,l.F)("inline-flex items-center justify-center rounded-md border px-2 py-0.5 text-xs font-medium w-fit whitespace-nowrap shrink-0 [&>svg]:size-3 gap-1 [&>svg]:pointer-events-none focus-visible:border-ring focus-visible:ring-ring/50 focus-visible:ring-[3px] aria-invalid:ring-destructive/20 dark:aria-invalid:ring-destructive/40 aria-invalid:border-destructive transition-[color,box-shadow] overflow-hidden",{variants:{variant:{default:"border-transparent bg-primary text-primary-foreground [a&]:hover:bg-primary/90",secondary:"border-transparent bg-secondary text-secondary-foreground [a&]:hover:bg-secondary/90",destructive:"border-transparent bg-destructive text-white [a&]:hover:bg-destructive/90 focus-visible:ring-destructive/20 dark:focus-visible:ring-destructive/40 dark:bg-destructive/60",outline:"text-foreground [a&]:hover:bg-accent [a&]:hover:text-accent-foreground"}},defaultVariants:{variant:"default"}});function d(e){let{className:r,variant:a,asChild:l=!1,...d}=e,o=l?t.DX:"span";return(0,s.jsx)(o,{"data-slot":"badge",className:(0,i.cn)(n({variant:a}),r),...d})}},61063:(e,r,a)=>{a.d(r,{cn:()=>l});var s=a(87786),t=a(34835);function l(){for(var e=arguments.length,r=Array(e),a=0;a<e;a++)r[a]=arguments[a];return(0,t.QP)((0,s.$)(r))}},78774:(e,r,a)=>{a.d(r,{default:()=>u});var s=a(16261),t=a(13466),l=a(82888),i=a(60025),n=a(95361),d=a(25716),o=a(18409),c=a(69397),m=a(17002),x=a(35654);function u(e){var r;let{section:a}=e,[u,h]=(0,n.useState)(null),[g,v]=(0,n.useState)(new Set);if(a.disabled)return null;let f=async(e,r)=>{try{await navigator.clipboard.writeText(e),h(r),setTimeout(()=>h(null),2e3)}catch(e){console.error("Failed to copy prompt:",e)}},p=e=>{let r=new Set(g);r.has(e)?r.delete(e):r.add(e),v(r)};return(0,s.jsxs)("section",{className:"container py-16",children:[(0,s.jsxs)("div",{className:"mx-auto mb-12 text-center",children:[(0,s.jsx)("h2",{className:"mb-6 text-pretty text-3xl font-bold lg:text-4xl",children:a.title}),(0,s.jsx)("p",{className:"mb-4 max-w-xl text-muted-foreground lg:max-w-none lg:text-lg",children:a.description})]}),(0,s.jsx)("div",{className:"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6",children:null===(r=a.items)||void 0===r?void 0:r.map((e,r)=>(0,s.jsxs)(t.Zp,{className:"overflow-hidden transition-all hover:shadow-lg dark:hover:shadow-primary/10 flex flex-col h-full",children:[e.image&&(0,s.jsxs)("div",{className:"relative aspect-[4/3] w-full overflow-hidden",children:[(0,s.jsx)(x.default,{src:e.image.src,alt:e.image.alt||e.title,fill:!0,className:"object-cover transition-transform duration-300 hover:scale-105"}),e.category&&(0,s.jsx)("div",{className:"absolute top-3 right-3",children:(0,s.jsx)(i.E,{variant:"outline",className:"bg-background/80 backdrop-blur-sm",children:e.category})})]}),(0,s.jsxs)(t.aR,{className:"pb-3",children:[(0,s.jsx)(t.ZB,{className:"text-lg font-semibold line-clamp-2",children:e.title}),e.description&&(0,s.jsx)("p",{className:"text-sm text-muted-foreground line-clamp-2 mt-1",children:e.description})]}),(0,s.jsxs)(t.Wu,{className:"flex-1 flex flex-col space-y-3",children:[e.tags&&e.tags.length>0&&(0,s.jsxs)("div",{className:"flex flex-wrap gap-1",children:[e.tags.slice(0,3).map((e,r)=>(0,s.jsx)(i.E,{variant:"secondary",className:"text-xs px-2 py-0.5",children:e},r)),e.tags.length>3&&(0,s.jsxs)(i.E,{variant:"secondary",className:"text-xs px-2 py-0.5",children:["+",e.tags.length-3]})]}),(0,s.jsx)("div",{className:"flex-1",children:g.has(r)&&(0,s.jsx)("div",{className:"mb-3",children:(0,s.jsx)("div",{className:"bg-muted/50 rounded-lg p-3 border border-border/50",children:(0,s.jsx)("p",{className:"text-xs font-mono leading-relaxed text-foreground/90 whitespace-pre-wrap break-words",children:e.prompt})})})}),(0,s.jsxs)("div",{className:"space-y-2",children:[(0,s.jsx)(l.$,{onClick:()=>p(r),variant:"outline",size:"sm",className:"w-full transition-all duration-200",children:g.has(r)?(0,s.jsxs)(s.Fragment,{children:[(0,s.jsx)(d.A,{className:"w-4 h-4 mr-2"}),"隐藏 Prompt"]}):(0,s.jsxs)(s.Fragment,{children:[(0,s.jsx)(o.A,{className:"w-4 h-4 mr-2"}),"显示 Prompt"]})}),g.has(r)&&(0,s.jsx)(l.$,{onClick:()=>f(e.prompt,r),variant:"default",size:"sm",className:"w-full transition-all duration-200",children:u===r?(0,s.jsxs)(s.Fragment,{children:[(0,s.jsx)(c.A,{className:"w-4 h-4 mr-2"}),"已复制"]}):(0,s.jsxs)(s.Fragment,{children:[(0,s.jsx)(m.A,{className:"w-4 h-4 mr-2"}),"复制 Prompt"]})})]})]})]},r))})]})}},82888:(e,r,a)=>{a.d(r,{$:()=>d,r:()=>n});var s=a(16261);a(95361);var t=a(76012),l=a(20604),i=a(61063);let n=(0,l.F)("inline-flex items-center justify-center gap-2 whitespace-nowrap rounded-lg text-sm font-medium transition-all duration-200 disabled:pointer-events-none disabled:opacity-50 [&_svg]:pointer-events-none [&_svg:not([class*='size-'])]:size-4 shrink-0 [&_svg]:shrink-0 outline-none focus-visible:border-ring focus-visible:ring-ring/50 focus-visible:ring-[3px] aria-invalid:ring-destructive/20 dark:aria-invalid:ring-destructive/40 aria-invalid:border-destructive",{variants:{variant:{default:"bg-gradient-to-r from-primary to-accent text-primary-foreground shadow-md hover:shadow-lg hover:scale-[1.02] active:scale-[0.98]",destructive:"bg-destructive text-destructive-foreground shadow-md hover:bg-destructive/90 hover:shadow-lg hover:scale-[1.02] active:scale-[0.98] focus-visible:ring-destructive/20 dark:focus-visible:ring-destructive/40",outline:"border-2 border-primary/20 bg-background/50 backdrop-blur-sm shadow-sm hover:bg-accent/50 hover:text-accent-foreground hover:border-primary/40 hover:shadow-md hover:scale-[1.02] active:scale-[0.98] dark:bg-input/30 dark:border-input dark:hover:bg-input/50",secondary:"bg-gradient-to-r from-secondary to-muted text-secondary-foreground shadow-sm hover:shadow-md hover:scale-[1.02] active:scale-[0.98]",ghost:"hover:bg-accent/50 hover:text-accent-foreground hover:scale-[1.02] active:scale-[0.98] dark:hover:bg-accent/30",link:"text-primary underline-offset-4 hover:underline hover:text-accent"},size:{default:"h-10 px-6 py-2 has-[>svg]:px-4",sm:"h-8 rounded-lg gap-1.5 px-4 has-[>svg]:px-3",lg:"h-12 rounded-lg px-8 has-[>svg]:px-6 text-base",icon:"size-10 rounded-full"}},defaultVariants:{variant:"default",size:"default"}});function d(e){let{className:r,variant:a,size:l,asChild:d=!1,...o}=e,c=d?t.DX:"button";return(0,s.jsx)(c,{"data-slot":"button",className:(0,i.cn)(n({variant:a,size:l,className:r})),...o})}}}]);