"use strict";(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[2721],{709:(e,t,a)=>{a.d(t,{I:()=>d,SQ:()=>o,_2:()=>c,lp:()=>u,mB:()=>m,rI:()=>i,ty:()=>l});var s=a(16261);a(95361);var n=a(89812),r=a(61063);function i(e){let{...t}=e;return(0,s.jsx)(n.bL,{"data-slot":"dropdown-menu",...t})}function l(e){let{...t}=e;return(0,s.jsx)(n.l9,{"data-slot":"dropdown-menu-trigger",...t})}function o(e){let{className:t,sideOffset:a=4,...i}=e;return(0,s.jsx)(n.ZL,{children:(0,s.jsx)(n.UC,{"data-slot":"dropdown-menu-content",sideOffset:a,className:(0,r.cn)("bg-popover text-popover-foreground data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=open]:fade-in-0 data-[state=closed]:zoom-out-95 data-[state=open]:zoom-in-95 data-[side=bottom]:slide-in-from-top-2 data-[side=left]:slide-in-from-right-2 data-[side=right]:slide-in-from-left-2 data-[side=top]:slide-in-from-bottom-2 z-50 max-h-(--radix-dropdown-menu-content-available-height) min-w-[8rem] origin-(--radix-dropdown-menu-content-transform-origin) overflow-x-hidden overflow-y-auto rounded-md border p-1 shadow-md",t),...i})})}function d(e){let{...t}=e;return(0,s.jsx)(n.YJ,{"data-slot":"dropdown-menu-group",...t})}function c(e){let{className:t,inset:a,variant:i="default",...l}=e;return(0,s.jsx)(n.q7,{"data-slot":"dropdown-menu-item","data-inset":a,"data-variant":i,className:(0,r.cn)("focus:bg-accent focus:text-accent-foreground data-[variant=destructive]:text-destructive data-[variant=destructive]:focus:bg-destructive/10 dark:data-[variant=destructive]:focus:bg-destructive/20 data-[variant=destructive]:focus:text-destructive data-[variant=destructive]:*:[svg]:!text-destructive [&_svg:not([class*='text-'])]:text-muted-foreground relative flex cursor-default items-center gap-2 rounded-sm px-2 py-1.5 text-sm outline-hidden select-none data-[disabled]:pointer-events-none data-[disabled]:opacity-50 data-[inset]:pl-8 [&_svg]:pointer-events-none [&_svg]:shrink-0 [&_svg:not([class*='size-'])]:size-4",t),...l})}function u(e){let{className:t,inset:a,...i}=e;return(0,s.jsx)(n.JU,{"data-slot":"dropdown-menu-label","data-inset":a,className:(0,r.cn)("px-2 py-1.5 text-sm font-medium data-[inset]:pl-8",t),...i})}function m(e){let{className:t,...a}=e;return(0,s.jsx)(n.wv,{"data-slot":"dropdown-menu-separator",className:(0,r.cn)("bg-border -mx-1 my-1 h-px",t),...a})}},5399:(e,t,a)=>{a.d(t,{Avatar:()=>i,AvatarImage:()=>l,q:()=>o});var s=a(16261);a(95361);var n=a(28806),r=a(61063);function i(e){let{className:t,...a}=e;return(0,s.jsx)(n.bL,{"data-slot":"avatar",className:(0,r.cn)("relative flex size-8 shrink-0 overflow-hidden rounded-full",t),...a})}function l(e){let{className:t,...a}=e;return(0,s.jsx)(n._V,{"data-slot":"avatar-image",className:(0,r.cn)("aspect-square size-full object-cover",t),...a})}function o(e){let{className:t,...a}=e;return(0,s.jsx)(n.H4,{"data-slot":"avatar-fallback",className:(0,r.cn)("bg-muted flex size-full items-center justify-center rounded-full",t),...a})}},17184:(e,t,a)=>{a.d(t,{PW:()=>r,np:()=>i,pr:()=>n});var s=a(63843);let n=e=>{let t=localStorage.getItem(e);if(!t)return null;let a=t.split(":");if(!a||a.length<2)return null;let n=Number(a[0]),r=(0,s.lg)();if(-1!==n&&n<r)return i(e),null;let l=a[0]+":";return t.replace(l,"")},r=(e,t,a)=>{localStorage.setItem(e,a+":"+t)},i=e=>{localStorage.removeItem(e)}},21608:(e,t,a)=>{a.d(t,{AppContextProvider:()=>x,U:()=>f});var s=a(16261),n=a(95361),r=a(17184),i=a(88999),l=a(96576),o=a.n(l),d=a(5657),c=a.n(d),u=a(80372);let m=(0,n.createContext)({}),f=()=>(0,n.useContext)(m),x=e=>{let{children:t}=e;!function(){let{data:e,status:t}=(0,u.wV)(),a=async function(){c()({client_id:"1011821969948-i0orej6mbfqon33v3klabnto7g49mvrh.apps.googleusercontent.com",auto_select:!1,cancel_on_tap_outside:!1,context:"signin"},e=>{console.log("onetap login ok",e),r(e.credential)})},r=async function(e){console.log("signIn ok",await (0,u.Jv)("google-one-tap",{credential:e,redirect:!1}))};(0,n.useEffect)(()=>{if("unauthenticated"===t){a();let e=setInterval(()=>{a()},3e3);return()=>{clearInterval(e)}}},[t]),s.Fragment}();let{data:a}=(0,u.wV)(),[l,d]=(0,n.useState)(()=>"light"),[f,x]=(0,n.useState)(!1),[h,v]=(0,n.useState)(null),[g,p]=(0,n.useState)(!1),b=async function(){try{let e=await fetch("/api/get-user-info",{method:"POST"});if(!e.ok)throw Error("fetch user info failed with status: "+e.status);let{code:t,message:a,data:s}=await e.json();if(0!==t)throw Error(a);v(s),j(s)}catch(e){console.log("fetch user info failed")}},j=async e=>{try{if(e.invited_by){console.log("user already been invited",e.invited_by);return}let t=(0,r.pr)(i.wD.InviteCode);if(!t)return;let a=o()(e.created_at).unix(),s=o()().unix(),n=Number(s-a);if(n<=0||n>7200){console.log("user created more than 2 hours");return}console.log("update invite",t,e.uuid);let l={invite_code:t,user_uuid:e.uuid},d=await fetch("/api/update-invite",{method:"POST",headers:{"Content-Type":"application/json"},body:JSON.stringify(l)});if(!d.ok)throw Error("update invite failed with status: "+d.status);let{code:c,message:u,data:m}=await d.json();if(0!==c)throw Error(u);v(m),(0,r.np)(i.wD.InviteCode)}catch(e){console.log("update invite failed: ",e)}};return(0,n.useEffect)(()=>{a&&a.user&&b()},[a]),(0,s.jsx)(m.Provider,{value:{theme:l,setTheme:d,showSignModal:f,setShowSignModal:x,user:h,setUser:v,showFeedback:g,setShowFeedback:p},children:t})}},34384:(e,t,a)=>{a.d(t,{bq:()=>u,eb:()=>f,gC:()=>m,l6:()=>d,yv:()=>c});var s=a(16261);a(95361);var n=a(36202),r=a(18409),i=a(69397),l=a(25716),o=a(61063);function d(e){let{...t}=e;return(0,s.jsx)(n.bL,{"data-slot":"select",...t})}function c(e){let{...t}=e;return(0,s.jsx)(n.WT,{"data-slot":"select-value",...t})}function u(e){let{className:t,size:a="default",children:i,...l}=e;return(0,s.jsxs)(n.l9,{"data-slot":"select-trigger","data-size":a,className:(0,o.cn)("border-input data-[placeholder]:text-muted-foreground [&_svg:not([class*='text-'])]:text-muted-foreground focus-visible:border-ring focus-visible:ring-ring/50 aria-invalid:ring-destructive/20 dark:aria-invalid:ring-destructive/40 aria-invalid:border-destructive dark:bg-input/30 dark:hover:bg-input/50 flex w-fit items-center justify-between gap-2 rounded-md border bg-transparent px-3 py-2 text-sm whitespace-nowrap shadow-xs transition-[color,box-shadow] outline-none focus-visible:ring-[3px] disabled:cursor-not-allowed disabled:opacity-50 data-[size=default]:h-9 data-[size=sm]:h-8 *:data-[slot=select-value]:line-clamp-1 *:data-[slot=select-value]:flex *:data-[slot=select-value]:items-center *:data-[slot=select-value]:gap-2 [&_svg]:pointer-events-none [&_svg]:shrink-0 [&_svg:not([class*='size-'])]:size-4",t),...l,children:[i,(0,s.jsx)(n.In,{asChild:!0,children:(0,s.jsx)(r.A,{className:"size-4 opacity-50"})})]})}function m(e){let{className:t,children:a,position:r="popper",...i}=e;return(0,s.jsx)(n.ZL,{children:(0,s.jsxs)(n.UC,{"data-slot":"select-content",className:(0,o.cn)("bg-popover text-popover-foreground data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=open]:fade-in-0 data-[state=closed]:zoom-out-95 data-[state=open]:zoom-in-95 data-[side=bottom]:slide-in-from-top-2 data-[side=left]:slide-in-from-right-2 data-[side=right]:slide-in-from-left-2 data-[side=top]:slide-in-from-bottom-2 relative z-50 max-h-(--radix-select-content-available-height) min-w-[8rem] origin-(--radix-select-content-transform-origin) overflow-x-hidden overflow-y-auto rounded-md border shadow-md","popper"===r&&"data-[side=bottom]:translate-y-1 data-[side=left]:-translate-x-1 data-[side=right]:translate-x-1 data-[side=top]:-translate-y-1",t),position:r,...i,children:[(0,s.jsx)(x,{}),(0,s.jsx)(n.LM,{className:(0,o.cn)("p-1","popper"===r&&"h-[var(--radix-select-trigger-height)] w-full min-w-[var(--radix-select-trigger-width)] scroll-my-1"),children:a}),(0,s.jsx)(h,{})]})})}function f(e){let{className:t,children:a,...r}=e;return(0,s.jsxs)(n.q7,{"data-slot":"select-item",className:(0,o.cn)("focus:bg-accent focus:text-accent-foreground [&_svg:not([class*='text-'])]:text-muted-foreground relative flex w-full cursor-default items-center gap-2 rounded-sm py-1.5 pr-8 pl-2 text-sm outline-hidden select-none data-[disabled]:pointer-events-none data-[disabled]:opacity-50 [&_svg]:pointer-events-none [&_svg]:shrink-0 [&_svg:not([class*='size-'])]:size-4 *:[span]:last:flex *:[span]:last:items-center *:[span]:last:gap-2",t),...r,children:[(0,s.jsx)("span",{className:"absolute right-2 flex size-3.5 items-center justify-center",children:(0,s.jsx)(n.VF,{children:(0,s.jsx)(i.A,{className:"size-4"})})}),(0,s.jsx)(n.p4,{children:a})]})}function x(e){let{className:t,...a}=e;return(0,s.jsx)(n.PP,{"data-slot":"select-scroll-up-button",className:(0,o.cn)("flex cursor-default items-center justify-center py-1",t),...a,children:(0,s.jsx)(l.A,{className:"size-4"})})}function h(e){let{className:t,...a}=e;return(0,s.jsx)(n.wn,{"data-slot":"select-scroll-down-button",className:(0,o.cn)("flex cursor-default items-center justify-center py-1",t),...a,children:(0,s.jsx)(r.A,{className:"size-4"})})}},39669:(e,t,a)=>{a.d(t,{CG:()=>o,Fm:()=>m,Qs:()=>x,cj:()=>l,h:()=>u,qp:()=>f});var s=a(16261);a(95361);var n=a(21494),r=a(87605),i=a(61063);function l(e){let{...t}=e;return(0,s.jsx)(n.bL,{"data-slot":"sheet",...t})}function o(e){let{...t}=e;return(0,s.jsx)(n.l9,{"data-slot":"sheet-trigger",...t})}function d(e){let{...t}=e;return(0,s.jsx)(n.ZL,{"data-slot":"sheet-portal",...t})}function c(e){let{className:t,...a}=e;return(0,s.jsx)(n.hJ,{"data-slot":"sheet-overlay",className:(0,i.cn)("data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=open]:fade-in-0 fixed inset-0 z-50 bg-black/50",t),...a})}function u(e){let{className:t,children:a,side:l="right",...o}=e;return(0,s.jsxs)(d,{children:[(0,s.jsx)(c,{}),(0,s.jsxs)(n.UC,{"data-slot":"sheet-content",className:(0,i.cn)("bg-background data-[state=open]:animate-in data-[state=closed]:animate-out fixed z-50 flex flex-col gap-4 shadow-lg transition ease-in-out data-[state=closed]:duration-300 data-[state=open]:duration-500","right"===l&&"data-[state=closed]:slide-out-to-right data-[state=open]:slide-in-from-right inset-y-0 right-0 h-full w-3/4 border-l sm:max-w-sm","left"===l&&"data-[state=closed]:slide-out-to-left data-[state=open]:slide-in-from-left inset-y-0 left-0 h-full w-3/4 border-r sm:max-w-sm","top"===l&&"data-[state=closed]:slide-out-to-top data-[state=open]:slide-in-from-top inset-x-0 top-0 h-auto border-b","bottom"===l&&"data-[state=closed]:slide-out-to-bottom data-[state=open]:slide-in-from-bottom inset-x-0 bottom-0 h-auto border-t",t),...o,children:[a,(0,s.jsxs)(n.bm,{className:"ring-offset-background focus:ring-ring data-[state=open]:bg-secondary absolute top-4 right-4 rounded-xs opacity-70 transition-opacity hover:opacity-100 focus:ring-2 focus:ring-offset-2 focus:outline-hidden disabled:pointer-events-none",children:[(0,s.jsx)(r.A,{className:"size-4"}),(0,s.jsx)("span",{className:"sr-only",children:"Close"})]})]})]})}function m(e){let{className:t,...a}=e;return(0,s.jsx)("div",{"data-slot":"sheet-header",className:(0,i.cn)("flex flex-col gap-1.5 p-4",t),...a})}function f(e){let{className:t,...a}=e;return(0,s.jsx)(n.hE,{"data-slot":"sheet-title",className:(0,i.cn)("text-foreground font-semibold",t),...a})}function x(e){let{className:t,...a}=e;return(0,s.jsx)(n.VY,{"data-slot":"sheet-description",className:(0,i.cn)("text-muted-foreground text-sm",t),...a})}},40528:(e,t,a)=>{a.r(t),a.d(t,{default:()=>d});var s=a(16261),n=a(95361),r=a(93694),i=a(98842),l=a(69507);let o={RiChatSmile3Line:r.Bgv,RiImageLine:r.fsL,RiVideoLine:r.xi0,RiMicLine:r.RQr,RiMoneyDollarCircleLine:r.AN5,RiArrowRightUpLine:r.SJ3,RiFlashlightFill:r.bwM,RiEyeLine:r.tLq,RiCpuLine:r.y_v,RiUserSmileLine:r.VNl,RiFlashlightLine:r.uEe,RiStarLine:r.WN7,RiPaletteLine:r.LrS,RiRocketLine:r.QWc,RiVoiceprintLine:r.Dcp,RiExchangeLine:r.Lcj,RiTwitterXFill:r.ase,RiGithubFill:r.sAW,RiDiscordFill:r.r53,RiMailLine:r.R0Y,FaRegHeart:i.sOK,GoThumbsup:l.VZG,GoArrowUpRight:l.zny},d=(0,n.memo)(e=>{let{name:t,className:a,onClick:n,...r}=e,i=o[t];return i?(0,s.jsx)(i,{className:a,onClick:n,style:{cursor:n?"pointer":"default"},...r}):null})},52721:(e,t,a)=>{a.d(t,{default:()=>G});var s=a(16261),n=a(55714),r=a(82888),i=a(95361),l=a(88818),o=a(20604),d=a(18409),c=a(61063);let u=i.forwardRef((e,t)=>{let{className:a,children:n,...r}=e;return(0,s.jsxs)(l.bL,{ref:t,className:(0,c.cn)("relative z-10 flex max-w-max flex-1 items-center justify-center",a),...r,children:[n,(0,s.jsx)(p,{})]})});u.displayName=l.bL.displayName;let m=i.forwardRef((e,t)=>{let{className:a,...n}=e;return(0,s.jsx)(l.B8,{ref:t,className:(0,c.cn)("group flex flex-1 list-none items-center justify-center space-x-1",a),...n})});m.displayName=l.B8.displayName;let f=l.q7,x=(0,o.F)("group inline-flex h-10 w-max items-center justify-center rounded-md bg-background px-4 py-2 text-sm font-medium transition-colors hover:bg-accent hover:text-accent-foreground focus:bg-accent focus:text-accent-foreground focus:outline-hidden disabled:pointer-events-none disabled:opacity-50 data-active:bg-accent/50 data-[state=open]:bg-accent/50"),h=i.forwardRef((e,t)=>{let{className:a,children:n,...r}=e;return(0,s.jsxs)(l.l9,{ref:t,className:(0,c.cn)(x(),"group",a),...r,children:[n," ",(0,s.jsx)(d.A,{className:"relative top-[1px] ml-1 h-3 w-3 transition duration-200 group-data-[state=open]:rotate-180","aria-hidden":"true"})]})});h.displayName=l.l9.displayName;let v=i.forwardRef((e,t)=>{let{className:a,...n}=e;return(0,s.jsx)(l.UC,{ref:t,className:(0,c.cn)("left-0 top-0 w-full data-[motion^=from-]:animate-in data-[motion^=to-]:animate-out data-[motion^=from-]:fade-in data-[motion^=to-]:fade-out data-[motion=from-end]:slide-in-from-right-52 data-[motion=from-start]:slide-in-from-left-52 data-[motion=to-end]:slide-out-to-right-52 data-[motion=to-start]:slide-out-to-left-52 md:absolute md:w-auto ",a),...n})});v.displayName=l.UC.displayName;let g=l.N_,p=i.forwardRef((e,t)=>{let{className:a,...n}=e;return(0,s.jsx)("div",{className:(0,c.cn)("absolute left-0 top-full flex justify-center"),children:(0,s.jsx)(l.LM,{className:(0,c.cn)("origin-top-center relative mt-1.5 h-[var(--radix-navigation-menu-viewport-height)] w-full overflow-hidden rounded-md border bg-popover text-popover-foreground shadow-lg data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:zoom-out-95 data-[state=open]:zoom-in-90 md:w-[var(--radix-navigation-menu-viewport-width)]",a),ref:t,...n})})});p.displayName=l.LM.displayName,i.forwardRef((e,t)=>{let{className:a,...n}=e;return(0,s.jsx)(l.C1,{ref:t,className:(0,c.cn)("top-full z-1 flex h-1.5 items-end justify-center overflow-hidden data-[state=visible]:animate-in data-[state=hidden]:animate-out data-[state=hidden]:fade-out data-[state=visible]:fade-in",a),...n,children:(0,s.jsx)("div",{className:"relative top-[60%] h-2 w-2 rotate-45 rounded-tl-sm bg-border shadow-md"})})}).displayName=l.C1.displayName;var b=a(39669),j=a(40528),N=a(62935),w=a(34384),y=a(89799),k=a(17502),_=a(55777);function z(e){let{isIcon:t=!1}=e,a=(0,y.useParams)().locale,n=(0,y.useRouter)(),r=(0,y.usePathname)();return(0,s.jsxs)(w.l6,{value:a,onValueChange:e=>{if(e!==a){let t=r.replace("/".concat(a),"/".concat(e));t.startsWith("/".concat(e))||(t="/".concat(e).concat(t)),n.push(t)}},children:[(0,s.jsxs)(w.bq,{className:"flex items-center gap-2 border-none text-muted-foreground hover:text-foreground hover:bg-accent/10 focus:ring-0 focus:ring-offset-0 transition-colors",children:[(0,s.jsx)(k.aVW,{className:"text-xl"}),!t&&(0,s.jsx)("span",{className:"hidden md:block",children:_.L$[a]})]}),(0,s.jsx)(w.gC,{className:"z-50 ",children:Object.keys(_.L$).map(e=>{let t=_.L$[e];return(0,s.jsx)(w.eb,{className:"cursor-pointer px-4",value:e,children:t},e)})})]})}var C=a(53164),L=a(21608),R=a(8624);function A(){let e=(0,R.c3)(),{setShowSignModal:t}=(0,L.U)();return(0,s.jsx)(r.$,{variant:"default",onClick:()=>t(!0),className:"cursor-pointer",children:e("user.sign_in")})}var I=a(5399),E=a(709),S=a(80372);function U(e){let{user:t}=e,a=(0,R.c3)(),n=[{title:t.nickname},{title:a("user.user_center"),url:"/my-orders"},{title:a("user.admin_system"),url:"/admin/users"},{title:a("user.sign_out"),onClick:()=>(0,S.CI)()}];return(0,s.jsxs)(E.rI,{children:[(0,s.jsx)(E.ty,{asChild:!0,children:(0,s.jsxs)(I.Avatar,{className:"cursor-pointer",children:[(0,s.jsx)(I.AvatarImage,{src:t.avatar_url,alt:t.nickname}),(0,s.jsx)(I.q,{children:t.nickname})]})}),(0,s.jsx)(E.SQ,{className:"mx-4 bg-background",children:n.map((e,t)=>(0,s.jsxs)(i.Fragment,{children:[(0,s.jsx)(E._2,{className:"flex justify-center cursor-pointer",children:e.url?(0,s.jsx)(N.N_,{href:e.url,target:e.target,children:e.title}):(0,s.jsx)("button",{onClick:e.onClick,children:e.title})},t),t!==n.length-1&&(0,s.jsx)(E.mB,{})]},t))})]})}function q(){(0,R.c3)();let{user:e}=(0,L.U)();return(0,s.jsx)("div",{className:"flex items-center gap-x-2 px-2 cursor-pointer",children:e?(0,s.jsx)(U,{user:e}):(0,s.jsx)(A,{})})}var D=a(92116),F=a(88999),P=a(17184);function V(){let{theme:e,setTheme:t}=(0,L.U)(),a=function(a){a!==e&&((0,P.PW)(F.wD.Theme,a,-1),t(a))};return(0,s.jsx)("div",{className:"flex items-center gap-x-2 px-2",children:"dark"===e?(0,s.jsx)(D.uSI,{className:"cursor-pointer text-lg text-muted-foreground",onClick:()=>a("light"),width:80,height:20}):(0,s.jsx)(D.KsI,{className:"cursor-pointer text-lg text-muted-foreground",onClick:()=>a("dark"),width:80,height:20})})}function G(e){var t,a,i,l,o,d,p,w,y,k,_,L,R,A,I,E,S,U,D,F,P;let{header:G}=e;return G.disabled?null:(0,s.jsx)("section",{className:"py-3",children:(0,s.jsxs)("div",{className:"container",children:[(0,s.jsxs)("nav",{className:"hidden justify-between lg:flex",children:[(0,s.jsxs)("div",{className:"flex items-center gap-6",children:[(0,s.jsxs)(N.N_,{href:(null===(t=G.brand)||void 0===t?void 0:t.url)||"/",className:"flex items-center gap-2",children:[(null===(i=G.brand)||void 0===i?void 0:null===(a=i.logo)||void 0===a?void 0:a.src)&&(0,s.jsx)("img",{src:G.brand.logo.src,alt:G.brand.logo.alt||G.brand.title,className:"w-8"}),(null===(l=G.brand)||void 0===l?void 0:l.title)&&(0,s.jsx)("span",{className:"text-xl text-primary font-bold",children:(null===(o=G.brand)||void 0===o?void 0:o.title)||""})]}),(0,s.jsx)("div",{className:"flex items-center",children:(0,s.jsx)(u,{children:(0,s.jsx)(m,{children:null===(p=G.nav)||void 0===p?void 0:null===(d=p.items)||void 0===d?void 0:d.map((e,t)=>e.children&&e.children.length>0?(0,s.jsxs)(f,{className:"text-muted-foreground",children:[(0,s.jsxs)(h,{children:[e.icon&&(0,s.jsx)(j.default,{name:e.icon,className:"size-4 shrink-0 mr-2"}),(0,s.jsx)("span",{children:e.title})]}),(0,s.jsx)(v,{children:(0,s.jsx)("ul",{className:"w-80 p-3",children:(0,s.jsx)(g,{children:e.children.map((e,t)=>(0,s.jsx)("li",{children:(0,s.jsxs)(N.N_,{className:(0,c.cn)("flex select-none gap-4 rounded-md p-3 leading-none no-underline outline-hidden transition-colors hover:bg-accent hover:text-accent-foreground focus:bg-accent focus:text-accent-foreground"),href:e.url,target:e.target,children:[e.icon&&(0,s.jsx)(j.default,{name:e.icon,className:"size-5 shrink-0"}),(0,s.jsxs)("div",{children:[(0,s.jsx)("div",{className:"text-sm font-semibold",children:e.title}),(0,s.jsx)("p",{className:"text-sm leading-snug text-muted-foreground",children:e.description})]})]})},t))})})})]},t):(0,s.jsx)(f,{children:(0,s.jsxs)(N.N_,{className:(0,c.cn)("text-muted-foreground",x,(0,r.r)({variant:"ghost"})),href:e.url,target:e.target,children:[e.icon&&(0,s.jsx)(j.default,{name:e.icon,className:"size-4 shrink-0 mr-0"}),e.title]})},t))})})})]}),(0,s.jsxs)("div",{className:"shrink-0 flex gap-2 items-center",children:[G.show_locale&&(0,s.jsx)(z,{}),G.show_theme&&(0,s.jsx)(V,{}),null===(w=G.buttons)||void 0===w?void 0:w.map((e,t)=>(0,s.jsx)(r.$,{variant:e.variant,children:(0,s.jsxs)(N.N_,{href:e.url,target:e.target||"",className:"flex items-center gap-1 cursor-pointer",children:[e.title,e.icon&&(0,s.jsx)(j.default,{name:e.icon,className:"size-4 shrink-0"})]})},t)),G.show_sign&&(0,s.jsx)(q,{})]})]}),(0,s.jsx)("div",{className:"block lg:hidden",children:(0,s.jsxs)("div",{className:"flex items-center justify-between",children:[(0,s.jsxs)(N.N_,{href:(null===(y=G.brand)||void 0===y?void 0:y.url)||"/",className:"flex items-center gap-2",children:[(null===(_=G.brand)||void 0===_?void 0:null===(k=_.logo)||void 0===k?void 0:k.src)&&(0,s.jsx)("img",{src:G.brand.logo.src,alt:G.brand.logo.alt||G.brand.title,className:"w-8"}),(null===(L=G.brand)||void 0===L?void 0:L.title)&&(0,s.jsx)("span",{className:"text-xl font-bold",children:(null===(R=G.brand)||void 0===R?void 0:R.title)||""})]}),(0,s.jsxs)(b.cj,{children:[(0,s.jsx)(b.CG,{asChild:!0,children:(0,s.jsx)(r.$,{variant:"default",size:"icon",children:(0,s.jsx)(C.A,{className:"size-4"})})}),(0,s.jsxs)(b.h,{className:"overflow-y-auto",children:[(0,s.jsx)(b.Fm,{children:(0,s.jsx)(b.qp,{children:(0,s.jsxs)(N.N_,{href:(null===(A=G.brand)||void 0===A?void 0:A.url)||"/",className:"flex items-center gap-2",children:[(null===(E=G.brand)||void 0===E?void 0:null===(I=E.logo)||void 0===I?void 0:I.src)&&(0,s.jsx)("img",{src:G.brand.logo.src,alt:G.brand.logo.alt||G.brand.title,className:"w-8"}),(null===(S=G.brand)||void 0===S?void 0:S.title)&&(0,s.jsx)("span",{className:"text-xl font-bold",children:(null===(U=G.brand)||void 0===U?void 0:U.title)||""})]})})}),(0,s.jsx)("div",{className:"mb-8 mt-8 flex flex-col gap-4",children:(0,s.jsx)(n.nD,{type:"single",collapsible:!0,className:"w-full",children:null===(F=G.nav)||void 0===F?void 0:null===(D=F.items)||void 0===D?void 0:D.map((e,t)=>e.children&&e.children.length>0?(0,s.jsxs)(n.As,{value:e.title||"",className:"border-b-0",children:[(0,s.jsx)(n.$m,{className:"mb-4 py-0 font-semibold hover:no-underline text-left",children:e.title}),(0,s.jsx)(n.ub,{className:"mt-2",children:e.children.map((e,t)=>(0,s.jsxs)(N.N_,{className:(0,c.cn)("flex select-none gap-4 rounded-md p-3 leading-none outline-hidden transition-colors hover:bg-accent hover:text-accent-foreground focus:bg-accent focus:text-accent-foreground"),href:e.url,target:e.target,children:[e.icon&&(0,s.jsx)(j.default,{name:e.icon,className:"size-4 shrink-0"}),(0,s.jsxs)("div",{children:[(0,s.jsx)("div",{className:"text-sm font-semibold",children:e.title}),(0,s.jsx)("p",{className:"text-sm leading-snug text-muted-foreground",children:e.description})]})]},t))})]},t):(0,s.jsxs)(N.N_,{href:e.url,target:e.target,className:"font-semibold my-4 flex items-center gap-2 px-4",children:[e.icon&&(0,s.jsx)(j.default,{name:e.icon,className:"size-4 shrink-0"}),e.title]},t))})}),(0,s.jsx)("div",{className:"flex-1"}),(0,s.jsxs)("div",{className:"border-t pt-4",children:[(0,s.jsxs)("div",{className:"mt-2 flex flex-col gap-3",children:[null===(P=G.buttons)||void 0===P?void 0:P.map((e,t)=>(0,s.jsx)(r.$,{variant:e.variant,children:(0,s.jsxs)(N.N_,{href:e.url,target:e.target||"",className:"flex items-center gap-1",children:[e.title,e.icon&&(0,s.jsx)(j.default,{name:e.icon,className:"size-4 shrink-0"})]})},t)),G.show_sign&&(0,s.jsx)(q,{})]}),(0,s.jsxs)("div",{className:"mt-4 flex items-center gap-2",children:[G.show_locale&&(0,s.jsx)(z,{}),(0,s.jsx)("div",{className:"flex-1"}),G.show_theme&&(0,s.jsx)(V,{})]})]})]})]})]})})]})})}},55714:(e,t,a)=>{a.d(t,{$m:()=>c,As:()=>d,nD:()=>o,ub:()=>u});var s=a(16261),n=a(95361),r=a(71741),i=a(18409),l=a(61063);let o=r.bL,d=n.forwardRef((e,t)=>{let{className:a,...n}=e;return(0,s.jsx)(r.q7,{ref:t,className:(0,l.cn)("border-b",a),...n})});d.displayName="AccordionItem";let c=n.forwardRef((e,t)=>{let{className:a,children:n,...o}=e;return(0,s.jsx)(r.Y9,{className:"flex",children:(0,s.jsxs)(r.l9,{ref:t,className:(0,l.cn)("flex flex-1 items-center justify-between py-4 font-medium transition-all hover:underline [&[data-state=open]>svg]:rotate-180",a),...o,children:[n,(0,s.jsx)(i.A,{className:"h-4 w-4 shrink-0 transition-transform duration-200"})]})})});c.displayName=r.l9.displayName;let u=n.forwardRef((e,t)=>{let{className:a,children:n,...i}=e;return(0,s.jsx)(r.UC,{ref:t,className:"overflow-hidden text-sm transition-all data-[state=closed]:animate-accordion-up data-[state=open]:animate-accordion-down",...i,children:(0,s.jsx)("div",{className:(0,l.cn)("pb-4 pt-0",a),children:n})})});u.displayName=r.UC.displayName},55777:(e,t,a)=>{a.d(t,{GB:()=>l,IB:()=>s,L$:()=>n,b:()=>i,q:()=>r,u7:()=>o});let s=["en","zh"],n={en:"English",zh:"中文"},r="en",i="as-needed",l=!1,o={en:{"privacy-policy":"/privacy-policy","terms-of-service":"/terms-of-service"}}},61063:(e,t,a)=>{a.d(t,{cn:()=>r});var s=a(87786),n=a(34835);function r(){for(var e=arguments.length,t=Array(e),a=0;a<e;a++)t[a]=arguments[a];return(0,n.QP)((0,s.$)(t))}},62935:(e,t,a)=>{a.d(t,{N_:()=>i,a8:()=>o,rd:()=>d});var s=a(55777),n=a(39754);let r=(0,a(31068).A)({locales:s.IB,defaultLocale:s.q,localePrefix:s.b,pathnames:s.u7,localeDetection:s.GB}),{Link:i,redirect:l,usePathname:o,useRouter:d}=(0,n.A)(r)},63843:(e,t,a)=>{a.d(t,{lg:()=>s});let s=()=>Date.parse(new Date().toUTCString())/1e3},82888:(e,t,a)=>{a.d(t,{$:()=>o,r:()=>l});var s=a(16261);a(95361);var n=a(76012),r=a(20604),i=a(61063);let l=(0,r.F)("inline-flex items-center justify-center gap-2 whitespace-nowrap rounded-lg text-sm font-medium transition-all duration-200 disabled:pointer-events-none disabled:opacity-50 [&_svg]:pointer-events-none [&_svg:not([class*='size-'])]:size-4 shrink-0 [&_svg]:shrink-0 outline-none focus-visible:border-ring focus-visible:ring-ring/50 focus-visible:ring-[3px] aria-invalid:ring-destructive/20 dark:aria-invalid:ring-destructive/40 aria-invalid:border-destructive",{variants:{variant:{default:"bg-gradient-to-r from-primary to-accent text-primary-foreground shadow-md hover:shadow-lg hover:scale-[1.02] active:scale-[0.98]",destructive:"bg-destructive text-destructive-foreground shadow-md hover:bg-destructive/90 hover:shadow-lg hover:scale-[1.02] active:scale-[0.98] focus-visible:ring-destructive/20 dark:focus-visible:ring-destructive/40",outline:"border-2 border-primary/20 bg-background/50 backdrop-blur-sm shadow-sm hover:bg-accent/50 hover:text-accent-foreground hover:border-primary/40 hover:shadow-md hover:scale-[1.02] active:scale-[0.98] dark:bg-input/30 dark:border-input dark:hover:bg-input/50",secondary:"bg-gradient-to-r from-secondary to-muted text-secondary-foreground shadow-sm hover:shadow-md hover:scale-[1.02] active:scale-[0.98]",ghost:"hover:bg-accent/50 hover:text-accent-foreground hover:scale-[1.02] active:scale-[0.98] dark:hover:bg-accent/30",link:"text-primary underline-offset-4 hover:underline hover:text-accent"},size:{default:"h-10 px-6 py-2 has-[>svg]:px-4",sm:"h-8 rounded-lg gap-1.5 px-4 has-[>svg]:px-3",lg:"h-12 rounded-lg px-8 has-[>svg]:px-6 text-base",icon:"size-10 rounded-full"}},defaultVariants:{variant:"default",size:"default"}});function o(e){let{className:t,variant:a,size:r,asChild:o=!1,...d}=e,c=o?n.DX:"button";return(0,s.jsx)(c,{"data-slot":"button",className:(0,i.cn)(l({variant:a,size:r,className:t})),...d})}},88999:(e,t,a)=>{a.d(t,{wD:()=>s});let s={Theme:"THEME",InviteCode:"INVITE_CODE",LanguagePreference:"LANGUAGE_PREFERENCE",LanguageSwitchAsked:"LANGUAGE_SWITCH_ASKED"}}}]);