"use strict";(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[35],{38675:(t,e,n)=>{n.d(e,{X:()=>o});var r=n(95361),i=n(53765);function o(t){let[e,n]=r.useState(void 0);return(0,i.N)(()=>{if(t){n({width:t.offsetWidth,height:t.offsetHeight});let e=new ResizeObserver(e=>{let r,i;if(!Array.isArray(e)||!e.length)return;let o=e[0];if("borderBoxSize"in o){let t=o.borderBoxSize,e=Array.isArray(t)?t[0]:t;r=e.inlineSize,i=e.blockSize}else r=t.offsetWidth,i=t.offsetHeight;n({width:r,height:i})});return e.observe(t,{box:"border-box"}),()=>e.unobserve(t)}n(void 0)},[t]),e}},72129:(t,e,n)=>{n.d(e,{Qg:()=>l,bL:()=>f});var r=n(95361),i=n(72637),o=n(16261),l=Object.freeze({position:"absolute",border:0,width:1,height:1,padding:0,margin:-1,overflow:"hidden",clip:"rect(0, 0, 0, 0)",whiteSpace:"nowrap",wordWrap:"normal"}),a=r.forwardRef((t,e)=>(0,o.jsx)(i.sG.span,{...t,ref:e,style:{...l,...t.style}}));a.displayName="VisuallyHidden";var f=a},90683:(t,e,n)=>{n.d(e,{Mz:()=>et,i3:()=>en,UC:()=>ee,bL:()=>t4,Bk:()=>t$});var r=n(95361);let i=["top","right","bottom","left"],o=Math.min,l=Math.max,a=Math.round,f=Math.floor,s=t=>({x:t,y:t}),c={left:"right",right:"left",bottom:"top",top:"bottom"},u={start:"end",end:"start"};function d(t,e){return"function"==typeof t?t(e):t}function p(t){return t.split("-")[0]}function h(t){return t.split("-")[1]}function m(t){return"x"===t?"y":"x"}function g(t){return"y"===t?"height":"width"}let y=new Set(["top","bottom"]);function w(t){return y.has(p(t))?"y":"x"}function v(t){return t.replace(/start|end/g,t=>u[t])}let x=["left","right"],b=["right","left"],A=["top","bottom"],R=["bottom","top"];function S(t){return t.replace(/left|right|bottom|top/g,t=>c[t])}function L(t){return"number"!=typeof t?{top:0,right:0,bottom:0,left:0,...t}:{top:t,right:t,bottom:t,left:t}}function T(t){let{x:e,y:n,width:r,height:i}=t;return{width:r,height:i,top:n,left:e,right:e+r,bottom:n+i,x:e,y:n}}function E(t,e,n){let r,{reference:i,floating:o}=t,l=w(e),a=m(w(e)),f=g(a),s=p(e),c="y"===l,u=i.x+i.width/2-o.width/2,d=i.y+i.height/2-o.height/2,y=i[f]/2-o[f]/2;switch(s){case"top":r={x:u,y:i.y-o.height};break;case"bottom":r={x:u,y:i.y+i.height};break;case"right":r={x:i.x+i.width,y:d};break;case"left":r={x:i.x-o.width,y:d};break;default:r={x:i.x,y:i.y}}switch(h(e)){case"start":r[a]-=y*(n&&c?-1:1);break;case"end":r[a]+=y*(n&&c?-1:1)}return r}let O=async(t,e,n)=>{let{placement:r="bottom",strategy:i="absolute",middleware:o=[],platform:l}=n,a=o.filter(Boolean),f=await (null==l.isRTL?void 0:l.isRTL(e)),s=await l.getElementRects({reference:t,floating:e,strategy:i}),{x:c,y:u}=E(s,r,f),d=r,p={},h=0;for(let n=0;n<a.length;n++){let{name:o,fn:m}=a[n],{x:g,y:y,data:w,reset:v}=await m({x:c,y:u,initialPlacement:r,placement:d,strategy:i,middlewareData:p,rects:s,platform:l,elements:{reference:t,floating:e}});c=null!=g?g:c,u=null!=y?y:u,p={...p,[o]:{...p[o],...w}},v&&h<=50&&(h++,"object"==typeof v&&(v.placement&&(d=v.placement),v.rects&&(s=!0===v.rects?await l.getElementRects({reference:t,floating:e,strategy:i}):v.rects),{x:c,y:u}=E(s,d,f)),n=-1)}return{x:c,y:u,placement:d,strategy:i,middlewareData:p}};async function C(t,e){var n;void 0===e&&(e={});let{x:r,y:i,platform:o,rects:l,elements:a,strategy:f}=t,{boundary:s="clippingAncestors",rootBoundary:c="viewport",elementContext:u="floating",altBoundary:p=!1,padding:h=0}=d(e,t),m=L(h),g=a[p?"floating"===u?"reference":"floating":u],y=T(await o.getClippingRect({element:null==(n=await (null==o.isElement?void 0:o.isElement(g)))||n?g:g.contextElement||await (null==o.getDocumentElement?void 0:o.getDocumentElement(a.floating)),boundary:s,rootBoundary:c,strategy:f})),w="floating"===u?{x:r,y:i,width:l.floating.width,height:l.floating.height}:l.reference,v=await (null==o.getOffsetParent?void 0:o.getOffsetParent(a.floating)),x=await (null==o.isElement?void 0:o.isElement(v))&&await (null==o.getScale?void 0:o.getScale(v))||{x:1,y:1},b=T(o.convertOffsetParentRelativeRectToViewportRelativeRect?await o.convertOffsetParentRelativeRectToViewportRelativeRect({elements:a,rect:w,offsetParent:v,strategy:f}):w);return{top:(y.top-b.top+m.top)/x.y,bottom:(b.bottom-y.bottom+m.bottom)/x.y,left:(y.left-b.left+m.left)/x.x,right:(b.right-y.right+m.right)/x.x}}function P(t,e){return{top:t.top-e.height,right:t.right-e.width,bottom:t.bottom-e.height,left:t.left-e.width}}function k(t){return i.some(e=>t[e]>=0)}let H=new Set(["left","top"]);async function D(t,e){let{placement:n,platform:r,elements:i}=t,o=await (null==r.isRTL?void 0:r.isRTL(i.floating)),l=p(n),a=h(n),f="y"===w(n),s=H.has(l)?-1:1,c=o&&f?-1:1,u=d(e,t),{mainAxis:m,crossAxis:g,alignmentAxis:y}="number"==typeof u?{mainAxis:u,crossAxis:0,alignmentAxis:null}:{mainAxis:u.mainAxis||0,crossAxis:u.crossAxis||0,alignmentAxis:u.alignmentAxis};return a&&"number"==typeof y&&(g="end"===a?-1*y:y),f?{x:g*c,y:m*s}:{x:m*s,y:g*c}}function j(){return"undefined"!=typeof window}function N(t){return z(t)?(t.nodeName||"").toLowerCase():"#document"}function F(t){var e;return(null==t||null==(e=t.ownerDocument)?void 0:e.defaultView)||window}function W(t){var e;return null==(e=(z(t)?t.ownerDocument:t.document)||window.document)?void 0:e.documentElement}function z(t){return!!j()&&(t instanceof Node||t instanceof F(t).Node)}function M(t){return!!j()&&(t instanceof Element||t instanceof F(t).Element)}function B(t){return!!j()&&(t instanceof HTMLElement||t instanceof F(t).HTMLElement)}function V(t){return!!j()&&"undefined"!=typeof ShadowRoot&&(t instanceof ShadowRoot||t instanceof F(t).ShadowRoot)}let _=new Set(["inline","contents"]);function X(t){let{overflow:e,overflowX:n,overflowY:r,display:i}=tt(t);return/auto|scroll|overlay|hidden|clip/.test(e+r+n)&&!_.has(i)}let I=new Set(["table","td","th"]),Y=[":popover-open",":modal"];function G(t){return Y.some(e=>{try{return t.matches(e)}catch(t){return!1}})}let $=["transform","translate","scale","rotate","perspective"],q=["transform","translate","scale","rotate","perspective","filter"],Q=["paint","layout","strict","content"];function U(t){let e=J(),n=M(t)?tt(t):t;return $.some(t=>!!n[t]&&"none"!==n[t])||!!n.containerType&&"normal"!==n.containerType||!e&&!!n.backdropFilter&&"none"!==n.backdropFilter||!e&&!!n.filter&&"none"!==n.filter||q.some(t=>(n.willChange||"").includes(t))||Q.some(t=>(n.contain||"").includes(t))}function J(){return"undefined"!=typeof CSS&&!!CSS.supports&&CSS.supports("-webkit-backdrop-filter","none")}let K=new Set(["html","body","#document"]);function Z(t){return K.has(N(t))}function tt(t){return F(t).getComputedStyle(t)}function te(t){return M(t)?{scrollLeft:t.scrollLeft,scrollTop:t.scrollTop}:{scrollLeft:t.scrollX,scrollTop:t.scrollY}}function tn(t){if("html"===N(t))return t;let e=t.assignedSlot||t.parentNode||V(t)&&t.host||W(t);return V(e)?e.host:e}function tr(t,e,n){var r;void 0===e&&(e=[]),void 0===n&&(n=!0);let i=function t(e){let n=tn(e);return Z(n)?e.ownerDocument?e.ownerDocument.body:e.body:B(n)&&X(n)?n:t(n)}(t),o=i===(null==(r=t.ownerDocument)?void 0:r.body),l=F(i);if(o){let t=ti(l);return e.concat(l,l.visualViewport||[],X(i)?i:[],t&&n?tr(t):[])}return e.concat(i,tr(i,[],n))}function ti(t){return t.parent&&Object.getPrototypeOf(t.parent)?t.frameElement:null}function to(t){let e=tt(t),n=parseFloat(e.width)||0,r=parseFloat(e.height)||0,i=B(t),o=i?t.offsetWidth:n,l=i?t.offsetHeight:r,f=a(n)!==o||a(r)!==l;return f&&(n=o,r=l),{width:n,height:r,$:f}}function tl(t){return M(t)?t:t.contextElement}function ta(t){let e=tl(t);if(!B(e))return s(1);let n=e.getBoundingClientRect(),{width:r,height:i,$:o}=to(e),l=(o?a(n.width):n.width)/r,f=(o?a(n.height):n.height)/i;return l&&Number.isFinite(l)||(l=1),f&&Number.isFinite(f)||(f=1),{x:l,y:f}}let tf=s(0);function ts(t){let e=F(t);return J()&&e.visualViewport?{x:e.visualViewport.offsetLeft,y:e.visualViewport.offsetTop}:tf}function tc(t,e,n,r){var i;void 0===e&&(e=!1),void 0===n&&(n=!1);let o=t.getBoundingClientRect(),l=tl(t),a=s(1);e&&(r?M(r)&&(a=ta(r)):a=ta(t));let f=(void 0===(i=n)&&(i=!1),r&&(!i||r===F(l))&&i)?ts(l):s(0),c=(o.left+f.x)/a.x,u=(o.top+f.y)/a.y,d=o.width/a.x,p=o.height/a.y;if(l){let t=F(l),e=r&&M(r)?F(r):r,n=t,i=ti(n);for(;i&&r&&e!==n;){let t=ta(i),e=i.getBoundingClientRect(),r=tt(i),o=e.left+(i.clientLeft+parseFloat(r.paddingLeft))*t.x,l=e.top+(i.clientTop+parseFloat(r.paddingTop))*t.y;c*=t.x,u*=t.y,d*=t.x,p*=t.y,c+=o,u+=l,i=ti(n=F(i))}}return T({width:d,height:p,x:c,y:u})}function tu(t,e){let n=te(t).scrollLeft;return e?e.left+n:tc(W(t)).left+n}function td(t,e,n){void 0===n&&(n=!1);let r=t.getBoundingClientRect();return{x:r.left+e.scrollLeft-(n?0:tu(t,r)),y:r.top+e.scrollTop}}let tp=new Set(["absolute","fixed"]);function th(t,e,n){let r;if("viewport"===e)r=function(t,e){let n=F(t),r=W(t),i=n.visualViewport,o=r.clientWidth,l=r.clientHeight,a=0,f=0;if(i){o=i.width,l=i.height;let t=J();(!t||t&&"fixed"===e)&&(a=i.offsetLeft,f=i.offsetTop)}return{width:o,height:l,x:a,y:f}}(t,n);else if("document"===e)r=function(t){let e=W(t),n=te(t),r=t.ownerDocument.body,i=l(e.scrollWidth,e.clientWidth,r.scrollWidth,r.clientWidth),o=l(e.scrollHeight,e.clientHeight,r.scrollHeight,r.clientHeight),a=-n.scrollLeft+tu(t),f=-n.scrollTop;return"rtl"===tt(r).direction&&(a+=l(e.clientWidth,r.clientWidth)-i),{width:i,height:o,x:a,y:f}}(W(t));else if(M(e))r=function(t,e){let n=tc(t,!0,"fixed"===e),r=n.top+t.clientTop,i=n.left+t.clientLeft,o=B(t)?ta(t):s(1),l=t.clientWidth*o.x,a=t.clientHeight*o.y;return{width:l,height:a,x:i*o.x,y:r*o.y}}(e,n);else{let n=ts(t);r={x:e.x-n.x,y:e.y-n.y,width:e.width,height:e.height}}return T(r)}function tm(t){return"static"===tt(t).position}function tg(t,e){if(!B(t)||"fixed"===tt(t).position)return null;if(e)return e(t);let n=t.offsetParent;return W(t)===n&&(n=n.ownerDocument.body),n}function ty(t,e){var n;let r=F(t);if(G(t))return r;if(!B(t)){let e=tn(t);for(;e&&!Z(e);){if(M(e)&&!tm(e))return e;e=tn(e)}return r}let i=tg(t,e);for(;i&&(n=i,I.has(N(n)))&&tm(i);)i=tg(i,e);return i&&Z(i)&&tm(i)&&!U(i)?r:i||function(t){let e=tn(t);for(;B(e)&&!Z(e);){if(U(e))return e;if(G(e))break;e=tn(e)}return null}(t)||r}let tw=async function(t){let e=this.getOffsetParent||ty,n=this.getDimensions,r=await n(t.floating);return{reference:function(t,e,n){let r=B(e),i=W(e),o="fixed"===n,l=tc(t,!0,o,e),a={scrollLeft:0,scrollTop:0},f=s(0);if(r||!r&&!o){if(("body"!==N(e)||X(i))&&(a=te(e)),r){let t=tc(e,!0,o,e);f.x=t.x+e.clientLeft,f.y=t.y+e.clientTop}else i&&(f.x=tu(i))}o&&!r&&i&&(f.x=tu(i));let c=!i||r||o?s(0):td(i,a);return{x:l.left+a.scrollLeft-f.x-c.x,y:l.top+a.scrollTop-f.y-c.y,width:l.width,height:l.height}}(t.reference,await e(t.floating),t.strategy),floating:{x:0,y:0,width:r.width,height:r.height}}},tv={convertOffsetParentRelativeRectToViewportRelativeRect:function(t){let{elements:e,rect:n,offsetParent:r,strategy:i}=t,o="fixed"===i,l=W(r),a=!!e&&G(e.floating);if(r===l||a&&o)return n;let f={scrollLeft:0,scrollTop:0},c=s(1),u=s(0),d=B(r);if((d||!d&&!o)&&(("body"!==N(r)||X(l))&&(f=te(r)),B(r))){let t=tc(r);c=ta(r),u.x=t.x+r.clientLeft,u.y=t.y+r.clientTop}let p=!l||d||o?s(0):td(l,f,!0);return{width:n.width*c.x,height:n.height*c.y,x:n.x*c.x-f.scrollLeft*c.x+u.x+p.x,y:n.y*c.y-f.scrollTop*c.y+u.y+p.y}},getDocumentElement:W,getClippingRect:function(t){let{element:e,boundary:n,rootBoundary:r,strategy:i}=t,a=[..."clippingAncestors"===n?G(e)?[]:function(t,e){let n=e.get(t);if(n)return n;let r=tr(t,[],!1).filter(t=>M(t)&&"body"!==N(t)),i=null,o="fixed"===tt(t).position,l=o?tn(t):t;for(;M(l)&&!Z(l);){let e=tt(l),n=U(l);n||"fixed"!==e.position||(i=null),(o?!n&&!i:!n&&"static"===e.position&&!!i&&tp.has(i.position)||X(l)&&!n&&function t(e,n){let r=tn(e);return!(r===n||!M(r)||Z(r))&&("fixed"===tt(r).position||t(r,n))}(t,l))?r=r.filter(t=>t!==l):i=e,l=tn(l)}return e.set(t,r),r}(e,this._c):[].concat(n),r],f=a[0],s=a.reduce((t,n)=>{let r=th(e,n,i);return t.top=l(r.top,t.top),t.right=o(r.right,t.right),t.bottom=o(r.bottom,t.bottom),t.left=l(r.left,t.left),t},th(e,f,i));return{width:s.right-s.left,height:s.bottom-s.top,x:s.left,y:s.top}},getOffsetParent:ty,getElementRects:tw,getClientRects:function(t){return Array.from(t.getClientRects())},getDimensions:function(t){let{width:e,height:n}=to(t);return{width:e,height:n}},getScale:ta,isElement:M,isRTL:function(t){return"rtl"===tt(t).direction}};function tx(t,e){return t.x===e.x&&t.y===e.y&&t.width===e.width&&t.height===e.height}let tb=t=>({name:"arrow",options:t,async fn(e){let{x:n,y:r,placement:i,rects:a,platform:f,elements:s,middlewareData:c}=e,{element:u,padding:p=0}=d(t,e)||{};if(null==u)return{};let y=L(p),v={x:n,y:r},x=m(w(i)),b=g(x),A=await f.getDimensions(u),R="y"===x,S=R?"clientHeight":"clientWidth",T=a.reference[b]+a.reference[x]-v[x]-a.floating[b],E=v[x]-a.reference[x],O=await (null==f.getOffsetParent?void 0:f.getOffsetParent(u)),C=O?O[S]:0;C&&await (null==f.isElement?void 0:f.isElement(O))||(C=s.floating[S]||a.floating[b]);let P=C/2-A[b]/2-1,k=o(y[R?"top":"left"],P),H=o(y[R?"bottom":"right"],P),D=C-A[b]-H,j=C/2-A[b]/2+(T/2-E/2),N=l(k,o(j,D)),F=!c.arrow&&null!=h(i)&&j!==N&&a.reference[b]/2-(j<k?k:H)-A[b]/2<0,W=F?j<k?j-k:j-D:0;return{[x]:v[x]+W,data:{[x]:N,centerOffset:j-N-W,...F&&{alignmentOffset:W}},reset:F}}}),tA=(t,e,n)=>{let r=new Map,i={platform:tv,...n},o={...i.platform,_c:r};return O(t,e,{...i,platform:o})};var tR=n(32196),tS="undefined"!=typeof document?r.useLayoutEffect:function(){};function tL(t,e){let n,r,i;if(t===e)return!0;if(typeof t!=typeof e)return!1;if("function"==typeof t&&t.toString()===e.toString())return!0;if(t&&e&&"object"==typeof t){if(Array.isArray(t)){if((n=t.length)!==e.length)return!1;for(r=n;0!=r--;)if(!tL(t[r],e[r]))return!1;return!0}if((n=(i=Object.keys(t)).length)!==Object.keys(e).length)return!1;for(r=n;0!=r--;)if(!({}).hasOwnProperty.call(e,i[r]))return!1;for(r=n;0!=r--;){let n=i[r];if(("_owner"!==n||!t.$$typeof)&&!tL(t[n],e[n]))return!1}return!0}return t!=t&&e!=e}function tT(t){return"undefined"==typeof window?1:(t.ownerDocument.defaultView||window).devicePixelRatio||1}function tE(t,e){let n=tT(t);return Math.round(e*n)/n}function tO(t){let e=r.useRef(t);return tS(()=>{e.current=t}),e}let tC=t=>({name:"arrow",options:t,fn(e){let{element:n,padding:r}="function"==typeof t?t(e):t;return n&&({}).hasOwnProperty.call(n,"current")?null!=n.current?tb({element:n.current,padding:r}).fn(e):{}:n?tb({element:n,padding:r}).fn(e):{}}}),tP=(t,e)=>({...function(t){return void 0===t&&(t=0),{name:"offset",options:t,async fn(e){var n,r;let{x:i,y:o,placement:l,middlewareData:a}=e,f=await D(e,t);return l===(null==(n=a.offset)?void 0:n.placement)&&null!=(r=a.arrow)&&r.alignmentOffset?{}:{x:i+f.x,y:o+f.y,data:{...f,placement:l}}}}}(t),options:[t,e]}),tk=(t,e)=>({...function(t){return void 0===t&&(t={}),{name:"shift",options:t,async fn(e){let{x:n,y:r,placement:i}=e,{mainAxis:a=!0,crossAxis:f=!1,limiter:s={fn:t=>{let{x:e,y:n}=t;return{x:e,y:n}}},...c}=d(t,e),u={x:n,y:r},h=await C(e,c),g=w(p(i)),y=m(g),v=u[y],x=u[g];if(a){let t="y"===y?"top":"left",e="y"===y?"bottom":"right",n=v+h[t],r=v-h[e];v=l(n,o(v,r))}if(f){let t="y"===g?"top":"left",e="y"===g?"bottom":"right",n=x+h[t],r=x-h[e];x=l(n,o(x,r))}let b=s.fn({...e,[y]:v,[g]:x});return{...b,data:{x:b.x-n,y:b.y-r,enabled:{[y]:a,[g]:f}}}}}}(t),options:[t,e]}),tH=(t,e)=>({...function(t){return void 0===t&&(t={}),{options:t,fn(e){let{x:n,y:r,placement:i,rects:o,middlewareData:l}=e,{offset:a=0,mainAxis:f=!0,crossAxis:s=!0}=d(t,e),c={x:n,y:r},u=w(i),h=m(u),g=c[h],y=c[u],v=d(a,e),x="number"==typeof v?{mainAxis:v,crossAxis:0}:{mainAxis:0,crossAxis:0,...v};if(f){let t="y"===h?"height":"width",e=o.reference[h]-o.floating[t]+x.mainAxis,n=o.reference[h]+o.reference[t]-x.mainAxis;g<e?g=e:g>n&&(g=n)}if(s){var b,A;let t="y"===h?"width":"height",e=H.has(p(i)),n=o.reference[u]-o.floating[t]+(e&&(null==(b=l.offset)?void 0:b[u])||0)+(e?0:x.crossAxis),r=o.reference[u]+o.reference[t]+(e?0:(null==(A=l.offset)?void 0:A[u])||0)-(e?x.crossAxis:0);y<n?y=n:y>r&&(y=r)}return{[h]:g,[u]:y}}}}(t),options:[t,e]}),tD=(t,e)=>({...function(t){return void 0===t&&(t={}),{name:"flip",options:t,async fn(e){var n,r,i,o,l;let{placement:a,middlewareData:f,rects:s,initialPlacement:c,platform:u,elements:y}=e,{mainAxis:L=!0,crossAxis:T=!0,fallbackPlacements:E,fallbackStrategy:O="bestFit",fallbackAxisSideDirection:P="none",flipAlignment:k=!0,...H}=d(t,e);if(null!=(n=f.arrow)&&n.alignmentOffset)return{};let D=p(a),j=w(c),N=p(c)===c,F=await (null==u.isRTL?void 0:u.isRTL(y.floating)),W=E||(N||!k?[S(c)]:function(t){let e=S(t);return[v(t),e,v(e)]}(c)),z="none"!==P;!E&&z&&W.push(...function(t,e,n,r){let i=h(t),o=function(t,e,n){switch(t){case"top":case"bottom":if(n)return e?b:x;return e?x:b;case"left":case"right":return e?A:R;default:return[]}}(p(t),"start"===n,r);return i&&(o=o.map(t=>t+"-"+i),e&&(o=o.concat(o.map(v)))),o}(c,k,P,F));let M=[c,...W],B=await C(e,H),V=[],_=(null==(r=f.flip)?void 0:r.overflows)||[];if(L&&V.push(B[D]),T){let t=function(t,e,n){void 0===n&&(n=!1);let r=h(t),i=m(w(t)),o=g(i),l="x"===i?r===(n?"end":"start")?"right":"left":"start"===r?"bottom":"top";return e.reference[o]>e.floating[o]&&(l=S(l)),[l,S(l)]}(a,s,F);V.push(B[t[0]],B[t[1]])}if(_=[..._,{placement:a,overflows:V}],!V.every(t=>t<=0)){let t=((null==(i=f.flip)?void 0:i.index)||0)+1,e=M[t];if(e&&("alignment"!==T||j===w(e)||_.every(t=>t.overflows[0]>0&&w(t.placement)===j)))return{data:{index:t,overflows:_},reset:{placement:e}};let n=null==(o=_.filter(t=>t.overflows[0]<=0).sort((t,e)=>t.overflows[1]-e.overflows[1])[0])?void 0:o.placement;if(!n)switch(O){case"bestFit":{let t=null==(l=_.filter(t=>{if(z){let e=w(t.placement);return e===j||"y"===e}return!0}).map(t=>[t.placement,t.overflows.filter(t=>t>0).reduce((t,e)=>t+e,0)]).sort((t,e)=>t[1]-e[1])[0])?void 0:l[0];t&&(n=t);break}case"initialPlacement":n=c}if(a!==n)return{reset:{placement:n}}}return{}}}}(t),options:[t,e]}),tj=(t,e)=>({...function(t){return void 0===t&&(t={}),{name:"size",options:t,async fn(e){var n,r;let i,a;let{placement:f,rects:s,platform:c,elements:u}=e,{apply:m=()=>{},...g}=d(t,e),y=await C(e,g),v=p(f),x=h(f),b="y"===w(f),{width:A,height:R}=s.floating;"top"===v||"bottom"===v?(i=v,a=x===(await (null==c.isRTL?void 0:c.isRTL(u.floating))?"start":"end")?"left":"right"):(a=v,i="end"===x?"top":"bottom");let S=R-y.top-y.bottom,L=A-y.left-y.right,T=o(R-y[i],S),E=o(A-y[a],L),O=!e.middlewareData.shift,P=T,k=E;if(null!=(n=e.middlewareData.shift)&&n.enabled.x&&(k=L),null!=(r=e.middlewareData.shift)&&r.enabled.y&&(P=S),O&&!x){let t=l(y.left,0),e=l(y.right,0),n=l(y.top,0),r=l(y.bottom,0);b?k=A-2*(0!==t||0!==e?t+e:l(y.left,y.right)):P=R-2*(0!==n||0!==r?n+r:l(y.top,y.bottom))}await m({...e,availableWidth:k,availableHeight:P});let H=await c.getDimensions(u.floating);return A!==H.width||R!==H.height?{reset:{rects:!0}}:{}}}}(t),options:[t,e]}),tN=(t,e)=>({...function(t){return void 0===t&&(t={}),{name:"hide",options:t,async fn(e){let{rects:n}=e,{strategy:r="referenceHidden",...i}=d(t,e);switch(r){case"referenceHidden":{let t=P(await C(e,{...i,elementContext:"reference"}),n.reference);return{data:{referenceHiddenOffsets:t,referenceHidden:k(t)}}}case"escaped":{let t=P(await C(e,{...i,altBoundary:!0}),n.floating);return{data:{escapedOffsets:t,escaped:k(t)}}}default:return{}}}}}(t),options:[t,e]}),tF=(t,e)=>({...tC(t),options:[t,e]});var tW=n(72637),tz=n(16261),tM=r.forwardRef((t,e)=>{let{children:n,width:r=10,height:i=5,...o}=t;return(0,tz.jsx)(tW.sG.svg,{...o,ref:e,width:r,height:i,viewBox:"0 0 30 10",preserveAspectRatio:"none",children:t.asChild?n:(0,tz.jsx)("polygon",{points:"0,0 30,0 15,10"})})});tM.displayName="Arrow";var tB=n(94286),tV=n(22408),t_=n(21751),tX=n(53765),tI=n(38675),tY="Popper",[tG,t$]=(0,tV.A)(tY),[tq,tQ]=tG(tY),tU=t=>{let{__scopePopper:e,children:n}=t,[i,o]=r.useState(null);return(0,tz.jsx)(tq,{scope:e,anchor:i,onAnchorChange:o,children:n})};tU.displayName=tY;var tJ="PopperAnchor",tK=r.forwardRef((t,e)=>{let{__scopePopper:n,virtualRef:i,...o}=t,l=tQ(tJ,n),a=r.useRef(null),f=(0,tB.s)(e,a);return r.useEffect(()=>{l.onAnchorChange((null==i?void 0:i.current)||a.current)}),i?null:(0,tz.jsx)(tW.sG.div,{...o,ref:f})});tK.displayName=tJ;var tZ="PopperContent",[t0,t1]=tG(tZ),t2=r.forwardRef((t,e)=>{var n,i,a,s,c,u,d,p;let{__scopePopper:h,side:m="bottom",sideOffset:g=0,align:y="center",alignOffset:w=0,arrowPadding:v=0,avoidCollisions:x=!0,collisionBoundary:b=[],collisionPadding:A=0,sticky:R="partial",hideWhenDetached:S=!1,updatePositionStrategy:L="optimized",onPlaced:T,...E}=t,O=tQ(tZ,h),[C,P]=r.useState(null),k=(0,tB.s)(e,t=>P(t)),[H,D]=r.useState(null),j=(0,tI.X)(H),N=null!==(d=null==j?void 0:j.width)&&void 0!==d?d:0,F=null!==(p=null==j?void 0:j.height)&&void 0!==p?p:0,z="number"==typeof A?A:{top:0,right:0,bottom:0,left:0,...A},M=Array.isArray(b)?b:[b],B=M.length>0,V={padding:z,boundary:M.filter(t7),altBoundary:B},{refs:_,floatingStyles:X,placement:I,isPositioned:Y,middlewareData:G}=function(t){void 0===t&&(t={});let{placement:e="bottom",strategy:n="absolute",middleware:i=[],platform:o,elements:{reference:l,floating:a}={},transform:f=!0,whileElementsMounted:s,open:c}=t,[u,d]=r.useState({x:0,y:0,strategy:n,placement:e,middlewareData:{},isPositioned:!1}),[p,h]=r.useState(i);tL(p,i)||h(i);let[m,g]=r.useState(null),[y,w]=r.useState(null),v=r.useCallback(t=>{t!==R.current&&(R.current=t,g(t))},[]),x=r.useCallback(t=>{t!==S.current&&(S.current=t,w(t))},[]),b=l||m,A=a||y,R=r.useRef(null),S=r.useRef(null),L=r.useRef(u),T=null!=s,E=tO(s),O=tO(o),C=tO(c),P=r.useCallback(()=>{if(!R.current||!S.current)return;let t={placement:e,strategy:n,middleware:p};O.current&&(t.platform=O.current),tA(R.current,S.current,t).then(t=>{let e={...t,isPositioned:!1!==C.current};k.current&&!tL(L.current,e)&&(L.current=e,tR.flushSync(()=>{d(e)}))})},[p,e,n,O,C]);tS(()=>{!1===c&&L.current.isPositioned&&(L.current.isPositioned=!1,d(t=>({...t,isPositioned:!1})))},[c]);let k=r.useRef(!1);tS(()=>(k.current=!0,()=>{k.current=!1}),[]),tS(()=>{if(b&&(R.current=b),A&&(S.current=A),b&&A){if(E.current)return E.current(b,A,P);P()}},[b,A,P,E,T]);let H=r.useMemo(()=>({reference:R,floating:S,setReference:v,setFloating:x}),[v,x]),D=r.useMemo(()=>({reference:b,floating:A}),[b,A]),j=r.useMemo(()=>{let t={position:n,left:0,top:0};if(!D.floating)return t;let e=tE(D.floating,u.x),r=tE(D.floating,u.y);return f?{...t,transform:"translate("+e+"px, "+r+"px)",...tT(D.floating)>=1.5&&{willChange:"transform"}}:{position:n,left:e,top:r}},[n,f,D.floating,u.x,u.y]);return r.useMemo(()=>({...u,update:P,refs:H,elements:D,floatingStyles:j}),[u,P,H,D,j])}({strategy:"fixed",placement:m+("center"!==y?"-"+y:""),whileElementsMounted:function(){for(var t=arguments.length,e=Array(t),n=0;n<t;n++)e[n]=arguments[n];return function(t,e,n,r){let i;void 0===r&&(r={});let{ancestorScroll:a=!0,ancestorResize:s=!0,elementResize:c="function"==typeof ResizeObserver,layoutShift:u="function"==typeof IntersectionObserver,animationFrame:d=!1}=r,p=tl(t),h=a||s?[...p?tr(p):[],...tr(e)]:[];h.forEach(t=>{a&&t.addEventListener("scroll",n,{passive:!0}),s&&t.addEventListener("resize",n)});let m=p&&u?function(t,e){let n,r=null,i=W(t);function a(){var t;clearTimeout(n),null==(t=r)||t.disconnect(),r=null}return!function s(c,u){void 0===c&&(c=!1),void 0===u&&(u=1),a();let d=t.getBoundingClientRect(),{left:p,top:h,width:m,height:g}=d;if(c||e(),!m||!g)return;let y=f(h),w=f(i.clientWidth-(p+m)),v={rootMargin:-y+"px "+-w+"px "+-f(i.clientHeight-(h+g))+"px "+-f(p)+"px",threshold:l(0,o(1,u))||1},x=!0;function b(e){let r=e[0].intersectionRatio;if(r!==u){if(!x)return s();r?s(!1,r):n=setTimeout(()=>{s(!1,1e-7)},1e3)}1!==r||tx(d,t.getBoundingClientRect())||s(),x=!1}try{r=new IntersectionObserver(b,{...v,root:i.ownerDocument})}catch(t){r=new IntersectionObserver(b,v)}r.observe(t)}(!0),a}(p,n):null,g=-1,y=null;c&&(y=new ResizeObserver(t=>{let[r]=t;r&&r.target===p&&y&&(y.unobserve(e),cancelAnimationFrame(g),g=requestAnimationFrame(()=>{var t;null==(t=y)||t.observe(e)})),n()}),p&&!d&&y.observe(p),y.observe(e));let w=d?tc(t):null;return d&&function e(){let r=tc(t);w&&!tx(w,r)&&n(),w=r,i=requestAnimationFrame(e)}(),n(),()=>{var t;h.forEach(t=>{a&&t.removeEventListener("scroll",n),s&&t.removeEventListener("resize",n)}),null==m||m(),null==(t=y)||t.disconnect(),y=null,d&&cancelAnimationFrame(i)}}(...e,{animationFrame:"always"===L})},elements:{reference:O.anchor},middleware:[tP({mainAxis:g+F,alignmentAxis:w}),x&&tk({mainAxis:!0,crossAxis:!1,limiter:"partial"===R?tH():void 0,...V}),x&&tD({...V}),tj({...V,apply:t=>{let{elements:e,rects:n,availableWidth:r,availableHeight:i}=t,{width:o,height:l}=n.reference,a=e.floating.style;a.setProperty("--radix-popper-available-width","".concat(r,"px")),a.setProperty("--radix-popper-available-height","".concat(i,"px")),a.setProperty("--radix-popper-anchor-width","".concat(o,"px")),a.setProperty("--radix-popper-anchor-height","".concat(l,"px"))}}),H&&tF({element:H,padding:v}),t9({arrowWidth:N,arrowHeight:F}),S&&tN({strategy:"referenceHidden",...V})]}),[$,q]=t8(I),Q=(0,t_.c)(T);(0,tX.N)(()=>{Y&&(null==Q||Q())},[Y,Q]);let U=null===(n=G.arrow)||void 0===n?void 0:n.x,J=null===(i=G.arrow)||void 0===i?void 0:i.y,K=(null===(a=G.arrow)||void 0===a?void 0:a.centerOffset)!==0,[Z,tt]=r.useState();return(0,tX.N)(()=>{C&&tt(window.getComputedStyle(C).zIndex)},[C]),(0,tz.jsx)("div",{ref:_.setFloating,"data-radix-popper-content-wrapper":"",style:{...X,transform:Y?X.transform:"translate(0, -200%)",minWidth:"max-content",zIndex:Z,"--radix-popper-transform-origin":[null===(s=G.transformOrigin)||void 0===s?void 0:s.x,null===(c=G.transformOrigin)||void 0===c?void 0:c.y].join(" "),...(null===(u=G.hide)||void 0===u?void 0:u.referenceHidden)&&{visibility:"hidden",pointerEvents:"none"}},dir:t.dir,children:(0,tz.jsx)(t0,{scope:h,placedSide:$,onArrowChange:D,arrowX:U,arrowY:J,shouldHideArrow:K,children:(0,tz.jsx)(tW.sG.div,{"data-side":$,"data-align":q,...E,ref:k,style:{...E.style,animation:Y?void 0:"none"}})})})});t2.displayName=tZ;var t5="PopperArrow",t3={top:"bottom",right:"left",bottom:"top",left:"right"},t6=r.forwardRef(function(t,e){let{__scopePopper:n,...r}=t,i=t1(t5,n),o=t3[i.placedSide];return(0,tz.jsx)("span",{ref:i.onArrowChange,style:{position:"absolute",left:i.arrowX,top:i.arrowY,[o]:0,transformOrigin:{top:"",right:"0 0",bottom:"center 0",left:"100% 0"}[i.placedSide],transform:{top:"translateY(100%)",right:"translateY(50%) rotate(90deg) translateX(-50%)",bottom:"rotate(180deg)",left:"translateY(50%) rotate(-90deg) translateX(50%)"}[i.placedSide],visibility:i.shouldHideArrow?"hidden":void 0},children:(0,tz.jsx)(tM,{...r,ref:e,style:{...r.style,display:"block"}})})});function t7(t){return null!==t}t6.displayName=t5;var t9=t=>({name:"transformOrigin",options:t,fn(e){var n,r,i,o,l;let{placement:a,rects:f,middlewareData:s}=e,c=(null===(n=s.arrow)||void 0===n?void 0:n.centerOffset)!==0,u=c?0:t.arrowWidth,d=c?0:t.arrowHeight,[p,h]=t8(a),m={start:"0%",center:"50%",end:"100%"}[h],g=(null!==(o=null===(r=s.arrow)||void 0===r?void 0:r.x)&&void 0!==o?o:0)+u/2,y=(null!==(l=null===(i=s.arrow)||void 0===i?void 0:i.y)&&void 0!==l?l:0)+d/2,w="",v="";return"bottom"===p?(w=c?m:"".concat(g,"px"),v="".concat(-d,"px")):"top"===p?(w=c?m:"".concat(g,"px"),v="".concat(f.floating.height+d,"px")):"right"===p?(w="".concat(-d,"px"),v=c?m:"".concat(y,"px")):"left"===p&&(w="".concat(f.floating.width+d,"px"),v=c?m:"".concat(y,"px")),{data:{x:w,y:v}}}});function t8(t){let[e,n="center"]=t.split("-");return[e,n]}var t4=tU,et=tK,ee=t2,en=t6}}]);