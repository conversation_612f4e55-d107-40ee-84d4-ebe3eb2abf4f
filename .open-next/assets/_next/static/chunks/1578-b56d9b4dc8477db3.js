"use strict";(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[1578],{18409:(t,e,n)=>{n.d(e,{A:()=>r});let r=(0,n(1316).A)("ChevronDown",[["path",{d:"m6 9 6 6 6-6",key:"qrunsl"}]])},25716:(t,e,n)=>{n.d(e,{A:()=>r});let r=(0,n(1316).A)("ChevronUp",[["path",{d:"m18 15-6-6-6 6",key:"153udz"}]])},37947:(t,e,n)=>{n.d(e,{A:()=>r});let r=(0,n(1316).A)("Star",[["polygon",{points:"12 2 15.09 8.26 22 9.27 17 14.14 18.18 21.02 12 17.77 5.82 21.02 7 14.14 2 9.27 8.91 8.26 12 2",key:"8f66p6"}]])},44756:(t,e,n)=>{n.d(e,{k5:()=>u});var r=n(95361),o={color:void 0,size:void 0,className:void 0,style:void 0,attr:void 0},i=r.createContext&&r.createContext(o),l=["attr","size","title"];function a(){return(a=Object.assign?Object.assign.bind():function(t){for(var e=1;e<arguments.length;e++){var n=arguments[e];for(var r in n)Object.prototype.hasOwnProperty.call(n,r)&&(t[r]=n[r])}return t}).apply(this,arguments)}function c(t,e){var n=Object.keys(t);if(Object.getOwnPropertySymbols){var r=Object.getOwnPropertySymbols(t);e&&(r=r.filter(function(e){return Object.getOwnPropertyDescriptor(t,e).enumerable})),n.push.apply(n,r)}return n}function s(t){for(var e=1;e<arguments.length;e++){var n=null!=arguments[e]?arguments[e]:{};e%2?c(Object(n),!0).forEach(function(e){var r,o,i;r=t,o=e,i=n[e],(o=function(t){var e=function(t,e){if("object"!=typeof t||!t)return t;var n=t[Symbol.toPrimitive];if(void 0!==n){var r=n.call(t,e||"default");if("object"!=typeof r)return r;throw TypeError("@@toPrimitive must return a primitive value.")}return("string"===e?String:Number)(t)}(t,"string");return"symbol"==typeof e?e:e+""}(o))in r?Object.defineProperty(r,o,{value:i,enumerable:!0,configurable:!0,writable:!0}):r[o]=i}):Object.getOwnPropertyDescriptors?Object.defineProperties(t,Object.getOwnPropertyDescriptors(n)):c(Object(n)).forEach(function(e){Object.defineProperty(t,e,Object.getOwnPropertyDescriptor(n,e))})}return t}function u(t){return e=>r.createElement(f,a({attr:s({},t.attr)},e),function t(e){return e&&e.map((e,n)=>r.createElement(e.tag,s({key:n},e.attr),t(e.child)))}(t.child))}function f(t){var e=e=>{var n,{attr:o,size:i,title:c}=t,u=function(t,e){if(null==t)return{};var n,r,o=function(t,e){if(null==t)return{};var n={};for(var r in t)if(Object.prototype.hasOwnProperty.call(t,r)){if(e.indexOf(r)>=0)continue;n[r]=t[r]}return n}(t,e);if(Object.getOwnPropertySymbols){var i=Object.getOwnPropertySymbols(t);for(r=0;r<i.length;r++)n=i[r],!(e.indexOf(n)>=0)&&Object.prototype.propertyIsEnumerable.call(t,n)&&(o[n]=t[n])}return o}(t,l),f=i||e.size||"1em";return e.className&&(n=e.className),t.className&&(n=(n?n+" ":"")+t.className),r.createElement("svg",a({stroke:"currentColor",fill:"currentColor",strokeWidth:"0"},e.attr,o,u,{className:n,style:s(s({color:t.color||e.color},e.style),t.style),height:f,width:f,xmlns:"http://www.w3.org/2000/svg"}),c&&r.createElement("title",null,c),t.children)};return void 0!==i?r.createElement(i.Consumer,null,t=>e(t)):e(o)}},69397:(t,e,n)=>{n.d(e,{A:()=>r});let r=(0,n(1316).A)("Check",[["path",{d:"M20 6 9 17l-5-5",key:"1gmf2c"}]])},79848:(t,e,n)=>{n.d(e,{A:()=>o});let r={direction:"forward",speed:2,startDelay:1e3,active:!0,breakpoints:{},playOnInit:!0,stopOnFocusIn:!0,stopOnInteraction:!0,stopOnMouseEnter:!1,rootNode:null};function o(t={}){let e,n,i,l,a;let c=0,s=!1,u=!1;function f(){if(i||s)return;n.emit("autoScroll:play");let t=n.internalEngine(),{ownerWindow:r}=t;c=r.setTimeout(()=>{t.scrollBody=function(t){let{location:r,previousLocation:o,offsetLocation:i,target:l,scrollTarget:a,index:c,indexPrevious:s,limit:{reachedMin:u,reachedMax:f,constrain:d},options:{loop:y}}=t,O="forward"===e.direction?-1:1,m=()=>j,g=0,b=0,v=r.get(),h=0,w=!1,j={direction:()=>b,duration:()=>-1,velocity:()=>g,settled:()=>w,seek:function(){let t=0;o.set(r),g=O*e.speed,v+=g,r.add(g),l.set(r),b=Math.sign(v-h),h=v;let m=a.byDistance(0,!1).index;c.get()!==m&&(s.set(c.get()),c.set(m),n.emit("select"));let E="forward"===e.direction?u(i.get()):f(i.get());if(!y&&E){w=!0;let t=d(r.get());r.set(t),l.set(r),p()}return j},useBaseFriction:m,useBaseDuration:m,useFriction:m,useDuration:m};return j}(t),t.animation.start()},l),s=!0}function p(){if(i||!s)return;n.emit("autoScroll:stop");let t=n.internalEngine(),{ownerWindow:e}=t;t.scrollBody=a,e.clearTimeout(c),c=0,s=!1}function d(){u||p()}function y(){u||b()}function O(){u=!0,p()}function m(){u=!1,f()}function g(){n.off("settle",g),f()}function b(){n.on("settle",g)}return{name:"autoScroll",options:t,init:function(c,s){n=c;let{mergeOptions:u,optionsAtMedia:g}=s,b=u(r,o.globalOptions);if(e=g(u(b,t)),n.scrollSnapList().length<=1)return;l=e.startDelay,i=!1,a=n.internalEngine().scrollBody;let{eventStore:v}=n.internalEngine(),h=!!n.internalEngine().options.watchDrag,w=function(t,e){let n=t.rootNode();return e&&e(n)||n}(n,e.rootNode);h&&n.on("pointerDown",d),h&&!e.stopOnInteraction&&n.on("pointerUp",y),e.stopOnMouseEnter&&v.add(w,"mouseenter",O),e.stopOnMouseEnter&&!e.stopOnInteraction&&v.add(w,"mouseleave",m),e.stopOnFocusIn&&n.on("slideFocusStart",p),e.stopOnFocusIn&&!e.stopOnInteraction&&v.add(n.containerNode(),"focusout",f),e.playOnInit&&f()},destroy:function(){n.off("pointerDown",d).off("pointerUp",y).off("slideFocusStart",p).off("settle",g),p(),i=!0,s=!1},play:function(t){void 0!==t&&(l=t),f()},stop:function(){s&&p()},reset:function(){s&&(p(),b())},isPlaying:function(){return s}}}o.globalOptions=void 0}}]);