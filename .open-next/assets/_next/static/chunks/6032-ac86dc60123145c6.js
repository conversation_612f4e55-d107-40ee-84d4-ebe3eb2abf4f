"use strict";(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[6032],{21494:(e,t,r)=>{r.d(t,{UC:()=>er,VY:()=>eo,ZL:()=>ee,bL:()=>Q,bm:()=>el,hE:()=>en,hJ:()=>et,l9:()=>$});var n=r(95361),o=r(75030),l=r(94286),i=r(22408),a=r(25747),s=r(4471),u=r(10730),c=r(70908),d=r(70200),p=r(12177),f=r(72637),g=r(57750),v=r(99126),h=r(91783),x=r(76012),m=r(16261),y="Dialog",[b,w]=(0,i.A)(y),[C,R]=b(y),j=e=>{let{__scopeDialog:t,children:r,open:o,defaultOpen:l,onOpenChange:i,modal:u=!0}=e,c=n.useRef(null),d=n.useRef(null),[p,f]=(0,s.i)({prop:o,defaultProp:null!=l&&l,onChange:i,caller:y});return(0,m.jsx)(C,{scope:t,triggerRef:c,contentRef:d,contentId:(0,a.B)(),titleId:(0,a.B)(),descriptionId:(0,a.B)(),open:p,onOpenChange:f,onOpenToggle:n.useCallback(()=>f(e=>!e),[f]),modal:u,children:r})};j.displayName=y;var E="DialogTrigger",T=n.forwardRef((e,t)=>{let{__scopeDialog:r,...n}=e,i=R(E,r),a=(0,l.s)(t,i.triggerRef);return(0,m.jsx)(f.sG.button,{type:"button","aria-haspopup":"dialog","aria-expanded":i.open,"aria-controls":i.contentId,"data-state":z(i.open),...n,ref:a,onClick:(0,o.m)(e.onClick,i.onOpenToggle)})});T.displayName=E;var D="DialogPortal",[k,_]=b(D,{forceMount:void 0}),I=e=>{let{__scopeDialog:t,forceMount:r,children:o,container:l}=e,i=R(D,t);return(0,m.jsx)(k,{scope:t,forceMount:r,children:n.Children.map(o,e=>(0,m.jsx)(p.C,{present:r||i.open,children:(0,m.jsx)(d.Z,{asChild:!0,container:l,children:e})}))})};I.displayName=D;var L="DialogOverlay",O=n.forwardRef((e,t)=>{let r=_(L,e.__scopeDialog),{forceMount:n=r.forceMount,...o}=e,l=R(L,e.__scopeDialog);return l.modal?(0,m.jsx)(p.C,{present:n||l.open,children:(0,m.jsx)(P,{...o,ref:t})}):null});O.displayName=L;var N=(0,x.TL)("DialogOverlay.RemoveScroll"),P=n.forwardRef((e,t)=>{let{__scopeDialog:r,...n}=e,o=R(L,r);return(0,m.jsx)(v.A,{as:N,allowPinchZoom:!0,shards:[o.contentRef],children:(0,m.jsx)(f.sG.div,{"data-state":z(o.open),...n,ref:t,style:{pointerEvents:"auto",...n.style}})})}),M="DialogContent",A=n.forwardRef((e,t)=>{let r=_(M,e.__scopeDialog),{forceMount:n=r.forceMount,...o}=e,l=R(M,e.__scopeDialog);return(0,m.jsx)(p.C,{present:n||l.open,children:l.modal?(0,m.jsx)(F,{...o,ref:t}):(0,m.jsx)(B,{...o,ref:t})})});A.displayName=M;var F=n.forwardRef((e,t)=>{let r=R(M,e.__scopeDialog),i=n.useRef(null),a=(0,l.s)(t,r.contentRef,i);return n.useEffect(()=>{let e=i.current;if(e)return(0,h.Eq)(e)},[]),(0,m.jsx)(q,{...e,ref:a,trapFocus:r.open,disableOutsidePointerEvents:!0,onCloseAutoFocus:(0,o.m)(e.onCloseAutoFocus,e=>{var t;e.preventDefault(),null===(t=r.triggerRef.current)||void 0===t||t.focus()}),onPointerDownOutside:(0,o.m)(e.onPointerDownOutside,e=>{let t=e.detail.originalEvent,r=0===t.button&&!0===t.ctrlKey;(2===t.button||r)&&e.preventDefault()}),onFocusOutside:(0,o.m)(e.onFocusOutside,e=>e.preventDefault())})}),B=n.forwardRef((e,t)=>{let r=R(M,e.__scopeDialog),o=n.useRef(!1),l=n.useRef(!1);return(0,m.jsx)(q,{...e,ref:t,trapFocus:!1,disableOutsidePointerEvents:!1,onCloseAutoFocus:t=>{var n,i;null===(n=e.onCloseAutoFocus)||void 0===n||n.call(e,t),t.defaultPrevented||(o.current||null===(i=r.triggerRef.current)||void 0===i||i.focus(),t.preventDefault()),o.current=!1,l.current=!1},onInteractOutside:t=>{var n,i;null===(n=e.onInteractOutside)||void 0===n||n.call(e,t),t.defaultPrevented||(o.current=!0,"pointerdown"!==t.detail.originalEvent.type||(l.current=!0));let a=t.target;(null===(i=r.triggerRef.current)||void 0===i?void 0:i.contains(a))&&t.preventDefault(),"focusin"===t.detail.originalEvent.type&&l.current&&t.preventDefault()}})}),q=n.forwardRef((e,t)=>{let{__scopeDialog:r,trapFocus:o,onOpenAutoFocus:i,onCloseAutoFocus:a,...s}=e,d=R(M,r),p=n.useRef(null),f=(0,l.s)(t,p);return(0,g.Oh)(),(0,m.jsxs)(m.Fragment,{children:[(0,m.jsx)(c.n,{asChild:!0,loop:!0,trapped:o,onMountAutoFocus:i,onUnmountAutoFocus:a,children:(0,m.jsx)(u.qW,{role:"dialog",id:d.contentId,"aria-describedby":d.descriptionId,"aria-labelledby":d.titleId,"data-state":z(d.open),...s,ref:f,onDismiss:()=>d.onOpenChange(!1)})}),(0,m.jsxs)(m.Fragment,{children:[(0,m.jsx)(V,{titleId:d.titleId}),(0,m.jsx)(J,{contentRef:p,descriptionId:d.descriptionId})]})]})}),G="DialogTitle",S=n.forwardRef((e,t)=>{let{__scopeDialog:r,...n}=e,o=R(G,r);return(0,m.jsx)(f.sG.h2,{id:o.titleId,...n,ref:t})});S.displayName=G;var W="DialogDescription",Z=n.forwardRef((e,t)=>{let{__scopeDialog:r,...n}=e,o=R(W,r);return(0,m.jsx)(f.sG.p,{id:o.descriptionId,...n,ref:t})});Z.displayName=W;var H="DialogClose",U=n.forwardRef((e,t)=>{let{__scopeDialog:r,...n}=e,l=R(H,r);return(0,m.jsx)(f.sG.button,{type:"button",...n,ref:t,onClick:(0,o.m)(e.onClick,()=>l.onOpenChange(!1))})});function z(e){return e?"open":"closed"}U.displayName=H;var X="DialogTitleWarning",[Y,K]=(0,i.q)(X,{contentName:M,titleName:G,docsSlug:"dialog"}),V=e=>{let{titleId:t}=e,r=K(X),o="`".concat(r.contentName,"` requires a `").concat(r.titleName,"` for the component to be accessible for screen reader users.\n\nIf you want to hide the `").concat(r.titleName,"`, you can wrap it with our VisuallyHidden component.\n\nFor more information, see https://radix-ui.com/primitives/docs/components/").concat(r.docsSlug);return n.useEffect(()=>{t&&!document.getElementById(t)&&console.error(o)},[o,t]),null},J=e=>{let{contentRef:t,descriptionId:r}=e,o=K("DialogDescriptionWarning"),l="Warning: Missing `Description` or `aria-describedby={undefined}` for {".concat(o.contentName,"}.");return n.useEffect(()=>{var e;let n=null===(e=t.current)||void 0===e?void 0:e.getAttribute("aria-describedby");r&&n&&!document.getElementById(r)&&console.warn(l)},[l,t,r]),null},Q=j,$=T,ee=I,et=O,er=A,en=S,eo=Z,el=U},45861:(e,t,r)=>{r.d(t,{Kq:()=>H,UC:()=>Y,ZL:()=>X,bL:()=>U,i3:()=>K,l9:()=>z});var n=r(95361),o=r(75030),l=r(94286),i=r(22408),a=r(10730),s=r(25747),u=r(90683),c=r(70200),d=r(12177),p=r(72637),f=r(76012),g=r(4471),v=r(72129),h=r(16261),[x,m]=(0,i.A)("Tooltip",[u.Bk]),y=(0,u.Bk)(),b="TooltipProvider",w="tooltip.open",[C,R]=x(b),j=e=>{let{__scopeTooltip:t,delayDuration:r=700,skipDelayDuration:o=300,disableHoverableContent:l=!1,children:i}=e,a=n.useRef(!0),s=n.useRef(!1),u=n.useRef(0);return n.useEffect(()=>{let e=u.current;return()=>window.clearTimeout(e)},[]),(0,h.jsx)(C,{scope:t,isOpenDelayedRef:a,delayDuration:r,onOpen:n.useCallback(()=>{window.clearTimeout(u.current),a.current=!1},[]),onClose:n.useCallback(()=>{window.clearTimeout(u.current),u.current=window.setTimeout(()=>a.current=!0,o)},[o]),isPointerInTransitRef:s,onPointerInTransitChange:n.useCallback(e=>{s.current=e},[]),disableHoverableContent:l,children:i})};j.displayName=b;var E="Tooltip",[T,D]=x(E),k=e=>{let{__scopeTooltip:t,children:r,open:o,defaultOpen:l,onOpenChange:i,disableHoverableContent:a,delayDuration:c}=e,d=R(E,e.__scopeTooltip),p=y(t),[f,v]=n.useState(null),x=(0,s.B)(),m=n.useRef(0),b=null!=a?a:d.disableHoverableContent,C=null!=c?c:d.delayDuration,j=n.useRef(!1),[D,k]=(0,g.i)({prop:o,defaultProp:null!=l&&l,onChange:e=>{e?(d.onOpen(),document.dispatchEvent(new CustomEvent(w))):d.onClose(),null==i||i(e)},caller:E}),_=n.useMemo(()=>D?j.current?"delayed-open":"instant-open":"closed",[D]),I=n.useCallback(()=>{window.clearTimeout(m.current),m.current=0,j.current=!1,k(!0)},[k]),L=n.useCallback(()=>{window.clearTimeout(m.current),m.current=0,k(!1)},[k]),O=n.useCallback(()=>{window.clearTimeout(m.current),m.current=window.setTimeout(()=>{j.current=!0,k(!0),m.current=0},C)},[C,k]);return n.useEffect(()=>()=>{m.current&&(window.clearTimeout(m.current),m.current=0)},[]),(0,h.jsx)(u.bL,{...p,children:(0,h.jsx)(T,{scope:t,contentId:x,open:D,stateAttribute:_,trigger:f,onTriggerChange:v,onTriggerEnter:n.useCallback(()=>{d.isOpenDelayedRef.current?O():I()},[d.isOpenDelayedRef,O,I]),onTriggerLeave:n.useCallback(()=>{b?L():(window.clearTimeout(m.current),m.current=0)},[L,b]),onOpen:I,onClose:L,disableHoverableContent:b,children:r})})};k.displayName=E;var _="TooltipTrigger",I=n.forwardRef((e,t)=>{let{__scopeTooltip:r,...i}=e,a=D(_,r),s=R(_,r),c=y(r),d=n.useRef(null),f=(0,l.s)(t,d,a.onTriggerChange),g=n.useRef(!1),v=n.useRef(!1),x=n.useCallback(()=>g.current=!1,[]);return n.useEffect(()=>()=>document.removeEventListener("pointerup",x),[x]),(0,h.jsx)(u.Mz,{asChild:!0,...c,children:(0,h.jsx)(p.sG.button,{"aria-describedby":a.open?a.contentId:void 0,"data-state":a.stateAttribute,...i,ref:f,onPointerMove:(0,o.m)(e.onPointerMove,e=>{"touch"===e.pointerType||v.current||s.isPointerInTransitRef.current||(a.onTriggerEnter(),v.current=!0)}),onPointerLeave:(0,o.m)(e.onPointerLeave,()=>{a.onTriggerLeave(),v.current=!1}),onPointerDown:(0,o.m)(e.onPointerDown,()=>{a.open&&a.onClose(),g.current=!0,document.addEventListener("pointerup",x,{once:!0})}),onFocus:(0,o.m)(e.onFocus,()=>{g.current||a.onOpen()}),onBlur:(0,o.m)(e.onBlur,a.onClose),onClick:(0,o.m)(e.onClick,a.onClose)})})});I.displayName=_;var L="TooltipPortal",[O,N]=x(L,{forceMount:void 0}),P=e=>{let{__scopeTooltip:t,forceMount:r,children:n,container:o}=e,l=D(L,t);return(0,h.jsx)(O,{scope:t,forceMount:r,children:(0,h.jsx)(d.C,{present:r||l.open,children:(0,h.jsx)(c.Z,{asChild:!0,container:o,children:n})})})};P.displayName=L;var M="TooltipContent",A=n.forwardRef((e,t)=>{let r=N(M,e.__scopeTooltip),{forceMount:n=r.forceMount,side:o="top",...l}=e,i=D(M,e.__scopeTooltip);return(0,h.jsx)(d.C,{present:n||i.open,children:i.disableHoverableContent?(0,h.jsx)(S,{side:o,...l,ref:t}):(0,h.jsx)(F,{side:o,...l,ref:t})})}),F=n.forwardRef((e,t)=>{let r=D(M,e.__scopeTooltip),o=R(M,e.__scopeTooltip),i=n.useRef(null),a=(0,l.s)(t,i),[s,u]=n.useState(null),{trigger:c,onClose:d}=r,p=i.current,{onPointerInTransitChange:f}=o,g=n.useCallback(()=>{u(null),f(!1)},[f]),v=n.useCallback((e,t)=>{let r=e.currentTarget,n={x:e.clientX,y:e.clientY},o=function(e,t){let r=Math.abs(t.top-e.y),n=Math.abs(t.bottom-e.y),o=Math.abs(t.right-e.x),l=Math.abs(t.left-e.x);switch(Math.min(r,n,o,l)){case l:return"left";case o:return"right";case r:return"top";case n:return"bottom";default:throw Error("unreachable")}}(n,r.getBoundingClientRect());u(function(e){let t=e.slice();return t.sort((e,t)=>e.x<t.x?-1:e.x>t.x?1:e.y<t.y?-1:+!!(e.y>t.y)),function(e){if(e.length<=1)return e.slice();let t=[];for(let r=0;r<e.length;r++){let n=e[r];for(;t.length>=2;){let e=t[t.length-1],r=t[t.length-2];if((e.x-r.x)*(n.y-r.y)>=(e.y-r.y)*(n.x-r.x))t.pop();else break}t.push(n)}t.pop();let r=[];for(let t=e.length-1;t>=0;t--){let n=e[t];for(;r.length>=2;){let e=r[r.length-1],t=r[r.length-2];if((e.x-t.x)*(n.y-t.y)>=(e.y-t.y)*(n.x-t.x))r.pop();else break}r.push(n)}return(r.pop(),1===t.length&&1===r.length&&t[0].x===r[0].x&&t[0].y===r[0].y)?t:t.concat(r)}(t)}([...function(e,t){let r=arguments.length>2&&void 0!==arguments[2]?arguments[2]:5,n=[];switch(t){case"top":n.push({x:e.x-r,y:e.y+r},{x:e.x+r,y:e.y+r});break;case"bottom":n.push({x:e.x-r,y:e.y-r},{x:e.x+r,y:e.y-r});break;case"left":n.push({x:e.x+r,y:e.y-r},{x:e.x+r,y:e.y+r});break;case"right":n.push({x:e.x-r,y:e.y-r},{x:e.x-r,y:e.y+r})}return n}(n,o),...function(e){let{top:t,right:r,bottom:n,left:o}=e;return[{x:o,y:t},{x:r,y:t},{x:r,y:n},{x:o,y:n}]}(t.getBoundingClientRect())])),f(!0)},[f]);return n.useEffect(()=>()=>g(),[g]),n.useEffect(()=>{if(c&&p){let e=e=>v(e,p),t=e=>v(e,c);return c.addEventListener("pointerleave",e),p.addEventListener("pointerleave",t),()=>{c.removeEventListener("pointerleave",e),p.removeEventListener("pointerleave",t)}}},[c,p,v,g]),n.useEffect(()=>{if(s){let e=e=>{let t=e.target,r={x:e.clientX,y:e.clientY},n=(null==c?void 0:c.contains(t))||(null==p?void 0:p.contains(t)),o=!function(e,t){let{x:r,y:n}=e,o=!1;for(let e=0,l=t.length-1;e<t.length;l=e++){let i=t[e],a=t[l],s=i.x,u=i.y,c=a.x,d=a.y;u>n!=d>n&&r<(c-s)*(n-u)/(d-u)+s&&(o=!o)}return o}(r,s);n?g():o&&(g(),d())};return document.addEventListener("pointermove",e),()=>document.removeEventListener("pointermove",e)}},[c,p,s,d,g]),(0,h.jsx)(S,{...e,ref:a})}),[B,q]=x(E,{isInside:!1}),G=(0,f.Dc)("TooltipContent"),S=n.forwardRef((e,t)=>{let{__scopeTooltip:r,children:o,"aria-label":l,onEscapeKeyDown:i,onPointerDownOutside:s,...c}=e,d=D(M,r),p=y(r),{onClose:f}=d;return n.useEffect(()=>(document.addEventListener(w,f),()=>document.removeEventListener(w,f)),[f]),n.useEffect(()=>{if(d.trigger){let e=e=>{let t=e.target;(null==t?void 0:t.contains(d.trigger))&&f()};return window.addEventListener("scroll",e,{capture:!0}),()=>window.removeEventListener("scroll",e,{capture:!0})}},[d.trigger,f]),(0,h.jsx)(a.qW,{asChild:!0,disableOutsidePointerEvents:!1,onEscapeKeyDown:i,onPointerDownOutside:s,onFocusOutside:e=>e.preventDefault(),onDismiss:f,children:(0,h.jsxs)(u.UC,{"data-state":d.stateAttribute,...p,...c,ref:t,style:{...c.style,"--radix-tooltip-content-transform-origin":"var(--radix-popper-transform-origin)","--radix-tooltip-content-available-width":"var(--radix-popper-available-width)","--radix-tooltip-content-available-height":"var(--radix-popper-available-height)","--radix-tooltip-trigger-width":"var(--radix-popper-anchor-width)","--radix-tooltip-trigger-height":"var(--radix-popper-anchor-height)"},children:[(0,h.jsx)(G,{children:o}),(0,h.jsx)(B,{scope:r,isInside:!0,children:(0,h.jsx)(v.bL,{id:d.contentId,role:"tooltip",children:l||o})})]})})});A.displayName=M;var W="TooltipArrow",Z=n.forwardRef((e,t)=>{let{__scopeTooltip:r,...n}=e,o=y(r);return q(W,r).isInside?null:(0,h.jsx)(u.i3,{...o,...n,ref:t})});Z.displayName=W;var H=j,U=k,z=I,X=P,Y=A,K=Z},66157:(e,t,r)=>{r.d(t,{b:()=>u});var n=r(95361),o=r(72637),l=r(16261),i="horizontal",a=["horizontal","vertical"],s=n.forwardRef((e,t)=>{var r;let{decorative:n,orientation:s=i,...u}=e,c=(r=s,a.includes(r))?s:i;return(0,l.jsx)(o.sG.div,{"data-orientation":c,...n?{role:"none"}:{"aria-orientation":"vertical"===c?c:void 0,role:"separator"},...u,ref:t})});s.displayName="Separator";var u=s},69907:(e,t,r)=>{r.d(t,{A:()=>n});let n=(0,r(1316).A)("PanelLeft",[["rect",{width:"18",height:"18",x:"3",y:"3",rx:"2",key:"afitv7"}],["path",{d:"M9 3v18",key:"fh3hqa"}]])},87605:(e,t,r)=>{r.d(t,{A:()=>n});let n=(0,r(1316).A)("X",[["path",{d:"M18 6 6 18",key:"1bl5f8"}],["path",{d:"m6 6 12 12",key:"d8bk6v"}]])}}]);