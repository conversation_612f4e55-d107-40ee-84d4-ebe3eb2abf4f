"use strict";(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[7582],{1316:(e,t,r)=>{r.d(t,{A:()=>a});var n=r(95361);let o=e=>e.replace(/([a-z0-9])([A-Z])/g,"$1-$2").toLowerCase(),l=function(){for(var e=arguments.length,t=Array(e),r=0;r<e;r++)t[r]=arguments[r];return t.filter((e,t,r)=>!!e&&r.indexOf(e)===t).join(" ")};var i={xmlns:"http://www.w3.org/2000/svg",width:24,height:24,viewBox:"0 0 24 24",fill:"none",stroke:"currentColor",strokeWidth:2,strokeLinecap:"round",strokeLinejoin:"round"};let u=(0,n.forwardRef)((e,t)=>{let{color:r="currentColor",size:o=24,strokeWidth:u=2,absoluteStrokeWidth:a,className:c="",children:s,iconNode:f,...d}=e;return(0,n.createElement)("svg",{ref:t,...i,width:o,height:o,stroke:r,strokeWidth:a?24*Number(u)/Number(o):u,className:l("lucide",c),...d},[...f.map(e=>{let[t,r]=e;return(0,n.createElement)(t,r)}),...Array.isArray(s)?s:[s]])}),a=(e,t)=>{let r=(0,n.forwardRef)((r,i)=>{let{className:a,...c}=r;return(0,n.createElement)(u,{ref:i,iconNode:t,className:l("lucide-".concat(o(e)),a),...c})});return r.displayName="".concat(e),r}},4471:(e,t,r)=>{r.d(t,{i:()=>u});var n,o=r(95361),l=r(53765),i=(n||(n=r.t(o,2)))[" useInsertionEffect ".trim().toString()]||l.N;function u({prop:e,defaultProp:t,onChange:r=()=>{},caller:n}){let[l,u,a]=function({defaultProp:e,onChange:t}){let[r,n]=o.useState(e),l=o.useRef(r),u=o.useRef(t);return i(()=>{u.current=t},[t]),o.useEffect(()=>{l.current!==r&&(u.current?.(r),l.current=r)},[r,l]),[r,n,u]}({defaultProp:t,onChange:r}),c=void 0!==e,s=c?e:l;{let t=o.useRef(void 0!==e);o.useEffect(()=>{let e=t.current;if(e!==c){let t=c?"controlled":"uncontrolled";console.warn(`${n} is changing from ${e?"controlled":"uncontrolled"} to ${t}. Components should not switch from controlled to uncontrolled (or vice versa). Decide between using a controlled or uncontrolled value for the lifetime of the component.`)}t.current=c},[c,n])}return[s,o.useCallback(t=>{if(c){let r="function"==typeof t?t(e):t;r!==e&&a.current?.(r)}else u(t)},[c,e,u,a])]}Symbol("RADIX:SYNC_STATE")},20604:(e,t,r)=>{r.d(t,{F:()=>i});var n=r(87786);let o=e=>"boolean"==typeof e?`${e}`:0===e?"0":e,l=n.$,i=(e,t)=>r=>{var n;if((null==t?void 0:t.variants)==null)return l(e,null==r?void 0:r.class,null==r?void 0:r.className);let{variants:i,defaultVariants:u}=t,a=Object.keys(i).map(e=>{let t=null==r?void 0:r[e],n=null==u?void 0:u[e];if(null===t)return null;let l=o(t)||o(n);return i[e][l]}),c=r&&Object.entries(r).reduce((e,t)=>{let[r,n]=t;return void 0===n||(e[r]=n),e},{});return l(e,a,null==t?void 0:null===(n=t.compoundVariants)||void 0===n?void 0:n.reduce((e,t)=>{let{class:r,className:n,...o}=t;return Object.entries(o).every(e=>{let[t,r]=e;return Array.isArray(r)?r.includes({...u,...c}[t]):({...u,...c})[t]===r})?[...e,r,n]:e},[]),null==r?void 0:r.class,null==r?void 0:r.className)}},21751:(e,t,r)=>{r.d(t,{c:()=>o});var n=r(95361);function o(e){let t=n.useRef(e);return n.useEffect(()=>{t.current=e}),n.useMemo(()=>(...e)=>t.current?.(...e),[])}},22408:(e,t,r)=>{r.d(t,{A:()=>i,q:()=>l});var n=r(95361),o=r(16261);function l(e,t){let r=n.createContext(t),l=e=>{let{children:t,...l}=e,i=n.useMemo(()=>l,Object.values(l));return(0,o.jsx)(r.Provider,{value:i,children:t})};return l.displayName=e+"Provider",[l,function(o){let l=n.useContext(r);if(l)return l;if(void 0!==t)return t;throw Error(`\`${o}\` must be used within \`${e}\``)}]}function i(e,t=[]){let r=[],l=()=>{let t=r.map(e=>n.createContext(e));return function(r){let o=r?.[e]||t;return n.useMemo(()=>({[`__scope${e}`]:{...r,[e]:o}}),[r,o])}};return l.scopeName=e,[function(t,l){let i=n.createContext(l),u=r.length;r=[...r,l];let a=t=>{let{scope:r,children:l,...a}=t,c=r?.[e]?.[u]||i,s=n.useMemo(()=>a,Object.values(a));return(0,o.jsx)(c.Provider,{value:s,children:l})};return a.displayName=t+"Provider",[a,function(r,o){let a=o?.[e]?.[u]||i,c=n.useContext(a);if(c)return c;if(void 0!==l)return l;throw Error(`\`${r}\` must be used within \`${t}\``)}]},function(...e){let t=e[0];if(1===e.length)return t;let r=()=>{let r=e.map(e=>({useScope:e(),scopeName:e.scopeName}));return function(e){let o=r.reduce((t,{useScope:r,scopeName:n})=>{let o=r(e)[`__scope${n}`];return{...t,...o}},{});return n.useMemo(()=>({[`__scope${t.scopeName}`]:o}),[o])}};return r.scopeName=t.scopeName,r}(l,...t)]}},25747:(e,t,r)=>{r.d(t,{B:()=>a});var n,o=r(95361),l=r(53765),i=(n||(n=r.t(o,2)))[" useId ".trim().toString()]||(()=>void 0),u=0;function a(e){let[t,r]=o.useState(i());return(0,l.N)(()=>{e||r(e=>e??String(u++))},[e]),e||(t?`radix-${t}`:"")}},44756:(e,t,r)=>{r.d(t,{k5:()=>s});var n=r(95361),o={color:void 0,size:void 0,className:void 0,style:void 0,attr:void 0},l=n.createContext&&n.createContext(o),i=["attr","size","title"];function u(){return(u=Object.assign?Object.assign.bind():function(e){for(var t=1;t<arguments.length;t++){var r=arguments[t];for(var n in r)Object.prototype.hasOwnProperty.call(r,n)&&(e[n]=r[n])}return e}).apply(this,arguments)}function a(e,t){var r=Object.keys(e);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(e);t&&(n=n.filter(function(t){return Object.getOwnPropertyDescriptor(e,t).enumerable})),r.push.apply(r,n)}return r}function c(e){for(var t=1;t<arguments.length;t++){var r=null!=arguments[t]?arguments[t]:{};t%2?a(Object(r),!0).forEach(function(t){var n,o,l;n=e,o=t,l=r[t],(o=function(e){var t=function(e,t){if("object"!=typeof e||!e)return e;var r=e[Symbol.toPrimitive];if(void 0!==r){var n=r.call(e,t||"default");if("object"!=typeof n)return n;throw TypeError("@@toPrimitive must return a primitive value.")}return("string"===t?String:Number)(e)}(e,"string");return"symbol"==typeof t?t:t+""}(o))in n?Object.defineProperty(n,o,{value:l,enumerable:!0,configurable:!0,writable:!0}):n[o]=l}):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(r)):a(Object(r)).forEach(function(t){Object.defineProperty(e,t,Object.getOwnPropertyDescriptor(r,t))})}return e}function s(e){return t=>n.createElement(f,u({attr:c({},e.attr)},t),function e(t){return t&&t.map((t,r)=>n.createElement(t.tag,c({key:r},t.attr),e(t.child)))}(e.child))}function f(e){var t=t=>{var r,{attr:o,size:l,title:a}=e,s=function(e,t){if(null==e)return{};var r,n,o=function(e,t){if(null==e)return{};var r={};for(var n in e)if(Object.prototype.hasOwnProperty.call(e,n)){if(t.indexOf(n)>=0)continue;r[n]=e[n]}return r}(e,t);if(Object.getOwnPropertySymbols){var l=Object.getOwnPropertySymbols(e);for(n=0;n<l.length;n++)r=l[n],!(t.indexOf(r)>=0)&&Object.prototype.propertyIsEnumerable.call(e,r)&&(o[r]=e[r])}return o}(e,i),f=l||t.size||"1em";return t.className&&(r=t.className),e.className&&(r=(r?r+" ":"")+e.className),n.createElement("svg",u({stroke:"currentColor",fill:"currentColor",strokeWidth:"0"},t.attr,o,s,{className:r,style:c(c({color:e.color||t.color},t.style),e.style),height:f,width:f,xmlns:"http://www.w3.org/2000/svg"}),a&&n.createElement("title",null,a),e.children)};return void 0!==l?n.createElement(l.Consumer,null,e=>t(e)):t(o)}},53765:(e,t,r)=>{r.d(t,{N:()=>o});var n=r(95361),o=globalThis?.document?n.useLayoutEffect:()=>{}},66157:(e,t,r)=>{r.d(t,{b:()=>c});var n=r(95361),o=r(72637),l=r(16261),i="horizontal",u=["horizontal","vertical"],a=n.forwardRef((e,t)=>{var r;let{decorative:n,orientation:a=i,...c}=e,s=(r=a,u.includes(r))?a:i;return(0,l.jsx)(o.sG.div,{"data-orientation":s,...n?{role:"none"}:{"aria-orientation":"vertical"===s?s:void 0,role:"separator"},...c,ref:t})});a.displayName="Separator";var c=a},72637:(e,t,r)=>{r.d(t,{hO:()=>a,sG:()=>u});var n=r(95361),o=r(32196),l=r(76012),i=r(16261),u=["a","button","div","form","h2","h3","img","input","label","li","nav","ol","p","select","span","svg","ul"].reduce((e,t)=>{let r=(0,l.TL)(`Primitive.${t}`),o=n.forwardRef((e,n)=>{let{asChild:o,...l}=e;return"undefined"!=typeof window&&(window[Symbol.for("radix-ui")]=!0),(0,i.jsx)(o?r:t,{...l,ref:n})});return o.displayName=`Primitive.${t}`,{...e,[t]:o}},{});function a(e,t){e&&o.flushSync(()=>e.dispatchEvent(t))}},75030:(e,t,r)=>{r.d(t,{m:()=>n});function n(e,t,{checkForDefaultPrevented:r=!0}={}){return function(n){if(e?.(n),!1===r||!n.defaultPrevented)return t?.(n)}}}}]);