(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[2130],{3932:(e,t,n)=>{"use strict";n.d(t,{default:()=>u});var r=n(12482),o=n(89799),i=n(95361),a=n(6002),l=n(68926),c=n(16261),u=(0,i.forwardRef)(function(e,t){let{href:n,locale:i,localeCookie:u,onClick:s,prefetch:f,...p}=e,d=(0,a.Ym)(),y=null!=i&&i!==d,m=(0,o.usePathname)();return y&&(f=!1),(0,c.jsx)(r,{ref:t,href:n,hrefLang:y?i:void 0,onClick:function(e){(0,l.A)(u,m,d,i),s&&s(e)},prefetch:f,...p})})},5657:e=>{e.exports=function({client_id:e,auto_select:t=!1,cancel_on_tap_outside:n=!1,context:r="signin",...o},i){if(!e)throw Error("client_id is required");if("undefined"!=typeof window&&window.document){let a=["signin","signup","use"].includes(r)?r:"signin",l=document.createElement("script");l.src="https://accounts.google.com/gsi/client",l.async=!0,l.defer=!0,document.head.appendChild(l),window.onload=function(){window.google.accounts.id.initialize({client_id:e,callback:i,auto_select:t,cancel_on_tap_outside:n,context:a,...o}),window.google.accounts.id.prompt()}}}},8624:(e,t,n)=>{"use strict";n.d(t,{c3:()=>i});var r=n(6002);function o(e,t){return(...e)=>{try{return t(...e)}catch{throw Error(void 0)}}}let i=o(0,r.c3);o(0,r.kc)},21494:(e,t,n)=>{"use strict";n.d(t,{UC:()=>en,VY:()=>eo,ZL:()=>ee,bL:()=>V,bm:()=>ei,hE:()=>er,hJ:()=>et,l9:()=>Q});var r=n(95361),o=n(75030),i=n(94286),a=n(22408),l=n(25747),c=n(4471),u=n(10730),s=n(70908),f=n(70200),p=n(12177),d=n(72637),y=n(57750),m=n(99126),g=n(91783),b=n(76012),h=n(16261),v="Dialog",[w,C]=(0,a.A)(v),[x,O]=w(v),j=e=>{let{__scopeDialog:t,children:n,open:o,defaultOpen:i,onOpenChange:a,modal:u=!0}=e,s=r.useRef(null),f=r.useRef(null),[p,d]=(0,c.i)({prop:o,defaultProp:null!=i&&i,onChange:a,caller:v});return(0,h.jsx)(x,{scope:t,triggerRef:s,contentRef:f,contentId:(0,l.B)(),titleId:(0,l.B)(),descriptionId:(0,l.B)(),open:p,onOpenChange:d,onOpenToggle:r.useCallback(()=>d(e=>!e),[d]),modal:u,children:n})};j.displayName=v;var D="DialogTrigger",R=r.forwardRef((e,t)=>{let{__scopeDialog:n,...r}=e,a=O(D,n),l=(0,i.s)(t,a.triggerRef);return(0,h.jsx)(d.sG.button,{type:"button","aria-haspopup":"dialog","aria-expanded":a.open,"aria-controls":a.contentId,"data-state":Z(a.open),...r,ref:l,onClick:(0,o.m)(e.onClick,a.onOpenToggle)})});R.displayName=D;var P="DialogPortal",[E,_]=w(P,{forceMount:void 0}),k=e=>{let{__scopeDialog:t,forceMount:n,children:o,container:i}=e,a=O(P,t);return(0,h.jsx)(E,{scope:t,forceMount:n,children:r.Children.map(o,e=>(0,h.jsx)(p.C,{present:n||a.open,children:(0,h.jsx)(f.Z,{asChild:!0,container:i,children:e})}))})};k.displayName=P;var A="DialogOverlay",S=r.forwardRef((e,t)=>{let n=_(A,e.__scopeDialog),{forceMount:r=n.forceMount,...o}=e,i=O(A,e.__scopeDialog);return i.modal?(0,h.jsx)(p.C,{present:r||i.open,children:(0,h.jsx)(T,{...o,ref:t})}):null});S.displayName=A;var I=(0,b.TL)("DialogOverlay.RemoveScroll"),T=r.forwardRef((e,t)=>{let{__scopeDialog:n,...r}=e,o=O(A,n);return(0,h.jsx)(m.A,{as:I,allowPinchZoom:!0,shards:[o.contentRef],children:(0,h.jsx)(d.sG.div,{"data-state":Z(o.open),...r,ref:t,style:{pointerEvents:"auto",...r.style}})})}),N="DialogContent",F=r.forwardRef((e,t)=>{let n=_(N,e.__scopeDialog),{forceMount:r=n.forceMount,...o}=e,i=O(N,e.__scopeDialog);return(0,h.jsx)(p.C,{present:r||i.open,children:i.modal?(0,h.jsx)(M,{...o,ref:t}):(0,h.jsx)(U,{...o,ref:t})})});F.displayName=N;var M=r.forwardRef((e,t)=>{let n=O(N,e.__scopeDialog),a=r.useRef(null),l=(0,i.s)(t,n.contentRef,a);return r.useEffect(()=>{let e=a.current;if(e)return(0,g.Eq)(e)},[]),(0,h.jsx)(L,{...e,ref:l,trapFocus:n.open,disableOutsidePointerEvents:!0,onCloseAutoFocus:(0,o.m)(e.onCloseAutoFocus,e=>{var t;e.preventDefault(),null===(t=n.triggerRef.current)||void 0===t||t.focus()}),onPointerDownOutside:(0,o.m)(e.onPointerDownOutside,e=>{let t=e.detail.originalEvent,n=0===t.button&&!0===t.ctrlKey;(2===t.button||n)&&e.preventDefault()}),onFocusOutside:(0,o.m)(e.onFocusOutside,e=>e.preventDefault())})}),U=r.forwardRef((e,t)=>{let n=O(N,e.__scopeDialog),o=r.useRef(!1),i=r.useRef(!1);return(0,h.jsx)(L,{...e,ref:t,trapFocus:!1,disableOutsidePointerEvents:!1,onCloseAutoFocus:t=>{var r,a;null===(r=e.onCloseAutoFocus)||void 0===r||r.call(e,t),t.defaultPrevented||(o.current||null===(a=n.triggerRef.current)||void 0===a||a.focus(),t.preventDefault()),o.current=!1,i.current=!1},onInteractOutside:t=>{var r,a;null===(r=e.onInteractOutside)||void 0===r||r.call(e,t),t.defaultPrevented||(o.current=!0,"pointerdown"!==t.detail.originalEvent.type||(i.current=!0));let l=t.target;(null===(a=n.triggerRef.current)||void 0===a?void 0:a.contains(l))&&t.preventDefault(),"focusin"===t.detail.originalEvent.type&&i.current&&t.preventDefault()}})}),L=r.forwardRef((e,t)=>{let{__scopeDialog:n,trapFocus:o,onOpenAutoFocus:a,onCloseAutoFocus:l,...c}=e,f=O(N,n),p=r.useRef(null),d=(0,i.s)(t,p);return(0,y.Oh)(),(0,h.jsxs)(h.Fragment,{children:[(0,h.jsx)(s.n,{asChild:!0,loop:!0,trapped:o,onMountAutoFocus:a,onUnmountAutoFocus:l,children:(0,h.jsx)(u.qW,{role:"dialog",id:f.contentId,"aria-describedby":f.descriptionId,"aria-labelledby":f.titleId,"data-state":Z(f.open),...c,ref:d,onDismiss:()=>f.onOpenChange(!1)})}),(0,h.jsxs)(h.Fragment,{children:[(0,h.jsx)(H,{titleId:f.titleId}),(0,h.jsx)(K,{contentRef:p,descriptionId:f.descriptionId})]})]})}),W="DialogTitle",$=r.forwardRef((e,t)=>{let{__scopeDialog:n,...r}=e,o=O(W,n);return(0,h.jsx)(d.sG.h2,{id:o.titleId,...r,ref:t})});$.displayName=W;var q="DialogDescription",B=r.forwardRef((e,t)=>{let{__scopeDialog:n,...r}=e,o=O(q,n);return(0,h.jsx)(d.sG.p,{id:o.descriptionId,...r,ref:t})});B.displayName=q;var G="DialogClose",X=r.forwardRef((e,t)=>{let{__scopeDialog:n,...r}=e,i=O(G,n);return(0,h.jsx)(d.sG.button,{type:"button",...r,ref:t,onClick:(0,o.m)(e.onClick,()=>i.onOpenChange(!1))})});function Z(e){return e?"open":"closed"}X.displayName=G;var z="DialogTitleWarning",[J,Y]=(0,a.q)(z,{contentName:N,titleName:W,docsSlug:"dialog"}),H=e=>{let{titleId:t}=e,n=Y(z),o="`".concat(n.contentName,"` requires a `").concat(n.titleName,"` for the component to be accessible for screen reader users.\n\nIf you want to hide the `").concat(n.titleName,"`, you can wrap it with our VisuallyHidden component.\n\nFor more information, see https://radix-ui.com/primitives/docs/components/").concat(n.docsSlug);return r.useEffect(()=>{t&&!document.getElementById(t)&&console.error(o)},[o,t]),null},K=e=>{let{contentRef:t,descriptionId:n}=e,o=Y("DialogDescriptionWarning"),i="Warning: Missing `Description` or `aria-describedby={undefined}` for {".concat(o.contentName,"}.");return r.useEffect(()=>{var e;let r=null===(e=t.current)||void 0===e?void 0:e.getAttribute("aria-describedby");n&&r&&!document.getElementById(n)&&console.warn(i)},[i,t,n]),null},V=j,Q=R,ee=k,et=S,en=F,er=$,eo=B,ei=X},24224:(e,t,n)=>{"use strict";function r(e){return(r="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(e){return typeof e}:function(e){return e&&"function"==typeof Symbol&&e.constructor===Symbol&&e!==Symbol.prototype?"symbol":typeof e})(e)}Object.defineProperty(t,"__esModule",{value:!0}),t.CopyToClipboard=void 0;var o=l(n(95361)),i=l(n(94427)),a=["text","onCopy","options","children"];function l(e){return e&&e.__esModule?e:{default:e}}function c(e,t){var n=Object.keys(e);if(Object.getOwnPropertySymbols){var r=Object.getOwnPropertySymbols(e);t&&(r=r.filter(function(t){return Object.getOwnPropertyDescriptor(e,t).enumerable})),n.push.apply(n,r)}return n}function u(e){for(var t=1;t<arguments.length;t++){var n=null!=arguments[t]?arguments[t]:{};t%2?c(Object(n),!0).forEach(function(t){d(e,t,n[t])}):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(n)):c(Object(n)).forEach(function(t){Object.defineProperty(e,t,Object.getOwnPropertyDescriptor(n,t))})}return e}function s(e,t){return(s=Object.setPrototypeOf||function(e,t){return e.__proto__=t,e})(e,t)}function f(e){if(void 0===e)throw ReferenceError("this hasn't been initialised - super() hasn't been called");return e}function p(e){return(p=Object.setPrototypeOf?Object.getPrototypeOf:function(e){return e.__proto__||Object.getPrototypeOf(e)})(e)}function d(e,t,n){return t in e?Object.defineProperty(e,t,{value:n,enumerable:!0,configurable:!0,writable:!0}):e[t]=n,e}var y=function(e){!function(e,t){if("function"!=typeof t&&null!==t)throw TypeError("Super expression must either be null or a function");e.prototype=Object.create(t&&t.prototype,{constructor:{value:e,writable:!0,configurable:!0}}),Object.defineProperty(e,"prototype",{writable:!1}),t&&s(e,t)}(c,e);var t,n,l=(t=function(){if("undefined"==typeof Reflect||!Reflect.construct||Reflect.construct.sham)return!1;if("function"==typeof Proxy)return!0;try{return Boolean.prototype.valueOf.call(Reflect.construct(Boolean,[],function(){})),!0}catch(e){return!1}}(),function(){var e,n=p(c);return e=t?Reflect.construct(n,arguments,p(this).constructor):n.apply(this,arguments),function(e,t){if(t&&("object"===r(t)||"function"==typeof t))return t;if(void 0!==t)throw TypeError("Derived constructors may only return object or undefined");return f(e)}(this,e)});function c(){var e;!function(e,t){if(!(e instanceof t))throw TypeError("Cannot call a class as a function")}(this,c);for(var t=arguments.length,n=Array(t),r=0;r<t;r++)n[r]=arguments[r];return d(f(e=l.call.apply(l,[this].concat(n))),"onClick",function(t){var n=e.props,r=n.text,a=n.onCopy,l=n.children,c=n.options,u=o.default.Children.only(l),s=(0,i.default)(r,c);a&&a(r,s),u&&u.props&&"function"==typeof u.props.onClick&&u.props.onClick(t)}),e}return n=[{key:"render",value:function(){var e=this.props,t=(e.text,e.onCopy,e.options,e.children),n=function(e,t){if(null==e)return{};var n,r,o=function(e,t){if(null==e)return{};var n,r,o={},i=Object.keys(e);for(r=0;r<i.length;r++)n=i[r],t.indexOf(n)>=0||(o[n]=e[n]);return o}(e,t);if(Object.getOwnPropertySymbols){var i=Object.getOwnPropertySymbols(e);for(r=0;r<i.length;r++)n=i[r],!(t.indexOf(n)>=0)&&Object.prototype.propertyIsEnumerable.call(e,n)&&(o[n]=e[n])}return o}(e,a),r=o.default.Children.only(t);return o.default.cloneElement(r,u(u({},n),{},{onClick:this.onClick}))}}],function(e,t){for(var n=0;n<t.length;n++){var r=t[n];r.enumerable=r.enumerable||!1,r.configurable=!0,"value"in r&&(r.writable=!0),Object.defineProperty(e,r.key,r)}}(c.prototype,n),Object.defineProperty(c,"prototype",{writable:!1}),c}(o.default.PureComponent);t.CopyToClipboard=y,d(y,"defaultProps",{onCopy:void 0,options:void 0})},29131:(e,t,n)=>{"use strict";var r=n(24224).CopyToClipboard;r.CopyToClipboard=r,e.exports=r},39921:e=>{e.exports=function(){var e=document.getSelection();if(!e.rangeCount)return function(){};for(var t=document.activeElement,n=[],r=0;r<e.rangeCount;r++)n.push(e.getRangeAt(r));switch(t.tagName.toUpperCase()){case"INPUT":case"TEXTAREA":t.blur();break;default:t=null}return e.removeAllRanges(),function(){"Caret"===e.type&&e.removeAllRanges(),e.rangeCount||n.forEach(function(t){e.addRange(t)}),t&&t.focus()}}},50349:(e,t,n)=>{"use strict";n.d(t,{FD:()=>b,MY:()=>i,PJ:()=>a,Wl:()=>c,XP:()=>f,_x:()=>o,bL:()=>p,po:()=>u,ql:()=>s,wO:()=>l,yL:()=>h});var r=n(55036);function o(e){return("object"==typeof e?null==e.host&&null==e.hostname:!/^[a-z]+:/i.test(e))&&!function(e){let t="object"==typeof e?e.pathname:e;return null!=t&&!t.startsWith("/")}(e)}function i(e,t){return e.replace(RegExp(`^${t}`),"")||"/"}function a(e,t){let n=e;return/^\/(\?.*)?$/.test(t)&&(t=t.slice(1)),n+=t}function l(e,t){return t===e||t.startsWith(`${e}/`)}function c(e,t,n){return"string"==typeof e?e:e[t]||n}function u(e){let t=function(){try{return"true"===r.env._next_intl_trailing_slash}catch{return!1}}(),[n,...o]=e.split("#"),i=o.join("#"),a=n;if("/"!==a){let e=a.endsWith("/");t&&!e?a+="/":!t&&e&&(a=a.slice(0,-1))}return i&&(a+="#"+i),a}function s(e,t){let n=u(e),r=u(t);return(function(e){let t=e.replace(/\[\[(\.\.\.[^\]]+)\]\]/g,"?(.*)").replace(/\[(\.\.\.[^\]]+)\]/g,"(.+)").replace(/\[([^\]]+)\]/g,"([^/]+)");return RegExp(`^${t}$`)})(n).test(r)}function f(e,t){return"never"!==t.mode&&t.prefixes?.[e]||p(e)}function p(e){return"/"+e}function d(e){return e.includes("[[...")}function y(e){return e.includes("[...")}function m(e){return e.includes("[")}function g(e,t){let n=e.split("/"),r=t.split("/"),o=Math.max(n.length,r.length);for(let e=0;e<o;e++){let t=n[e],o=r[e];if(!t&&o)return -1;if(t&&!o)return 1;if(t||o){if(!m(t)&&m(o))return -1;if(m(t)&&!m(o))return 1;if(!y(t)&&y(o))return -1;if(y(t)&&!y(o))return 1;if(!d(t)&&d(o))return -1;if(d(t)&&!d(o))return 1}}return 0}function b(e){return e.sort(g)}function h(e){return"function"==typeof e.then}},68926:(e,t,n)=>{"use strict";n.d(t,{A:()=>o});var r=n(88454);function o(e,t,n,o){if(!e||o===n||null==o||!t)return;let i=(0,r.DT)(t),{name:a,...l}=e;l.path||(l.path=""!==i?i:"/");let c=`${a}=${o};`;for(let[e,t]of Object.entries(l))c+=`${"maxAge"===e?"max-age":e}`,"boolean"!=typeof t&&(c+="="+t),c+=";";document.cookie=c}},87605:(e,t,n)=>{"use strict";n.d(t,{A:()=>r});let r=(0,n(1316).A)("X",[["path",{d:"M18 6 6 18",key:"1bl5f8"}],["path",{d:"m6 6 12 12",key:"d8bk6v"}]])},88454:(e,t,n)=>{"use strict";n.d(t,{DT:()=>c,FP:()=>a,TK:()=>o,Zn:()=>i,aM:()=>l,x3:()=>u});var r=n(50349);function o(e){return"string"==typeof e?{pathname:e}:e}function i(e){let t=new URLSearchParams;for(let[n,r]of Object.entries(e))Array.isArray(r)?r.forEach(e=>{t.append(n,String(e))}):t.set(n,String(r));return"?"+t.toString()}function a({pathname:e,locale:t,params:n,pathnames:o,query:a}){function l(e){let l;let c=o[e];return c?(l=(0,r.Wl)(c,t,e),n&&Object.entries(n).forEach(([e,t])=>{let n,r;Array.isArray(t)?(n=`(\\[)?\\[...${e}\\](\\])?`,r=t.map(e=>String(e)).join("/")):(n=`\\[${e}\\]`,r=String(t)),l=l.replace(RegExp(n,"g"),r)}),l=(l=l.replace(/\[\[\.\.\..+\]\]/g,"")).split("/").map(e=>encodeURIComponent(e)).join("/")):l=e,l=(0,r.po)(l),a&&(l+=i(a)),l}if("string"==typeof e)return l(e);{let{pathname:t,...n}=e;return{...n,pathname:l(t)}}}function l(e,t,n){let o=(0,r.FD)(Object.keys(n)),i=decodeURI(t);for(let t of o){let o=n[t];if("string"==typeof o){if((0,r.ql)(o,i))return t}else if((0,r.ql)((0,r.Wl)(o,e,t),i))return t}return t}function c(e,t=window.location.pathname){return"/"===e?t:t.replace(e,"")}function u(e,t,n,o){let i;let{mode:a}=n.localePrefix;return void 0!==o?i=o:(0,r._x)(e)&&("always"===a?i=!0:"as-needed"===a&&(i=n.domains?!n.domains.some(e=>e.defaultLocale===t):t!==n.defaultLocale)),i?(0,r.PJ)((0,r.XP)(t,n.localePrefix),e):e}},89799:(e,t,n)=>{"use strict";var r=n(8911);n.o(r,"permanentRedirect")&&n.d(t,{permanentRedirect:function(){return r.permanentRedirect}}),n.o(r,"redirect")&&n.d(t,{redirect:function(){return r.redirect}}),n.o(r,"useParams")&&n.d(t,{useParams:function(){return r.useParams}}),n.o(r,"usePathname")&&n.d(t,{usePathname:function(){return r.usePathname}}),n.o(r,"useRouter")&&n.d(t,{useRouter:function(){return r.useRouter}})},94427:(e,t,n)=>{"use strict";var r=n(39921),o={"text/plain":"Text","text/html":"Url",default:"Text"};e.exports=function(e,t){var n,i,a,l,c,u,s,f,p=!1;t||(t={}),a=t.debug||!1;try{if(c=r(),u=document.createRange(),s=document.getSelection(),(f=document.createElement("span")).textContent=e,f.ariaHidden="true",f.style.all="unset",f.style.position="fixed",f.style.top=0,f.style.clip="rect(0, 0, 0, 0)",f.style.whiteSpace="pre",f.style.webkitUserSelect="text",f.style.MozUserSelect="text",f.style.msUserSelect="text",f.style.userSelect="text",f.addEventListener("copy",function(n){if(n.stopPropagation(),t.format){if(n.preventDefault(),void 0===n.clipboardData){a&&console.warn("unable to use e.clipboardData"),a&&console.warn("trying IE specific stuff"),window.clipboardData.clearData();var r=o[t.format]||o.default;window.clipboardData.setData(r,e)}else n.clipboardData.clearData(),n.clipboardData.setData(t.format,e)}t.onCopy&&(n.preventDefault(),t.onCopy(n.clipboardData))}),document.body.appendChild(f),u.selectNodeContents(f),s.addRange(u),!document.execCommand("copy"))throw Error("copy command was unsuccessful");p=!0}catch(r){a&&console.error("unable to copy using execCommand: ",r),a&&console.warn("trying IE specific stuff");try{window.clipboardData.setData(t.format||"text",e),t.onCopy&&t.onCopy(window.clipboardData),p=!0}catch(r){a&&console.error("unable to copy using clipboardData: ",r),a&&console.error("falling back to prompt"),n="message"in t?t.message:"Copy to clipboard: #{key}, Enter",i=(/mac os x/i.test(navigator.userAgent)?"⌘":"Ctrl")+"+C",l=n.replace(/#{\s*key\s*}/g,i),window.prompt(l,e)}}finally{s&&("function"==typeof s.removeRange?s.removeRange(u):s.removeAllRanges()),f&&document.body.removeChild(f),c()}return p}}}]);