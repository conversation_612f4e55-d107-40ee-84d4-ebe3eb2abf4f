"use strict";(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[9812],{89812:(e,r,n)=>{n.d(r,{UC:()=>eH,YJ:()=>eJ,q7:()=>eQ,JU:()=>eW,ZL:()=>ez,bL:()=>eY,wv:()=>e$,l9:()=>eZ});var t=n(95361),o=n(75030),a=n(94286),l=n(22408),u=n(4471),i=n(72637),d=n(77002),s=n(37651),c=n(10730),p=n(57750),f=n(70908),v=n(25747),m=n(90683),h=n(70200),w=n(12177),g=n(88295),x=n(76012),y=n(21751),C=n(91783),M=n(99126),b=n(16261),R=["Enter"," "],j=["ArrowUp","PageDown","End"],D=["ArrowDown","PageUp","Home",...j],_={ltr:[...R,"ArrowRight"],rtl:[...R,"ArrowLeft"]},k={ltr:["ArrowLeft"],rtl:["ArrowRight"]},P="Menu",[I,E,N]=(0,d.N)(P),[T,O]=(0,l.A)(P,[N,m.Bk,g.RG]),L=(0,m.Bk)(),S=(0,g.RG)(),[A,F]=T(P),[K,G]=T(P),U=e=>{let{__scopeMenu:r,open:n=!1,children:o,dir:a,onOpenChange:l,modal:u=!0}=e,i=L(r),[d,c]=t.useState(null),p=t.useRef(!1),f=(0,y.c)(l),v=(0,s.jH)(a);return t.useEffect(()=>{let e=()=>{p.current=!0,document.addEventListener("pointerdown",r,{capture:!0,once:!0}),document.addEventListener("pointermove",r,{capture:!0,once:!0})},r=()=>p.current=!1;return document.addEventListener("keydown",e,{capture:!0}),()=>{document.removeEventListener("keydown",e,{capture:!0}),document.removeEventListener("pointerdown",r,{capture:!0}),document.removeEventListener("pointermove",r,{capture:!0})}},[]),(0,b.jsx)(m.bL,{...i,children:(0,b.jsx)(A,{scope:r,open:n,onOpenChange:f,content:d,onContentChange:c,children:(0,b.jsx)(K,{scope:r,onClose:t.useCallback(()=>f(!1),[f]),isUsingKeyboardRef:p,dir:v,modal:u,children:o})})})};U.displayName=P;var B=t.forwardRef((e,r)=>{let{__scopeMenu:n,...t}=e,o=L(n);return(0,b.jsx)(m.Mz,{...o,...t,ref:r})});B.displayName="MenuAnchor";var V="MenuPortal",[X,q]=T(V,{forceMount:void 0}),Y=e=>{let{__scopeMenu:r,forceMount:n,children:t,container:o}=e,a=F(V,r);return(0,b.jsx)(X,{scope:r,forceMount:n,children:(0,b.jsx)(w.C,{present:n||a.open,children:(0,b.jsx)(h.Z,{asChild:!0,container:o,children:t})})})};Y.displayName=V;var Z="MenuContent",[z,H]=T(Z),J=t.forwardRef((e,r)=>{let n=q(Z,e.__scopeMenu),{forceMount:t=n.forceMount,...o}=e,a=F(Z,e.__scopeMenu),l=G(Z,e.__scopeMenu);return(0,b.jsx)(I.Provider,{scope:e.__scopeMenu,children:(0,b.jsx)(w.C,{present:t||a.open,children:(0,b.jsx)(I.Slot,{scope:e.__scopeMenu,children:l.modal?(0,b.jsx)(W,{...o,ref:r}):(0,b.jsx)(Q,{...o,ref:r})})})})}),W=t.forwardRef((e,r)=>{let n=F(Z,e.__scopeMenu),l=t.useRef(null),u=(0,a.s)(r,l);return t.useEffect(()=>{let e=l.current;if(e)return(0,C.Eq)(e)},[]),(0,b.jsx)(ee,{...e,ref:u,trapFocus:n.open,disableOutsidePointerEvents:n.open,disableOutsideScroll:!0,onFocusOutside:(0,o.m)(e.onFocusOutside,e=>e.preventDefault(),{checkForDefaultPrevented:!1}),onDismiss:()=>n.onOpenChange(!1)})}),Q=t.forwardRef((e,r)=>{let n=F(Z,e.__scopeMenu);return(0,b.jsx)(ee,{...e,ref:r,trapFocus:!1,disableOutsidePointerEvents:!1,disableOutsideScroll:!1,onDismiss:()=>n.onOpenChange(!1)})}),$=(0,x.TL)("MenuContent.ScrollLock"),ee=t.forwardRef((e,r)=>{let{__scopeMenu:n,loop:l=!1,trapFocus:u,onOpenAutoFocus:i,onCloseAutoFocus:d,disableOutsidePointerEvents:s,onEntryFocus:v,onEscapeKeyDown:h,onPointerDownOutside:w,onFocusOutside:x,onInteractOutside:y,onDismiss:C,disableOutsideScroll:R,..._}=e,k=F(Z,n),P=G(Z,n),I=L(n),N=S(n),T=E(n),[O,A]=t.useState(null),K=t.useRef(null),U=(0,a.s)(r,K,k.onContentChange),B=t.useRef(0),V=t.useRef(""),X=t.useRef(0),q=t.useRef(null),Y=t.useRef("right"),H=t.useRef(0),J=R?M.A:t.Fragment,W=e=>{var r,n;let t=V.current+e,o=T().filter(e=>!e.disabled),a=document.activeElement,l=null===(r=o.find(e=>e.ref.current===a))||void 0===r?void 0:r.textValue,u=function(e,r,n){var t;let o=r.length>1&&Array.from(r).every(e=>e===r[0])?r[0]:r,a=(t=Math.max(n?e.indexOf(n):-1,0),e.map((r,n)=>e[(t+n)%e.length]));1===o.length&&(a=a.filter(e=>e!==n));let l=a.find(e=>e.toLowerCase().startsWith(o.toLowerCase()));return l!==n?l:void 0}(o.map(e=>e.textValue),t,l),i=null===(n=o.find(e=>e.textValue===u))||void 0===n?void 0:n.ref.current;!function e(r){V.current=r,window.clearTimeout(B.current),""!==r&&(B.current=window.setTimeout(()=>e(""),1e3))}(t),i&&setTimeout(()=>i.focus())};t.useEffect(()=>()=>window.clearTimeout(B.current),[]),(0,p.Oh)();let Q=t.useCallback(e=>{var r,n;return Y.current===(null===(r=q.current)||void 0===r?void 0:r.side)&&function(e,r){return!!r&&function(e,r){let{x:n,y:t}=e,o=!1;for(let e=0,a=r.length-1;e<r.length;a=e++){let l=r[e],u=r[a],i=l.x,d=l.y,s=u.x,c=u.y;d>t!=c>t&&n<(s-i)*(t-d)/(c-d)+i&&(o=!o)}return o}({x:e.clientX,y:e.clientY},r)}(e,null===(n=q.current)||void 0===n?void 0:n.area)},[]);return(0,b.jsx)(z,{scope:n,searchRef:V,onItemEnter:t.useCallback(e=>{Q(e)&&e.preventDefault()},[Q]),onItemLeave:t.useCallback(e=>{var r;Q(e)||(null===(r=K.current)||void 0===r||r.focus(),A(null))},[Q]),onTriggerLeave:t.useCallback(e=>{Q(e)&&e.preventDefault()},[Q]),pointerGraceTimerRef:X,onPointerGraceIntentChange:t.useCallback(e=>{q.current=e},[]),children:(0,b.jsx)(J,{...R?{as:$,allowPinchZoom:!0}:void 0,children:(0,b.jsx)(f.n,{asChild:!0,trapped:u,onMountAutoFocus:(0,o.m)(i,e=>{var r;e.preventDefault(),null===(r=K.current)||void 0===r||r.focus({preventScroll:!0})}),onUnmountAutoFocus:d,children:(0,b.jsx)(c.qW,{asChild:!0,disableOutsidePointerEvents:s,onEscapeKeyDown:h,onPointerDownOutside:w,onFocusOutside:x,onInteractOutside:y,onDismiss:C,children:(0,b.jsx)(g.bL,{asChild:!0,...N,dir:P.dir,orientation:"vertical",loop:l,currentTabStopId:O,onCurrentTabStopIdChange:A,onEntryFocus:(0,o.m)(v,e=>{P.isUsingKeyboardRef.current||e.preventDefault()}),preventScrollOnEntryFocus:!0,children:(0,b.jsx)(m.UC,{role:"menu","aria-orientation":"vertical","data-state":eD(k.open),"data-radix-menu-content":"",dir:P.dir,...I,..._,ref:U,style:{outline:"none",..._.style},onKeyDown:(0,o.m)(_.onKeyDown,e=>{let r=e.target.closest("[data-radix-menu-content]")===e.currentTarget,n=e.ctrlKey||e.altKey||e.metaKey,t=1===e.key.length;r&&("Tab"===e.key&&e.preventDefault(),!n&&t&&W(e.key));let o=K.current;if(e.target!==o||!D.includes(e.key))return;e.preventDefault();let a=T().filter(e=>!e.disabled).map(e=>e.ref.current);j.includes(e.key)&&a.reverse(),function(e){let r=document.activeElement;for(let n of e)if(n===r||(n.focus(),document.activeElement!==r))return}(a)}),onBlur:(0,o.m)(e.onBlur,e=>{e.currentTarget.contains(e.target)||(window.clearTimeout(B.current),V.current="")}),onPointerMove:(0,o.m)(e.onPointerMove,eP(e=>{let r=e.target,n=H.current!==e.clientX;e.currentTarget.contains(r)&&n&&(Y.current=e.clientX>H.current?"right":"left",H.current=e.clientX)}))})})})})})})});J.displayName=Z;var er=t.forwardRef((e,r)=>{let{__scopeMenu:n,...t}=e;return(0,b.jsx)(i.sG.div,{role:"group",...t,ref:r})});er.displayName="MenuGroup";var en=t.forwardRef((e,r)=>{let{__scopeMenu:n,...t}=e;return(0,b.jsx)(i.sG.div,{...t,ref:r})});en.displayName="MenuLabel";var et="MenuItem",eo="menu.itemSelect",ea=t.forwardRef((e,r)=>{let{disabled:n=!1,onSelect:l,...u}=e,d=t.useRef(null),s=G(et,e.__scopeMenu),c=H(et,e.__scopeMenu),p=(0,a.s)(r,d),f=t.useRef(!1);return(0,b.jsx)(el,{...u,ref:p,disabled:n,onClick:(0,o.m)(e.onClick,()=>{let e=d.current;if(!n&&e){let r=new CustomEvent(eo,{bubbles:!0,cancelable:!0});e.addEventListener(eo,e=>null==l?void 0:l(e),{once:!0}),(0,i.hO)(e,r),r.defaultPrevented?f.current=!1:s.onClose()}}),onPointerDown:r=>{var n;null===(n=e.onPointerDown)||void 0===n||n.call(e,r),f.current=!0},onPointerUp:(0,o.m)(e.onPointerUp,e=>{var r;f.current||null===(r=e.currentTarget)||void 0===r||r.click()}),onKeyDown:(0,o.m)(e.onKeyDown,e=>{let r=""!==c.searchRef.current;!n&&(!r||" "!==e.key)&&R.includes(e.key)&&(e.currentTarget.click(),e.preventDefault())})})});ea.displayName=et;var el=t.forwardRef((e,r)=>{let{__scopeMenu:n,disabled:l=!1,textValue:u,...d}=e,s=H(et,n),c=S(n),p=t.useRef(null),f=(0,a.s)(r,p),[v,m]=t.useState(!1),[h,w]=t.useState("");return t.useEffect(()=>{let e=p.current;if(e){var r;w((null!==(r=e.textContent)&&void 0!==r?r:"").trim())}},[d.children]),(0,b.jsx)(I.ItemSlot,{scope:n,disabled:l,textValue:null!=u?u:h,children:(0,b.jsx)(g.q7,{asChild:!0,...c,focusable:!l,children:(0,b.jsx)(i.sG.div,{role:"menuitem","data-highlighted":v?"":void 0,"aria-disabled":l||void 0,"data-disabled":l?"":void 0,...d,ref:f,onPointerMove:(0,o.m)(e.onPointerMove,eP(e=>{l?s.onItemLeave(e):(s.onItemEnter(e),e.defaultPrevented||e.currentTarget.focus({preventScroll:!0}))})),onPointerLeave:(0,o.m)(e.onPointerLeave,eP(e=>s.onItemLeave(e))),onFocus:(0,o.m)(e.onFocus,()=>m(!0)),onBlur:(0,o.m)(e.onBlur,()=>m(!1))})})})}),eu=t.forwardRef((e,r)=>{let{checked:n=!1,onCheckedChange:t,...a}=e;return(0,b.jsx)(em,{scope:e.__scopeMenu,checked:n,children:(0,b.jsx)(ea,{role:"menuitemcheckbox","aria-checked":e_(n)?"mixed":n,...a,ref:r,"data-state":ek(n),onSelect:(0,o.m)(a.onSelect,()=>null==t?void 0:t(!!e_(n)||!n),{checkForDefaultPrevented:!1})})})});eu.displayName="MenuCheckboxItem";var ei="MenuRadioGroup",[ed,es]=T(ei,{value:void 0,onValueChange:()=>{}}),ec=t.forwardRef((e,r)=>{let{value:n,onValueChange:t,...o}=e,a=(0,y.c)(t);return(0,b.jsx)(ed,{scope:e.__scopeMenu,value:n,onValueChange:a,children:(0,b.jsx)(er,{...o,ref:r})})});ec.displayName=ei;var ep="MenuRadioItem",ef=t.forwardRef((e,r)=>{let{value:n,...t}=e,a=es(ep,e.__scopeMenu),l=n===a.value;return(0,b.jsx)(em,{scope:e.__scopeMenu,checked:l,children:(0,b.jsx)(ea,{role:"menuitemradio","aria-checked":l,...t,ref:r,"data-state":ek(l),onSelect:(0,o.m)(t.onSelect,()=>{var e;return null===(e=a.onValueChange)||void 0===e?void 0:e.call(a,n)},{checkForDefaultPrevented:!1})})})});ef.displayName=ep;var ev="MenuItemIndicator",[em,eh]=T(ev,{checked:!1}),ew=t.forwardRef((e,r)=>{let{__scopeMenu:n,forceMount:t,...o}=e,a=eh(ev,n);return(0,b.jsx)(w.C,{present:t||e_(a.checked)||!0===a.checked,children:(0,b.jsx)(i.sG.span,{...o,ref:r,"data-state":ek(a.checked)})})});ew.displayName=ev;var eg=t.forwardRef((e,r)=>{let{__scopeMenu:n,...t}=e;return(0,b.jsx)(i.sG.div,{role:"separator","aria-orientation":"horizontal",...t,ref:r})});eg.displayName="MenuSeparator";var ex=t.forwardRef((e,r)=>{let{__scopeMenu:n,...t}=e,o=L(n);return(0,b.jsx)(m.i3,{...o,...t,ref:r})});ex.displayName="MenuArrow";var[ey,eC]=T("MenuSub"),eM="MenuSubTrigger",eb=t.forwardRef((e,r)=>{let n=F(eM,e.__scopeMenu),l=G(eM,e.__scopeMenu),u=eC(eM,e.__scopeMenu),i=H(eM,e.__scopeMenu),d=t.useRef(null),{pointerGraceTimerRef:s,onPointerGraceIntentChange:c}=i,p={__scopeMenu:e.__scopeMenu},f=t.useCallback(()=>{d.current&&window.clearTimeout(d.current),d.current=null},[]);return t.useEffect(()=>f,[f]),t.useEffect(()=>{let e=s.current;return()=>{window.clearTimeout(e),c(null)}},[s,c]),(0,b.jsx)(B,{asChild:!0,...p,children:(0,b.jsx)(el,{id:u.triggerId,"aria-haspopup":"menu","aria-expanded":n.open,"aria-controls":u.contentId,"data-state":eD(n.open),...e,ref:(0,a.t)(r,u.onTriggerChange),onClick:r=>{var t;null===(t=e.onClick)||void 0===t||t.call(e,r),e.disabled||r.defaultPrevented||(r.currentTarget.focus(),n.open||n.onOpenChange(!0))},onPointerMove:(0,o.m)(e.onPointerMove,eP(r=>{i.onItemEnter(r),r.defaultPrevented||e.disabled||n.open||d.current||(i.onPointerGraceIntentChange(null),d.current=window.setTimeout(()=>{n.onOpenChange(!0),f()},100))})),onPointerLeave:(0,o.m)(e.onPointerLeave,eP(e=>{var r,t;f();let o=null===(r=n.content)||void 0===r?void 0:r.getBoundingClientRect();if(o){let r=null===(t=n.content)||void 0===t?void 0:t.dataset.side,a="right"===r,l=o[a?"left":"right"],u=o[a?"right":"left"];i.onPointerGraceIntentChange({area:[{x:e.clientX+(a?-5:5),y:e.clientY},{x:l,y:o.top},{x:u,y:o.top},{x:u,y:o.bottom},{x:l,y:o.bottom}],side:r}),window.clearTimeout(s.current),s.current=window.setTimeout(()=>i.onPointerGraceIntentChange(null),300)}else{if(i.onTriggerLeave(e),e.defaultPrevented)return;i.onPointerGraceIntentChange(null)}})),onKeyDown:(0,o.m)(e.onKeyDown,r=>{let t=""!==i.searchRef.current;if(!e.disabled&&(!t||" "!==r.key)&&_[l.dir].includes(r.key)){var o;n.onOpenChange(!0),null===(o=n.content)||void 0===o||o.focus(),r.preventDefault()}})})})});eb.displayName=eM;var eR="MenuSubContent",ej=t.forwardRef((e,r)=>{let n=q(Z,e.__scopeMenu),{forceMount:l=n.forceMount,...u}=e,i=F(Z,e.__scopeMenu),d=G(Z,e.__scopeMenu),s=eC(eR,e.__scopeMenu),c=t.useRef(null),p=(0,a.s)(r,c);return(0,b.jsx)(I.Provider,{scope:e.__scopeMenu,children:(0,b.jsx)(w.C,{present:l||i.open,children:(0,b.jsx)(I.Slot,{scope:e.__scopeMenu,children:(0,b.jsx)(ee,{id:s.contentId,"aria-labelledby":s.triggerId,...u,ref:p,align:"start",side:"rtl"===d.dir?"left":"right",disableOutsidePointerEvents:!1,disableOutsideScroll:!1,trapFocus:!1,onOpenAutoFocus:e=>{var r;d.isUsingKeyboardRef.current&&(null===(r=c.current)||void 0===r||r.focus()),e.preventDefault()},onCloseAutoFocus:e=>e.preventDefault(),onFocusOutside:(0,o.m)(e.onFocusOutside,e=>{e.target!==s.trigger&&i.onOpenChange(!1)}),onEscapeKeyDown:(0,o.m)(e.onEscapeKeyDown,e=>{d.onClose(),e.preventDefault()}),onKeyDown:(0,o.m)(e.onKeyDown,e=>{let r=e.currentTarget.contains(e.target),n=k[d.dir].includes(e.key);if(r&&n){var t;i.onOpenChange(!1),null===(t=s.trigger)||void 0===t||t.focus(),e.preventDefault()}})})})})})});function eD(e){return e?"open":"closed"}function e_(e){return"indeterminate"===e}function ek(e){return e_(e)?"indeterminate":e?"checked":"unchecked"}function eP(e){return r=>"mouse"===r.pointerType?e(r):void 0}ej.displayName=eR;var eI="DropdownMenu",[eE,eN]=(0,l.A)(eI,[O]),eT=O(),[eO,eL]=eE(eI),eS=e=>{let{__scopeDropdownMenu:r,children:n,dir:o,open:a,defaultOpen:l,onOpenChange:i,modal:d=!0}=e,s=eT(r),c=t.useRef(null),[p,f]=(0,u.i)({prop:a,defaultProp:null!=l&&l,onChange:i,caller:eI});return(0,b.jsx)(eO,{scope:r,triggerId:(0,v.B)(),triggerRef:c,contentId:(0,v.B)(),open:p,onOpenChange:f,onOpenToggle:t.useCallback(()=>f(e=>!e),[f]),modal:d,children:(0,b.jsx)(U,{...s,open:p,onOpenChange:f,dir:o,modal:d,children:n})})};eS.displayName=eI;var eA="DropdownMenuTrigger",eF=t.forwardRef((e,r)=>{let{__scopeDropdownMenu:n,disabled:t=!1,...l}=e,u=eL(eA,n),d=eT(n);return(0,b.jsx)(B,{asChild:!0,...d,children:(0,b.jsx)(i.sG.button,{type:"button",id:u.triggerId,"aria-haspopup":"menu","aria-expanded":u.open,"aria-controls":u.open?u.contentId:void 0,"data-state":u.open?"open":"closed","data-disabled":t?"":void 0,disabled:t,...l,ref:(0,a.t)(r,u.triggerRef),onPointerDown:(0,o.m)(e.onPointerDown,e=>{t||0!==e.button||!1!==e.ctrlKey||(u.onOpenToggle(),u.open||e.preventDefault())}),onKeyDown:(0,o.m)(e.onKeyDown,e=>{!t&&(["Enter"," "].includes(e.key)&&u.onOpenToggle(),"ArrowDown"===e.key&&u.onOpenChange(!0),["Enter"," ","ArrowDown"].includes(e.key)&&e.preventDefault())})})})});eF.displayName=eA;var eK=e=>{let{__scopeDropdownMenu:r,...n}=e,t=eT(r);return(0,b.jsx)(Y,{...t,...n})};eK.displayName="DropdownMenuPortal";var eG="DropdownMenuContent",eU=t.forwardRef((e,r)=>{let{__scopeDropdownMenu:n,...a}=e,l=eL(eG,n),u=eT(n),i=t.useRef(!1);return(0,b.jsx)(J,{id:l.contentId,"aria-labelledby":l.triggerId,...u,...a,ref:r,onCloseAutoFocus:(0,o.m)(e.onCloseAutoFocus,e=>{var r;i.current||null===(r=l.triggerRef.current)||void 0===r||r.focus(),i.current=!1,e.preventDefault()}),onInteractOutside:(0,o.m)(e.onInteractOutside,e=>{let r=e.detail.originalEvent,n=0===r.button&&!0===r.ctrlKey,t=2===r.button||n;(!l.modal||t)&&(i.current=!0)}),style:{...e.style,"--radix-dropdown-menu-content-transform-origin":"var(--radix-popper-transform-origin)","--radix-dropdown-menu-content-available-width":"var(--radix-popper-available-width)","--radix-dropdown-menu-content-available-height":"var(--radix-popper-available-height)","--radix-dropdown-menu-trigger-width":"var(--radix-popper-anchor-width)","--radix-dropdown-menu-trigger-height":"var(--radix-popper-anchor-height)"}})});eU.displayName=eG;var eB=t.forwardRef((e,r)=>{let{__scopeDropdownMenu:n,...t}=e,o=eT(n);return(0,b.jsx)(er,{...o,...t,ref:r})});eB.displayName="DropdownMenuGroup";var eV=t.forwardRef((e,r)=>{let{__scopeDropdownMenu:n,...t}=e,o=eT(n);return(0,b.jsx)(en,{...o,...t,ref:r})});eV.displayName="DropdownMenuLabel";var eX=t.forwardRef((e,r)=>{let{__scopeDropdownMenu:n,...t}=e,o=eT(n);return(0,b.jsx)(ea,{...o,...t,ref:r})});eX.displayName="DropdownMenuItem",t.forwardRef((e,r)=>{let{__scopeDropdownMenu:n,...t}=e,o=eT(n);return(0,b.jsx)(eu,{...o,...t,ref:r})}).displayName="DropdownMenuCheckboxItem",t.forwardRef((e,r)=>{let{__scopeDropdownMenu:n,...t}=e,o=eT(n);return(0,b.jsx)(ec,{...o,...t,ref:r})}).displayName="DropdownMenuRadioGroup",t.forwardRef((e,r)=>{let{__scopeDropdownMenu:n,...t}=e,o=eT(n);return(0,b.jsx)(ef,{...o,...t,ref:r})}).displayName="DropdownMenuRadioItem",t.forwardRef((e,r)=>{let{__scopeDropdownMenu:n,...t}=e,o=eT(n);return(0,b.jsx)(ew,{...o,...t,ref:r})}).displayName="DropdownMenuItemIndicator";var eq=t.forwardRef((e,r)=>{let{__scopeDropdownMenu:n,...t}=e,o=eT(n);return(0,b.jsx)(eg,{...o,...t,ref:r})});eq.displayName="DropdownMenuSeparator",t.forwardRef((e,r)=>{let{__scopeDropdownMenu:n,...t}=e,o=eT(n);return(0,b.jsx)(ex,{...o,...t,ref:r})}).displayName="DropdownMenuArrow",t.forwardRef((e,r)=>{let{__scopeDropdownMenu:n,...t}=e,o=eT(n);return(0,b.jsx)(eb,{...o,...t,ref:r})}).displayName="DropdownMenuSubTrigger",t.forwardRef((e,r)=>{let{__scopeDropdownMenu:n,...t}=e,o=eT(n);return(0,b.jsx)(ej,{...o,...t,ref:r,style:{...e.style,"--radix-dropdown-menu-content-transform-origin":"var(--radix-popper-transform-origin)","--radix-dropdown-menu-content-available-width":"var(--radix-popper-available-width)","--radix-dropdown-menu-content-available-height":"var(--radix-popper-available-height)","--radix-dropdown-menu-trigger-width":"var(--radix-popper-anchor-width)","--radix-dropdown-menu-trigger-height":"var(--radix-popper-anchor-height)"}})}).displayName="DropdownMenuSubContent";var eY=eS,eZ=eF,ez=eK,eH=eU,eJ=eB,eW=eV,eQ=eX,e$=eq}}]);