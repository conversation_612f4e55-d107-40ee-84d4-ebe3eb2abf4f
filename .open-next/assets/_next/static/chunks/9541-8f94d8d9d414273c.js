(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[9541],{5657:e=>{e.exports=function({client_id:e,auto_select:t=!1,cancel_on_tap_outside:n=!1,context:r="signin",...o},a){if(!e)throw Error("client_id is required");if("undefined"!=typeof window&&window.document){let i=["signin","signup","use"].includes(r)?r:"signin",l=document.createElement("script");l.src="https://accounts.google.com/gsi/client",l.async=!0,l.defer=!0,document.head.appendChild(l),window.onload=function(){window.google.accounts.id.initialize({client_id:e,callback:a,auto_select:t,cancel_on_tap_outside:n,context:i,...o}),window.google.accounts.id.prompt()}}}},8624:(e,t,n)=>{"use strict";n.d(t,{c3:()=>a});var r=n(6002);function o(e,t){return(...e)=>{try{return t(...e)}catch{throw Error(void 0)}}}let a=o(0,r.c3);o(0,r.kc)},21494:(e,t,n)=>{"use strict";n.d(t,{UC:()=>en,VY:()=>eo,ZL:()=>ee,bL:()=>Q,bm:()=>ea,hE:()=>er,hJ:()=>et,l9:()=>$});var r=n(95361),o=n(75030),a=n(94286),i=n(22408),l=n(25747),s=n(4471),u=n(10730),c=n(70908),d=n(70200),f=n(12177),v=n(72637),p=n(57750),m=n(99126),g=n(91783),h=n(76012),w=n(16261),x="Dialog",[y,C]=(0,i.A)(x),[R,b]=y(x),E=e=>{let{__scopeDialog:t,children:n,open:o,defaultOpen:a,onOpenChange:i,modal:u=!0}=e,c=r.useRef(null),d=r.useRef(null),[f,v]=(0,s.i)({prop:o,defaultProp:null!=a&&a,onChange:i,caller:x});return(0,w.jsx)(R,{scope:t,triggerRef:c,contentRef:d,contentId:(0,l.B)(),titleId:(0,l.B)(),descriptionId:(0,l.B)(),open:f,onOpenChange:v,onOpenToggle:r.useCallback(()=>v(e=>!e),[v]),modal:u,children:n})};E.displayName=x;var j="DialogTrigger",N=r.forwardRef((e,t)=>{let{__scopeDialog:n,...r}=e,i=b(j,n),l=(0,a.s)(t,i.triggerRef);return(0,w.jsx)(v.sG.button,{type:"button","aria-haspopup":"dialog","aria-expanded":i.open,"aria-controls":i.contentId,"data-state":B(i.open),...r,ref:l,onClick:(0,o.m)(e.onClick,i.onOpenToggle)})});N.displayName=j;var P="DialogPortal",[D,k]=y(P,{forceMount:void 0}),M=e=>{let{__scopeDialog:t,forceMount:n,children:o,container:a}=e,i=b(P,t);return(0,w.jsx)(D,{scope:t,forceMount:n,children:r.Children.map(o,e=>(0,w.jsx)(f.C,{present:n||i.open,children:(0,w.jsx)(d.Z,{asChild:!0,container:a,children:e})}))})};M.displayName=P;var T="DialogOverlay",_=r.forwardRef((e,t)=>{let n=k(T,e.__scopeDialog),{forceMount:r=n.forceMount,...o}=e,a=b(T,e.__scopeDialog);return a.modal?(0,w.jsx)(f.C,{present:r||a.open,children:(0,w.jsx)(L,{...o,ref:t})}):null});_.displayName=T;var I=(0,h.TL)("DialogOverlay.RemoveScroll"),L=r.forwardRef((e,t)=>{let{__scopeDialog:n,...r}=e,o=b(T,n);return(0,w.jsx)(m.A,{as:I,allowPinchZoom:!0,shards:[o.contentRef],children:(0,w.jsx)(v.sG.div,{"data-state":B(o.open),...r,ref:t,style:{pointerEvents:"auto",...r.style}})})}),A="DialogContent",F=r.forwardRef((e,t)=>{let n=k(A,e.__scopeDialog),{forceMount:r=n.forceMount,...o}=e,a=b(A,e.__scopeDialog);return(0,w.jsx)(f.C,{present:r||a.open,children:a.modal?(0,w.jsx)(O,{...o,ref:t}):(0,w.jsx)(S,{...o,ref:t})})});F.displayName=A;var O=r.forwardRef((e,t)=>{let n=b(A,e.__scopeDialog),i=r.useRef(null),l=(0,a.s)(t,n.contentRef,i);return r.useEffect(()=>{let e=i.current;if(e)return(0,g.Eq)(e)},[]),(0,w.jsx)(G,{...e,ref:l,trapFocus:n.open,disableOutsidePointerEvents:!0,onCloseAutoFocus:(0,o.m)(e.onCloseAutoFocus,e=>{var t;e.preventDefault(),null===(t=n.triggerRef.current)||void 0===t||t.focus()}),onPointerDownOutside:(0,o.m)(e.onPointerDownOutside,e=>{let t=e.detail.originalEvent,n=0===t.button&&!0===t.ctrlKey;(2===t.button||n)&&e.preventDefault()}),onFocusOutside:(0,o.m)(e.onFocusOutside,e=>e.preventDefault())})}),S=r.forwardRef((e,t)=>{let n=b(A,e.__scopeDialog),o=r.useRef(!1),a=r.useRef(!1);return(0,w.jsx)(G,{...e,ref:t,trapFocus:!1,disableOutsidePointerEvents:!1,onCloseAutoFocus:t=>{var r,i;null===(r=e.onCloseAutoFocus)||void 0===r||r.call(e,t),t.defaultPrevented||(o.current||null===(i=n.triggerRef.current)||void 0===i||i.focus(),t.preventDefault()),o.current=!1,a.current=!1},onInteractOutside:t=>{var r,i;null===(r=e.onInteractOutside)||void 0===r||r.call(e,t),t.defaultPrevented||(o.current=!0,"pointerdown"!==t.detail.originalEvent.type||(a.current=!0));let l=t.target;(null===(i=n.triggerRef.current)||void 0===i?void 0:i.contains(l))&&t.preventDefault(),"focusin"===t.detail.originalEvent.type&&a.current&&t.preventDefault()}})}),G=r.forwardRef((e,t)=>{let{__scopeDialog:n,trapFocus:o,onOpenAutoFocus:i,onCloseAutoFocus:l,...s}=e,d=b(A,n),f=r.useRef(null),v=(0,a.s)(t,f);return(0,p.Oh)(),(0,w.jsxs)(w.Fragment,{children:[(0,w.jsx)(c.n,{asChild:!0,loop:!0,trapped:o,onMountAutoFocus:i,onUnmountAutoFocus:l,children:(0,w.jsx)(u.qW,{role:"dialog",id:d.contentId,"aria-describedby":d.descriptionId,"aria-labelledby":d.titleId,"data-state":B(d.open),...s,ref:v,onDismiss:()=>d.onOpenChange(!1)})}),(0,w.jsxs)(w.Fragment,{children:[(0,w.jsx)(X,{titleId:d.titleId}),(0,w.jsx)(J,{contentRef:f,descriptionId:d.descriptionId})]})]})}),K="DialogTitle",V=r.forwardRef((e,t)=>{let{__scopeDialog:n,...r}=e,o=b(K,n);return(0,w.jsx)(v.sG.h2,{id:o.titleId,...r,ref:t})});V.displayName=K;var q="DialogDescription",z=r.forwardRef((e,t)=>{let{__scopeDialog:n,...r}=e,o=b(q,n);return(0,w.jsx)(v.sG.p,{id:o.descriptionId,...r,ref:t})});z.displayName=q;var W="DialogClose",Y=r.forwardRef((e,t)=>{let{__scopeDialog:n,...r}=e,a=b(W,n);return(0,w.jsx)(v.sG.button,{type:"button",...r,ref:t,onClick:(0,o.m)(e.onClick,()=>a.onOpenChange(!1))})});function B(e){return e?"open":"closed"}Y.displayName=W;var U="DialogTitleWarning",[H,Z]=(0,i.q)(U,{contentName:A,titleName:K,docsSlug:"dialog"}),X=e=>{let{titleId:t}=e,n=Z(U),o="`".concat(n.contentName,"` requires a `").concat(n.titleName,"` for the component to be accessible for screen reader users.\n\nIf you want to hide the `").concat(n.titleName,"`, you can wrap it with our VisuallyHidden component.\n\nFor more information, see https://radix-ui.com/primitives/docs/components/").concat(n.docsSlug);return r.useEffect(()=>{t&&!document.getElementById(t)&&console.error(o)},[o,t]),null},J=e=>{let{contentRef:t,descriptionId:n}=e,o=Z("DialogDescriptionWarning"),a="Warning: Missing `Description` or `aria-describedby={undefined}` for {".concat(o.contentName,"}.");return r.useEffect(()=>{var e;let r=null===(e=t.current)||void 0===e?void 0:e.getAttribute("aria-describedby");n&&r&&!document.getElementById(n)&&console.warn(a)},[a,t,n]),null},Q=E,$=N,ee=M,et=_,en=F,er=V,eo=z,ea=Y},31068:(e,t,n)=>{"use strict";function r(e){return e}n.d(t,{A:()=>r})},39754:(e,t,n)=>{"use strict";n.d(t,{A:()=>v});var r=n(89799),o=n(95361),a=n.t(o,2),i=n(6002),l=a["use".trim()],s=n(50349),u=n(3932),c=n(88454),d=n(16261),f=n(68926);function v(e){let{Link:t,config:n,getPathname:a,...v}=function(e,t){var n,a,i;let f={...n=t||{},localePrefix:"object"==typeof(i=n.localePrefix)?i:{mode:i||"always"},localeCookie:!!((a=n.localeCookie)??1)&&{name:"NEXT_LOCALE",sameSite:"lax",..."object"==typeof a&&a},localeDetection:n.localeDetection??!0,alternateLinks:n.alternateLinks??!0},v=f.pathnames,p=(0,o.forwardRef)(function({href:t,locale:n,...r},o){let a,i;"object"==typeof t?(a=t.pathname,i=t.params):a=t;let c=(0,s._x)(t),p=e(),g=(0,s.yL)(p)?l(p):p,h=c?m({locale:n||g,href:null==v?a:{pathname:a,params:i},forcePrefix:null!=n||void 0}):a;return(0,d.jsx)(u.default,{ref:o,href:"object"==typeof t?{...t,pathname:h}:h,locale:n,localeCookie:f.localeCookie,...r})});function m(e){let t;let{forcePrefix:n,href:r,locale:o}=e;return null==v?"object"==typeof r?(t=r.pathname,r.query&&(t+=(0,c.Zn)(r.query))):t=r:t=(0,c.FP)({locale:o,...(0,c.TK)(r),pathnames:f.pathnames}),(0,c.x3)(t,o,f,n)}function g(e){return function(t,...n){return e(m(t),...n)}}return{config:f,Link:p,redirect:g(r.redirect),permanentRedirect:g(r.permanentRedirect),getPathname:m}}(i.Ym,e);return{...v,Link:t,usePathname:function(){let e=function(e){let t=(0,r.usePathname)(),n=(0,i.Ym)();return(0,o.useMemo)(()=>{if(!t)return t;let r=t,o=(0,s.XP)(n,e.localePrefix);if((0,s.wO)(o,t))r=(0,s.MY)(t,o);else if("as-needed"===e.localePrefix.mode&&e.localePrefix.prefixes){let e=(0,s.bL)(n);(0,s.wO)(e,t)&&(r=(0,s.MY)(t,e))}return r},[e.localePrefix,n,t])}(n),t=(0,i.Ym)();return(0,o.useMemo)(()=>e&&n.pathnames?(0,c.aM)(t,e,n.pathnames):e,[t,e])},useRouter:function(){let e=(0,r.useRouter)(),t=(0,i.Ym)(),l=(0,r.usePathname)();return(0,o.useMemo)(()=>{function r(e){return function(r,o){let{locale:i,...s}=o||{},u=[a({href:r,locale:i||t})];Object.keys(s).length>0&&u.push(s),(0,f.A)(n.localeCookie,l,t,i),e(...u)}}return{...e,push:r(e.push),replace:r(e.replace),prefetch:r(e.prefetch)}},[t,l,e])},getPathname:a}}},53164:(e,t,n)=>{"use strict";n.d(t,{A:()=>r});let r=(0,n(1316).A)("Menu",[["line",{x1:"4",x2:"20",y1:"12",y2:"12",key:"1e0a9i"}],["line",{x1:"4",x2:"20",y1:"6",y2:"6",key:"1owob3"}],["line",{x1:"4",x2:"20",y1:"18",y2:"18",key:"yk5zj1"}]])},87605:(e,t,n)=>{"use strict";n.d(t,{A:()=>r});let r=(0,n(1316).A)("X",[["path",{d:"M18 6 6 18",key:"1bl5f8"}],["path",{d:"m6 6 12 12",key:"d8bk6v"}]])},88818:(e,t,n)=>{"use strict";n.d(t,{B8:()=>ep,C1:()=>ew,LM:()=>ey,N_:()=>eh,UC:()=>ex,bL:()=>ev,l9:()=>eg,q7:()=>em});var r=n(95361),o=n(32196),a=n(22408),i=n(75030),l=n(72637),s=n(4471),u=n(94286),c=n(37651),d=n(12177),f=n(25747),v=n(77002),p=n(10730),m=n(64595),g=n(53765),h=n(21751),w=n(72129),x=n(16261),y="NavigationMenu",[C,R,b]=(0,v.N)(y),[E,j,N]=(0,v.N)(y),[P,D]=(0,a.A)(y,[b,N]),[k,M]=P(y),[T,_]=P(y),I=r.forwardRef((e,t)=>{let{__scopeNavigationMenu:n,value:o,onValueChange:a,defaultValue:i,delayDuration:d=200,skipDelayDuration:f=300,orientation:v="horizontal",dir:p,...m}=e,[g,h]=r.useState(null),w=(0,u.s)(t,e=>h(e)),C=(0,c.jH)(p),R=r.useRef(0),b=r.useRef(0),E=r.useRef(0),[j,N]=r.useState(!0),[P,D]=(0,s.i)({prop:o,onChange:e=>{let t=f>0;""!==e?(window.clearTimeout(E.current),t&&N(!1)):(window.clearTimeout(E.current),E.current=window.setTimeout(()=>N(!0),f)),null==a||a(e)},defaultProp:null!=i?i:"",caller:y}),k=r.useCallback(()=>{window.clearTimeout(b.current),b.current=window.setTimeout(()=>D(""),150)},[D]),M=r.useCallback(e=>{window.clearTimeout(b.current),D(e)},[D]),T=r.useCallback(e=>{P===e?window.clearTimeout(b.current):R.current=window.setTimeout(()=>{window.clearTimeout(b.current),D(e)},d)},[P,D,d]);return r.useEffect(()=>()=>{window.clearTimeout(R.current),window.clearTimeout(b.current),window.clearTimeout(E.current)},[]),(0,x.jsx)(A,{scope:n,isRootMenu:!0,value:P,dir:C,orientation:v,rootNavigationMenu:g,onTriggerEnter:e=>{window.clearTimeout(R.current),j?T(e):M(e)},onTriggerLeave:()=>{window.clearTimeout(R.current),k()},onContentEnter:()=>window.clearTimeout(b.current),onContentLeave:k,onItemSelect:e=>{D(t=>t===e?"":e)},onItemDismiss:()=>D(""),children:(0,x.jsx)(l.sG.nav,{"aria-label":"Main","data-orientation":v,dir:C,...m,ref:w})})});I.displayName=y;var L="NavigationMenuSub";r.forwardRef((e,t)=>{let{__scopeNavigationMenu:n,value:r,onValueChange:o,defaultValue:a,orientation:i="horizontal",...u}=e,c=M(L,n),[d,f]=(0,s.i)({prop:r,onChange:o,defaultProp:null!=a?a:"",caller:L});return(0,x.jsx)(A,{scope:n,isRootMenu:!1,value:d,dir:c.dir,orientation:i,rootNavigationMenu:c.rootNavigationMenu,onTriggerEnter:e=>f(e),onItemSelect:e=>f(e),onItemDismiss:()=>f(""),children:(0,x.jsx)(l.sG.div,{"data-orientation":i,...u,ref:t})})}).displayName=L;var A=e=>{let{scope:t,isRootMenu:n,rootNavigationMenu:o,dir:a,orientation:i,children:l,value:s,onItemSelect:u,onItemDismiss:c,onTriggerEnter:d,onTriggerLeave:v,onContentEnter:p,onContentLeave:g}=e,[w,y]=r.useState(null),[R,b]=r.useState(new Map),[E,j]=r.useState(null);return(0,x.jsx)(k,{scope:t,isRootMenu:n,rootNavigationMenu:o,value:s,previousValue:(0,m.Z)(s),baseId:(0,f.B)(),dir:a,orientation:i,viewport:w,onViewportChange:y,indicatorTrack:E,onIndicatorTrackChange:j,onTriggerEnter:(0,h.c)(d),onTriggerLeave:(0,h.c)(v),onContentEnter:(0,h.c)(p),onContentLeave:(0,h.c)(g),onItemSelect:(0,h.c)(u),onItemDismiss:(0,h.c)(c),onViewportContentChange:r.useCallback((e,t)=>{b(n=>(n.set(e,t),new Map(n)))},[]),onViewportContentRemove:r.useCallback(e=>{b(t=>t.has(e)?(t.delete(e),new Map(t)):t)},[]),children:(0,x.jsx)(C.Provider,{scope:t,children:(0,x.jsx)(T,{scope:t,items:R,children:l})})})},F="NavigationMenuList",O=r.forwardRef((e,t)=>{let{__scopeNavigationMenu:n,...r}=e,o=M(F,n),a=(0,x.jsx)(l.sG.ul,{"data-orientation":o.orientation,...r,ref:t});return(0,x.jsx)(l.sG.div,{style:{position:"relative"},ref:o.onIndicatorTrackChange,children:(0,x.jsx)(C.Slot,{scope:n,children:o.isRootMenu?(0,x.jsx)(er,{asChild:!0,children:a}):a})})});O.displayName=F;var S="NavigationMenuItem",[G,K]=P(S),V=r.forwardRef((e,t)=>{let{__scopeNavigationMenu:n,value:o,...a}=e,i=(0,f.B)(),s=r.useRef(null),u=r.useRef(null),c=r.useRef(null),d=r.useRef(()=>{}),v=r.useRef(!1),p=r.useCallback(function(){let e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:"start";if(s.current){d.current();let t=ei(s.current);t.length&&el("start"===e?t:t.reverse())}},[]),m=r.useCallback(()=>{if(s.current){let e=ei(s.current);e.length&&(d.current=function(e){return e.forEach(e=>{e.dataset.tabindex=e.getAttribute("tabindex")||"",e.setAttribute("tabindex","-1")}),()=>{e.forEach(e=>{let t=e.dataset.tabindex;e.setAttribute("tabindex",t)})}}(e))}},[]);return(0,x.jsx)(G,{scope:n,value:o||i||"LEGACY_REACT_AUTO_VALUE",triggerRef:u,contentRef:s,focusProxyRef:c,wasEscapeCloseRef:v,onEntryKeyDown:p,onFocusProxyEnter:p,onRootContentClose:m,onContentFocusOutside:m,children:(0,x.jsx)(l.sG.li,{...a,ref:t})})});V.displayName=S;var q="NavigationMenuTrigger",z=r.forwardRef((e,t)=>{let{__scopeNavigationMenu:n,disabled:o,...a}=e,s=M(q,e.__scopeNavigationMenu),c=K(q,e.__scopeNavigationMenu),d=r.useRef(null),f=(0,u.s)(d,c.triggerRef,t),v=ec(s.baseId,c.value),p=ed(s.baseId,c.value),m=r.useRef(!1),g=r.useRef(!1),h=c.value===s.value;return(0,x.jsxs)(x.Fragment,{children:[(0,x.jsx)(C.ItemSlot,{scope:n,value:c.value,children:(0,x.jsx)(ea,{asChild:!0,children:(0,x.jsx)(l.sG.button,{id:v,disabled:o,"data-disabled":o?"":void 0,"data-state":eu(h),"aria-expanded":h,"aria-controls":p,...a,ref:f,onPointerEnter:(0,i.m)(e.onPointerEnter,()=>{g.current=!1,c.wasEscapeCloseRef.current=!1}),onPointerMove:(0,i.m)(e.onPointerMove,ef(()=>{o||g.current||c.wasEscapeCloseRef.current||m.current||(s.onTriggerEnter(c.value),m.current=!0)})),onPointerLeave:(0,i.m)(e.onPointerLeave,ef(()=>{o||(s.onTriggerLeave(),m.current=!1)})),onClick:(0,i.m)(e.onClick,()=>{s.onItemSelect(c.value),g.current=h}),onKeyDown:(0,i.m)(e.onKeyDown,e=>{let t={horizontal:"ArrowDown",vertical:"rtl"===s.dir?"ArrowLeft":"ArrowRight"}[s.orientation];h&&e.key===t&&(c.onEntryKeyDown(),e.preventDefault())})})})}),h&&(0,x.jsxs)(x.Fragment,{children:[(0,x.jsx)(w.bL,{"aria-hidden":!0,tabIndex:0,ref:c.focusProxyRef,onFocus:e=>{let t=c.contentRef.current,n=e.relatedTarget,r=n===d.current,o=null==t?void 0:t.contains(n);(r||!o)&&c.onFocusProxyEnter(r?"start":"end")}}),s.viewport&&(0,x.jsx)("span",{"aria-owns":p})]})]})});z.displayName=q;var W="navigationMenu.linkSelect",Y=r.forwardRef((e,t)=>{let{__scopeNavigationMenu:n,active:r,onSelect:o,...a}=e;return(0,x.jsx)(ea,{asChild:!0,children:(0,x.jsx)(l.sG.a,{"data-active":r?"":void 0,"aria-current":r?"page":void 0,...a,ref:t,onClick:(0,i.m)(e.onClick,e=>{let t=e.target,n=new CustomEvent(W,{bubbles:!0,cancelable:!0});if(t.addEventListener(W,e=>null==o?void 0:o(e),{once:!0}),(0,l.hO)(t,n),!n.defaultPrevented&&!e.metaKey){let e=new CustomEvent(Q,{bubbles:!0,cancelable:!0});(0,l.hO)(t,e)}},{checkForDefaultPrevented:!1})})})});Y.displayName="NavigationMenuLink";var B="NavigationMenuIndicator",U=r.forwardRef((e,t)=>{let{forceMount:n,...r}=e,a=M(B,e.__scopeNavigationMenu),i=!!a.value;return a.indicatorTrack?o.createPortal((0,x.jsx)(d.C,{present:n||i,children:(0,x.jsx)(H,{...r,ref:t})}),a.indicatorTrack):null});U.displayName=B;var H=r.forwardRef((e,t)=>{let{__scopeNavigationMenu:n,...o}=e,a=M(B,n),i=R(n),[s,u]=r.useState(null),[c,d]=r.useState(null),f="horizontal"===a.orientation,v=!!a.value;r.useEffect(()=>{var e;let t=null===(e=i().find(e=>e.value===a.value))||void 0===e?void 0:e.ref.current;t&&u(t)},[i,a.value]);let p=()=>{s&&d({size:f?s.offsetWidth:s.offsetHeight,offset:f?s.offsetLeft:s.offsetTop})};return es(s,p),es(a.indicatorTrack,p),c?(0,x.jsx)(l.sG.div,{"aria-hidden":!0,"data-state":v?"visible":"hidden","data-orientation":a.orientation,...o,ref:t,style:{position:"absolute",...f?{left:0,width:c.size+"px",transform:"translateX(".concat(c.offset,"px)")}:{top:0,height:c.size+"px",transform:"translateY(".concat(c.offset,"px)")},...o.style}}):null}),Z="NavigationMenuContent",X=r.forwardRef((e,t)=>{let{forceMount:n,...r}=e,o=M(Z,e.__scopeNavigationMenu),a=K(Z,e.__scopeNavigationMenu),l=(0,u.s)(a.contentRef,t),s=a.value===o.value,c={value:a.value,triggerRef:a.triggerRef,focusProxyRef:a.focusProxyRef,wasEscapeCloseRef:a.wasEscapeCloseRef,onContentFocusOutside:a.onContentFocusOutside,onRootContentClose:a.onRootContentClose,...r};return o.viewport?(0,x.jsx)(J,{forceMount:n,...c,ref:l}):(0,x.jsx)(d.C,{present:n||s,children:(0,x.jsx)($,{"data-state":eu(s),...c,ref:l,onPointerEnter:(0,i.m)(e.onPointerEnter,o.onContentEnter),onPointerLeave:(0,i.m)(e.onPointerLeave,ef(o.onContentLeave)),style:{pointerEvents:!s&&o.isRootMenu?"none":void 0,...c.style}})})});X.displayName=Z;var J=r.forwardRef((e,t)=>{let{onViewportContentChange:n,onViewportContentRemove:r}=M(Z,e.__scopeNavigationMenu);return(0,g.N)(()=>{n(e.value,{ref:t,...e})},[e,t,n]),(0,g.N)(()=>()=>r(e.value),[e.value,r]),null}),Q="navigationMenu.rootContentDismiss",$=r.forwardRef((e,t)=>{let{__scopeNavigationMenu:n,value:o,triggerRef:a,focusProxyRef:l,wasEscapeCloseRef:s,onRootContentClose:c,onContentFocusOutside:d,...f}=e,v=M(Z,n),m=r.useRef(null),g=(0,u.s)(m,t),h=ec(v.baseId,o),w=ed(v.baseId,o),y=R(n),C=r.useRef(null),{onItemDismiss:b}=v;r.useEffect(()=>{let e=m.current;if(v.isRootMenu&&e){let t=()=>{var t;b(),c(),e.contains(document.activeElement)&&(null===(t=a.current)||void 0===t||t.focus())};return e.addEventListener(Q,t),()=>e.removeEventListener(Q,t)}},[v.isRootMenu,e.value,a,b,c]);let E=r.useMemo(()=>{let e=y().map(e=>e.value);"rtl"===v.dir&&e.reverse();let t=e.indexOf(v.value),n=e.indexOf(v.previousValue),r=o===v.value,a=n===e.indexOf(o);if(!r&&!a)return C.current;let i=(()=>{if(t!==n){if(r&&-1!==n)return t>n?"from-end":"from-start";if(a&&-1!==t)return t>n?"to-start":"to-end"}return null})();return C.current=i,i},[v.previousValue,v.value,v.dir,y,o]);return(0,x.jsx)(er,{asChild:!0,children:(0,x.jsx)(p.qW,{id:w,"aria-labelledby":h,"data-motion":E,"data-orientation":v.orientation,...f,ref:g,disableOutsidePointerEvents:!1,onDismiss:()=>{var e;let t=new Event(Q,{bubbles:!0,cancelable:!0});null===(e=m.current)||void 0===e||e.dispatchEvent(t)},onFocusOutside:(0,i.m)(e.onFocusOutside,e=>{var t;d();let n=e.target;(null===(t=v.rootNavigationMenu)||void 0===t?void 0:t.contains(n))&&e.preventDefault()}),onPointerDownOutside:(0,i.m)(e.onPointerDownOutside,e=>{var t;let n=e.target,r=y().some(e=>{var t;return null===(t=e.ref.current)||void 0===t?void 0:t.contains(n)}),o=v.isRootMenu&&(null===(t=v.viewport)||void 0===t?void 0:t.contains(n));(r||o||!v.isRootMenu)&&e.preventDefault()}),onKeyDown:(0,i.m)(e.onKeyDown,e=>{let t=e.altKey||e.ctrlKey||e.metaKey;if("Tab"===e.key&&!t){let t=ei(e.currentTarget),r=document.activeElement,o=t.findIndex(e=>e===r);if(el(e.shiftKey?t.slice(0,o).reverse():t.slice(o+1,t.length)))e.preventDefault();else{var n;null===(n=l.current)||void 0===n||n.focus()}}}),onEscapeKeyDown:(0,i.m)(e.onEscapeKeyDown,e=>{s.current=!0})})})}),ee="NavigationMenuViewport",et=r.forwardRef((e,t)=>{let{forceMount:n,...r}=e,o=!!M(ee,e.__scopeNavigationMenu).value;return(0,x.jsx)(d.C,{present:n||o,children:(0,x.jsx)(en,{...r,ref:t})})});et.displayName=ee;var en=r.forwardRef((e,t)=>{let{__scopeNavigationMenu:n,children:o,...a}=e,s=M(ee,n),c=(0,u.s)(t,s.onViewportChange),f=_(Z,e.__scopeNavigationMenu),[v,p]=r.useState(null),[m,g]=r.useState(null),h=v?(null==v?void 0:v.width)+"px":void 0,w=v?(null==v?void 0:v.height)+"px":void 0,y=!!s.value,C=y?s.value:s.previousValue;return es(m,()=>{m&&p({width:m.offsetWidth,height:m.offsetHeight})}),(0,x.jsx)(l.sG.div,{"data-state":eu(y),"data-orientation":s.orientation,...a,ref:c,style:{pointerEvents:!y&&s.isRootMenu?"none":void 0,"--radix-navigation-menu-viewport-width":h,"--radix-navigation-menu-viewport-height":w,...a.style},onPointerEnter:(0,i.m)(e.onPointerEnter,s.onContentEnter),onPointerLeave:(0,i.m)(e.onPointerLeave,ef(s.onContentLeave)),children:Array.from(f.items).map(e=>{let[t,{ref:n,forceMount:r,...o}]=e,a=C===t;return(0,x.jsx)(d.C,{present:r||a,children:(0,x.jsx)($,{...o,ref:(0,u.t)(n,e=>{a&&e&&g(e)})})},t)})})}),er=r.forwardRef((e,t)=>{let{__scopeNavigationMenu:n,...r}=e,o=M("FocusGroup",n);return(0,x.jsx)(E.Provider,{scope:n,children:(0,x.jsx)(E.Slot,{scope:n,children:(0,x.jsx)(l.sG.div,{dir:o.dir,...r,ref:t})})})}),eo=["ArrowRight","ArrowLeft","ArrowUp","ArrowDown"],ea=r.forwardRef((e,t)=>{let{__scopeNavigationMenu:n,...r}=e,o=j(n),a=M("FocusGroupItem",n);return(0,x.jsx)(E.ItemSlot,{scope:n,children:(0,x.jsx)(l.sG.button,{...r,ref:t,onKeyDown:(0,i.m)(e.onKeyDown,e=>{if(["Home","End",...eo].includes(e.key)){let t=o().map(e=>e.ref.current);if(["rtl"===a.dir?"ArrowRight":"ArrowLeft","ArrowUp","End"].includes(e.key)&&t.reverse(),eo.includes(e.key)){let n=t.indexOf(e.currentTarget);t=t.slice(n+1)}setTimeout(()=>el(t)),e.preventDefault()}})})})});function ei(e){let t=[],n=document.createTreeWalker(e,NodeFilter.SHOW_ELEMENT,{acceptNode:e=>{let t="INPUT"===e.tagName&&"hidden"===e.type;return e.disabled||e.hidden||t?NodeFilter.FILTER_SKIP:e.tabIndex>=0?NodeFilter.FILTER_ACCEPT:NodeFilter.FILTER_SKIP}});for(;n.nextNode();)t.push(n.currentNode);return t}function el(e){let t=document.activeElement;return e.some(e=>e===t||(e.focus(),document.activeElement!==t))}function es(e,t){let n=(0,h.c)(t);(0,g.N)(()=>{let t=0;if(e){let r=new ResizeObserver(()=>{cancelAnimationFrame(t),t=window.requestAnimationFrame(n)});return r.observe(e),()=>{window.cancelAnimationFrame(t),r.unobserve(e)}}},[e,n])}function eu(e){return e?"open":"closed"}function ec(e,t){return"".concat(e,"-trigger-").concat(t)}function ed(e,t){return"".concat(e,"-content-").concat(t)}function ef(e){return t=>"mouse"===t.pointerType?e(t):void 0}var ev=I,ep=O,em=V,eg=z,eh=Y,ew=U,ex=X,ey=et}}]);