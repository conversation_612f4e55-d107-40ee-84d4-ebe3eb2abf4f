"use strict";(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[8472],{15074:(e,t,a)=>{a.d(t,{A0:()=>c,DQ:()=>h,OE:()=>f,l5:()=>g,or:()=>u,rB:()=>d});var n=a(17184),r=a(88999),s=a(63843);function l(e){let t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:30,a=-1===t?-1:(0,s.lg)()+86400*t;(0,n.PW)(r.wD.LanguagePreference,JSON.stringify(e),a)}function o(){let e=(0,n.pr)(r.wD.LanguagePreference);if(!e)return null;try{return JSON.parse(e)}catch(e){return console.error("Failed to parse language preference:",e),c(),null}}function c(){(0,n.np)(r.wD.LanguagePreference)}function i(e,t,a){let l=arguments.length>3&&void 0!==arguments[3]?arguments[3]:7,o={languagePair:"".concat(e,"-").concat(t),timestamp:(0,s.lg)(),choice:a},c=(0,s.lg)()+86400*l;(0,n.PW)(r.wD.LanguageSwitchAsked,JSON.stringify(o),c)}function u(){(0,n.np)(r.wD.LanguageSwitchAsked)}function d(e,t){let a=o();if(a&&(a.selectedLanguage===e||a.declined&&a.detectedLanguage===t&&a.selectedLanguage===e))return!1;let s=function(e,t){let a=(0,n.pr)(r.wD.LanguageSwitchAsked);if(!a)return null;try{let n=JSON.parse(a),r="".concat(e,"-").concat(t);if(n.languagePair===r)return n;return null}catch(e){return console.error("Failed to parse language switch asked record:",e),null}}(e,t);return!s||(s.choice,!1)}function g(e,t,a){l({selectedLanguage:t,detectedLanguage:a,declined:!1,timestamp:(0,s.lg)()}),i(e,t,"accepted")}function f(e,t,a){l({selectedLanguage:e,detectedLanguage:a,declined:!0,timestamp:(0,s.lg)()}),i(e,t,"declined")}function h(){return{preference:o(),switchAsked:(0,n.pr)(r.wD.LanguageSwitchAsked),timestamp:(0,s.lg)()}}},17184:(e,t,a)=>{a.d(t,{PW:()=>s,np:()=>l,pr:()=>r});var n=a(63843);let r=e=>{let t=localStorage.getItem(e);if(!t)return null;let a=t.split(":");if(!a||a.length<2)return null;let r=Number(a[0]),s=(0,n.lg)();if(-1!==r&&r<s)return l(e),null;let o=a[0]+":";return t.replace(o,"")},s=(e,t,a)=>{localStorage.setItem(e,a+":"+t)},l=e=>{localStorage.removeItem(e)}},36254:(e,t,a)=>{a.d(t,{BZ:()=>o,aj:()=>c,eV:()=>i,k3:()=>u,xd:()=>s});var n=a(55777);let r={en:"en","en-US":"en","en-GB":"en","en-CA":"en","en-AU":"en",zh:"zh","zh-CN":"zh","zh-TW":"zh","zh-HK":"zh","zh-MO":"zh","zh-SG":"zh"};function s(){return navigator.language?navigator.language:navigator.languages&&navigator.languages.length>0?navigator.languages[0]:null}function l(){let e=[];return navigator.language&&e.push(navigator.language),navigator.languages&&navigator.languages.forEach(t=>{e.includes(t)||e.push(t)}),e}function o(){for(let e of l()){let t=function(e){if(r[e])return r[e];let t=e.split("-")[0];return r[t]?r[t]:null}(e);if(t&&n.IB.includes(t))return t}return null}function c(e){let t=o();return t&&t!==e?t:null}function i(e){return({en:"English",zh:"中文"})[e]||e}function u(e){let t=s(),a=l(),i=o(),u=c(e);return{browserLanguage:t,browserLanguages:a,supportedBrowserLang:i,currentLocale:e,suggestedLang:u,appSupportedLanguages:n.IB,languageMapping:r}}},48472:(e,t,a)=>{a.d(t,{G:()=>m,default:()=>h});var n=a(16261),r=a(95361),s=a(89799),l=a(36254),o=a(15074),c=a(62184),i=a(82888),u=a(17502);function d(e){let{open:t,currentLanguage:a,suggestedLanguage:r,onSwitch:s,onCancel:o,onClose:d,texts:g}=e,f=(0,l.eV)(a),h=(0,l.eV)(r),m=(e,t)=>"zh"===e?"切换到".concat(t):"Switch to ".concat(t),p=(e,t)=>"zh"===e?"保持".concat(t):"Keep ".concat(t),x={title:"Switch Language?",description:"We detected that your browser language is ".concat(h,". Would you like to switch from ").concat(f," to ").concat(h,"?"),switchButton:m(r,h),cancelButton:p(a,f)},v={title:"切换语言？",description:"我们检测到您的浏览器语言是".concat(h,"。您是否要从").concat(f,"切换到").concat(h,"？"),switchButton:m(r,h),cancelButton:p(a,f)},w=g||("zh"===a?v:x);return(0,n.jsx)(c.lG,{open:t,onOpenChange:d,children:(0,n.jsxs)(c.Cf,{className:"sm:max-w-md",children:[(0,n.jsxs)(c.c7,{children:[(0,n.jsxs)(c.L3,{className:"flex items-center gap-2",children:[(0,n.jsx)(u.aVW,{className:"text-xl text-primary"}),w.title]}),(0,n.jsx)(c.rr,{className:"text-left",children:w.description})]}),(0,n.jsxs)(c.Es,{className:"flex-col sm:flex-row gap-2",children:[(0,n.jsx)(i.$,{variant:"outline",onClick:()=>{o(),d()},className:"w-full sm:w-auto",children:w.cancelButton}),(0,n.jsx)(i.$,{onClick:()=>{s(),d()},className:"w-full sm:w-auto",children:w.switchButton})]})]})})}function g(e){let{open:t,currentLanguage:a,suggestedLanguage:r,onSwitch:s,onCancel:l,onClose:o}=e;return(0,n.jsx)(d,{open:t,currentLanguage:a,suggestedLanguage:r,onSwitch:s,onCancel:l,onClose:o})}var f=a(98690);function h(e){let{debug:t=!1,detectionDelay:a=1e3,onLanguageSwitch:c,useI18n:i=!0}=e,u=(0,s.useParams)(),d=(0,s.useRouter)(),h=(0,s.usePathname)(),m=u.locale,[p,x]=(0,r.useState)(!1),[v,w]=(0,r.useState)(null),[b,N]=(0,r.useState)(!1),j=(e,t)=>{if(c)c(e,t);else{let a=h.replace("/".concat(e),"/".concat(t));a.startsWith("/".concat(t))||(a="/".concat(t).concat(a)),d.push(a)}},y=()=>{if(!v)return;let e=(0,l.aj)(m);e&&((0,o.l5)(m,v,e),j(m,v))},L=()=>{if(!v)return;let e=(0,l.aj)(m);e&&(0,o.OE)(m,v,e)},k=()=>{try{let e=(0,l.aj)(m);if(t){let e=(0,l.k3)(m);console.log("Language Detection Info:",e)}if(e){let a=(0,o.rB)(m,e);t&&(console.log("Should show language suggestion:",a),console.log("Suggested language:",e)),a&&(w(e),x(!0))}}catch(e){console.error("Language detection failed:",e)}finally{N(!0)}};return((0,r.useEffect)(()=>{let e=setTimeout(()=>{k()},a);return()=>clearTimeout(e)},[m,a]),t&&b)?(0,n.jsxs)("div",{className:"fixed bottom-4 right-4 bg-black/80 text-white p-4 rounded-lg text-xs max-w-sm z-50",children:[(0,n.jsx)("h4",{className:"font-bold mb-2",children:"Language Detection Debug"}),(0,n.jsxs)("div",{className:"space-y-1",children:[(0,n.jsxs)("div",{children:["Current: ",m]}),(0,n.jsxs)("div",{children:["Suggested: ",v||"None"]}),(0,n.jsxs)("div",{children:["Show Dialog: ",p?"Yes":"No"]})]}),p&&(0,n.jsx)(g,{open:p,currentLanguage:m,suggestedLanguage:v,onSwitch:y,onCancel:L,onClose:()=>x(!1)})]}):(0,n.jsx)(n.Fragment,{children:p&&v&&(i?(0,n.jsx)(f.A,{open:p,currentLanguage:m,suggestedLanguage:v,onSwitch:y,onCancel:L,onClose:()=>x(!1)}):(0,n.jsx)(g,{open:p,currentLanguage:m,suggestedLanguage:v,onSwitch:y,onCancel:L,onClose:()=>x(!1)}))})}function m(){return(0,n.jsx)(h,{debug:!0,detectionDelay:500})}},55777:(e,t,a)=>{a.d(t,{GB:()=>o,IB:()=>n,L$:()=>r,b:()=>l,q:()=>s,u7:()=>c});let n=["en","zh"],r={en:"English",zh:"中文"},s="en",l="as-needed",o=!1,c={en:{"privacy-policy":"/privacy-policy","terms-of-service":"/terms-of-service"}}},61063:(e,t,a)=>{a.d(t,{cn:()=>s});var n=a(87786),r=a(34835);function s(){for(var e=arguments.length,t=Array(e),a=0;a<e;a++)t[a]=arguments[a];return(0,r.QP)((0,n.$)(t))}},62184:(e,t,a)=>{a.d(t,{Cf:()=>d,Es:()=>f,L3:()=>h,c7:()=>g,lG:()=>c,rr:()=>m});var n=a(16261),r=a(95361),s=a(21494),l=a(87605),o=a(61063);let c=s.bL;s.l9;let i=s.ZL;s.bm;let u=r.forwardRef((e,t)=>{let{className:a,...r}=e;return(0,n.jsx)(s.hJ,{ref:t,className:(0,o.cn)("fixed inset-0 z-50 bg-black/80  data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=open]:fade-in-0",a),...r})});u.displayName=s.hJ.displayName;let d=r.forwardRef((e,t)=>{let{className:a,children:r,...c}=e;return(0,n.jsxs)(i,{children:[(0,n.jsx)(u,{}),(0,n.jsxs)(s.UC,{ref:t,className:(0,o.cn)("fixed left-[50%] top-[50%] z-50 grid w-full max-w-lg translate-x-[-50%] translate-y-[-50%] gap-4 border bg-background p-6 shadow-lg duration-200 data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=open]:fade-in-0 data-[state=closed]:zoom-out-95 data-[state=open]:zoom-in-95 data-[state=closed]:slide-out-to-left-1/2 data-[state=closed]:slide-out-to-top-[48%] data-[state=open]:slide-in-from-left-1/2 data-[state=open]:slide-in-from-top-[48%] sm:rounded-lg",a),...c,children:[r,(0,n.jsxs)(s.bm,{className:"absolute right-4 top-4 rounded-sm opacity-70 ring-offset-background transition-opacity hover:opacity-100 focus:outline-hidden focus:ring-2 focus:ring-ring focus:ring-offset-2 disabled:pointer-events-none data-[state=open]:bg-accent data-[state=open]:text-muted-foreground",children:[(0,n.jsx)(l.A,{className:"h-4 w-4"}),(0,n.jsx)("span",{className:"sr-only",children:"Close"})]})]})]})});d.displayName=s.UC.displayName;let g=e=>{let{className:t,...a}=e;return(0,n.jsx)("div",{className:(0,o.cn)("flex flex-col space-y-1.5 text-center sm:text-left",t),...a})};g.displayName="DialogHeader";let f=e=>{let{className:t,...a}=e;return(0,n.jsx)("div",{className:(0,o.cn)("flex flex-col-reverse sm:flex-row sm:justify-end sm:space-x-2",t),...a})};f.displayName="DialogFooter";let h=r.forwardRef((e,t)=>{let{className:a,...r}=e;return(0,n.jsx)(s.hE,{ref:t,className:(0,o.cn)("text-lg font-semibold leading-none tracking-tight",a),...r})});h.displayName=s.hE.displayName;let m=r.forwardRef((e,t)=>{let{className:a,...r}=e;return(0,n.jsx)(s.VY,{ref:t,className:(0,o.cn)("text-sm text-muted-foreground",a),...r})});m.displayName=s.VY.displayName},63843:(e,t,a)=>{a.d(t,{lg:()=>n});let n=()=>Date.parse(new Date().toUTCString())/1e3},82888:(e,t,a)=>{a.d(t,{$:()=>c,r:()=>o});var n=a(16261);a(95361);var r=a(76012),s=a(20604),l=a(61063);let o=(0,s.F)("inline-flex items-center justify-center gap-2 whitespace-nowrap rounded-lg text-sm font-medium transition-all duration-200 disabled:pointer-events-none disabled:opacity-50 [&_svg]:pointer-events-none [&_svg:not([class*='size-'])]:size-4 shrink-0 [&_svg]:shrink-0 outline-none focus-visible:border-ring focus-visible:ring-ring/50 focus-visible:ring-[3px] aria-invalid:ring-destructive/20 dark:aria-invalid:ring-destructive/40 aria-invalid:border-destructive",{variants:{variant:{default:"bg-gradient-to-r from-primary to-accent text-primary-foreground shadow-md hover:shadow-lg hover:scale-[1.02] active:scale-[0.98]",destructive:"bg-destructive text-destructive-foreground shadow-md hover:bg-destructive/90 hover:shadow-lg hover:scale-[1.02] active:scale-[0.98] focus-visible:ring-destructive/20 dark:focus-visible:ring-destructive/40",outline:"border-2 border-primary/20 bg-background/50 backdrop-blur-sm shadow-sm hover:bg-accent/50 hover:text-accent-foreground hover:border-primary/40 hover:shadow-md hover:scale-[1.02] active:scale-[0.98] dark:bg-input/30 dark:border-input dark:hover:bg-input/50",secondary:"bg-gradient-to-r from-secondary to-muted text-secondary-foreground shadow-sm hover:shadow-md hover:scale-[1.02] active:scale-[0.98]",ghost:"hover:bg-accent/50 hover:text-accent-foreground hover:scale-[1.02] active:scale-[0.98] dark:hover:bg-accent/30",link:"text-primary underline-offset-4 hover:underline hover:text-accent"},size:{default:"h-10 px-6 py-2 has-[>svg]:px-4",sm:"h-8 rounded-lg gap-1.5 px-4 has-[>svg]:px-3",lg:"h-12 rounded-lg px-8 has-[>svg]:px-6 text-base",icon:"size-10 rounded-full"}},defaultVariants:{variant:"default",size:"default"}});function c(e){let{className:t,variant:a,size:s,asChild:c=!1,...i}=e,u=c?r.DX:"button";return(0,n.jsx)(u,{"data-slot":"button",className:(0,l.cn)(o({variant:a,size:s,className:t})),...i})}},88999:(e,t,a)=>{a.d(t,{wD:()=>n});let n={Theme:"THEME",InviteCode:"INVITE_CODE",LanguagePreference:"LANGUAGE_PREFERENCE",LanguageSwitchAsked:"LANGUAGE_SWITCH_ASKED"}},98690:(e,t,a)=>{a.d(t,{A:()=>i});var n=a(16261);a(95361);var r=a(8624),s=a(62184),l=a(82888),o=a(17502),c=a(36254);function i(e){let{open:t,currentLanguage:a,suggestedLanguage:i,onSwitch:u,onCancel:d,onClose:g}=e,f=(0,r.c3)("language_switch"),h=(0,c.eV)(a),m=(0,c.eV)(i),p=f("description",{currentLanguage:h,suggestedLanguage:m}),x=f("switch_button_".concat(i),{suggestedLanguage:m}),v=f("cancel_button_".concat(a),{currentLanguage:h});return(0,n.jsx)(s.lG,{open:t,onOpenChange:g,children:(0,n.jsxs)(s.Cf,{className:"sm:max-w-md",children:[(0,n.jsxs)(s.c7,{children:[(0,n.jsxs)(s.L3,{className:"flex items-center gap-2",children:[(0,n.jsx)(o.aVW,{className:"text-xl text-primary"}),f("title")]}),(0,n.jsx)(s.rr,{className:"text-left",children:p})]}),(0,n.jsxs)(s.Es,{className:"flex-col sm:flex-row gap-2",children:[(0,n.jsx)(l.$,{variant:"outline",onClick:()=>{d(),g()},className:"w-full sm:w-auto",children:v}),(0,n.jsx)(l.$,{onClick:()=>{u(),g()},className:"w-full sm:w-auto",children:x})]})]})})}}}]);