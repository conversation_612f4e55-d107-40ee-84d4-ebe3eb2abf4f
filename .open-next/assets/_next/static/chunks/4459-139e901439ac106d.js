(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[4459],{3932:(e,t,r)=>{"use strict";r.d(t,{default:()=>l});var n=r(12482),o=r(89799),i=r(95361),a=r(6002),c=r(68926),u=r(16261),l=(0,i.forwardRef)(function(e,t){let{href:r,locale:i,localeCookie:l,onClick:f,prefetch:s,...p}=e,y=(0,a.Ym)(),d=null!=i&&i!==y,b=(0,o.usePathname)();return d&&(s=!1),(0,u.jsx)(n,{ref:t,href:r,hrefLang:d?i:void 0,onClick:function(e){(0,c.A)(l,b,y,i),f&&f(e)},prefetch:s,...p})})},24224:(e,t,r)=>{"use strict";function n(e){return(n="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(e){return typeof e}:function(e){return e&&"function"==typeof Symbol&&e.constructor===Symbol&&e!==Symbol.prototype?"symbol":typeof e})(e)}Object.defineProperty(t,"__esModule",{value:!0}),t.CopyToClipboard=void 0;var o=c(r(95361)),i=c(r(94427)),a=["text","onCopy","options","children"];function c(e){return e&&e.__esModule?e:{default:e}}function u(e,t){var r=Object.keys(e);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(e);t&&(n=n.filter(function(t){return Object.getOwnPropertyDescriptor(e,t).enumerable})),r.push.apply(r,n)}return r}function l(e){for(var t=1;t<arguments.length;t++){var r=null!=arguments[t]?arguments[t]:{};t%2?u(Object(r),!0).forEach(function(t){y(e,t,r[t])}):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(r)):u(Object(r)).forEach(function(t){Object.defineProperty(e,t,Object.getOwnPropertyDescriptor(r,t))})}return e}function f(e,t){return(f=Object.setPrototypeOf||function(e,t){return e.__proto__=t,e})(e,t)}function s(e){if(void 0===e)throw ReferenceError("this hasn't been initialised - super() hasn't been called");return e}function p(e){return(p=Object.setPrototypeOf?Object.getPrototypeOf:function(e){return e.__proto__||Object.getPrototypeOf(e)})(e)}function y(e,t,r){return t in e?Object.defineProperty(e,t,{value:r,enumerable:!0,configurable:!0,writable:!0}):e[t]=r,e}var d=function(e){!function(e,t){if("function"!=typeof t&&null!==t)throw TypeError("Super expression must either be null or a function");e.prototype=Object.create(t&&t.prototype,{constructor:{value:e,writable:!0,configurable:!0}}),Object.defineProperty(e,"prototype",{writable:!1}),t&&f(e,t)}(u,e);var t,r,c=(t=function(){if("undefined"==typeof Reflect||!Reflect.construct||Reflect.construct.sham)return!1;if("function"==typeof Proxy)return!0;try{return Boolean.prototype.valueOf.call(Reflect.construct(Boolean,[],function(){})),!0}catch(e){return!1}}(),function(){var e,r=p(u);return e=t?Reflect.construct(r,arguments,p(this).constructor):r.apply(this,arguments),function(e,t){if(t&&("object"===n(t)||"function"==typeof t))return t;if(void 0!==t)throw TypeError("Derived constructors may only return object or undefined");return s(e)}(this,e)});function u(){var e;!function(e,t){if(!(e instanceof t))throw TypeError("Cannot call a class as a function")}(this,u);for(var t=arguments.length,r=Array(t),n=0;n<t;n++)r[n]=arguments[n];return y(s(e=c.call.apply(c,[this].concat(r))),"onClick",function(t){var r=e.props,n=r.text,a=r.onCopy,c=r.children,u=r.options,l=o.default.Children.only(c),f=(0,i.default)(n,u);a&&a(n,f),l&&l.props&&"function"==typeof l.props.onClick&&l.props.onClick(t)}),e}return r=[{key:"render",value:function(){var e=this.props,t=(e.text,e.onCopy,e.options,e.children),r=function(e,t){if(null==e)return{};var r,n,o=function(e,t){if(null==e)return{};var r,n,o={},i=Object.keys(e);for(n=0;n<i.length;n++)r=i[n],t.indexOf(r)>=0||(o[r]=e[r]);return o}(e,t);if(Object.getOwnPropertySymbols){var i=Object.getOwnPropertySymbols(e);for(n=0;n<i.length;n++)r=i[n],!(t.indexOf(r)>=0)&&Object.prototype.propertyIsEnumerable.call(e,r)&&(o[r]=e[r])}return o}(e,a),n=o.default.Children.only(t);return o.default.cloneElement(n,l(l({},r),{},{onClick:this.onClick}))}}],function(e,t){for(var r=0;r<t.length;r++){var n=t[r];n.enumerable=n.enumerable||!1,n.configurable=!0,"value"in n&&(n.writable=!0),Object.defineProperty(e,n.key,n)}}(u.prototype,r),Object.defineProperty(u,"prototype",{writable:!1}),u}(o.default.PureComponent);t.CopyToClipboard=d,y(d,"defaultProps",{onCopy:void 0,options:void 0})},29131:(e,t,r)=>{"use strict";var n=r(24224).CopyToClipboard;n.CopyToClipboard=n,e.exports=n},39921:e=>{e.exports=function(){var e=document.getSelection();if(!e.rangeCount)return function(){};for(var t=document.activeElement,r=[],n=0;n<e.rangeCount;n++)r.push(e.getRangeAt(n));switch(t.tagName.toUpperCase()){case"INPUT":case"TEXTAREA":t.blur();break;default:t=null}return e.removeAllRanges(),function(){"Caret"===e.type&&e.removeAllRanges(),e.rangeCount||r.forEach(function(t){e.addRange(t)}),t&&t.focus()}}},44756:(e,t,r)=>{"use strict";r.d(t,{k5:()=>f});var n=r(95361),o={color:void 0,size:void 0,className:void 0,style:void 0,attr:void 0},i=n.createContext&&n.createContext(o),a=["attr","size","title"];function c(){return(c=Object.assign?Object.assign.bind():function(e){for(var t=1;t<arguments.length;t++){var r=arguments[t];for(var n in r)Object.prototype.hasOwnProperty.call(r,n)&&(e[n]=r[n])}return e}).apply(this,arguments)}function u(e,t){var r=Object.keys(e);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(e);t&&(n=n.filter(function(t){return Object.getOwnPropertyDescriptor(e,t).enumerable})),r.push.apply(r,n)}return r}function l(e){for(var t=1;t<arguments.length;t++){var r=null!=arguments[t]?arguments[t]:{};t%2?u(Object(r),!0).forEach(function(t){var n,o,i;n=e,o=t,i=r[t],(o=function(e){var t=function(e,t){if("object"!=typeof e||!e)return e;var r=e[Symbol.toPrimitive];if(void 0!==r){var n=r.call(e,t||"default");if("object"!=typeof n)return n;throw TypeError("@@toPrimitive must return a primitive value.")}return("string"===t?String:Number)(e)}(e,"string");return"symbol"==typeof t?t:t+""}(o))in n?Object.defineProperty(n,o,{value:i,enumerable:!0,configurable:!0,writable:!0}):n[o]=i}):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(r)):u(Object(r)).forEach(function(t){Object.defineProperty(e,t,Object.getOwnPropertyDescriptor(r,t))})}return e}function f(e){return t=>n.createElement(s,c({attr:l({},e.attr)},t),function e(t){return t&&t.map((t,r)=>n.createElement(t.tag,l({key:r},t.attr),e(t.child)))}(e.child))}function s(e){var t=t=>{var r,{attr:o,size:i,title:u}=e,f=function(e,t){if(null==e)return{};var r,n,o=function(e,t){if(null==e)return{};var r={};for(var n in e)if(Object.prototype.hasOwnProperty.call(e,n)){if(t.indexOf(n)>=0)continue;r[n]=e[n]}return r}(e,t);if(Object.getOwnPropertySymbols){var i=Object.getOwnPropertySymbols(e);for(n=0;n<i.length;n++)r=i[n],!(t.indexOf(r)>=0)&&Object.prototype.propertyIsEnumerable.call(e,r)&&(o[r]=e[r])}return o}(e,a),s=i||t.size||"1em";return t.className&&(r=t.className),e.className&&(r=(r?r+" ":"")+e.className),n.createElement("svg",c({stroke:"currentColor",fill:"currentColor",strokeWidth:"0"},t.attr,o,f,{className:r,style:l(l({color:e.color||t.color},t.style),e.style),height:s,width:s,xmlns:"http://www.w3.org/2000/svg"}),u&&n.createElement("title",null,u),e.children)};return void 0!==i?n.createElement(i.Consumer,null,e=>t(e)):t(o)}},50349:(e,t,r)=>{"use strict";r.d(t,{FD:()=>h,MY:()=>i,PJ:()=>a,Wl:()=>u,XP:()=>s,_x:()=>o,bL:()=>p,po:()=>l,ql:()=>f,wO:()=>c,yL:()=>g});var n=r(55036);function o(e){return("object"==typeof e?null==e.host&&null==e.hostname:!/^[a-z]+:/i.test(e))&&!function(e){let t="object"==typeof e?e.pathname:e;return null!=t&&!t.startsWith("/")}(e)}function i(e,t){return e.replace(RegExp(`^${t}`),"")||"/"}function a(e,t){let r=e;return/^\/(\?.*)?$/.test(t)&&(t=t.slice(1)),r+=t}function c(e,t){return t===e||t.startsWith(`${e}/`)}function u(e,t,r){return"string"==typeof e?e:e[t]||r}function l(e){let t=function(){try{return"true"===n.env._next_intl_trailing_slash}catch{return!1}}(),[r,...o]=e.split("#"),i=o.join("#"),a=r;if("/"!==a){let e=a.endsWith("/");t&&!e?a+="/":!t&&e&&(a=a.slice(0,-1))}return i&&(a+="#"+i),a}function f(e,t){let r=l(e),n=l(t);return(function(e){let t=e.replace(/\[\[(\.\.\.[^\]]+)\]\]/g,"?(.*)").replace(/\[(\.\.\.[^\]]+)\]/g,"(.+)").replace(/\[([^\]]+)\]/g,"([^/]+)");return RegExp(`^${t}$`)})(r).test(n)}function s(e,t){return"never"!==t.mode&&t.prefixes?.[e]||p(e)}function p(e){return"/"+e}function y(e){return e.includes("[[...")}function d(e){return e.includes("[...")}function b(e){return e.includes("[")}function m(e,t){let r=e.split("/"),n=t.split("/"),o=Math.max(r.length,n.length);for(let e=0;e<o;e++){let t=r[e],o=n[e];if(!t&&o)return -1;if(t&&!o)return 1;if(t||o){if(!b(t)&&b(o))return -1;if(b(t)&&!b(o))return 1;if(!d(t)&&d(o))return -1;if(d(t)&&!d(o))return 1;if(!y(t)&&y(o))return -1;if(y(t)&&!y(o))return 1}}return 0}function h(e){return e.sort(m)}function g(e){return"function"==typeof e.then}},68926:(e,t,r)=>{"use strict";r.d(t,{A:()=>o});var n=r(88454);function o(e,t,r,o){if(!e||o===r||null==o||!t)return;let i=(0,n.DT)(t),{name:a,...c}=e;c.path||(c.path=""!==i?i:"/");let u=`${a}=${o};`;for(let[e,t]of Object.entries(c))u+=`${"maxAge"===e?"max-age":e}`,"boolean"!=typeof t&&(u+="="+t),u+=";";document.cookie=u}},88454:(e,t,r)=>{"use strict";r.d(t,{DT:()=>u,FP:()=>a,TK:()=>o,Zn:()=>i,aM:()=>c,x3:()=>l});var n=r(50349);function o(e){return"string"==typeof e?{pathname:e}:e}function i(e){let t=new URLSearchParams;for(let[r,n]of Object.entries(e))Array.isArray(n)?n.forEach(e=>{t.append(r,String(e))}):t.set(r,String(n));return"?"+t.toString()}function a({pathname:e,locale:t,params:r,pathnames:o,query:a}){function c(e){let c;let u=o[e];return u?(c=(0,n.Wl)(u,t,e),r&&Object.entries(r).forEach(([e,t])=>{let r,n;Array.isArray(t)?(r=`(\\[)?\\[...${e}\\](\\])?`,n=t.map(e=>String(e)).join("/")):(r=`\\[${e}\\]`,n=String(t)),c=c.replace(RegExp(r,"g"),n)}),c=(c=c.replace(/\[\[\.\.\..+\]\]/g,"")).split("/").map(e=>encodeURIComponent(e)).join("/")):c=e,c=(0,n.po)(c),a&&(c+=i(a)),c}if("string"==typeof e)return c(e);{let{pathname:t,...r}=e;return{...r,pathname:c(t)}}}function c(e,t,r){let o=(0,n.FD)(Object.keys(r)),i=decodeURI(t);for(let t of o){let o=r[t];if("string"==typeof o){if((0,n.ql)(o,i))return t}else if((0,n.ql)((0,n.Wl)(o,e,t),i))return t}return t}function u(e,t=window.location.pathname){return"/"===e?t:t.replace(e,"")}function l(e,t,r,o){let i;let{mode:a}=r.localePrefix;return void 0!==o?i=o:(0,n._x)(e)&&("always"===a?i=!0:"as-needed"===a&&(i=r.domains?!r.domains.some(e=>e.defaultLocale===t):t!==r.defaultLocale)),i?(0,n.PJ)((0,n.XP)(t,r.localePrefix),e):e}},89799:(e,t,r)=>{"use strict";var n=r(8911);r.o(n,"permanentRedirect")&&r.d(t,{permanentRedirect:function(){return n.permanentRedirect}}),r.o(n,"redirect")&&r.d(t,{redirect:function(){return n.redirect}}),r.o(n,"useParams")&&r.d(t,{useParams:function(){return n.useParams}}),r.o(n,"usePathname")&&r.d(t,{usePathname:function(){return n.usePathname}}),r.o(n,"useRouter")&&r.d(t,{useRouter:function(){return n.useRouter}})},94427:(e,t,r)=>{"use strict";var n=r(39921),o={"text/plain":"Text","text/html":"Url",default:"Text"};e.exports=function(e,t){var r,i,a,c,u,l,f,s,p=!1;t||(t={}),a=t.debug||!1;try{if(u=n(),l=document.createRange(),f=document.getSelection(),(s=document.createElement("span")).textContent=e,s.ariaHidden="true",s.style.all="unset",s.style.position="fixed",s.style.top=0,s.style.clip="rect(0, 0, 0, 0)",s.style.whiteSpace="pre",s.style.webkitUserSelect="text",s.style.MozUserSelect="text",s.style.msUserSelect="text",s.style.userSelect="text",s.addEventListener("copy",function(r){if(r.stopPropagation(),t.format){if(r.preventDefault(),void 0===r.clipboardData){a&&console.warn("unable to use e.clipboardData"),a&&console.warn("trying IE specific stuff"),window.clipboardData.clearData();var n=o[t.format]||o.default;window.clipboardData.setData(n,e)}else r.clipboardData.clearData(),r.clipboardData.setData(t.format,e)}t.onCopy&&(r.preventDefault(),t.onCopy(r.clipboardData))}),document.body.appendChild(s),l.selectNodeContents(s),f.addRange(l),!document.execCommand("copy"))throw Error("copy command was unsuccessful");p=!0}catch(n){a&&console.error("unable to copy using execCommand: ",n),a&&console.warn("trying IE specific stuff");try{window.clipboardData.setData(t.format||"text",e),t.onCopy&&t.onCopy(window.clipboardData),p=!0}catch(n){a&&console.error("unable to copy using clipboardData: ",n),a&&console.error("falling back to prompt"),r="message"in t?t.message:"Copy to clipboard: #{key}, Enter",i=(/mac os x/i.test(navigator.userAgent)?"⌘":"Ctrl")+"+C",c=r.replace(/#{\s*key\s*}/g,i),window.prompt(c,e)}}finally{f&&("function"==typeof f.removeRange?f.removeRange(l):f.removeAllRanges()),s&&document.body.removeChild(s),u()}return p}}}]);