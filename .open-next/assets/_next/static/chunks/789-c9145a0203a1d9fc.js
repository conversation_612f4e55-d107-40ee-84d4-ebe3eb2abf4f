"use strict";(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[789],{1316:(e,t,n)=>{n.d(t,{A:()=>a});var r=n(95361);let o=e=>e.replace(/([a-z0-9])([A-Z])/g,"$1-$2").toLowerCase(),u=function(){for(var e=arguments.length,t=Array(e),n=0;n<e;n++)t[n]=arguments[n];return t.filter((e,t,n)=>!!e&&n.indexOf(e)===t).join(" ")};var l={xmlns:"http://www.w3.org/2000/svg",width:24,height:24,viewBox:"0 0 24 24",fill:"none",stroke:"currentColor",strokeWidth:2,strokeLinecap:"round",strokeLinejoin:"round"};let i=(0,r.forwardRef)((e,t)=>{let{color:n="currentColor",size:o=24,strokeWidth:i=2,absoluteStrokeWidth:a,className:s="",children:c,iconNode:d,...f}=e;return(0,r.createElement)("svg",{ref:t,...l,width:o,height:o,stroke:n,strokeWidth:a?24*Number(i)/Number(o):i,className:u("lucide",s),...f},[...d.map(e=>{let[t,n]=e;return(0,r.createElement)(t,n)}),...Array.isArray(c)?c:[c]])}),a=(e,t)=>{let n=(0,r.forwardRef)((n,l)=>{let{className:a,...s}=n;return(0,r.createElement)(i,{ref:l,iconNode:t,className:u("lucide-".concat(o(e)),a),...s})});return n.displayName="".concat(e),n}},4471:(e,t,n)=>{n.d(t,{i:()=>i});var r,o=n(95361),u=n(53765),l=(r||(r=n.t(o,2)))[" useInsertionEffect ".trim().toString()]||u.N;function i({prop:e,defaultProp:t,onChange:n=()=>{},caller:r}){let[u,i,a]=function({defaultProp:e,onChange:t}){let[n,r]=o.useState(e),u=o.useRef(n),i=o.useRef(t);return l(()=>{i.current=t},[t]),o.useEffect(()=>{u.current!==n&&(i.current?.(n),u.current=n)},[n,u]),[n,r,i]}({defaultProp:t,onChange:n}),s=void 0!==e,c=s?e:u;{let t=o.useRef(void 0!==e);o.useEffect(()=>{let e=t.current;if(e!==s){let t=s?"controlled":"uncontrolled";console.warn(`${r} is changing from ${e?"controlled":"uncontrolled"} to ${t}. Components should not switch from controlled to uncontrolled (or vice versa). Decide between using a controlled or uncontrolled value for the lifetime of the component.`)}t.current=s},[s,r])}return[c,o.useCallback(t=>{if(s){let n="function"==typeof t?t(e):t;n!==e&&a.current?.(n)}else i(t)},[s,e,i,a])]}Symbol("RADIX:SYNC_STATE")},12177:(e,t,n)=>{n.d(t,{C:()=>l});var r=n(95361),o=n(94286),u=n(53765),l=e=>{let{present:t,children:n}=e,l=function(e){var t,n;let[o,l]=r.useState(),a=r.useRef(null),s=r.useRef(e),c=r.useRef("none"),[d,f]=(t=e?"mounted":"unmounted",n={mounted:{UNMOUNT:"unmounted",ANIMATION_OUT:"unmountSuspended"},unmountSuspended:{MOUNT:"mounted",ANIMATION_END:"unmounted"},unmounted:{MOUNT:"mounted"}},r.useReducer((e,t)=>{let r=n[e][t];return null!=r?r:e},t));return r.useEffect(()=>{let e=i(a.current);c.current="mounted"===d?e:"none"},[d]),(0,u.N)(()=>{let t=a.current,n=s.current;if(n!==e){let r=c.current,o=i(t);e?f("MOUNT"):"none"===o||(null==t?void 0:t.display)==="none"?f("UNMOUNT"):n&&r!==o?f("ANIMATION_OUT"):f("UNMOUNT"),s.current=e}},[e,f]),(0,u.N)(()=>{if(o){var e;let t;let n=null!==(e=o.ownerDocument.defaultView)&&void 0!==e?e:window,r=e=>{let r=i(a.current).includes(e.animationName);if(e.target===o&&r&&(f("ANIMATION_END"),!s.current)){let e=o.style.animationFillMode;o.style.animationFillMode="forwards",t=n.setTimeout(()=>{"forwards"===o.style.animationFillMode&&(o.style.animationFillMode=e)})}},u=e=>{e.target===o&&(c.current=i(a.current))};return o.addEventListener("animationstart",u),o.addEventListener("animationcancel",r),o.addEventListener("animationend",r),()=>{n.clearTimeout(t),o.removeEventListener("animationstart",u),o.removeEventListener("animationcancel",r),o.removeEventListener("animationend",r)}}f("ANIMATION_END")},[o,f]),{isPresent:["mounted","unmountSuspended"].includes(d),ref:r.useCallback(e=>{a.current=e?getComputedStyle(e):null,l(e)},[])}}(t),a="function"==typeof n?n({present:l.isPresent}):r.Children.only(n),s=(0,o.s)(l.ref,function(e){var t,n;let r=null===(t=Object.getOwnPropertyDescriptor(e.props,"ref"))||void 0===t?void 0:t.get,o=r&&"isReactWarning"in r&&r.isReactWarning;return o?e.ref:(o=(r=null===(n=Object.getOwnPropertyDescriptor(e,"ref"))||void 0===n?void 0:n.get)&&"isReactWarning"in r&&r.isReactWarning)?e.props.ref:e.props.ref||e.ref}(a));return"function"==typeof n||l.isPresent?r.cloneElement(a,{ref:s}):null};function i(e){return(null==e?void 0:e.animationName)||"none"}l.displayName="Presence"},20604:(e,t,n)=>{n.d(t,{F:()=>l});var r=n(87786);let o=e=>"boolean"==typeof e?`${e}`:0===e?"0":e,u=r.$,l=(e,t)=>n=>{var r;if((null==t?void 0:t.variants)==null)return u(e,null==n?void 0:n.class,null==n?void 0:n.className);let{variants:l,defaultVariants:i}=t,a=Object.keys(l).map(e=>{let t=null==n?void 0:n[e],r=null==i?void 0:i[e];if(null===t)return null;let u=o(t)||o(r);return l[e][u]}),s=n&&Object.entries(n).reduce((e,t)=>{let[n,r]=t;return void 0===r||(e[n]=r),e},{});return u(e,a,null==t?void 0:null===(r=t.compoundVariants)||void 0===r?void 0:r.reduce((e,t)=>{let{class:n,className:r,...o}=t;return Object.entries(o).every(e=>{let[t,n]=e;return Array.isArray(n)?n.includes({...i,...s}[t]):({...i,...s})[t]===n})?[...e,n,r]:e},[]),null==n?void 0:n.class,null==n?void 0:n.className)}},21751:(e,t,n)=>{n.d(t,{c:()=>o});var r=n(95361);function o(e){let t=r.useRef(e);return r.useEffect(()=>{t.current=e}),r.useMemo(()=>(...e)=>t.current?.(...e),[])}},22408:(e,t,n)=>{n.d(t,{A:()=>l,q:()=>u});var r=n(95361),o=n(16261);function u(e,t){let n=r.createContext(t),u=e=>{let{children:t,...u}=e,l=r.useMemo(()=>u,Object.values(u));return(0,o.jsx)(n.Provider,{value:l,children:t})};return u.displayName=e+"Provider",[u,function(o){let u=r.useContext(n);if(u)return u;if(void 0!==t)return t;throw Error(`\`${o}\` must be used within \`${e}\``)}]}function l(e,t=[]){let n=[],u=()=>{let t=n.map(e=>r.createContext(e));return function(n){let o=n?.[e]||t;return r.useMemo(()=>({[`__scope${e}`]:{...n,[e]:o}}),[n,o])}};return u.scopeName=e,[function(t,u){let l=r.createContext(u),i=n.length;n=[...n,u];let a=t=>{let{scope:n,children:u,...a}=t,s=n?.[e]?.[i]||l,c=r.useMemo(()=>a,Object.values(a));return(0,o.jsx)(s.Provider,{value:c,children:u})};return a.displayName=t+"Provider",[a,function(n,o){let a=o?.[e]?.[i]||l,s=r.useContext(a);if(s)return s;if(void 0!==u)return u;throw Error(`\`${n}\` must be used within \`${t}\``)}]},function(...e){let t=e[0];if(1===e.length)return t;let n=()=>{let n=e.map(e=>({useScope:e(),scopeName:e.scopeName}));return function(e){let o=n.reduce((t,{useScope:n,scopeName:r})=>{let o=n(e)[`__scope${r}`];return{...t,...o}},{});return r.useMemo(()=>({[`__scope${t.scopeName}`]:o}),[o])}};return n.scopeName=t.scopeName,n}(u,...t)]}},25747:(e,t,n)=>{n.d(t,{B:()=>a});var r,o=n(95361),u=n(53765),l=(r||(r=n.t(o,2)))[" useId ".trim().toString()]||(()=>void 0),i=0;function a(e){let[t,n]=o.useState(l());return(0,u.N)(()=>{e||n(e=>e??String(i++))},[e]),e||(t?`radix-${t}`:"")}},53765:(e,t,n)=>{n.d(t,{N:()=>o});var r=n(95361),o=globalThis?.document?r.useLayoutEffect:()=>{}},72637:(e,t,n)=>{n.d(t,{hO:()=>a,sG:()=>i});var r=n(95361),o=n(32196),u=n(76012),l=n(16261),i=["a","button","div","form","h2","h3","img","input","label","li","nav","ol","p","select","span","svg","ul"].reduce((e,t)=>{let n=(0,u.TL)(`Primitive.${t}`),o=r.forwardRef((e,r)=>{let{asChild:o,...u}=e;return"undefined"!=typeof window&&(window[Symbol.for("radix-ui")]=!0),(0,l.jsx)(o?n:t,{...u,ref:r})});return o.displayName=`Primitive.${t}`,{...e,[t]:o}},{});function a(e,t){e&&o.flushSync(()=>e.dispatchEvent(t))}},75030:(e,t,n)=>{n.d(t,{m:()=>r});function r(e,t,{checkForDefaultPrevented:n=!0}={}){return function(r){if(e?.(r),!1===n||!r.defaultPrevented)return t?.(r)}}}}]);