"use strict";(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[5325],{44756:(e,t,n)=>{n.d(t,{k5:()=>u});var r=n(95361),s={color:void 0,size:void 0,className:void 0,style:void 0,attr:void 0},o=r.createContext&&r.createContext(s),a=["attr","size","title"];function i(){return(i=Object.assign?Object.assign.bind():function(e){for(var t=1;t<arguments.length;t++){var n=arguments[t];for(var r in n)Object.prototype.hasOwnProperty.call(n,r)&&(e[r]=n[r])}return e}).apply(this,arguments)}function l(e,t){var n=Object.keys(e);if(Object.getOwnPropertySymbols){var r=Object.getOwnPropertySymbols(e);t&&(r=r.filter(function(t){return Object.getOwnPropertyDescriptor(e,t).enumerable})),n.push.apply(n,r)}return n}function c(e){for(var t=1;t<arguments.length;t++){var n=null!=arguments[t]?arguments[t]:{};t%2?l(Object(n),!0).forEach(function(t){var r,s,o;r=e,s=t,o=n[t],(s=function(e){var t=function(e,t){if("object"!=typeof e||!e)return e;var n=e[Symbol.toPrimitive];if(void 0!==n){var r=n.call(e,t||"default");if("object"!=typeof r)return r;throw TypeError("@@toPrimitive must return a primitive value.")}return("string"===t?String:Number)(e)}(e,"string");return"symbol"==typeof t?t:t+""}(s))in r?Object.defineProperty(r,s,{value:o,enumerable:!0,configurable:!0,writable:!0}):r[s]=o}):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(n)):l(Object(n)).forEach(function(t){Object.defineProperty(e,t,Object.getOwnPropertyDescriptor(n,t))})}return e}function u(e){return t=>r.createElement(d,i({attr:c({},e.attr)},t),function e(t){return t&&t.map((t,n)=>r.createElement(t.tag,c({key:n},t.attr),e(t.child)))}(e.child))}function d(e){var t=t=>{var n,{attr:s,size:o,title:l}=e,u=function(e,t){if(null==e)return{};var n,r,s=function(e,t){if(null==e)return{};var n={};for(var r in e)if(Object.prototype.hasOwnProperty.call(e,r)){if(t.indexOf(r)>=0)continue;n[r]=e[r]}return n}(e,t);if(Object.getOwnPropertySymbols){var o=Object.getOwnPropertySymbols(e);for(r=0;r<o.length;r++)n=o[r],!(t.indexOf(n)>=0)&&Object.prototype.propertyIsEnumerable.call(e,n)&&(s[n]=e[n])}return s}(e,a),d=o||t.size||"1em";return t.className&&(n=t.className),e.className&&(n=(n?n+" ":"")+e.className),r.createElement("svg",i({stroke:"currentColor",fill:"currentColor",strokeWidth:"0"},t.attr,s,u,{className:n,style:c(c({color:e.color||t.color},t.style),e.style),height:d,width:d,xmlns:"http://www.w3.org/2000/svg"}),l&&r.createElement("title",null,l),e.children)};return void 0!==o?r.createElement(o.Consumer,null,e=>t(e)):t(s)}},80372:(e,t,n)=>{n.d(t,{CP:()=>eu,Jv:()=>el,CI:()=>ec,wV:()=>es});var r,s,o,a,i,l=n(16261),c=n(95361),u=n.t(c,2);class d extends Error{constructor(e,t){e instanceof Error?super(void 0,{cause:{err:e,...e.cause,...t}}):"string"==typeof e?(t instanceof Error&&(t={err:t,...t.cause}),super(e,t)):super(void 0,e),this.name=this.constructor.name,this.type=this.constructor.type??"AuthError",this.kind=this.constructor.kind??"error",Error.captureStackTrace?.(this,this.constructor);let n=`https://errors.authjs.dev#${this.type.toLowerCase()}`;this.message+=`${this.message?". ":""}Read more at ${n}`}}class v extends d{}v.kind="signIn";class p extends d{}p.type="AdapterError";class f extends d{}f.type="AccessDenied";class h extends d{}h.type="CallbackRouteError";class y extends d{}y.type="ErrorPageLoop";class w extends d{}w.type="EventError";class g extends d{}g.type="InvalidCallbackUrl";class b extends v{constructor(){super(...arguments),this.code="credentials"}}b.type="CredentialsSignin";class m extends d{}m.type="InvalidEndpoints";class E extends d{}E.type="InvalidCheck";class S extends d{}S.type="JWTSessionError";class x extends d{}x.type="MissingAdapter";class O extends d{}O.type="MissingAdapterMethods";class _ extends d{}_.type="MissingAuthorize";class k extends d{}k.type="MissingSecret";class U extends v{}U.type="OAuthAccountNotLinked";class L extends v{}L.type="OAuthCallbackError";class P extends d{}P.type="OAuthProfileParseError";class R extends d{}R.type="SessionTokenError";class C extends v{}C.type="OAuthSignInError";class j extends v{}j.type="EmailSignInError";class A extends d{}A.type="SignOutError";class T extends d{}T.type="UnknownAction";class N extends d{}N.type="UnsupportedStrategy";class I extends d{}I.type="InvalidProvider";class M extends d{}M.type="UntrustedHost";class X extends d{}X.type="Verification";class D extends v{}D.type="MissingCSRF";class H extends d{}H.type="DuplicateConditionalUI";class W extends d{}W.type="MissingWebAuthnAutocomplete";class V extends d{}V.type="WebAuthnVerificationError";class z extends v{}z.type="AccountNotLinked";class $ extends d{}$.type="ExperimentalFeatureNotEnabled";class F extends d{}class J extends d{}async function B(e,t,n){let r=arguments.length>3&&void 0!==arguments[3]?arguments[3]:{},s="".concat(q(t),"/").concat(e);try{var o;let e={headers:{"Content-Type":"application/json",...(null==r?void 0:null===(o=r.headers)||void 0===o?void 0:o.cookie)?{cookie:r.headers.cookie}:{}}};(null==r?void 0:r.body)&&(e.body=JSON.stringify(r.body),e.method="POST");let t=await fetch(s,e),n=await t.json();if(!t.ok)throw n;return n}catch(e){return n.error(new F(e.message,e)),null}}function q(e){return"undefined"==typeof window?"".concat(e.baseUrlServer).concat(e.basePathServer):e.basePath}function G(){return Math.floor(Date.now()/1e3)}function K(e){let t=new URL("http://localhost:3000/api/auth");e&&!e.startsWith("http")&&(e="https://".concat(e));let n=new URL(e||t),r=("/"===n.pathname?t.pathname:n.pathname).replace(/\/$/,""),s="".concat(n.origin).concat(r);return{origin:n.origin,host:n.host,path:r,base:s,toString:()=>s}}var Q=n(55036);let Y={baseUrl:K(null!==(s=Q.env.NEXTAUTH_URL)&&void 0!==s?s:Q.env.VERCEL_URL).origin,basePath:K(Q.env.NEXTAUTH_URL).path,baseUrlServer:K(null!==(a=null!==(o=Q.env.NEXTAUTH_URL_INTERNAL)&&void 0!==o?o:Q.env.NEXTAUTH_URL)&&void 0!==a?a:Q.env.VERCEL_URL).origin,basePathServer:K(null!==(i=Q.env.NEXTAUTH_URL_INTERNAL)&&void 0!==i?i:Q.env.NEXTAUTH_URL).path,_lastSync:0,_session:void 0,_getSession:()=>{}},Z=null;function ee(){return new BroadcastChannel("next-auth")}function et(){return"undefined"==typeof BroadcastChannel?{postMessage:()=>{},addEventListener:()=>{},removeEventListener:()=>{}}:(null===Z&&(Z=ee()),Z)}let en={debug:console.debug,error:console.error,warn:console.warn},er=null===(r=c.createContext)||void 0===r?void 0:r.call(u,void 0);function es(e){if(!er)throw Error("React Context is unavailable in Server Components");let t=c.useContext(er),{required:n,onUnauthenticated:r}=null!=e?e:{},s=n&&"unauthenticated"===t.status;return(c.useEffect(()=>{if(s){let e="".concat(Y.basePath,"/signin?").concat(new URLSearchParams({error:"SessionRequired",callbackUrl:window.location.href}));r?r():window.location.href=e}},[s,r]),s)?{data:t.data,update:t.update,status:"loading"}:t}async function eo(e){var t;let n=await B("session",Y,en,e);return(null===(t=null==e?void 0:e.broadcast)||void 0===t||t)&&ee().postMessage({event:"session",data:{trigger:"getSession"}}),n}async function ea(){var e;let t=await B("csrf",Y,en);return null!==(e=null==t?void 0:t.csrfToken)&&void 0!==e?e:""}async function ei(){return B("providers",Y,en)}async function el(e,t,n){var r,s,o;let{redirect:a=!0}=null!=t?t:{},i=null!==(s=null!==(r=null==t?void 0:t.redirectTo)&&void 0!==r?r:null==t?void 0:t.callbackUrl)&&void 0!==s?s:window.location.href,l=q(Y),c=await ei();if(!c){window.location.href="".concat(l,"/error");return}if(!e||!(e in c)){window.location.href="".concat(l,"/signin?").concat(new URLSearchParams({callbackUrl:i}));return}let u="credentials"===c[e].type,d="email"===c[e].type,v="".concat(l,"/").concat(u?"callback":"signin","/").concat(e),p=await ea(),f=await fetch("".concat(v,"?").concat(new URLSearchParams(n)),{method:"post",headers:{"Content-Type":"application/x-www-form-urlencoded","X-Auth-Return-Redirect":"1"},body:new URLSearchParams({...t,csrfToken:p,callbackUrl:i})}),h=await f.json();if(a||!(u||d)){let e=null!==(o=h.url)&&void 0!==o?o:i;window.location.href=e,e.includes("#")&&window.location.reload();return}let y=new URL(h.url).searchParams.get("error"),w=new URL(h.url).searchParams.get("code");return f.ok&&await Y._getSession({event:"storage"}),{error:y,code:w,status:f.status,ok:f.ok,url:y?null:h.url}}async function ec(e){var t,n,r,s;let o=null!==(n=null!==(t=null==e?void 0:e.redirectTo)&&void 0!==t?t:null==e?void 0:e.callbackUrl)&&void 0!==n?n:window.location.href,a=q(Y),i=await ea(),l=await fetch("".concat(a,"/signout"),{method:"post",headers:{"Content-Type":"application/x-www-form-urlencoded","X-Auth-Return-Redirect":"1"},body:new URLSearchParams({csrfToken:i,callbackUrl:o})}),c=await l.json();if(et().postMessage({event:"session",data:{trigger:"signout"}}),null===(r=null==e?void 0:e.redirect)||void 0===r||r){let e=null!==(s=c.url)&&void 0!==s?s:o;window.location.href=e,e.includes("#")&&window.location.reload();return}return await Y._getSession({event:"storage"}),c}function eu(e){if(!er)throw Error("React Context is unavailable in Server Components");let{children:t,basePath:n,refetchInterval:r,refetchWhenOffline:s}=e;n&&(Y.basePath=n);let o=void 0!==e.session;Y._lastSync=o?G():0;let[a,i]=c.useState(()=>(o&&(Y._session=e.session),e.session)),[u,d]=c.useState(!o);c.useEffect(()=>(Y._getSession=async function(){let{event:e}=arguments.length>0&&void 0!==arguments[0]?arguments[0]:{};try{let t="storage"===e;if(t||void 0===Y._session){Y._lastSync=G(),Y._session=await eo({broadcast:!t}),i(Y._session);return}if(!e||null===Y._session||G()<Y._lastSync)return;Y._lastSync=G(),Y._session=await eo(),i(Y._session)}catch(e){en.error(new J(e.message,e))}finally{d(!1)}},Y._getSession(),()=>{Y._lastSync=0,Y._session=void 0,Y._getSession=()=>{}}),[]),c.useEffect(()=>{let e=()=>Y._getSession({event:"storage"});return et().addEventListener("message",e),()=>et().removeEventListener("message",e)},[]),c.useEffect(()=>{let{refetchOnWindowFocus:t=!0}=e,n=()=>{t&&"visible"===document.visibilityState&&Y._getSession({event:"visibilitychange"})};return document.addEventListener("visibilitychange",n,!1),()=>document.removeEventListener("visibilitychange",n,!1)},[e.refetchOnWindowFocus]);let v=function(){let[e,t]=c.useState("undefined"!=typeof navigator&&navigator.onLine),n=()=>t(!0),r=()=>t(!1);return c.useEffect(()=>(window.addEventListener("online",n),window.addEventListener("offline",r),()=>{window.removeEventListener("online",n),window.removeEventListener("offline",r)}),[]),e}(),p=!1!==s||v;c.useEffect(()=>{if(r&&p){let e=setInterval(()=>{Y._session&&Y._getSession({event:"poll"})},1e3*r);return()=>clearInterval(e)}},[r,p]);let f=c.useMemo(()=>({data:a,status:u?"loading":a?"authenticated":"unauthenticated",async update(e){if(u)return;d(!0);let t=await B("session",Y,en,void 0===e?void 0:{body:{csrfToken:await ea(),data:e}});return d(!1),t&&(i(t),et().postMessage({event:"session",data:{trigger:"getSession"}})),t}}),[a,u]);return(0,l.jsx)(er.Provider,{value:f,children:t})}}}]);