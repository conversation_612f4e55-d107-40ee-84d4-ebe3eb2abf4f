(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[5804],{3932:(e,t,n)=>{"use strict";n.d(t,{default:()=>s});var r=n(12482),a=n(89799),o=n(95361),i=n(6002),l=n(68926),u=n(16261),s=(0,o.forwardRef)(function(e,t){let{href:n,locale:o,localeCookie:s,onClick:c,prefetch:d,...f}=e,p=(0,i.Ym)(),m=null!=o&&o!==p,g=(0,a.usePathname)();return m&&(d=!1),(0,u.jsx)(r,{ref:t,href:n,hrefLang:m?o:void 0,onClick:function(e){(0,l.A)(s,g,p,o),c&&c(e)},prefetch:d,...f})})},4753:e=>{"use strict";e.exports=JSON.parse('{"id":"google-analytics","description":"Install a Google Analytics tag on your website","website":"https://analytics.google.com/analytics/web/","scripts":[{"url":"https://www.googletagmanager.com/gtag/js","params":["id"],"strategy":"worker","location":"head","action":"append"},{"code":"window.dataLayer=window.dataLayer||[];window.gtag=function gtag(){window.dataLayer.push(arguments);};gtag(\'js\',new Date());gtag(\'config\',\'${args.id}\')","strategy":"worker","location":"head","action":"append"}]}')},5657:e=>{e.exports=function({client_id:e,auto_select:t=!1,cancel_on_tap_outside:n=!1,context:r="signin",...a},o){if(!e)throw Error("client_id is required");if("undefined"!=typeof window&&window.document){let i=["signin","signup","use"].includes(r)?r:"signin",l=document.createElement("script");l.src="https://accounts.google.com/gsi/client",l.async=!0,l.defer=!0,document.head.appendChild(l),window.onload=function(){window.google.accounts.id.initialize({client_id:e,callback:o,auto_select:t,cancel_on_tap_outside:n,context:i,...a}),window.google.accounts.id.prompt()}}}},8624:(e,t,n)=>{"use strict";n.d(t,{c3:()=>o});var r=n(6002);function a(e,t){return(...e)=>{try{return t(...e)}catch{throw Error(void 0)}}}let o=a(0,r.c3);a(0,r.kc)},15755:(e,t,n)=>{"use strict";n.d(t,{A:()=>r});let r=(0,n(1316).A)("LoaderCircle",[["path",{d:"M21 12a9 9 0 1 1-6.219-8.56",key:"13zald"}]])},18319:(e,t,n)=>{"use strict";let r;n.d(t,{_s:()=>L});var a=n(21494),o=n(95361);let i=o.createContext({drawerRef:{current:null},overlayRef:{current:null},onPress:()=>{},onRelease:()=>{},onDrag:()=>{},onNestedDrag:()=>{},onNestedOpenChange:()=>{},onNestedRelease:()=>{},openProp:void 0,dismissible:!1,isOpen:!1,isDragging:!1,keyboardIsOpen:{current:!1},snapPointsOffset:null,snapPoints:null,handleOnly:!1,modal:!1,shouldFade:!1,activeSnapPoint:null,onOpenChange:()=>{},setActiveSnapPoint:()=>{},closeDrawer:()=>{},direction:"bottom",shouldAnimate:{current:!0},shouldScaleBackground:!1,setBackgroundColorOnScale:!0,noBodyStyles:!1,container:null,autoFocus:!1}),l=()=>{let e=o.useContext(i);if(!e)throw Error("useDrawerContext must be used within a Drawer.Root");return e};function u(){return/^((?!chrome|android).)*safari/i.test(navigator.userAgent)}function s(){return c(/^iPhone/)||c(/^iPad/)||c(/^Mac/)&&navigator.maxTouchPoints>1}function c(e){return"undefined"!=typeof window&&null!=window.navigator?e.test(window.navigator.platform):void 0}!function(e){if(!e||"undefined"==typeof document)return;let t=document.head||document.getElementsByTagName("head")[0],n=document.createElement("style");n.type="text/css",t.appendChild(n),n.styleSheet?n.styleSheet.cssText=e:n.appendChild(document.createTextNode(e))}("[data-vaul-drawer]{touch-action:none;will-change:transform;transition:transform .5s cubic-bezier(.32, .72, 0, 1);animation-duration:.5s;animation-timing-function:cubic-bezier(0.32,0.72,0,1)}[data-vaul-drawer][data-vaul-snap-points=false][data-vaul-drawer-direction=bottom][data-state=open]{animation-name:slideFromBottom}[data-vaul-drawer][data-vaul-snap-points=false][data-vaul-drawer-direction=bottom][data-state=closed]{animation-name:slideToBottom}[data-vaul-drawer][data-vaul-snap-points=false][data-vaul-drawer-direction=top][data-state=open]{animation-name:slideFromTop}[data-vaul-drawer][data-vaul-snap-points=false][data-vaul-drawer-direction=top][data-state=closed]{animation-name:slideToTop}[data-vaul-drawer][data-vaul-snap-points=false][data-vaul-drawer-direction=left][data-state=open]{animation-name:slideFromLeft}[data-vaul-drawer][data-vaul-snap-points=false][data-vaul-drawer-direction=left][data-state=closed]{animation-name:slideToLeft}[data-vaul-drawer][data-vaul-snap-points=false][data-vaul-drawer-direction=right][data-state=open]{animation-name:slideFromRight}[data-vaul-drawer][data-vaul-snap-points=false][data-vaul-drawer-direction=right][data-state=closed]{animation-name:slideToRight}[data-vaul-drawer][data-vaul-snap-points=true][data-vaul-drawer-direction=bottom]{transform:translate3d(0,var(--initial-transform,100%),0)}[data-vaul-drawer][data-vaul-snap-points=true][data-vaul-drawer-direction=top]{transform:translate3d(0,calc(var(--initial-transform,100%) * -1),0)}[data-vaul-drawer][data-vaul-snap-points=true][data-vaul-drawer-direction=left]{transform:translate3d(calc(var(--initial-transform,100%) * -1),0,0)}[data-vaul-drawer][data-vaul-snap-points=true][data-vaul-drawer-direction=right]{transform:translate3d(var(--initial-transform,100%),0,0)}[data-vaul-drawer][data-vaul-delayed-snap-points=true][data-vaul-drawer-direction=top]{transform:translate3d(0,var(--snap-point-height,0),0)}[data-vaul-drawer][data-vaul-delayed-snap-points=true][data-vaul-drawer-direction=bottom]{transform:translate3d(0,var(--snap-point-height,0),0)}[data-vaul-drawer][data-vaul-delayed-snap-points=true][data-vaul-drawer-direction=left]{transform:translate3d(var(--snap-point-height,0),0,0)}[data-vaul-drawer][data-vaul-delayed-snap-points=true][data-vaul-drawer-direction=right]{transform:translate3d(var(--snap-point-height,0),0,0)}[data-vaul-overlay][data-vaul-snap-points=false]{animation-duration:.5s;animation-timing-function:cubic-bezier(0.32,0.72,0,1)}[data-vaul-overlay][data-vaul-snap-points=false][data-state=open]{animation-name:fadeIn}[data-vaul-overlay][data-state=closed]{animation-name:fadeOut}[data-vaul-animate=false]{animation:none!important}[data-vaul-overlay][data-vaul-snap-points=true]{opacity:0;transition:opacity .5s cubic-bezier(.32, .72, 0, 1)}[data-vaul-overlay][data-vaul-snap-points=true]{opacity:1}[data-vaul-drawer]:not([data-vaul-custom-container=true])::after{content:'';position:absolute;background:inherit;background-color:inherit}[data-vaul-drawer][data-vaul-drawer-direction=top]::after{top:initial;bottom:100%;left:0;right:0;height:200%}[data-vaul-drawer][data-vaul-drawer-direction=bottom]::after{top:100%;bottom:initial;left:0;right:0;height:200%}[data-vaul-drawer][data-vaul-drawer-direction=left]::after{left:initial;right:100%;top:0;bottom:0;width:200%}[data-vaul-drawer][data-vaul-drawer-direction=right]::after{left:100%;right:initial;top:0;bottom:0;width:200%}[data-vaul-overlay][data-vaul-snap-points=true]:not([data-vaul-snap-points-overlay=true]):not(\n[data-state=closed]\n){opacity:0}[data-vaul-overlay][data-vaul-snap-points-overlay=true]{opacity:1}[data-vaul-handle]{display:block;position:relative;opacity:.7;background:#e2e2e4;margin-left:auto;margin-right:auto;height:5px;width:32px;border-radius:1rem;touch-action:pan-y}[data-vaul-handle]:active,[data-vaul-handle]:hover{opacity:1}[data-vaul-handle-hitarea]{position:absolute;left:50%;top:50%;transform:translate(-50%,-50%);width:max(100%,2.75rem);height:max(100%,2.75rem);touch-action:inherit}@media (hover:hover) and (pointer:fine){[data-vaul-drawer]{user-select:none}}@media (pointer:fine){[data-vaul-handle-hitarea]:{width:100%;height:100%}}@keyframes fadeIn{from{opacity:0}to{opacity:1}}@keyframes fadeOut{to{opacity:0}}@keyframes slideFromBottom{from{transform:translate3d(0,var(--initial-transform,100%),0)}to{transform:translate3d(0,0,0)}}@keyframes slideToBottom{to{transform:translate3d(0,var(--initial-transform,100%),0)}}@keyframes slideFromTop{from{transform:translate3d(0,calc(var(--initial-transform,100%) * -1),0)}to{transform:translate3d(0,0,0)}}@keyframes slideToTop{to{transform:translate3d(0,calc(var(--initial-transform,100%) * -1),0)}}@keyframes slideFromLeft{from{transform:translate3d(calc(var(--initial-transform,100%) * -1),0,0)}to{transform:translate3d(0,0,0)}}@keyframes slideToLeft{to{transform:translate3d(calc(var(--initial-transform,100%) * -1),0,0)}}@keyframes slideFromRight{from{transform:translate3d(var(--initial-transform,100%),0,0)}to{transform:translate3d(0,0,0)}}@keyframes slideToRight{to{transform:translate3d(var(--initial-transform,100%),0,0)}}");let d="undefined"!=typeof window?o.useLayoutEffect:o.useEffect;function f(){for(var e=arguments.length,t=Array(e),n=0;n<e;n++)t[n]=arguments[n];return function(){for(var e=arguments.length,n=Array(e),r=0;r<e;r++)n[r]=arguments[r];for(let e of t)"function"==typeof e&&e(...n)}}let p="undefined"!=typeof document&&window.visualViewport;function m(e){let t=window.getComputedStyle(e);return/(auto|scroll)/.test(t.overflow+t.overflowX+t.overflowY)}function g(e){for(m(e)&&(e=e.parentElement);e&&!m(e);)e=e.parentElement;return e||document.scrollingElement||document.documentElement}let h=new Set(["checkbox","radio","range","color","file","image","button","submit","reset"]),v=0;function w(e,t,n,r){return e.addEventListener(t,n,r),()=>{e.removeEventListener(t,n,r)}}function y(e){let t=document.scrollingElement||document.documentElement;for(;e&&e!==t;){let t=g(e);if(t!==document.documentElement&&t!==document.body&&t!==e){let n=t.getBoundingClientRect().top,r=e.getBoundingClientRect().top;e.getBoundingClientRect().bottom>t.getBoundingClientRect().bottom+24&&(t.scrollTop+=r-n)}e=t.parentElement}}function b(e){return e instanceof HTMLInputElement&&!h.has(e.type)||e instanceof HTMLTextAreaElement||e instanceof HTMLElement&&e.isContentEditable}function x(){for(var e=arguments.length,t=Array(e),n=0;n<e;n++)t[n]=arguments[n];return o.useCallback(function(){for(var e=arguments.length,t=Array(e),n=0;n<e;n++)t[n]=arguments[n];return e=>t.forEach(t=>{"function"==typeof t?t(e):null!=t&&(t.current=e)})}(...t),t)}let E=new WeakMap;function O(e,t){let n=arguments.length>2&&void 0!==arguments[2]&&arguments[2];if(!e||!(e instanceof HTMLElement))return;let r={};Object.entries(t).forEach(t=>{let[n,a]=t;if(n.startsWith("--")){e.style.setProperty(n,a);return}r[n]=e.style[n],e.style[n]=a}),n||E.set(e,r)}let _=e=>{switch(e){case"top":case"bottom":return!0;case"left":case"right":return!1;default:return e}};function j(e,t){if(!e)return null;let n=window.getComputedStyle(e),r=n.transform||n.webkitTransform||n.mozTransform,a=r.match(/^matrix3d\((.+)\)$/);return a?parseFloat(a[1].split(", ")[_(t)?13:12]):(a=r.match(/^matrix\((.+)\)$/))?parseFloat(a[1].split(", ")[_(t)?5:4]):null}function R(e,t){if(!e)return()=>{};let n=e.style.cssText;return Object.assign(e.style,t),()=>{e.style.cssText=n}}let T={DURATION:.5,EASE:[.32,.72,0,1]},M="vaul-dragging";function P(e){let t=o.useRef(e);return o.useEffect(()=>{t.current=e}),o.useMemo(()=>function(){for(var e=arguments.length,n=Array(e),r=0;r<e;r++)n[r]=arguments[r];return null==t.current?void 0:t.current.call(t,...n)},[])}function D(e){let{prop:t,defaultProp:n,onChange:r=()=>{}}=e,[a,i]=function(e){let{defaultProp:t,onChange:n}=e,r=o.useState(t),[a]=r,i=o.useRef(a),l=P(n);return o.useEffect(()=>{i.current!==a&&(l(a),i.current=a)},[a,i,l]),r}({defaultProp:n,onChange:r}),l=void 0!==t,u=l?t:a,s=P(r);return[u,o.useCallback(e=>{if(l){let n="function"==typeof e?e(t):e;n!==t&&s(n)}else i(e)},[l,t,i,s])]}let A=()=>()=>{},C=null;function k(e){var t,n;let{open:l,onOpenChange:c,children:m,onDrag:h,onRelease:x,snapPoints:R,shouldScaleBackground:P=!1,setBackgroundColorOnScale:A=!0,closeThreshold:k=.25,scrollLockTimeout:S=100,dismissible:I=!0,handleOnly:N=!1,fadeFromIndex:L=R&&R.length-1,activeSnapPoint:F,setActiveSnapPoint:G,fixed:H,modal:q=!0,onClose:U,nested:$,noBodyStyles:B=!1,direction:W="bottom",defaultOpen:z=!1,disablePreventScroll:Y=!0,snapToSequentialPoint:J=!1,preventScrollRestoration:X=!1,repositionInputs:V=!0,onAnimationEnd:Z,container:K,autoFocus:Q=!1}=e,[ee=!1,et]=D({defaultProp:z,prop:l,onChange:e=>{null==c||c(e),e||$||eC(),setTimeout(()=>{null==Z||Z(e)},1e3*T.DURATION),e&&!q&&"undefined"!=typeof window&&window.requestAnimationFrame(()=>{document.body.style.pointerEvents="auto"}),e||(document.body.style.pointerEvents="auto")}}),[en,er]=o.useState(!1),[ea,eo]=o.useState(!1),[ei,el]=o.useState(!1),eu=o.useRef(null),es=o.useRef(null),ec=o.useRef(null),ed=o.useRef(null),ef=o.useRef(null),ep=o.useRef(!1),em=o.useRef(null),eg=o.useRef(0),eh=o.useRef(!1),ev=o.useRef(!z),ew=o.useRef(0),ey=o.useRef(null),eb=o.useRef((null==(t=ey.current)?void 0:t.getBoundingClientRect().height)||0),ex=o.useRef((null==(n=ey.current)?void 0:n.getBoundingClientRect().width)||0),eE=o.useRef(0),eO=o.useCallback(e=>{R&&e===eM.length-1&&(es.current=new Date)},[]),{activeSnapPoint:e_,activeSnapPointIndex:ej,setActiveSnapPoint:eR,onRelease:eT,snapPointsOffset:eM,onDrag:eP,shouldFade:eD,getPercentageDragged:eA}=function(e){let{activeSnapPointProp:t,setActiveSnapPointProp:n,snapPoints:r,drawerRef:a,overlayRef:i,fadeFromIndex:l,onSnapPointChange:u,direction:s="bottom",container:c,snapToSequentialPoint:d}=e,[f,p]=D({prop:t,defaultProp:null==r?void 0:r[0],onChange:n}),[m,g]=o.useState("undefined"!=typeof window?{innerWidth:window.innerWidth,innerHeight:window.innerHeight}:void 0);o.useEffect(()=>{function e(){g({innerWidth:window.innerWidth,innerHeight:window.innerHeight})}return window.addEventListener("resize",e),()=>window.removeEventListener("resize",e)},[]);let h=o.useMemo(()=>f===(null==r?void 0:r[r.length-1])||null,[r,f]),v=o.useMemo(()=>{var e;return null!=(e=null==r?void 0:r.findIndex(e=>e===f))?e:null},[r,f]),w=r&&r.length>0&&(l||0===l)&&!Number.isNaN(l)&&r[l]===f||!r,y=o.useMemo(()=>{var e;let t=c?{width:c.getBoundingClientRect().width,height:c.getBoundingClientRect().height}:"undefined"!=typeof window?{width:window.innerWidth,height:window.innerHeight}:{width:0,height:0};return null!=(e=null==r?void 0:r.map(e=>{let n="string"==typeof e,r=0;if(n&&(r=parseInt(e,10)),_(s)){let a=n?r:m?e*t.height:0;return m?"bottom"===s?t.height-a:-t.height+a:a}let a=n?r:m?e*t.width:0;return m?"right"===s?t.width-a:-t.width+a:a}))?e:[]},[r,m,c]),b=o.useMemo(()=>null!==v?null==y?void 0:y[v]:null,[y,v]),x=o.useCallback(e=>{var t;let n=null!=(t=null==y?void 0:y.findIndex(t=>t===e))?t:null;u(n),O(a.current,{transition:"transform ".concat(T.DURATION,"s cubic-bezier(").concat(T.EASE.join(","),")"),transform:_(s)?"translate3d(0, ".concat(e,"px, 0)"):"translate3d(".concat(e,"px, 0, 0)")}),y&&n!==y.length-1&&void 0!==l&&n!==l&&n<l?O(i.current,{transition:"opacity ".concat(T.DURATION,"s cubic-bezier(").concat(T.EASE.join(","),")"),opacity:"0"}):O(i.current,{transition:"opacity ".concat(T.DURATION,"s cubic-bezier(").concat(T.EASE.join(","),")"),opacity:"1"}),p(null==r?void 0:r[Math.max(n,0)])},[a.current,r,y,l,i,p]);return o.useEffect(()=>{if(f||t){var e;let n=null!=(e=null==r?void 0:r.findIndex(e=>e===t||e===f))?e:-1;y&&-1!==n&&"number"==typeof y[n]&&x(y[n])}},[f,t,r,y,x]),{isLastSnapPoint:h,activeSnapPoint:f,shouldFade:w,getPercentageDragged:function(e,t){if(!r||"number"!=typeof v||!y||void 0===l)return null;let n=v===l-1;if(v>=l&&t)return 0;if(n&&!t)return 1;if(!w&&!n)return null;let a=n?v+1:v-1,o=e/Math.abs(n?y[a]-y[a-1]:y[a+1]-y[a]);return n?1-o:o},setActiveSnapPoint:p,activeSnapPointIndex:v,onRelease:function(e){let{draggedDistance:t,closeDrawer:n,velocity:a,dismissible:o}=e;if(void 0===l)return;let u="bottom"===s||"right"===s?(null!=b?b:0)-t:(null!=b?b:0)+t,c=v===l-1,f=0===v,p=t>0;if(c&&O(i.current,{transition:"opacity ".concat(T.DURATION,"s cubic-bezier(").concat(T.EASE.join(","),")")}),!d&&a>2&&!p){o?n():x(y[0]);return}if(!d&&a>2&&p&&y&&r){x(y[r.length-1]);return}let m=null==y?void 0:y.reduce((e,t)=>"number"!=typeof e||"number"!=typeof t?e:Math.abs(t-u)<Math.abs(e-u)?t:e),g=_(s)?window.innerHeight:window.innerWidth;if(a>.4&&Math.abs(t)<.4*g){let e=p?1:-1;if(e>0&&h&&r){x(y[r.length-1]);return}if(f&&e<0&&o&&n(),null===v)return;x(y[v+e]);return}x(m)},onDrag:function(e){let{draggedDistance:t}=e;if(null===b)return;let n="bottom"===s||"right"===s?b-t:b+t;("bottom"!==s&&"right"!==s||!(n<y[y.length-1]))&&("top"!==s&&"left"!==s||!(n>y[y.length-1]))&&O(a.current,{transform:_(s)?"translate3d(0, ".concat(n,"px, 0)"):"translate3d(".concat(n,"px, 0, 0)")})},snapPointsOffset:y}}({snapPoints:R,activeSnapPointProp:F,setActiveSnapPointProp:G,drawerRef:ey,fadeFromIndex:L,overlayRef:eu,onSnapPointChange:eO,direction:W,container:K,snapToSequentialPoint:J});!function(){let e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:{},{isDisabled:t}=e;d(()=>{if(!t){var e,n,a;let t,o,i,l,u,c,d;return 1==++v&&s()&&(i=0,l=window.pageXOffset,u=window.pageYOffset,c=f((e=document.documentElement,n="paddingRight",a="".concat(window.innerWidth-document.documentElement.clientWidth,"px"),o=e.style[n],e.style[n]=a,()=>{e.style[n]=o})),window.scrollTo(0,0),d=f(w(document,"touchstart",e=>{((t=g(e.target))!==document.documentElement||t!==document.body)&&(i=e.changedTouches[0].pageY)},{passive:!1,capture:!0}),w(document,"touchmove",e=>{if(!t||t===document.documentElement||t===document.body){e.preventDefault();return}let n=e.changedTouches[0].pageY,r=t.scrollTop,a=t.scrollHeight-t.clientHeight;0!==a&&((r<=0&&n>i||r>=a&&n<i)&&e.preventDefault(),i=n)},{passive:!1,capture:!0}),w(document,"touchend",e=>{let t=e.target;b(t)&&t!==document.activeElement&&(e.preventDefault(),t.style.transform="translateY(-2000px)",t.focus(),requestAnimationFrame(()=>{t.style.transform=""}))},{passive:!1,capture:!0}),w(document,"focus",e=>{let t=e.target;b(t)&&(t.style.transform="translateY(-2000px)",requestAnimationFrame(()=>{t.style.transform="",p&&(p.height<window.innerHeight?requestAnimationFrame(()=>{y(t)}):p.addEventListener("resize",()=>y(t),{once:!0}))}))},!0),w(window,"scroll",()=>{window.scrollTo(0,0)})),r=()=>{c(),d(),window.scrollTo(l,u)}),()=>{0==--v&&(null==r||r())}}},[t])}({isDisabled:!ee||ea||!q||ei||!en||!V||!Y});let{restorePositionSetting:eC}=function(e){let{isOpen:t,modal:n,nested:r,hasBeenOpened:a,preventScrollRestoration:i,noBodyStyles:l}=e,[s,c]=o.useState(()=>"undefined"!=typeof window?window.location.href:""),d=o.useRef(0),f=o.useCallback(()=>{if(u()&&null===C&&t&&!l){C={position:document.body.style.position,top:document.body.style.top,left:document.body.style.left,height:document.body.style.height,right:"unset"};let{scrollX:e,innerHeight:t}=window;document.body.style.setProperty("position","fixed","important"),Object.assign(document.body.style,{top:"".concat(-d.current,"px"),left:"".concat(-e,"px"),right:"0px",height:"auto"}),window.setTimeout(()=>window.requestAnimationFrame(()=>{let e=t-window.innerHeight;e&&d.current>=t&&(document.body.style.top="".concat(-(d.current+e),"px"))}),300)}},[t]),p=o.useCallback(()=>{if(u()&&null!==C&&!l){let e=-parseInt(document.body.style.top,10),t=-parseInt(document.body.style.left,10);Object.assign(document.body.style,C),window.requestAnimationFrame(()=>{if(i&&s!==window.location.href){c(window.location.href);return}window.scrollTo(t,e)}),C=null}},[s]);return o.useEffect(()=>{function e(){d.current=window.scrollY}return e(),window.addEventListener("scroll",e),()=>{window.removeEventListener("scroll",e)}},[]),o.useEffect(()=>{if(n)return()=>{"undefined"!=typeof document&&(document.querySelector("[data-vaul-drawer]")||p())}},[n,p]),o.useEffect(()=>{r||!a||(t?(window.matchMedia("(display-mode: standalone)").matches||f(),n||window.setTimeout(()=>{p()},500)):p())},[t,a,s,n,r,f,p]),{restorePositionSetting:p}}({isOpen:ee,modal:q,nested:null!=$&&$,hasBeenOpened:en,preventScrollRestoration:X,noBodyStyles:B});function ek(){return(window.innerWidth-26)/window.innerWidth}function eS(e,t){var n;let r=e,a=null==(n=window.getSelection())?void 0:n.toString(),o=ey.current?j(ey.current,W):null,i=new Date;if("SELECT"===r.tagName||r.hasAttribute("data-vaul-no-drag")||r.closest("[data-vaul-no-drag]"))return!1;if("right"===W||"left"===W)return!0;if(es.current&&i.getTime()-es.current.getTime()<500)return!1;if(null!==o&&("bottom"===W?o>0:o<0))return!0;if(a&&a.length>0)return!1;if(ef.current&&i.getTime()-ef.current.getTime()<S&&0===o||t)return ef.current=i,!1;for(;r;){if(r.scrollHeight>r.clientHeight){if(0!==r.scrollTop)return ef.current=new Date,!1;if("dialog"===r.getAttribute("role"))break}r=r.parentNode}return!0}function eI(e){ea&&ey.current&&(ey.current.classList.remove(M),ep.current=!1,eo(!1),ed.current=new Date),null==U||U(),e||et(!1),setTimeout(()=>{R&&eR(R[0])},1e3*T.DURATION)}function eN(){if(!ey.current)return;let e=document.querySelector("[data-vaul-drawer-wrapper]"),t=j(ey.current,W);O(ey.current,{transform:"translate3d(0, 0, 0)",transition:"transform ".concat(T.DURATION,"s cubic-bezier(").concat(T.EASE.join(","),")")}),O(eu.current,{transition:"opacity ".concat(T.DURATION,"s cubic-bezier(").concat(T.EASE.join(","),")"),opacity:"1"}),P&&t&&t>0&&ee&&O(e,{borderRadius:"".concat(8,"px"),overflow:"hidden",..._(W)?{transform:"scale(".concat(ek(),") translate3d(0, calc(env(safe-area-inset-top) + 14px), 0)"),transformOrigin:"top"}:{transform:"scale(".concat(ek(),") translate3d(calc(env(safe-area-inset-top) + 14px), 0, 0)"),transformOrigin:"left"},transitionProperty:"transform, border-radius",transitionDuration:"".concat(T.DURATION,"s"),transitionTimingFunction:"cubic-bezier(".concat(T.EASE.join(","),")")},!0)}return o.useEffect(()=>{window.requestAnimationFrame(()=>{ev.current=!0})},[]),o.useEffect(()=>{var e;function t(){if(ey.current&&V&&(b(document.activeElement)||eh.current)){var e;let t=(null==(e=window.visualViewport)?void 0:e.height)||0,n=window.innerHeight,r=n-t,a=ey.current.getBoundingClientRect().height||0;eE.current||(eE.current=a);let o=ey.current.getBoundingClientRect().top;if(Math.abs(ew.current-r)>60&&(eh.current=!eh.current),R&&R.length>0&&eM&&ej&&(r+=eM[ej]||0),ew.current=r,a>t||eh.current){let e=ey.current.getBoundingClientRect().height,i=e;e>t&&(i=t-(a>.8*n?o:26)),H?ey.current.style.height="".concat(e-Math.max(r,0),"px"):ey.current.style.height="".concat(Math.max(i,t-o),"px")}else!function(){let e=navigator.userAgent;return"undefined"!=typeof window&&(/Firefox/.test(e)&&/Mobile/.test(e)||/FxiOS/.test(e))}()&&(ey.current.style.height="".concat(eE.current,"px"));R&&R.length>0&&!eh.current?ey.current.style.bottom="0px":ey.current.style.bottom="".concat(Math.max(r,0),"px")}}return null==(e=window.visualViewport)||e.addEventListener("resize",t),()=>{var e;return null==(e=window.visualViewport)?void 0:e.removeEventListener("resize",t)}},[ej,R,eM]),o.useEffect(()=>(ee&&(O(document.documentElement,{scrollBehavior:"auto"}),es.current=new Date),()=>{!function(e,t){if(!e||!(e instanceof HTMLElement))return;let n=E.get(e);n&&(e.style[t]=n[t])}(document.documentElement,"scrollBehavior")}),[ee]),o.useEffect(()=>{q||window.requestAnimationFrame(()=>{document.body.style.pointerEvents="auto"})},[q]),o.createElement(a.bL,{defaultOpen:z,onOpenChange:e=>{(I||e)&&(e?er(!0):eI(!0),et(e))},open:ee},o.createElement(i.Provider,{value:{activeSnapPoint:e_,snapPoints:R,setActiveSnapPoint:eR,drawerRef:ey,overlayRef:eu,onOpenChange:c,onPress:function(e){var t,n;(I||R)&&(!ey.current||ey.current.contains(e.target))&&(eb.current=(null==(t=ey.current)?void 0:t.getBoundingClientRect().height)||0,ex.current=(null==(n=ey.current)?void 0:n.getBoundingClientRect().width)||0,eo(!0),ec.current=new Date,s()&&window.addEventListener("touchend",()=>ep.current=!1,{once:!0}),e.target.setPointerCapture(e.pointerId),eg.current=_(W)?e.pageY:e.pageX)},onRelease:function(e){var t,n;if(!ea||!ey.current)return;ey.current.classList.remove(M),ep.current=!1,eo(!1),ed.current=new Date;let r=j(ey.current,W);if(!e||!eS(e.target,!1)||!r||Number.isNaN(r)||null===ec.current)return;let a=ed.current.getTime()-ec.current.getTime(),o=eg.current-(_(W)?e.pageY:e.pageX),i=Math.abs(o)/a;if(i>.05&&(el(!0),setTimeout(()=>{el(!1)},200)),R){eT({draggedDistance:o*("bottom"===W||"right"===W?1:-1),closeDrawer:eI,velocity:i,dismissible:I}),null==x||x(e,!0);return}if("bottom"===W||"right"===W?o>0:o<0){eN(),null==x||x(e,!0);return}if(i>.4){eI(),null==x||x(e,!1);return}let l=Math.min(null!=(t=ey.current.getBoundingClientRect().height)?t:0,window.innerHeight),u=Math.min(null!=(n=ey.current.getBoundingClientRect().width)?n:0,window.innerWidth);if(Math.abs(r)>=("left"===W||"right"===W?u:l)*k){eI(),null==x||x(e,!1);return}null==x||x(e,!0),eN()},onDrag:function(e){if(ey.current&&ea){let t="bottom"===W||"right"===W?1:-1,n=(eg.current-(_(W)?e.pageY:e.pageX))*t,r=n>0,a=R&&!I&&!r;if(a&&0===ej)return;let o=Math.abs(n),i=document.querySelector("[data-vaul-drawer-wrapper]"),l=o/("bottom"===W||"top"===W?eb.current:ex.current),u=eA(o,r);if(null!==u&&(l=u),a&&l>=1||!ep.current&&!eS(e.target,r))return;if(ey.current.classList.add(M),ep.current=!0,O(ey.current,{transition:"none"}),O(eu.current,{transition:"none"}),R&&eP({draggedDistance:n}),r&&!R){let e=Math.min(-(8*(Math.log(n+1)-2)*1),0)*t;O(ey.current,{transform:_(W)?"translate3d(0, ".concat(e,"px, 0)"):"translate3d(".concat(e,"px, 0, 0)")});return}let s=1-l;if((eD||L&&ej===L-1)&&(null==h||h(e,l),O(eu.current,{opacity:"".concat(s),transition:"none"},!0)),i&&eu.current&&P){let e=Math.min(ek()+l*(1-ek()),1),t=8-8*l,n=Math.max(0,14-14*l);O(i,{borderRadius:"".concat(t,"px"),transform:_(W)?"scale(".concat(e,") translate3d(0, ").concat(n,"px, 0)"):"scale(".concat(e,") translate3d(").concat(n,"px, 0, 0)"),transition:"none"},!0)}if(!R){let e=o*t;O(ey.current,{transform:_(W)?"translate3d(0, ".concat(e,"px, 0)"):"translate3d(".concat(e,"px, 0, 0)")})}}},dismissible:I,shouldAnimate:ev,handleOnly:N,isOpen:ee,isDragging:ea,shouldFade:eD,closeDrawer:eI,onNestedDrag:function(e,t){if(t<0)return;let n=(window.innerWidth-16)/window.innerWidth,r=n+t*(1-n),a=-16+16*t;O(ey.current,{transform:_(W)?"scale(".concat(r,") translate3d(0, ").concat(a,"px, 0)"):"scale(".concat(r,") translate3d(").concat(a,"px, 0, 0)"),transition:"none"})},onNestedOpenChange:function(e){let t=e?(window.innerWidth-16)/window.innerWidth:1,n=e?-16:0;em.current&&window.clearTimeout(em.current),O(ey.current,{transition:"transform ".concat(T.DURATION,"s cubic-bezier(").concat(T.EASE.join(","),")"),transform:_(W)?"scale(".concat(t,") translate3d(0, ").concat(n,"px, 0)"):"scale(".concat(t,") translate3d(").concat(n,"px, 0, 0)")}),!e&&ey.current&&(em.current=setTimeout(()=>{let e=j(ey.current,W);O(ey.current,{transition:"none",transform:_(W)?"translate3d(0, ".concat(e,"px, 0)"):"translate3d(".concat(e,"px, 0, 0)")})},500))},onNestedRelease:function(e,t){let n=_(W)?window.innerHeight:window.innerWidth,r=t?(n-16)/n:1,a=t?-16:0;t&&O(ey.current,{transition:"transform ".concat(T.DURATION,"s cubic-bezier(").concat(T.EASE.join(","),")"),transform:_(W)?"scale(".concat(r,") translate3d(0, ").concat(a,"px, 0)"):"scale(".concat(r,") translate3d(").concat(a,"px, 0, 0)")})},keyboardIsOpen:eh,modal:q,snapPointsOffset:eM,activeSnapPointIndex:ej,direction:W,shouldScaleBackground:P,setBackgroundColorOnScale:A,noBodyStyles:B,container:K,autoFocus:Q}},m))}let S=o.forwardRef(function(e,t){let{...n}=e,{overlayRef:r,snapPoints:i,onRelease:u,shouldFade:s,isOpen:c,modal:d,shouldAnimate:f}=l(),p=x(t,r),m=i&&i.length>0;if(!d)return null;let g=o.useCallback(e=>u(e),[u]);return o.createElement(a.hJ,{onMouseUp:g,ref:p,"data-vaul-overlay":"","data-vaul-snap-points":c&&m?"true":"false","data-vaul-snap-points-overlay":c&&s?"true":"false","data-vaul-animate":(null==f?void 0:f.current)?"true":"false",...n})});S.displayName="Drawer.Overlay";let I=o.forwardRef(function(e,t){let{onPointerDownOutside:n,style:r,onOpenAutoFocus:i,...u}=e,{drawerRef:s,onPress:c,onRelease:d,onDrag:f,keyboardIsOpen:p,snapPointsOffset:m,activeSnapPointIndex:g,modal:h,isOpen:v,direction:w,snapPoints:y,container:b,handleOnly:E,shouldAnimate:O,autoFocus:j}=l(),[M,P]=o.useState(!1),D=x(t,s),C=o.useRef(null),k=o.useRef(null),S=o.useRef(!1),I=y&&y.length>0;!function(){let{direction:e,isOpen:t,shouldScaleBackground:n,setBackgroundColorOnScale:r,noBodyStyles:a}=l(),i=o.useRef(null),u=(0,o.useMemo)(()=>document.body.style.backgroundColor,[]);function s(){return(window.innerWidth-26)/window.innerWidth}o.useEffect(()=>{if(t&&n){i.current&&clearTimeout(i.current);let t=document.querySelector("[data-vaul-drawer-wrapper]")||document.querySelector("[vaul-drawer-wrapper]");if(!t)return;!function(){for(var e=arguments.length,t=Array(e),n=0;n<e;n++)t[n]=arguments[n]}(r&&!a?R(document.body,{background:"black"}):A,R(t,{transformOrigin:_(e)?"top":"left",transitionProperty:"transform, border-radius",transitionDuration:"".concat(T.DURATION,"s"),transitionTimingFunction:"cubic-bezier(".concat(T.EASE.join(","),")")}));let n=R(t,{borderRadius:"".concat(8,"px"),overflow:"hidden",..._(e)?{transform:"scale(".concat(s(),") translate3d(0, calc(env(safe-area-inset-top) + 14px), 0)")}:{transform:"scale(".concat(s(),") translate3d(calc(env(safe-area-inset-top) + 14px), 0, 0)")}});return()=>{n(),i.current=window.setTimeout(()=>{u?document.body.style.background=u:document.body.style.removeProperty("background")},1e3*T.DURATION)}}},[t,n,u])}();let N=function(e,t){let n=arguments.length>2&&void 0!==arguments[2]?arguments[2]:0;if(S.current)return!0;let r=Math.abs(e.y),a=Math.abs(e.x),o=a>r,i=["bottom","right"].includes(t)?1:-1;if("left"===t||"right"===t){if(!(e.x*i<0)&&a>=0&&a<=n)return o}else if(!(e.y*i<0)&&r>=0&&r<=n)return!o;return S.current=!0,!0};function L(e){C.current=null,S.current=!1,d(e)}return o.useEffect(()=>{I&&window.requestAnimationFrame(()=>{P(!0)})},[]),o.createElement(a.UC,{"data-vaul-drawer-direction":w,"data-vaul-drawer":"","data-vaul-delayed-snap-points":M?"true":"false","data-vaul-snap-points":v&&I?"true":"false","data-vaul-custom-container":b?"true":"false","data-vaul-animate":(null==O?void 0:O.current)?"true":"false",...u,ref:D,style:m&&m.length>0?{"--snap-point-height":"".concat(m[null!=g?g:0],"px"),...r}:r,onPointerDown:e=>{E||(null==u.onPointerDown||u.onPointerDown.call(u,e),C.current={x:e.pageX,y:e.pageY},c(e))},onOpenAutoFocus:e=>{null==i||i(e),j||e.preventDefault()},onPointerDownOutside:e=>{if(null==n||n(e),!h||e.defaultPrevented){e.preventDefault();return}p.current&&(p.current=!1)},onFocusOutside:e=>{if(!h){e.preventDefault();return}},onPointerMove:e=>{if(k.current=e,E||(null==u.onPointerMove||u.onPointerMove.call(u,e),!C.current))return;let t=e.pageY-C.current.y,n=e.pageX-C.current.x,r="touch"===e.pointerType?10:2;N({x:n,y:t},w,r)?f(e):(Math.abs(n)>r||Math.abs(t)>r)&&(C.current=null)},onPointerUp:e=>{null==u.onPointerUp||u.onPointerUp.call(u,e),C.current=null,S.current=!1,d(e)},onPointerOut:e=>{null==u.onPointerOut||u.onPointerOut.call(u,e),L(k.current)},onContextMenu:e=>{null==u.onContextMenu||u.onContextMenu.call(u,e),k.current&&L(k.current)}})});I.displayName="Drawer.Content";let N=o.forwardRef(function(e,t){let{preventCycle:n=!1,children:r,...a}=e,{closeDrawer:i,isDragging:u,snapPoints:s,activeSnapPoint:c,setActiveSnapPoint:d,dismissible:f,handleOnly:p,isOpen:m,onPress:g,onDrag:h}=l(),v=o.useRef(null),w=o.useRef(!1);function y(){v.current&&window.clearTimeout(v.current),w.current=!1}return o.createElement("div",{onClick:function(){if(w.current){y();return}window.setTimeout(()=>{!function(){if(u||n||w.current){y();return}if(y(),!s||0===s.length){f||i();return}if(c===s[s.length-1]&&f){i();return}let e=s.findIndex(e=>e===c);-1!==e&&d(s[e+1])}()},120)},onPointerCancel:y,onPointerDown:e=>{p&&g(e),v.current=window.setTimeout(()=>{w.current=!0},250)},onPointerMove:e=>{p&&h(e)},ref:t,"data-vaul-drawer-visible":m?"true":"false","data-vaul-handle":"","aria-hidden":"true",...a},o.createElement("span",{"data-vaul-handle-hitarea":"","aria-hidden":"true"},r))});N.displayName="Drawer.Handle";let L={Root:k,NestedRoot:function(e){let{onDrag:t,onOpenChange:n,open:r,...a}=e,{onNestedDrag:i,onNestedOpenChange:u,onNestedRelease:s}=l();if(!i)throw Error("Drawer.NestedRoot must be placed in another drawer");return o.createElement(k,{nested:!0,open:r,onClose:()=>{u(!1)},onDrag:(e,n)=>{i(e,n),null==t||t(e,n)},onOpenChange:e=>{e&&u(e),null==n||n(e)},onRelease:s,...a})},Content:I,Overlay:S,Trigger:a.l9,Portal:function(e){let t=l(),{container:n=t.container,...r}=e;return o.createElement(a.ZL,{container:n,...r})},Handle:N,Close:a.bm,Title:a.hE,Description:a.VY}},18877:e=>{e.exports={style:{fontFamily:"'Inter', 'Inter Fallback'",fontStyle:"normal"},className:"__className_e8ce0c",variable:"__variable_e8ce0c"}},21494:(e,t,n)=>{"use strict";n.d(t,{UC:()=>en,VY:()=>ea,ZL:()=>ee,bL:()=>K,bm:()=>eo,hE:()=>er,hJ:()=>et,l9:()=>Q});var r=n(95361),a=n(75030),o=n(94286),i=n(22408),l=n(25747),u=n(4471),s=n(10730),c=n(70908),d=n(70200),f=n(12177),p=n(72637),m=n(57750),g=n(99126),h=n(91783),v=n(76012),w=n(16261),y="Dialog",[b,x]=(0,i.A)(y),[E,O]=b(y),_=e=>{let{__scopeDialog:t,children:n,open:a,defaultOpen:o,onOpenChange:i,modal:s=!0}=e,c=r.useRef(null),d=r.useRef(null),[f,p]=(0,u.i)({prop:a,defaultProp:null!=o&&o,onChange:i,caller:y});return(0,w.jsx)(E,{scope:t,triggerRef:c,contentRef:d,contentId:(0,l.B)(),titleId:(0,l.B)(),descriptionId:(0,l.B)(),open:f,onOpenChange:p,onOpenToggle:r.useCallback(()=>p(e=>!e),[p]),modal:s,children:n})};_.displayName=y;var j="DialogTrigger",R=r.forwardRef((e,t)=>{let{__scopeDialog:n,...r}=e,i=O(j,n),l=(0,o.s)(t,i.triggerRef);return(0,w.jsx)(p.sG.button,{type:"button","aria-haspopup":"dialog","aria-expanded":i.open,"aria-controls":i.contentId,"data-state":z(i.open),...r,ref:l,onClick:(0,a.m)(e.onClick,i.onOpenToggle)})});R.displayName=j;var T="DialogPortal",[M,P]=b(T,{forceMount:void 0}),D=e=>{let{__scopeDialog:t,forceMount:n,children:a,container:o}=e,i=O(T,t);return(0,w.jsx)(M,{scope:t,forceMount:n,children:r.Children.map(a,e=>(0,w.jsx)(f.C,{present:n||i.open,children:(0,w.jsx)(d.Z,{asChild:!0,container:o,children:e})}))})};D.displayName=T;var A="DialogOverlay",C=r.forwardRef((e,t)=>{let n=P(A,e.__scopeDialog),{forceMount:r=n.forceMount,...a}=e,o=O(A,e.__scopeDialog);return o.modal?(0,w.jsx)(f.C,{present:r||o.open,children:(0,w.jsx)(S,{...a,ref:t})}):null});C.displayName=A;var k=(0,v.TL)("DialogOverlay.RemoveScroll"),S=r.forwardRef((e,t)=>{let{__scopeDialog:n,...r}=e,a=O(A,n);return(0,w.jsx)(g.A,{as:k,allowPinchZoom:!0,shards:[a.contentRef],children:(0,w.jsx)(p.sG.div,{"data-state":z(a.open),...r,ref:t,style:{pointerEvents:"auto",...r.style}})})}),I="DialogContent",N=r.forwardRef((e,t)=>{let n=P(I,e.__scopeDialog),{forceMount:r=n.forceMount,...a}=e,o=O(I,e.__scopeDialog);return(0,w.jsx)(f.C,{present:r||o.open,children:o.modal?(0,w.jsx)(L,{...a,ref:t}):(0,w.jsx)(F,{...a,ref:t})})});N.displayName=I;var L=r.forwardRef((e,t)=>{let n=O(I,e.__scopeDialog),i=r.useRef(null),l=(0,o.s)(t,n.contentRef,i);return r.useEffect(()=>{let e=i.current;if(e)return(0,h.Eq)(e)},[]),(0,w.jsx)(G,{...e,ref:l,trapFocus:n.open,disableOutsidePointerEvents:!0,onCloseAutoFocus:(0,a.m)(e.onCloseAutoFocus,e=>{var t;e.preventDefault(),null===(t=n.triggerRef.current)||void 0===t||t.focus()}),onPointerDownOutside:(0,a.m)(e.onPointerDownOutside,e=>{let t=e.detail.originalEvent,n=0===t.button&&!0===t.ctrlKey;(2===t.button||n)&&e.preventDefault()}),onFocusOutside:(0,a.m)(e.onFocusOutside,e=>e.preventDefault())})}),F=r.forwardRef((e,t)=>{let n=O(I,e.__scopeDialog),a=r.useRef(!1),o=r.useRef(!1);return(0,w.jsx)(G,{...e,ref:t,trapFocus:!1,disableOutsidePointerEvents:!1,onCloseAutoFocus:t=>{var r,i;null===(r=e.onCloseAutoFocus)||void 0===r||r.call(e,t),t.defaultPrevented||(a.current||null===(i=n.triggerRef.current)||void 0===i||i.focus(),t.preventDefault()),a.current=!1,o.current=!1},onInteractOutside:t=>{var r,i;null===(r=e.onInteractOutside)||void 0===r||r.call(e,t),t.defaultPrevented||(a.current=!0,"pointerdown"!==t.detail.originalEvent.type||(o.current=!0));let l=t.target;(null===(i=n.triggerRef.current)||void 0===i?void 0:i.contains(l))&&t.preventDefault(),"focusin"===t.detail.originalEvent.type&&o.current&&t.preventDefault()}})}),G=r.forwardRef((e,t)=>{let{__scopeDialog:n,trapFocus:a,onOpenAutoFocus:i,onCloseAutoFocus:l,...u}=e,d=O(I,n),f=r.useRef(null),p=(0,o.s)(t,f);return(0,m.Oh)(),(0,w.jsxs)(w.Fragment,{children:[(0,w.jsx)(c.n,{asChild:!0,loop:!0,trapped:a,onMountAutoFocus:i,onUnmountAutoFocus:l,children:(0,w.jsx)(s.qW,{role:"dialog",id:d.contentId,"aria-describedby":d.descriptionId,"aria-labelledby":d.titleId,"data-state":z(d.open),...u,ref:p,onDismiss:()=>d.onOpenChange(!1)})}),(0,w.jsxs)(w.Fragment,{children:[(0,w.jsx)(V,{titleId:d.titleId}),(0,w.jsx)(Z,{contentRef:f,descriptionId:d.descriptionId})]})]})}),H="DialogTitle",q=r.forwardRef((e,t)=>{let{__scopeDialog:n,...r}=e,a=O(H,n);return(0,w.jsx)(p.sG.h2,{id:a.titleId,...r,ref:t})});q.displayName=H;var U="DialogDescription",$=r.forwardRef((e,t)=>{let{__scopeDialog:n,...r}=e,a=O(U,n);return(0,w.jsx)(p.sG.p,{id:a.descriptionId,...r,ref:t})});$.displayName=U;var B="DialogClose",W=r.forwardRef((e,t)=>{let{__scopeDialog:n,...r}=e,o=O(B,n);return(0,w.jsx)(p.sG.button,{type:"button",...r,ref:t,onClick:(0,a.m)(e.onClick,()=>o.onOpenChange(!1))})});function z(e){return e?"open":"closed"}W.displayName=B;var Y="DialogTitleWarning",[J,X]=(0,i.q)(Y,{contentName:I,titleName:H,docsSlug:"dialog"}),V=e=>{let{titleId:t}=e,n=X(Y),a="`".concat(n.contentName,"` requires a `").concat(n.titleName,"` for the component to be accessible for screen reader users.\n\nIf you want to hide the `").concat(n.titleName,"`, you can wrap it with our VisuallyHidden component.\n\nFor more information, see https://radix-ui.com/primitives/docs/components/").concat(n.docsSlug);return r.useEffect(()=>{t&&!document.getElementById(t)&&console.error(a)},[a,t]),null},Z=e=>{let{contentRef:t,descriptionId:n}=e,a=X("DialogDescriptionWarning"),o="Warning: Missing `Description` or `aria-describedby={undefined}` for {".concat(a.contentName,"}.");return r.useEffect(()=>{var e;let r=null===(e=t.current)||void 0===e?void 0:e.getAttribute("aria-describedby");n&&r&&!document.getElementById(n)&&console.warn(o)},[o,t,n]),null},K=_,Q=R,ee=D,et=C,en=N,er=q,ea=$,eo=W},29698:(e,t,n)=>{"use strict";n.d(t,{uS:()=>l});var r=n(62650),a=n(95361),o=class{constructor(e){this.baseUrl=e.baseUrl,this.headers={"Content-Type":"application/json",...e.defaultHeaders},this.maxRetries=e.maxRetries??3,this.initialRetryDelay=e.initialRetryDelay??500}async resolveHeaders(){let e={};for(let[t,n]of Object.entries(this.headers)){let r=await n;null!==r&&(e[t]=r)}return e}addHeader(e,t){this.headers[e]=t}async post(e,t,n,r){try{let r=await fetch(e,{method:"POST",headers:await this.resolveHeaders(),body:JSON.stringify(t??{}),keepalive:!0,...n});if(401===r.status)return null;if(200!==r.status&&202!==r.status)throw Error(`HTTP error! status: ${r.status}`);let a=await r.text();return a?JSON.parse(a):null}catch(a){if(r<this.maxRetries){let a=this.initialRetryDelay*Math.pow(2,r);return await new Promise(e=>setTimeout(e,a)),this.post(e,t,n,r+1)}return console.error("Max retries reached:",a),null}}async fetch(e,t,n={}){let r=`${this.baseUrl}${e}`;return this.post(r,t,n,0)}},i=e=>"object"==typeof e&&null!=e?`{${Object.entries(e).map(([e,t])=>"filter"===e?`"${e}":${t}`:`"${e}":${JSON.stringify(t)}`).join(",")}}`:JSON.stringify(e);function l({profileId:e,cdnUrl:t,globalProperties:n,...o}){let l=[{name:"init",value:{...o,sdk:"nextjs",sdkVersion:"1.0.8"}}];return e&&l.push({name:"identify",value:{profileId:e}}),n&&l.push({name:"setGlobalProperties",value:n}),a.createElement(a.Fragment,null,a.createElement(r.default,{src:t??"https://openpanel.dev/op1.js",async:!0,defer:!0}),a.createElement(r.default,{strategy:"beforeInteractive",dangerouslySetInnerHTML:{__html:`window.op = window.op || function(...args) {(window.op.q = window.op.q || []).push(args)};
          ${l.map(e=>`window.op('${e.name}', ${i(e.value)});`).join(`
`)}`}}))}},31083:function(e,t,n){"use strict";var r=this&&this.__rest||function(e,t){var n={};for(var r in e)Object.prototype.hasOwnProperty.call(e,r)&&0>t.indexOf(r)&&(n[r]=e[r]);if(null!=e&&"function"==typeof Object.getOwnPropertySymbols)for(var a=0,r=Object.getOwnPropertySymbols(e);a<r.length;a++)0>t.indexOf(r[a])&&Object.prototype.propertyIsEnumerable.call(e,r[a])&&(n[r[a]]=e[r[a]]);return n},a=this&&this.__importDefault||function(e){return e&&e.__esModule?e:{default:e}};Object.defineProperty(t,"__esModule",{value:!0}),t.YouTubeEmbed=void 0;let o=a(n(49870)),i=n(96004);t.YouTubeEmbed=e=>{var t=r(e,[]);return(0,i.formatData)(o.default,t)}},31738:e=>{"use strict";e.exports=JSON.parse('{"id":"google-maps-embed","description":"Embed a Google Maps embed on your webpage","website":"https://developers.google.com/maps/documentation/embed/get-started","html":{"element":"iframe","attributes":{"loading":"lazy","src":{"url":"https://www.google.com/maps/embed/v1/place","slugParam":"mode","params":["key","q","center","zoom","maptype","language","region"]},"referrerpolicy":"no-referrer-when-downgrade","frameborder":"0","style":"border:0","allowfullscreen":true,"width":null,"height":null}}}')},49042:(e,t)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),Object.defineProperty(t,"setAttributesFromProps",{enumerable:!0,get:function(){return o}});let n={acceptCharset:"accept-charset",className:"class",htmlFor:"for",httpEquiv:"http-equiv",noModule:"noModule"},r=["onLoad","onReady","dangerouslySetInnerHTML","children","onError","strategy","stylesheets"];function a(e){return["async","defer","noModule"].includes(e)}function o(e,t){for(let[o,i]of Object.entries(t)){if(!t.hasOwnProperty(o)||r.includes(o)||void 0===i)continue;let l=n[o]||o.toLowerCase();"SCRIPT"===e.tagName&&a(l)?e[l]=!!i:e.setAttribute(l,String(i)),(!1===i||"SCRIPT"===e.tagName&&a(l)&&(!i||"false"===i))&&(e.setAttribute(l,""),e.removeAttribute(l))}}("function"==typeof t.default||"object"==typeof t.default&&null!==t.default)&&void 0===t.default.__esModule&&(Object.defineProperty(t.default,"__esModule",{value:!0}),Object.assign(t.default,t),e.exports=t.default)},49870:e=>{"use strict";e.exports=JSON.parse('{"id":"youtube-embed","description":"Embed a YouTube embed on your webpage.","website":"https://github.com/paulirish/lite-youtube-embed","html":{"element":"lite-youtube","attributes":{"videoid":null,"playlabel":null}},"stylesheets":["https://cdn.jsdelivr.net/gh/paulirish/lite-youtube-embed@master/src/lite-yt-embed.css"],"scripts":[{"url":"https://cdn.jsdelivr.net/gh/paulirish/lite-youtube-embed@master/src/lite-yt-embed.js","strategy":"idle","location":"head","action":"append"}]}')},50349:(e,t,n)=>{"use strict";n.d(t,{FD:()=>v,MY:()=>o,PJ:()=>i,Wl:()=>u,XP:()=>d,_x:()=>a,bL:()=>f,po:()=>s,ql:()=>c,wO:()=>l,yL:()=>w});var r=n(55036);function a(e){return("object"==typeof e?null==e.host&&null==e.hostname:!/^[a-z]+:/i.test(e))&&!function(e){let t="object"==typeof e?e.pathname:e;return null!=t&&!t.startsWith("/")}(e)}function o(e,t){return e.replace(RegExp(`^${t}`),"")||"/"}function i(e,t){let n=e;return/^\/(\?.*)?$/.test(t)&&(t=t.slice(1)),n+=t}function l(e,t){return t===e||t.startsWith(`${e}/`)}function u(e,t,n){return"string"==typeof e?e:e[t]||n}function s(e){let t=function(){try{return"true"===r.env._next_intl_trailing_slash}catch{return!1}}(),[n,...a]=e.split("#"),o=a.join("#"),i=n;if("/"!==i){let e=i.endsWith("/");t&&!e?i+="/":!t&&e&&(i=i.slice(0,-1))}return o&&(i+="#"+o),i}function c(e,t){let n=s(e),r=s(t);return(function(e){let t=e.replace(/\[\[(\.\.\.[^\]]+)\]\]/g,"?(.*)").replace(/\[(\.\.\.[^\]]+)\]/g,"(.+)").replace(/\[([^\]]+)\]/g,"([^/]+)");return RegExp(`^${t}$`)})(n).test(r)}function d(e,t){return"never"!==t.mode&&t.prefixes?.[e]||f(e)}function f(e){return"/"+e}function p(e){return e.includes("[[...")}function m(e){return e.includes("[...")}function g(e){return e.includes("[")}function h(e,t){let n=e.split("/"),r=t.split("/"),a=Math.max(n.length,r.length);for(let e=0;e<a;e++){let t=n[e],a=r[e];if(!t&&a)return -1;if(t&&!a)return 1;if(t||a){if(!g(t)&&g(a))return -1;if(g(t)&&!g(a))return 1;if(!m(t)&&m(a))return -1;if(m(t)&&!m(a))return 1;if(!p(t)&&p(a))return -1;if(p(t)&&!p(a))return 1}}return 0}function v(e){return e.sort(h)}function w(e){return"function"==typeof e.then}},57123:(e,t,n)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),t.default=function(e){let{html:t,height:n=null,width:o=null,children:i,dataNtpc:l=""}=e;return(0,a.useEffect)(()=>{l&&performance.mark("mark_feature_usage",{detail:{feature:"next-third-parties-".concat(l)}})},[l]),(0,r.jsxs)(r.Fragment,{children:[i,t?(0,r.jsx)("div",{style:{height:null!=n?"".concat(n,"px"):"auto",width:null!=o?"".concat(o,"px"):"auto"},"data-ntpc":l,dangerouslySetInnerHTML:{__html:t}}):null]})};let r=n(16261),a=n(95361)},58182:function(e,t,n){"use strict";var r=this&&this.__rest||function(e,t){var n={};for(var r in e)Object.prototype.hasOwnProperty.call(e,r)&&0>t.indexOf(r)&&(n[r]=e[r]);if(null!=e&&"function"==typeof Object.getOwnPropertySymbols)for(var a=0,r=Object.getOwnPropertySymbols(e);a<r.length;a++)0>t.indexOf(r[a])&&Object.prototype.propertyIsEnumerable.call(e,r[a])&&(n[r[a]]=e[r[a]]);return n},a=this&&this.__importDefault||function(e){return e&&e.__esModule?e:{default:e}};Object.defineProperty(t,"__esModule",{value:!0}),t.GoogleAnalytics=void 0;let o=a(n(4753)),i=n(96004);t.GoogleAnalytics=e=>{var t=r(e,[]);return(0,i.formatData)(o.default,t)}},62346:(e,t,n)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),t.YouTubeEmbed=t.GoogleMapsEmbed=t.GoogleAnalytics=void 0;var r=n(58182);Object.defineProperty(t,"GoogleAnalytics",{enumerable:!0,get:function(){return r.GoogleAnalytics}});var a=n(92703);Object.defineProperty(t,"GoogleMapsEmbed",{enumerable:!0,get:function(){return a.GoogleMapsEmbed}});var o=n(31083);Object.defineProperty(t,"YouTubeEmbed",{enumerable:!0,get:function(){return o.YouTubeEmbed}})},62650:(e,t,n)=>{"use strict";n.r(t),n.d(t,{default:()=>a.a});var r=n(97571),a=n.n(r),o={};for(let e in r)"default"!==e&&(o[e]=()=>r[e]);n.d(t,o)},65834:(e,t,n)=>{"use strict";let r;Object.defineProperty(t,"__esModule",{value:!0}),t.GoogleAnalytics=function(e){let{gaId:t,debugMode:n,dataLayerName:l="dataLayer",nonce:u}=e;return void 0===r&&(r=l),(0,o.useEffect)(()=>{performance.mark("mark_feature_usage",{detail:{feature:"next-third-parties-ga"}})},[]),(0,a.jsxs)(a.Fragment,{children:[(0,a.jsx)(i.default,{id:"_next-ga-init",dangerouslySetInnerHTML:{__html:"\n          window['".concat(l,"'] = window['").concat(l,"'] || [];\n          function gtag(){window['").concat(l,"'].push(arguments);}\n          gtag('js', new Date());\n\n          gtag('config', '").concat(t,"' ").concat(n?",{ 'debug_mode': true }":"",");")},nonce:u}),(0,a.jsx)(i.default,{id:"_next-ga",src:"https://www.googletagmanager.com/gtag/js?id=".concat(t),nonce:u})]})},t.sendGAEvent=function(){for(var e=arguments.length,t=Array(e),n=0;n<e;n++)t[n]=arguments[n];if(void 0===r){console.warn("@next/third-parties: GA has not been initialized");return}window[r]?window[r].push(arguments):console.warn("@next/third-parties: GA dataLayer ".concat(r," does not exist"))};let a=n(16261),o=n(95361),i=function(e){return e&&e.__esModule?e:{default:e}}(n(62650))},68926:(e,t,n)=>{"use strict";n.d(t,{A:()=>a});var r=n(88454);function a(e,t,n,a){if(!e||a===n||null==a||!t)return;let o=(0,r.DT)(t),{name:i,...l}=e;l.path||(l.path=""!==o?o:"/");let u=`${i}=${a};`;for(let[e,t]of Object.entries(l))u+=`${"maxAge"===e?"max-age":e}`,"boolean"!=typeof t&&(u+="="+t),u+=";";document.cookie=u}},70542:(e,t)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),!function(e,t){for(var n in t)Object.defineProperty(e,n,{enumerable:!0,get:t[n]})}(t,{cancelIdleCallback:function(){return r},requestIdleCallback:function(){return n}});let n="undefined"!=typeof self&&self.requestIdleCallback&&self.requestIdleCallback.bind(window)||function(e){let t=Date.now();return self.setTimeout(function(){e({didTimeout:!1,timeRemaining:function(){return Math.max(0,50-(Date.now()-t))}})},1)},r="undefined"!=typeof self&&self.cancelIdleCallback&&self.cancelIdleCallback.bind(window)||function(e){return clearTimeout(e)};("function"==typeof t.default||"object"==typeof t.default&&null!==t.default)&&void 0===t.default.__esModule&&(Object.defineProperty(t.default,"__esModule",{value:!0}),Object.assign(t.default,t),e.exports=t.default)},74836:(e,t,n)=>{"use strict";n.d(t,{default:()=>o});var r=n(6002),a=n(16261);function o(e){let{locale:t,...n}=e;if(!t)throw Error(void 0);return(0,a.jsx)(r.Dk,{locale:t,...n})}},83668:(e,t,n)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),t.sendGTMEvent=void 0,t.GoogleTagManager=function(e){let{gtmId:t,gtmScriptUrl:n="https://www.googletagmanager.com/gtm.js",dataLayerName:l="dataLayer",auth:u,preview:s,dataLayer:c,nonce:d}=e;i=l;let f="dataLayer"!==l?"&l=".concat(l):"";return(0,a.useEffect)(()=>{performance.mark("mark_feature_usage",{detail:{feature:"next-third-parties-gtm"}})},[]),(0,r.jsxs)(r.Fragment,{children:[(0,r.jsx)(o.default,{id:"_next-gtm-init",dangerouslySetInnerHTML:{__html:"\n      (function(w,l){\n        w[l]=w[l]||[];\n        w[l].push({'gtm.start': new Date().getTime(),event:'gtm.js'});\n        ".concat(c?"w[l].push(".concat(JSON.stringify(c),")"):"","\n      })(window,'").concat(l,"');")},nonce:d}),(0,r.jsx)(o.default,{id:"_next-gtm","data-ntpc":"GTM",src:"".concat(n,"?id=").concat(t).concat(f).concat(u?"&gtm_auth=".concat(u):"").concat(s?"&gtm_preview=".concat(s,"&gtm_cookies_win=x"):""),nonce:d})]})};let r=n(16261),a=n(95361),o=function(e){return e&&e.__esModule?e:{default:e}}(n(62650)),i="dataLayer";t.sendGTMEvent=(e,t)=>{let n=t||i;window[n]=window[n]||[],window[n].push(e)}},86179:function(e,t,n){"use strict";var r=this&&this.__importDefault||function(e){return e&&e.__esModule?e:{default:e}};Object.defineProperty(t,"__esModule",{value:!0}),t.default=function(e){let{apiKey:t,...n}=e,r={...n,key:t},{html:l}=(0,o.GoogleMapsEmbed)(r);return(0,a.jsx)(i.default,{height:r.height||null,width:r.width||null,html:l,dataNtpc:"GoogleMapsEmbed"})};let a=n(16261),o=n(62346),i=r(n(57123))},87258:function(e,t,n){"use strict";var r=this&&this.__importDefault||function(e){return e&&e.__esModule?e:{default:e}};Object.defineProperty(t,"__esModule",{value:!0}),t.sendGAEvent=t.GoogleAnalytics=t.sendGTMEvent=t.GoogleTagManager=t.YouTubeEmbed=t.GoogleMapsEmbed=void 0;var a=n(86179);Object.defineProperty(t,"GoogleMapsEmbed",{enumerable:!0,get:function(){return r(a).default}});var o=n(90171);Object.defineProperty(t,"YouTubeEmbed",{enumerable:!0,get:function(){return r(o).default}});var i=n(83668);Object.defineProperty(t,"GoogleTagManager",{enumerable:!0,get:function(){return i.GoogleTagManager}}),Object.defineProperty(t,"sendGTMEvent",{enumerable:!0,get:function(){return i.sendGTMEvent}});var l=n(65834);Object.defineProperty(t,"GoogleAnalytics",{enumerable:!0,get:function(){return l.GoogleAnalytics}}),Object.defineProperty(t,"sendGAEvent",{enumerable:!0,get:function(){return l.sendGAEvent}})},87605:(e,t,n)=>{"use strict";n.d(t,{A:()=>r});let r=(0,n(1316).A)("X",[["path",{d:"M18 6 6 18",key:"1bl5f8"}],["path",{d:"m6 6 12 12",key:"d8bk6v"}]])},88454:(e,t,n)=>{"use strict";n.d(t,{DT:()=>u,FP:()=>i,TK:()=>a,Zn:()=>o,aM:()=>l,x3:()=>s});var r=n(50349);function a(e){return"string"==typeof e?{pathname:e}:e}function o(e){let t=new URLSearchParams;for(let[n,r]of Object.entries(e))Array.isArray(r)?r.forEach(e=>{t.append(n,String(e))}):t.set(n,String(r));return"?"+t.toString()}function i({pathname:e,locale:t,params:n,pathnames:a,query:i}){function l(e){let l;let u=a[e];return u?(l=(0,r.Wl)(u,t,e),n&&Object.entries(n).forEach(([e,t])=>{let n,r;Array.isArray(t)?(n=`(\\[)?\\[...${e}\\](\\])?`,r=t.map(e=>String(e)).join("/")):(n=`\\[${e}\\]`,r=String(t)),l=l.replace(RegExp(n,"g"),r)}),l=(l=l.replace(/\[\[\.\.\..+\]\]/g,"")).split("/").map(e=>encodeURIComponent(e)).join("/")):l=e,l=(0,r.po)(l),i&&(l+=o(i)),l}if("string"==typeof e)return l(e);{let{pathname:t,...n}=e;return{...n,pathname:l(t)}}}function l(e,t,n){let a=(0,r.FD)(Object.keys(n)),o=decodeURI(t);for(let t of a){let a=n[t];if("string"==typeof a){if((0,r.ql)(a,o))return t}else if((0,r.ql)((0,r.Wl)(a,e,t),o))return t}return t}function u(e,t=window.location.pathname){return"/"===e?t:t.replace(e,"")}function s(e,t,n,a){let o;let{mode:i}=n.localePrefix;return void 0!==a?o=a:(0,r._x)(e)&&("always"===i?o=!0:"as-needed"===i&&(o=n.domains?!n.domains.some(e=>e.defaultLocale===t):t!==n.defaultLocale)),o?(0,r.PJ)((0,r.XP)(t,n.localePrefix),e):e}},89799:(e,t,n)=>{"use strict";var r=n(8911);n.o(r,"permanentRedirect")&&n.d(t,{permanentRedirect:function(){return r.permanentRedirect}}),n.o(r,"redirect")&&n.d(t,{redirect:function(){return r.redirect}}),n.o(r,"useParams")&&n.d(t,{useParams:function(){return r.useParams}}),n.o(r,"usePathname")&&n.d(t,{usePathname:function(){return r.usePathname}}),n.o(r,"useRouter")&&n.d(t,{useRouter:function(){return r.useRouter}})},90171:function(e,t,n){"use strict";var r=this&&this.__importDefault||function(e){return e&&e.__esModule?e:{default:e}};Object.defineProperty(t,"__esModule",{value:!0}),t.default=function(e){let{html:t,scripts:n,stylesheets:r}=(0,i.YouTubeEmbed)(e);return(0,a.jsx)(l.default,{height:e.height||null,width:e.width||null,html:t,dataNtpc:"YouTubeEmbed",children:null==n?void 0:n.map(e=>(0,a.jsx)(o.default,{src:e.url,strategy:u[e.strategy],stylesheets:r},e.url))})};let a=n(16261),o=r(n(62650)),i=n(62346),l=r(n(57123)),u={server:"beforeInteractive",client:"afterInteractive",idle:"lazyOnload",worker:"worker"}},92703:function(e,t,n){"use strict";var r=this&&this.__rest||function(e,t){var n={};for(var r in e)Object.prototype.hasOwnProperty.call(e,r)&&0>t.indexOf(r)&&(n[r]=e[r]);if(null!=e&&"function"==typeof Object.getOwnPropertySymbols)for(var a=0,r=Object.getOwnPropertySymbols(e);a<r.length;a++)0>t.indexOf(r[a])&&Object.prototype.propertyIsEnumerable.call(e,r[a])&&(n[r[a]]=e[r[a]]);return n},a=this&&this.__importDefault||function(e){return e&&e.__esModule?e:{default:e}};Object.defineProperty(t,"__esModule",{value:!0}),t.GoogleMapsEmbed=void 0;let o=a(n(31738)),i=n(96004);t.GoogleMapsEmbed=e=>{var t=r(e,[]);return(0,i.formatData)(o.default,t)}},96004:(e,t)=>{"use strict";function n(e,t,r=!1){return t?Object.keys(e).filter(e=>r?!t.includes(e):t.includes(e)).reduce((t,n)=>(t[n]=e[n],t),{}):{}}function r(e,t,n,r){let a=r&&Object.keys(r).length>0?new URL(Object.values(r)[0],e):new URL(e);return t&&n&&t.forEach(e=>{n[e]&&a.searchParams.set(e,n[e])}),a.toString()}function a(e,t,n,a,o){var i;if(!t)return`<${e}></${e}>`;let l=(null===(i=t.src)||void 0===i?void 0:i.url)?Object.assign(Object.assign({},t),{src:r(t.src.url,t.src.params,a,o)}):t,u=Object.keys(Object.assign(Object.assign({},l),n)).reduce((e,t)=>{let r=null==n?void 0:n[t],a=l[t],o=null!=r?r:a,i=!0===o?t:`${t}="${o}"`;return o?e+` ${i}`:e},"");return`<${e}${u}></${e}>`}Object.defineProperty(t,"__esModule",{value:!0}),t.formatData=t.createHtml=t.formatUrl=void 0,t.formatUrl=r,t.createHtml=a,t.formatData=function(e,t){var o,i,l,u,s;let c=n(t,null===(o=e.scripts)||void 0===o?void 0:o.reduce((e,t)=>[...e,...Array.isArray(t.params)?t.params:[]],[])),d=n(t,null===(l=null===(i=e.html)||void 0===i?void 0:i.attributes.src)||void 0===l?void 0:l.params),f=n(t,[null===(s=null===(u=e.html)||void 0===u?void 0:u.attributes.src)||void 0===s?void 0:s.slugParam]),p=n(t,[...Object.keys(c),...Object.keys(d),...Object.keys(f)],!0);return Object.assign(Object.assign({},e),{html:e.html?a(e.html.element,e.html.attributes,p,d,f):null,scripts:e.scripts?e.scripts.map(e=>Object.assign(Object.assign({},e),{url:r(e.url,e.params,c)})):null})}},97571:(e,t,n)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),!function(e,t){for(var n in t)Object.defineProperty(e,n,{enumerable:!0,get:t[n]})}(t,{default:function(){return w},handleClientScriptLoad:function(){return g},initScriptLoader:function(){return h}});let r=n(96847),a=n(11308),o=n(16261),i=r._(n(32196)),l=a._(n(95361)),u=n(10374),s=n(49042),c=n(70542),d=new Map,f=new Set,p=e=>{if(i.default.preinit){e.forEach(e=>{i.default.preinit(e,{as:"style"})});return}{let t=document.head;e.forEach(e=>{let n=document.createElement("link");n.type="text/css",n.rel="stylesheet",n.href=e,t.appendChild(n)})}},m=e=>{let{src:t,id:n,onLoad:r=()=>{},onReady:a=null,dangerouslySetInnerHTML:o,children:i="",strategy:l="afterInteractive",onError:u,stylesheets:c}=e,m=n||t;if(m&&f.has(m))return;if(d.has(t)){f.add(m),d.get(t).then(r,u);return}let g=()=>{a&&a(),f.add(m)},h=document.createElement("script"),v=new Promise((e,t)=>{h.addEventListener("load",function(t){e(),r&&r.call(this,t),g()}),h.addEventListener("error",function(e){t(e)})}).catch(function(e){u&&u(e)});o?(h.innerHTML=o.__html||"",g()):i?(h.textContent="string"==typeof i?i:Array.isArray(i)?i.join(""):"",g()):t&&(h.src=t,d.set(t,v)),(0,s.setAttributesFromProps)(h,e),"worker"===l&&h.setAttribute("type","text/partytown"),h.setAttribute("data-nscript",l),c&&p(c),document.body.appendChild(h)};function g(e){let{strategy:t="afterInteractive"}=e;"lazyOnload"===t?window.addEventListener("load",()=>{(0,c.requestIdleCallback)(()=>m(e))}):m(e)}function h(e){e.forEach(g),[...document.querySelectorAll('[data-nscript="beforeInteractive"]'),...document.querySelectorAll('[data-nscript="beforePageRender"]')].forEach(e=>{let t=e.id||e.getAttribute("src");f.add(t)})}function v(e){let{id:t,src:n="",onLoad:r=()=>{},onReady:a=null,strategy:s="afterInteractive",onError:d,stylesheets:p,...g}=e,{updateScripts:h,scripts:v,getIsSsr:w,appDir:y,nonce:b}=(0,l.useContext)(u.HeadManagerContext),x=(0,l.useRef)(!1);(0,l.useEffect)(()=>{let e=t||n;x.current||(a&&e&&f.has(e)&&a(),x.current=!0)},[a,t,n]);let E=(0,l.useRef)(!1);if((0,l.useEffect)(()=>{if(!E.current){if("afterInteractive"===s)m(e);else if("lazyOnload"===s)"complete"===document.readyState?(0,c.requestIdleCallback)(()=>m(e)):window.addEventListener("load",()=>{(0,c.requestIdleCallback)(()=>m(e))});E.current=!0}},[e,s]),("beforeInteractive"===s||"worker"===s)&&(h?(v[s]=(v[s]||[]).concat([{id:t,src:n,onLoad:r,onReady:a,onError:d,...g}]),h(v)):w&&w()?f.add(t||n):w&&!w()&&m(e)),y){if(p&&p.forEach(e=>{i.default.preinit(e,{as:"style"})}),"beforeInteractive"===s)return n?(i.default.preload(n,g.integrity?{as:"script",integrity:g.integrity,nonce:b,crossOrigin:g.crossOrigin}:{as:"script",nonce:b,crossOrigin:g.crossOrigin}),(0,o.jsx)("script",{nonce:b,dangerouslySetInnerHTML:{__html:"(self.__next_s=self.__next_s||[]).push("+JSON.stringify([n,{...g,id:t}])+")"}})):(g.dangerouslySetInnerHTML&&(g.children=g.dangerouslySetInnerHTML.__html,delete g.dangerouslySetInnerHTML),(0,o.jsx)("script",{nonce:b,dangerouslySetInnerHTML:{__html:"(self.__next_s=self.__next_s||[]).push("+JSON.stringify([0,{...g,id:t}])+")"}}));"afterInteractive"===s&&n&&i.default.preload(n,g.integrity?{as:"script",integrity:g.integrity,nonce:b,crossOrigin:g.crossOrigin}:{as:"script",nonce:b,crossOrigin:g.crossOrigin})}return null}Object.defineProperty(v,"__nextScript",{value:!0});let w=v;("function"==typeof t.default||"object"==typeof t.default&&null!==t.default)&&void 0===t.default.__esModule&&(Object.defineProperty(t.default,"__esModule",{value:!0}),Object.assign(t.default,t),e.exports=t.default)}}]);