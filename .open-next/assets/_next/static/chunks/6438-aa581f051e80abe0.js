(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[6438],{5657:e=>{e.exports=function({client_id:e,auto_select:t=!1,cancel_on_tap_outside:r=!1,context:n="signin",...o},a){if(!e)throw Error("client_id is required");if("undefined"!=typeof window&&window.document){let i=["signin","signup","use"].includes(n)?n:"signin",c=document.createElement("script");c.src="https://accounts.google.com/gsi/client",c.async=!0,c.defer=!0,document.head.appendChild(c),window.onload=function(){window.google.accounts.id.initialize({client_id:e,callback:a,auto_select:t,cancel_on_tap_outside:r,context:i,...o}),window.google.accounts.id.prompt()}}}},24224:(e,t,r)=>{"use strict";function n(e){return(n="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(e){return typeof e}:function(e){return e&&"function"==typeof Symbol&&e.constructor===Symbol&&e!==Symbol.prototype?"symbol":typeof e})(e)}Object.defineProperty(t,"__esModule",{value:!0}),t.CopyToClipboard=void 0;var o=c(r(95361)),a=c(r(94427)),i=["text","onCopy","options","children"];function c(e){return e&&e.__esModule?e:{default:e}}function l(e,t){var r=Object.keys(e);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(e);t&&(n=n.filter(function(t){return Object.getOwnPropertyDescriptor(e,t).enumerable})),r.push.apply(r,n)}return r}function u(e){for(var t=1;t<arguments.length;t++){var r=null!=arguments[t]?arguments[t]:{};t%2?l(Object(r),!0).forEach(function(t){f(e,t,r[t])}):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(r)):l(Object(r)).forEach(function(t){Object.defineProperty(e,t,Object.getOwnPropertyDescriptor(r,t))})}return e}function s(e,t){return(s=Object.setPrototypeOf||function(e,t){return e.__proto__=t,e})(e,t)}function d(e){if(void 0===e)throw ReferenceError("this hasn't been initialised - super() hasn't been called");return e}function p(e){return(p=Object.setPrototypeOf?Object.getPrototypeOf:function(e){return e.__proto__||Object.getPrototypeOf(e)})(e)}function f(e,t,r){return t in e?Object.defineProperty(e,t,{value:r,enumerable:!0,configurable:!0,writable:!0}):e[t]=r,e}var y=function(e){!function(e,t){if("function"!=typeof t&&null!==t)throw TypeError("Super expression must either be null or a function");e.prototype=Object.create(t&&t.prototype,{constructor:{value:e,writable:!0,configurable:!0}}),Object.defineProperty(e,"prototype",{writable:!1}),t&&s(e,t)}(l,e);var t,r,c=(t=function(){if("undefined"==typeof Reflect||!Reflect.construct||Reflect.construct.sham)return!1;if("function"==typeof Proxy)return!0;try{return Boolean.prototype.valueOf.call(Reflect.construct(Boolean,[],function(){})),!0}catch(e){return!1}}(),function(){var e,r=p(l);return e=t?Reflect.construct(r,arguments,p(this).constructor):r.apply(this,arguments),function(e,t){if(t&&("object"===n(t)||"function"==typeof t))return t;if(void 0!==t)throw TypeError("Derived constructors may only return object or undefined");return d(e)}(this,e)});function l(){var e;!function(e,t){if(!(e instanceof t))throw TypeError("Cannot call a class as a function")}(this,l);for(var t=arguments.length,r=Array(t),n=0;n<t;n++)r[n]=arguments[n];return f(d(e=c.call.apply(c,[this].concat(r))),"onClick",function(t){var r=e.props,n=r.text,i=r.onCopy,c=r.children,l=r.options,u=o.default.Children.only(c),s=(0,a.default)(n,l);i&&i(n,s),u&&u.props&&"function"==typeof u.props.onClick&&u.props.onClick(t)}),e}return r=[{key:"render",value:function(){var e=this.props,t=(e.text,e.onCopy,e.options,e.children),r=function(e,t){if(null==e)return{};var r,n,o=function(e,t){if(null==e)return{};var r,n,o={},a=Object.keys(e);for(n=0;n<a.length;n++)r=a[n],t.indexOf(r)>=0||(o[r]=e[r]);return o}(e,t);if(Object.getOwnPropertySymbols){var a=Object.getOwnPropertySymbols(e);for(n=0;n<a.length;n++)r=a[n],!(t.indexOf(r)>=0)&&Object.prototype.propertyIsEnumerable.call(e,r)&&(o[r]=e[r])}return o}(e,i),n=o.default.Children.only(t);return o.default.cloneElement(n,u(u({},r),{},{onClick:this.onClick}))}}],function(e,t){for(var r=0;r<t.length;r++){var n=t[r];n.enumerable=n.enumerable||!1,n.configurable=!0,"value"in n&&(n.writable=!0),Object.defineProperty(e,n.key,n)}}(l.prototype,r),Object.defineProperty(l,"prototype",{writable:!1}),l}(o.default.PureComponent);t.CopyToClipboard=y,f(y,"defaultProps",{onCopy:void 0,options:void 0})},29131:(e,t,r)=>{"use strict";var n=r(24224).CopyToClipboard;n.CopyToClipboard=n,e.exports=n},39921:e=>{e.exports=function(){var e=document.getSelection();if(!e.rangeCount)return function(){};for(var t=document.activeElement,r=[],n=0;n<e.rangeCount;n++)r.push(e.getRangeAt(n));switch(t.tagName.toUpperCase()){case"INPUT":case"TEXTAREA":t.blur();break;default:t=null}return e.removeAllRanges(),function(){"Caret"===e.type&&e.removeAllRanges(),e.rangeCount||r.forEach(function(t){e.addRange(t)}),t&&t.focus()}}},67689:(e,t,r)=>{"use strict";r.d(t,{C1:()=>U,bL:()=>G,q7:()=>N});var n=r(95361),o=r(75030),a=r(94286),i=r(22408),c=r(72637),l=r(88295),u=r(4471),s=r(37651),d=r(38675),p=r(64595),f=r(12177),y=r(16261),v="Radio",[m,b]=(0,i.A)(v),[w,h]=m(v),g=n.forwardRef((e,t)=>{let{__scopeRadio:r,name:i,checked:l=!1,required:u,disabled:s,value:d="on",onCheck:p,form:f,...v}=e,[m,b]=n.useState(null),h=(0,a.s)(t,e=>b(e)),g=n.useRef(!1),j=!m||f||!!m.closest("form");return(0,y.jsxs)(w,{scope:r,checked:l,disabled:s,children:[(0,y.jsx)(c.sG.button,{type:"button",role:"radio","aria-checked":l,"data-state":E(l),"data-disabled":s?"":void 0,disabled:s,value:d,...v,ref:h,onClick:(0,o.m)(e.onClick,e=>{l||null==p||p(),j&&(g.current=e.isPropagationStopped(),g.current||e.stopPropagation())})}),j&&(0,y.jsx)(x,{control:m,bubbles:!g.current,name:i,value:d,checked:l,required:u,disabled:s,form:f,style:{transform:"translateX(-100%)"}})]})});g.displayName=v;var j="RadioIndicator",C=n.forwardRef((e,t)=>{let{__scopeRadio:r,forceMount:n,...o}=e,a=h(j,r);return(0,y.jsx)(f.C,{present:n||a.checked,children:(0,y.jsx)(c.sG.span,{"data-state":E(a.checked),"data-disabled":a.disabled?"":void 0,...o,ref:t})})});C.displayName=j;var x=n.forwardRef((e,t)=>{let{__scopeRadio:r,control:o,checked:i,bubbles:l=!0,...u}=e,s=n.useRef(null),f=(0,a.s)(s,t),v=(0,p.Z)(i),m=(0,d.X)(o);return n.useEffect(()=>{let e=s.current;if(!e)return;let t=Object.getOwnPropertyDescriptor(window.HTMLInputElement.prototype,"checked").set;if(v!==i&&t){let r=new Event("click",{bubbles:l});t.call(e,i),e.dispatchEvent(r)}},[v,i,l]),(0,y.jsx)(c.sG.input,{type:"radio","aria-hidden":!0,defaultChecked:i,...u,tabIndex:-1,ref:f,style:{...u.style,...m,position:"absolute",pointerEvents:"none",opacity:0,margin:0}})});function E(e){return e?"checked":"unchecked"}x.displayName="RadioBubbleInput";var R=["ArrowUp","ArrowDown","ArrowLeft","ArrowRight"],k="RadioGroup",[O,S]=(0,i.A)(k,[l.RG,b]),D=(0,l.RG)(),P=b(),[A,_]=O(k),T=n.forwardRef((e,t)=>{let{__scopeRadioGroup:r,name:n,defaultValue:o,value:a,required:i=!1,disabled:d=!1,orientation:p,dir:f,loop:v=!0,onValueChange:m,...b}=e,w=D(r),h=(0,s.jH)(f),[g,j]=(0,u.i)({prop:a,defaultProp:null!=o?o:null,onChange:m,caller:k});return(0,y.jsx)(A,{scope:r,name:n,required:i,disabled:d,value:g,onValueChange:j,children:(0,y.jsx)(l.bL,{asChild:!0,...w,orientation:p,dir:h,loop:v,children:(0,y.jsx)(c.sG.div,{role:"radiogroup","aria-required":i,"aria-orientation":p,"data-disabled":d?"":void 0,dir:h,...b,ref:t})})})});T.displayName=k;var I="RadioGroupItem",F=n.forwardRef((e,t)=>{let{__scopeRadioGroup:r,disabled:i,...c}=e,u=_(I,r),s=u.disabled||i,d=D(r),p=P(r),f=n.useRef(null),v=(0,a.s)(t,f),m=u.value===c.value,b=n.useRef(!1);return n.useEffect(()=>{let e=e=>{R.includes(e.key)&&(b.current=!0)},t=()=>b.current=!1;return document.addEventListener("keydown",e),document.addEventListener("keyup",t),()=>{document.removeEventListener("keydown",e),document.removeEventListener("keyup",t)}},[]),(0,y.jsx)(l.q7,{asChild:!0,...d,focusable:!s,active:m,children:(0,y.jsx)(g,{disabled:s,required:u.required,checked:m,...p,...c,name:u.name,ref:v,onCheck:()=>u.onValueChange(c.value),onKeyDown:(0,o.m)(e=>{"Enter"===e.key&&e.preventDefault()}),onFocus:(0,o.m)(c.onFocus,()=>{var e;b.current&&(null===(e=f.current)||void 0===e||e.click())})})})});F.displayName=I;var L=n.forwardRef((e,t)=>{let{__scopeRadioGroup:r,...n}=e,o=P(r);return(0,y.jsx)(C,{...o,...n,ref:t})});L.displayName="RadioGroupIndicator";var G=T,N=F,U=L},77666:(e,t,r)=>{"use strict";r.d(t,{c:()=>b});var n,o="https://js.stripe.com",a="".concat(o,"/v3"),i=/^https:\/\/js\.stripe\.com\/v3\/?(\?.*)?$/,c=/^https:\/\/js\.stripe\.com\/(v3|[a-z]+)\/stripe\.js(\?.*)?$/,l=function(){for(var e=document.querySelectorAll('script[src^="'.concat(o,'"]')),t=0;t<e.length;t++){var r,n=e[t];if(r=n.src,i.test(r)||c.test(r))return n}return null},u=function(e){var t=e&&!e.advancedFraudSignals?"?advancedFraudSignals=false":"",r=document.createElement("script");r.src="".concat(a).concat(t);var n=document.head||document.body;if(!n)throw Error("Expected document.body not to be null. Stripe.js requires a <body> element.");return n.appendChild(r),r},s=function(e,t){e&&e._registerWrapper&&e._registerWrapper({name:"stripe-js",version:"5.10.0",startTime:t})},d=null,p=null,f=null,y=function(e,t,r){if(null===e)return null;var n,o=t[0].match(/^pk_test/),a=3===(n=e.version)?"v3":n;o&&"v3"!==a&&console.warn("Stripe.js@".concat(a," was loaded on the page, but @stripe/stripe-js@").concat("5.10.0"," expected Stripe.js@").concat("v3",". This may result in unexpected behavior. For more information, see https://docs.stripe.com/sdks/stripejs-versioning"));var i=e.apply(void 0,t);return s(i,r),i},v=!1,m=function(){return n?n:n=(null!==d?d:(d=new Promise(function(e,t){if("undefined"==typeof window||"undefined"==typeof document){e(null);return}if(window.Stripe,window.Stripe){e(window.Stripe);return}try{var r,n=l();n?n&&null!==f&&null!==p&&(n.removeEventListener("load",f),n.removeEventListener("error",p),null===(r=n.parentNode)||void 0===r||r.removeChild(n),n=u(null)):n=u(null),f=function(){window.Stripe?e(window.Stripe):t(Error("Stripe.js not available"))},p=function(e){t(Error("Failed to load Stripe.js",{cause:e}))},n.addEventListener("load",f),n.addEventListener("error",p)}catch(e){t(e);return}})).catch(function(e){return d=null,Promise.reject(e)})).catch(function(e){return n=null,Promise.reject(e)})};Promise.resolve().then(function(){return m()}).catch(function(e){v||console.warn(e)});var b=function(){for(var e=arguments.length,t=Array(e),r=0;r<e;r++)t[r]=arguments[r];v=!0;var n=Date.now();return m().then(function(e){return y(e,t,n)})}},88295:(e,t,r)=>{"use strict";r.d(t,{RG:()=>j,bL:()=>P,q7:()=>A});var n=r(95361),o=r(75030),a=r(77002),i=r(94286),c=r(22408),l=r(25747),u=r(72637),s=r(21751),d=r(4471),p=r(37651),f=r(16261),y="rovingFocusGroup.onEntryFocus",v={bubbles:!1,cancelable:!0},m="RovingFocusGroup",[b,w,h]=(0,a.N)(m),[g,j]=(0,c.A)(m,[h]),[C,x]=g(m),E=n.forwardRef((e,t)=>(0,f.jsx)(b.Provider,{scope:e.__scopeRovingFocusGroup,children:(0,f.jsx)(b.Slot,{scope:e.__scopeRovingFocusGroup,children:(0,f.jsx)(R,{...e,ref:t})})}));E.displayName=m;var R=n.forwardRef((e,t)=>{let{__scopeRovingFocusGroup:r,orientation:a,loop:c=!1,dir:l,currentTabStopId:b,defaultCurrentTabStopId:h,onCurrentTabStopIdChange:g,onEntryFocus:j,preventScrollOnEntryFocus:x=!1,...E}=e,R=n.useRef(null),k=(0,i.s)(t,R),O=(0,p.jH)(l),[S,P]=(0,d.i)({prop:b,defaultProp:null!=h?h:null,onChange:g,caller:m}),[A,_]=n.useState(!1),T=(0,s.c)(j),I=w(r),F=n.useRef(!1),[L,G]=n.useState(0);return n.useEffect(()=>{let e=R.current;if(e)return e.addEventListener(y,T),()=>e.removeEventListener(y,T)},[T]),(0,f.jsx)(C,{scope:r,orientation:a,dir:O,loop:c,currentTabStopId:S,onItemFocus:n.useCallback(e=>P(e),[P]),onItemShiftTab:n.useCallback(()=>_(!0),[]),onFocusableItemAdd:n.useCallback(()=>G(e=>e+1),[]),onFocusableItemRemove:n.useCallback(()=>G(e=>e-1),[]),children:(0,f.jsx)(u.sG.div,{tabIndex:A||0===L?-1:0,"data-orientation":a,...E,ref:k,style:{outline:"none",...e.style},onMouseDown:(0,o.m)(e.onMouseDown,()=>{F.current=!0}),onFocus:(0,o.m)(e.onFocus,e=>{let t=!F.current;if(e.target===e.currentTarget&&t&&!A){let t=new CustomEvent(y,v);if(e.currentTarget.dispatchEvent(t),!t.defaultPrevented){let e=I().filter(e=>e.focusable);D([e.find(e=>e.active),e.find(e=>e.id===S),...e].filter(Boolean).map(e=>e.ref.current),x)}}F.current=!1}),onBlur:(0,o.m)(e.onBlur,()=>_(!1))})})}),k="RovingFocusGroupItem",O=n.forwardRef((e,t)=>{let{__scopeRovingFocusGroup:r,focusable:a=!0,active:i=!1,tabStopId:c,children:s,...d}=e,p=(0,l.B)(),y=c||p,v=x(k,r),m=v.currentTabStopId===y,h=w(r),{onFocusableItemAdd:g,onFocusableItemRemove:j,currentTabStopId:C}=v;return n.useEffect(()=>{if(a)return g(),()=>j()},[a,g,j]),(0,f.jsx)(b.ItemSlot,{scope:r,id:y,focusable:a,active:i,children:(0,f.jsx)(u.sG.span,{tabIndex:m?0:-1,"data-orientation":v.orientation,...d,ref:t,onMouseDown:(0,o.m)(e.onMouseDown,e=>{a?v.onItemFocus(y):e.preventDefault()}),onFocus:(0,o.m)(e.onFocus,()=>v.onItemFocus(y)),onKeyDown:(0,o.m)(e.onKeyDown,e=>{if("Tab"===e.key&&e.shiftKey){v.onItemShiftTab();return}if(e.target!==e.currentTarget)return;let t=function(e,t,r){var n;let o=(n=e.key,"rtl"!==r?n:"ArrowLeft"===n?"ArrowRight":"ArrowRight"===n?"ArrowLeft":n);if(!("vertical"===t&&["ArrowLeft","ArrowRight"].includes(o))&&!("horizontal"===t&&["ArrowUp","ArrowDown"].includes(o)))return S[o]}(e,v.orientation,v.dir);if(void 0!==t){if(e.metaKey||e.ctrlKey||e.altKey||e.shiftKey)return;e.preventDefault();let r=h().filter(e=>e.focusable).map(e=>e.ref.current);if("last"===t)r.reverse();else if("prev"===t||"next"===t){"prev"===t&&r.reverse();let n=r.indexOf(e.currentTarget);r=v.loop?function(e,t){return e.map((r,n)=>e[(t+n)%e.length])}(r,n+1):r.slice(n+1)}setTimeout(()=>D(r))}}),children:"function"==typeof s?s({isCurrentTabStop:m,hasTabStop:null!=C}):s})})});O.displayName=k;var S={ArrowLeft:"prev",ArrowUp:"prev",ArrowRight:"next",ArrowDown:"next",PageUp:"first",Home:"first",PageDown:"last",End:"last"};function D(e){let t=arguments.length>1&&void 0!==arguments[1]&&arguments[1],r=document.activeElement;for(let n of e)if(n===r||(n.focus({preventScroll:t}),document.activeElement!==r))return}var P=E,A=O},91571:(e,t,r)=>{"use strict";r.d(t,{A:()=>n});let n=(0,r(1316).A)("Circle",[["circle",{cx:"12",cy:"12",r:"10",key:"1mglay"}]])},94427:(e,t,r)=>{"use strict";var n=r(39921),o={"text/plain":"Text","text/html":"Url",default:"Text"};e.exports=function(e,t){var r,a,i,c,l,u,s,d,p=!1;t||(t={}),i=t.debug||!1;try{if(l=n(),u=document.createRange(),s=document.getSelection(),(d=document.createElement("span")).textContent=e,d.ariaHidden="true",d.style.all="unset",d.style.position="fixed",d.style.top=0,d.style.clip="rect(0, 0, 0, 0)",d.style.whiteSpace="pre",d.style.webkitUserSelect="text",d.style.MozUserSelect="text",d.style.msUserSelect="text",d.style.userSelect="text",d.addEventListener("copy",function(r){if(r.stopPropagation(),t.format){if(r.preventDefault(),void 0===r.clipboardData){i&&console.warn("unable to use e.clipboardData"),i&&console.warn("trying IE specific stuff"),window.clipboardData.clearData();var n=o[t.format]||o.default;window.clipboardData.setData(n,e)}else r.clipboardData.clearData(),r.clipboardData.setData(t.format,e)}t.onCopy&&(r.preventDefault(),t.onCopy(r.clipboardData))}),document.body.appendChild(d),u.selectNodeContents(d),s.addRange(u),!document.execCommand("copy"))throw Error("copy command was unsuccessful");p=!0}catch(n){i&&console.error("unable to copy using execCommand: ",n),i&&console.warn("trying IE specific stuff");try{window.clipboardData.setData(t.format||"text",e),t.onCopy&&t.onCopy(window.clipboardData),p=!0}catch(n){i&&console.error("unable to copy using clipboardData: ",n),i&&console.error("falling back to prompt"),r="message"in t?t.message:"Copy to clipboard: #{key}, Enter",a=(/mac os x/i.test(navigator.userAgent)?"⌘":"Ctrl")+"+C",c=r.replace(/#{\s*key\s*}/g,a),window.prompt(c,e)}}finally{s&&("function"==typeof s.removeRange?s.removeRange(u):s.removeAllRanges()),d&&document.body.removeChild(d),l()}return p}}}]);