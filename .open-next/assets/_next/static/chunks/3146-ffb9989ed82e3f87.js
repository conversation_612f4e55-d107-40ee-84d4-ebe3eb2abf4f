(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[3146,4459],{3932:(e,t,r)=>{"use strict";r.d(t,{default:()=>l});var n=r(12482),o=r(89799),i=r(95361),c=r(6002),a=r(68926),u=r(16261),l=(0,i.forwardRef)(function(e,t){let{href:r,locale:i,localeCookie:l,onClick:s,prefetch:f,...p}=e,d=(0,c.Ym)(),y=null!=i&&i!==d,m=(0,o.usePathname)();return y&&(f=!1),(0,u.jsx)(n,{ref:t,href:r,hrefLang:y?i:void 0,onClick:function(e){(0,a.A)(l,m,d,i),s&&s(e)},prefetch:f,...p})})},24224:(e,t,r)=>{"use strict";function n(e){return(n="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(e){return typeof e}:function(e){return e&&"function"==typeof Symbol&&e.constructor===Symbol&&e!==Symbol.prototype?"symbol":typeof e})(e)}Object.defineProperty(t,"__esModule",{value:!0}),t.CopyToClipboard=void 0;var o=a(r(95361)),i=a(r(94427)),c=["text","onCopy","options","children"];function a(e){return e&&e.__esModule?e:{default:e}}function u(e,t){var r=Object.keys(e);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(e);t&&(n=n.filter(function(t){return Object.getOwnPropertyDescriptor(e,t).enumerable})),r.push.apply(r,n)}return r}function l(e){for(var t=1;t<arguments.length;t++){var r=null!=arguments[t]?arguments[t]:{};t%2?u(Object(r),!0).forEach(function(t){d(e,t,r[t])}):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(r)):u(Object(r)).forEach(function(t){Object.defineProperty(e,t,Object.getOwnPropertyDescriptor(r,t))})}return e}function s(e,t){return(s=Object.setPrototypeOf||function(e,t){return e.__proto__=t,e})(e,t)}function f(e){if(void 0===e)throw ReferenceError("this hasn't been initialised - super() hasn't been called");return e}function p(e){return(p=Object.setPrototypeOf?Object.getPrototypeOf:function(e){return e.__proto__||Object.getPrototypeOf(e)})(e)}function d(e,t,r){return t in e?Object.defineProperty(e,t,{value:r,enumerable:!0,configurable:!0,writable:!0}):e[t]=r,e}var y=function(e){!function(e,t){if("function"!=typeof t&&null!==t)throw TypeError("Super expression must either be null or a function");e.prototype=Object.create(t&&t.prototype,{constructor:{value:e,writable:!0,configurable:!0}}),Object.defineProperty(e,"prototype",{writable:!1}),t&&s(e,t)}(u,e);var t,r,a=(t=function(){if("undefined"==typeof Reflect||!Reflect.construct||Reflect.construct.sham)return!1;if("function"==typeof Proxy)return!0;try{return Boolean.prototype.valueOf.call(Reflect.construct(Boolean,[],function(){})),!0}catch(e){return!1}}(),function(){var e,r=p(u);return e=t?Reflect.construct(r,arguments,p(this).constructor):r.apply(this,arguments),function(e,t){if(t&&("object"===n(t)||"function"==typeof t))return t;if(void 0!==t)throw TypeError("Derived constructors may only return object or undefined");return f(e)}(this,e)});function u(){var e;!function(e,t){if(!(e instanceof t))throw TypeError("Cannot call a class as a function")}(this,u);for(var t=arguments.length,r=Array(t),n=0;n<t;n++)r[n]=arguments[n];return d(f(e=a.call.apply(a,[this].concat(r))),"onClick",function(t){var r=e.props,n=r.text,c=r.onCopy,a=r.children,u=r.options,l=o.default.Children.only(a),s=(0,i.default)(n,u);c&&c(n,s),l&&l.props&&"function"==typeof l.props.onClick&&l.props.onClick(t)}),e}return r=[{key:"render",value:function(){var e=this.props,t=(e.text,e.onCopy,e.options,e.children),r=function(e,t){if(null==e)return{};var r,n,o=function(e,t){if(null==e)return{};var r,n,o={},i=Object.keys(e);for(n=0;n<i.length;n++)r=i[n],t.indexOf(r)>=0||(o[r]=e[r]);return o}(e,t);if(Object.getOwnPropertySymbols){var i=Object.getOwnPropertySymbols(e);for(n=0;n<i.length;n++)r=i[n],!(t.indexOf(r)>=0)&&Object.prototype.propertyIsEnumerable.call(e,r)&&(o[r]=e[r])}return o}(e,c),n=o.default.Children.only(t);return o.default.cloneElement(n,l(l({},r),{},{onClick:this.onClick}))}}],function(e,t){for(var r=0;r<t.length;r++){var n=t[r];n.enumerable=n.enumerable||!1,n.configurable=!0,"value"in n&&(n.writable=!0),Object.defineProperty(e,n.key,n)}}(u.prototype,r),Object.defineProperty(u,"prototype",{writable:!1}),u}(o.default.PureComponent);t.CopyToClipboard=y,d(y,"defaultProps",{onCopy:void 0,options:void 0})},29131:(e,t,r)=>{"use strict";var n=r(24224).CopyToClipboard;n.CopyToClipboard=n,e.exports=n},32276:(e,t,r)=>{"use strict";r.d(t,{A:()=>n});let n=(0,r(1316).A)("Ellipsis",[["circle",{cx:"12",cy:"12",r:"1",key:"41hilf"}],["circle",{cx:"19",cy:"12",r:"1",key:"1wjl8i"}],["circle",{cx:"5",cy:"12",r:"1",key:"1pcz8c"}]])},39921:e=>{e.exports=function(){var e=document.getSelection();if(!e.rangeCount)return function(){};for(var t=document.activeElement,r=[],n=0;n<e.rangeCount;n++)r.push(e.getRangeAt(n));switch(t.tagName.toUpperCase()){case"INPUT":case"TEXTAREA":t.blur();break;default:t=null}return e.removeAllRanges(),function(){"Caret"===e.type&&e.removeAllRanges(),e.rangeCount||r.forEach(function(t){e.addRange(t)}),t&&t.focus()}}},44756:(e,t,r)=>{"use strict";r.d(t,{k5:()=>s});var n=r(95361),o={color:void 0,size:void 0,className:void 0,style:void 0,attr:void 0},i=n.createContext&&n.createContext(o),c=["attr","size","title"];function a(){return(a=Object.assign?Object.assign.bind():function(e){for(var t=1;t<arguments.length;t++){var r=arguments[t];for(var n in r)Object.prototype.hasOwnProperty.call(r,n)&&(e[n]=r[n])}return e}).apply(this,arguments)}function u(e,t){var r=Object.keys(e);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(e);t&&(n=n.filter(function(t){return Object.getOwnPropertyDescriptor(e,t).enumerable})),r.push.apply(r,n)}return r}function l(e){for(var t=1;t<arguments.length;t++){var r=null!=arguments[t]?arguments[t]:{};t%2?u(Object(r),!0).forEach(function(t){var n,o,i;n=e,o=t,i=r[t],(o=function(e){var t=function(e,t){if("object"!=typeof e||!e)return e;var r=e[Symbol.toPrimitive];if(void 0!==r){var n=r.call(e,t||"default");if("object"!=typeof n)return n;throw TypeError("@@toPrimitive must return a primitive value.")}return("string"===t?String:Number)(e)}(e,"string");return"symbol"==typeof t?t:t+""}(o))in n?Object.defineProperty(n,o,{value:i,enumerable:!0,configurable:!0,writable:!0}):n[o]=i}):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(r)):u(Object(r)).forEach(function(t){Object.defineProperty(e,t,Object.getOwnPropertyDescriptor(r,t))})}return e}function s(e){return t=>n.createElement(f,a({attr:l({},e.attr)},t),function e(t){return t&&t.map((t,r)=>n.createElement(t.tag,l({key:r},t.attr),e(t.child)))}(e.child))}function f(e){var t=t=>{var r,{attr:o,size:i,title:u}=e,s=function(e,t){if(null==e)return{};var r,n,o=function(e,t){if(null==e)return{};var r={};for(var n in e)if(Object.prototype.hasOwnProperty.call(e,n)){if(t.indexOf(n)>=0)continue;r[n]=e[n]}return r}(e,t);if(Object.getOwnPropertySymbols){var i=Object.getOwnPropertySymbols(e);for(n=0;n<i.length;n++)r=i[n],!(t.indexOf(r)>=0)&&Object.prototype.propertyIsEnumerable.call(e,r)&&(o[r]=e[r])}return o}(e,c),f=i||t.size||"1em";return t.className&&(r=t.className),e.className&&(r=(r?r+" ":"")+e.className),n.createElement("svg",a({stroke:"currentColor",fill:"currentColor",strokeWidth:"0"},t.attr,o,s,{className:r,style:l(l({color:e.color||t.color},t.style),e.style),height:f,width:f,xmlns:"http://www.w3.org/2000/svg"}),u&&n.createElement("title",null,u),e.children)};return void 0!==i?n.createElement(i.Consumer,null,e=>t(e)):t(o)}},50349:(e,t,r)=>{"use strict";r.d(t,{FD:()=>v,MY:()=>i,PJ:()=>c,Wl:()=>u,XP:()=>f,_x:()=>o,bL:()=>p,po:()=>l,ql:()=>s,wO:()=>a,yL:()=>h});var n=r(55036);function o(e){return("object"==typeof e?null==e.host&&null==e.hostname:!/^[a-z]+:/i.test(e))&&!function(e){let t="object"==typeof e?e.pathname:e;return null!=t&&!t.startsWith("/")}(e)}function i(e,t){return e.replace(RegExp(`^${t}`),"")||"/"}function c(e,t){let r=e;return/^\/(\?.*)?$/.test(t)&&(t=t.slice(1)),r+=t}function a(e,t){return t===e||t.startsWith(`${e}/`)}function u(e,t,r){return"string"==typeof e?e:e[t]||r}function l(e){let t=function(){try{return"true"===n.env._next_intl_trailing_slash}catch{return!1}}(),[r,...o]=e.split("#"),i=o.join("#"),c=r;if("/"!==c){let e=c.endsWith("/");t&&!e?c+="/":!t&&e&&(c=c.slice(0,-1))}return i&&(c+="#"+i),c}function s(e,t){let r=l(e),n=l(t);return(function(e){let t=e.replace(/\[\[(\.\.\.[^\]]+)\]\]/g,"?(.*)").replace(/\[(\.\.\.[^\]]+)\]/g,"(.+)").replace(/\[([^\]]+)\]/g,"([^/]+)");return RegExp(`^${t}$`)})(r).test(n)}function f(e,t){return"never"!==t.mode&&t.prefixes?.[e]||p(e)}function p(e){return"/"+e}function d(e){return e.includes("[[...")}function y(e){return e.includes("[...")}function m(e){return e.includes("[")}function b(e,t){let r=e.split("/"),n=t.split("/"),o=Math.max(r.length,n.length);for(let e=0;e<o;e++){let t=r[e],o=n[e];if(!t&&o)return -1;if(t&&!o)return 1;if(t||o){if(!m(t)&&m(o))return -1;if(m(t)&&!m(o))return 1;if(!y(t)&&y(o))return -1;if(y(t)&&!y(o))return 1;if(!d(t)&&d(o))return -1;if(d(t)&&!d(o))return 1}}return 0}function v(e){return e.sort(b)}function h(e){return"function"==typeof e.then}},68926:(e,t,r)=>{"use strict";r.d(t,{A:()=>o});var n=r(88454);function o(e,t,r,o){if(!e||o===r||null==o||!t)return;let i=(0,n.DT)(t),{name:c,...a}=e;a.path||(a.path=""!==i?i:"/");let u=`${c}=${o};`;for(let[e,t]of Object.entries(a))u+=`${"maxAge"===e?"max-age":e}`,"boolean"!=typeof t&&(u+="="+t),u+=";";document.cookie=u}},88295:(e,t,r)=>{"use strict";r.d(t,{RG:()=>O,bL:()=>k,q7:()=>A});var n=r(95361),o=r(75030),i=r(77002),c=r(94286),a=r(22408),u=r(25747),l=r(72637),s=r(21751),f=r(4471),p=r(37651),d=r(16261),y="rovingFocusGroup.onEntryFocus",m={bubbles:!1,cancelable:!0},b="RovingFocusGroup",[v,h,g]=(0,i.N)(b),[w,O]=(0,a.A)(b,[g]),[j,x]=w(b),P=n.forwardRef((e,t)=>(0,d.jsx)(v.Provider,{scope:e.__scopeRovingFocusGroup,children:(0,d.jsx)(v.Slot,{scope:e.__scopeRovingFocusGroup,children:(0,d.jsx)(C,{...e,ref:t})})}));P.displayName=b;var C=n.forwardRef((e,t)=>{let{__scopeRovingFocusGroup:r,orientation:i,loop:a=!1,dir:u,currentTabStopId:v,defaultCurrentTabStopId:g,onCurrentTabStopIdChange:w,onEntryFocus:O,preventScrollOnEntryFocus:x=!1,...P}=e,C=n.useRef(null),E=(0,c.s)(t,C),R=(0,p.jH)(u),[D,k]=(0,f.i)({prop:v,defaultProp:null!=g?g:null,onChange:w,caller:b}),[A,T]=n.useState(!1),_=(0,s.c)(O),I=h(r),F=n.useRef(!1),[N,L]=n.useState(0);return n.useEffect(()=>{let e=C.current;if(e)return e.addEventListener(y,_),()=>e.removeEventListener(y,_)},[_]),(0,d.jsx)(j,{scope:r,orientation:i,dir:R,loop:a,currentTabStopId:D,onItemFocus:n.useCallback(e=>k(e),[k]),onItemShiftTab:n.useCallback(()=>T(!0),[]),onFocusableItemAdd:n.useCallback(()=>L(e=>e+1),[]),onFocusableItemRemove:n.useCallback(()=>L(e=>e-1),[]),children:(0,d.jsx)(l.sG.div,{tabIndex:A||0===N?-1:0,"data-orientation":i,...P,ref:E,style:{outline:"none",...e.style},onMouseDown:(0,o.m)(e.onMouseDown,()=>{F.current=!0}),onFocus:(0,o.m)(e.onFocus,e=>{let t=!F.current;if(e.target===e.currentTarget&&t&&!A){let t=new CustomEvent(y,m);if(e.currentTarget.dispatchEvent(t),!t.defaultPrevented){let e=I().filter(e=>e.focusable);S([e.find(e=>e.active),e.find(e=>e.id===D),...e].filter(Boolean).map(e=>e.ref.current),x)}}F.current=!1}),onBlur:(0,o.m)(e.onBlur,()=>T(!1))})})}),E="RovingFocusGroupItem",R=n.forwardRef((e,t)=>{let{__scopeRovingFocusGroup:r,focusable:i=!0,active:c=!1,tabStopId:a,children:s,...f}=e,p=(0,u.B)(),y=a||p,m=x(E,r),b=m.currentTabStopId===y,g=h(r),{onFocusableItemAdd:w,onFocusableItemRemove:O,currentTabStopId:j}=m;return n.useEffect(()=>{if(i)return w(),()=>O()},[i,w,O]),(0,d.jsx)(v.ItemSlot,{scope:r,id:y,focusable:i,active:c,children:(0,d.jsx)(l.sG.span,{tabIndex:b?0:-1,"data-orientation":m.orientation,...f,ref:t,onMouseDown:(0,o.m)(e.onMouseDown,e=>{i?m.onItemFocus(y):e.preventDefault()}),onFocus:(0,o.m)(e.onFocus,()=>m.onItemFocus(y)),onKeyDown:(0,o.m)(e.onKeyDown,e=>{if("Tab"===e.key&&e.shiftKey){m.onItemShiftTab();return}if(e.target!==e.currentTarget)return;let t=function(e,t,r){var n;let o=(n=e.key,"rtl"!==r?n:"ArrowLeft"===n?"ArrowRight":"ArrowRight"===n?"ArrowLeft":n);if(!("vertical"===t&&["ArrowLeft","ArrowRight"].includes(o))&&!("horizontal"===t&&["ArrowUp","ArrowDown"].includes(o)))return D[o]}(e,m.orientation,m.dir);if(void 0!==t){if(e.metaKey||e.ctrlKey||e.altKey||e.shiftKey)return;e.preventDefault();let r=g().filter(e=>e.focusable).map(e=>e.ref.current);if("last"===t)r.reverse();else if("prev"===t||"next"===t){"prev"===t&&r.reverse();let n=r.indexOf(e.currentTarget);r=m.loop?function(e,t){return e.map((r,n)=>e[(t+n)%e.length])}(r,n+1):r.slice(n+1)}setTimeout(()=>S(r))}}),children:"function"==typeof s?s({isCurrentTabStop:b,hasTabStop:null!=j}):s})})});R.displayName=E;var D={ArrowLeft:"prev",ArrowUp:"prev",ArrowRight:"next",ArrowDown:"next",PageUp:"first",Home:"first",PageDown:"last",End:"last"};function S(e){let t=arguments.length>1&&void 0!==arguments[1]&&arguments[1],r=document.activeElement;for(let n of e)if(n===r||(n.focus({preventScroll:t}),document.activeElement!==r))return}var k=P,A=R},88454:(e,t,r)=>{"use strict";r.d(t,{DT:()=>u,FP:()=>c,TK:()=>o,Zn:()=>i,aM:()=>a,x3:()=>l});var n=r(50349);function o(e){return"string"==typeof e?{pathname:e}:e}function i(e){let t=new URLSearchParams;for(let[r,n]of Object.entries(e))Array.isArray(n)?n.forEach(e=>{t.append(r,String(e))}):t.set(r,String(n));return"?"+t.toString()}function c({pathname:e,locale:t,params:r,pathnames:o,query:c}){function a(e){let a;let u=o[e];return u?(a=(0,n.Wl)(u,t,e),r&&Object.entries(r).forEach(([e,t])=>{let r,n;Array.isArray(t)?(r=`(\\[)?\\[...${e}\\](\\])?`,n=t.map(e=>String(e)).join("/")):(r=`\\[${e}\\]`,n=String(t)),a=a.replace(RegExp(r,"g"),n)}),a=(a=a.replace(/\[\[\.\.\..+\]\]/g,"")).split("/").map(e=>encodeURIComponent(e)).join("/")):a=e,a=(0,n.po)(a),c&&(a+=i(c)),a}if("string"==typeof e)return a(e);{let{pathname:t,...r}=e;return{...r,pathname:a(t)}}}function a(e,t,r){let o=(0,n.FD)(Object.keys(r)),i=decodeURI(t);for(let t of o){let o=r[t];if("string"==typeof o){if((0,n.ql)(o,i))return t}else if((0,n.ql)((0,n.Wl)(o,e,t),i))return t}return t}function u(e,t=window.location.pathname){return"/"===e?t:t.replace(e,"")}function l(e,t,r,o){let i;let{mode:c}=r.localePrefix;return void 0!==o?i=o:(0,n._x)(e)&&("always"===c?i=!0:"as-needed"===c&&(i=r.domains?!r.domains.some(e=>e.defaultLocale===t):t!==r.defaultLocale)),i?(0,n.PJ)((0,n.XP)(t,r.localePrefix),e):e}},89799:(e,t,r)=>{"use strict";var n=r(8911);r.o(n,"permanentRedirect")&&r.d(t,{permanentRedirect:function(){return n.permanentRedirect}}),r.o(n,"redirect")&&r.d(t,{redirect:function(){return n.redirect}}),r.o(n,"useParams")&&r.d(t,{useParams:function(){return n.useParams}}),r.o(n,"usePathname")&&r.d(t,{usePathname:function(){return n.usePathname}}),r.o(n,"useRouter")&&r.d(t,{useRouter:function(){return n.useRouter}})},94427:(e,t,r)=>{"use strict";var n=r(39921),o={"text/plain":"Text","text/html":"Url",default:"Text"};e.exports=function(e,t){var r,i,c,a,u,l,s,f,p=!1;t||(t={}),c=t.debug||!1;try{if(u=n(),l=document.createRange(),s=document.getSelection(),(f=document.createElement("span")).textContent=e,f.ariaHidden="true",f.style.all="unset",f.style.position="fixed",f.style.top=0,f.style.clip="rect(0, 0, 0, 0)",f.style.whiteSpace="pre",f.style.webkitUserSelect="text",f.style.MozUserSelect="text",f.style.msUserSelect="text",f.style.userSelect="text",f.addEventListener("copy",function(r){if(r.stopPropagation(),t.format){if(r.preventDefault(),void 0===r.clipboardData){c&&console.warn("unable to use e.clipboardData"),c&&console.warn("trying IE specific stuff"),window.clipboardData.clearData();var n=o[t.format]||o.default;window.clipboardData.setData(n,e)}else r.clipboardData.clearData(),r.clipboardData.setData(t.format,e)}t.onCopy&&(r.preventDefault(),t.onCopy(r.clipboardData))}),document.body.appendChild(f),l.selectNodeContents(f),s.addRange(l),!document.execCommand("copy"))throw Error("copy command was unsuccessful");p=!0}catch(n){c&&console.error("unable to copy using execCommand: ",n),c&&console.warn("trying IE specific stuff");try{window.clipboardData.setData(t.format||"text",e),t.onCopy&&t.onCopy(window.clipboardData),p=!0}catch(n){c&&console.error("unable to copy using clipboardData: ",n),c&&console.error("falling back to prompt"),r="message"in t?t.message:"Copy to clipboard: #{key}, Enter",i=(/mac os x/i.test(navigator.userAgent)?"⌘":"Ctrl")+"+C",a=r.replace(/#{\s*key\s*}/g,i),window.prompt(a,e)}}finally{s&&("function"==typeof s.removeRange?s.removeRange(l):s.removeAllRanges()),f&&document.body.removeChild(f),u()}return p}}}]);