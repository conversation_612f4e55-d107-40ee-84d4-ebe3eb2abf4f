"use strict";(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[6343],{17184:(e,t,r)=>{r.d(t,{PW:()=>s,np:()=>i,pr:()=>a});var l=r(63843);let a=e=>{let t=localStorage.getItem(e);if(!t)return null;let r=t.split(":");if(!r||r.length<2)return null;let a=Number(r[0]),s=(0,l.lg)();if(-1!==a&&a<s)return i(e),null;let n=r[0]+":";return t.replace(n,"")},s=(e,t,r)=>{localStorage.setItem(e,r+":"+t)},i=e=>{localStorage.removeItem(e)}},21608:(e,t,r)=>{r.d(t,{AppContextProvider:()=>p,U:()=>f});var l=r(16261),a=r(95361),s=r(17184),i=r(88999),n=r(96576),o=r.n(n),c=r(5657),d=r.n(c),u=r(80372);let m=(0,a.createContext)({}),f=()=>(0,a.useContext)(m),p=e=>{let{children:t}=e;!function(){let{data:e,status:t}=(0,u.wV)(),r=async function(){d()({client_id:"1011821969948-i0orej6mbfqon33v3klabnto7g49mvrh.apps.googleusercontent.com",auto_select:!1,cancel_on_tap_outside:!1,context:"signin"},e=>{console.log("onetap login ok",e),s(e.credential)})},s=async function(e){console.log("signIn ok",await (0,u.Jv)("google-one-tap",{credential:e,redirect:!1}))};(0,a.useEffect)(()=>{if("unauthenticated"===t){r();let e=setInterval(()=>{r()},3e3);return()=>{clearInterval(e)}}},[t]),l.Fragment}();let{data:r}=(0,u.wV)(),[n,c]=(0,a.useState)(()=>"light"),[f,p]=(0,a.useState)(!1),[h,x]=(0,a.useState)(null),[g,b]=(0,a.useState)(!1),v=async function(){try{let e=await fetch("/api/get-user-info",{method:"POST"});if(!e.ok)throw Error("fetch user info failed with status: "+e.status);let{code:t,message:r,data:l}=await e.json();if(0!==t)throw Error(r);x(l),j(l)}catch(e){console.log("fetch user info failed")}},j=async e=>{try{if(e.invited_by){console.log("user already been invited",e.invited_by);return}let t=(0,s.pr)(i.wD.InviteCode);if(!t)return;let r=o()(e.created_at).unix(),l=o()().unix(),a=Number(l-r);if(a<=0||a>7200){console.log("user created more than 2 hours");return}console.log("update invite",t,e.uuid);let n={invite_code:t,user_uuid:e.uuid},c=await fetch("/api/update-invite",{method:"POST",headers:{"Content-Type":"application/json"},body:JSON.stringify(n)});if(!c.ok)throw Error("update invite failed with status: "+c.status);let{code:d,message:u,data:m}=await c.json();if(0!==d)throw Error(u);x(m),(0,s.np)(i.wD.InviteCode)}catch(e){console.log("update invite failed: ",e)}};return(0,a.useEffect)(()=>{r&&r.user&&v()},[r]),(0,l.jsx)(m.Provider,{value:{theme:n,setTheme:c,showSignModal:f,setShowSignModal:p,user:h,setUser:x,showFeedback:g,setShowFeedback:b},children:t})}},63843:(e,t,r)=>{r.d(t,{lg:()=>l});let l=()=>Date.parse(new Date().toUTCString())/1e3},66343:(e,t,r)=>{r.d(t,{default:()=>v});var l=r(16261),a=r(69397),s=r(64852),i=r(95361),n=r(67689),o=r(91571),c=r(61063);let d=i.forwardRef((e,t)=>{let{className:r,...a}=e;return(0,l.jsx)(n.bL,{className:(0,c.cn)("grid gap-2",r),...a,ref:t})});d.displayName=n.bL.displayName;let u=i.forwardRef((e,t)=>{let{className:r,...a}=e;return(0,l.jsx)(n.q7,{ref:t,className:(0,c.cn)("aspect-square h-4 w-4 rounded-full border border-primary text-primary ring-offset-background focus:outline-hidden focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:cursor-not-allowed disabled:opacity-50",r),...a,children:(0,l.jsx)(n.C1,{className:"flex items-center justify-center",children:(0,l.jsx)(o.A,{className:"h-2.5 w-2.5 fill-current text-current"})})})});u.displayName=n.q7.displayName;var m=r(60025),f=r(82888),p=r(40528),h=r(66786),x=r(77666),g=r(31399),b=r(21608);function v(e){var t,r,n,o,c;let{pricing:v}=e;if(v.disabled)return null;let{user:j,setShowSignModal:N}=(0,b.U)(),[y,_]=(0,i.useState)((null===(r=v.groups)||void 0===r?void 0:null===(t=r[0])||void 0===t?void 0:t.name)||"yearly"),[w,k]=(0,i.useState)(!1),[E,S]=(0,i.useState)(null),C=async e=>{try{if(!j){N(!0);return}let t={product_id:e.product_id,product_name:e.product_name,credits:e.credits,interval:e.interval,amount:e.amount,currency:e.currency,valid_months:e.valid_months};k(!0),S(e.product_id);let r=await fetch("/api/checkout",{method:"POST",headers:{"Content-Type":"application/json"},body:JSON.stringify(t)});if(401===r.status){k(!1),S(null),N(!0);return}let{code:l,message:a,data:s}=await r.json();if(0!==l){g.oR.error(a);return}let{public_key:i,session_id:n}=s,o=await (0,x.c)(i);if(!o){g.oR.error("checkout failed");return}let c=await o.redirectToCheckout({sessionId:n});c.error&&g.oR.error(c.error.message)}catch(e){console.log("checkout failed: ",e),g.oR.error("checkout failed")}finally{k(!1),S(null)}};return(0,l.jsx)("section",{id:v.name,className:"py-16",children:(0,l.jsxs)("div",{className:"container",children:[(0,l.jsxs)("div",{className:"mx-auto mb-12 text-center",children:[(0,l.jsx)("h2",{className:"mb-4 text-4xl font-semibold lg:text-5xl",children:v.title}),(0,l.jsx)("p",{className:"text-muted-foreground lg:text-lg",children:v.description})]}),(0,l.jsxs)("div",{className:"w-full flex flex-col items-center gap-2",children:[v.groups&&v.groups.length>0&&(0,l.jsx)("div",{className:"flex h-12 mb-12 items-center rounded-md bg-muted p-1 text-lg",children:(0,l.jsx)(d,{value:y,className:"h-full grid-cols-".concat(v.groups.length),onValueChange:e=>{_(e)},children:v.groups.map((e,t)=>(0,l.jsxs)("div",{className:'h-full rounded-md transition-all border-2 border-transparent has-[button[data-state="checked"]]:border-primary has-[button[data-state="checked"]]:bg-transparent',children:[(0,l.jsx)(u,{value:e.name||"",id:e.name,className:"peer sr-only"}),(0,l.jsxs)(h.J,{htmlFor:e.name,className:"flex h-full cursor-pointer items-center justify-center px-7 font-semibold text-muted-foreground peer-data-[state=checked]:text-primary",children:[e.title,e.label&&(0,l.jsx)(m.E,{variant:"outline",className:"border-primary bg-primary px-1.5 ml-1 text-primary-foreground",children:e.label})]})]},t))})}),(0,l.jsx)("div",{className:"w-full mt-0 grid gap-6 md:grid-cols-".concat(null===(o=v.items)||void 0===o?void 0:null===(n=o.filter(e=>!e.group||e.group===y))||void 0===n?void 0:n.length),children:null===(c=v.items)||void 0===c?void 0:c.map((e,t)=>e.group&&e.group!==y?null:(0,l.jsx)("div",{className:"rounded-lg p-6 ".concat(e.is_featured?"border-primary border-2 bg-card text-card-foreground":"border-muted border"),children:(0,l.jsxs)("div",{className:"flex h-full flex-col justify-between gap-5",children:[(0,l.jsxs)("div",{children:[(0,l.jsxs)("div",{className:"flex items-center gap-2 mb-4",children:[e.title&&(0,l.jsx)("h3",{className:"text-xl font-semibold",children:e.title}),(0,l.jsx)("div",{className:"flex-1"}),e.label&&(0,l.jsx)(m.E,{variant:"outline",className:"border-primary bg-primary px-1.5 text-primary-foreground",children:e.label})]}),(0,l.jsxs)("div",{className:"flex items-end gap-2 mb-4",children:[e.original_price&&(0,l.jsx)("span",{className:"text-xl text-muted-foreground font-semibold line-through",children:e.original_price}),e.price&&(0,l.jsx)("span",{className:"text-5xl font-semibold",children:e.price}),e.unit&&(0,l.jsx)("span",{className:"block font-semibold",dangerouslySetInnerHTML:{__html:e.unit}})]}),e.description&&(0,l.jsx)("p",{className:"text-muted-foreground",children:e.description}),e.features_title&&(0,l.jsx)("p",{className:"mb-3 mt-6 font-semibold",children:e.features_title}),e.features&&(0,l.jsx)("ul",{className:"flex flex-col gap-3",children:e.features.map((e,t)=>(0,l.jsxs)("li",{className:"flex gap-2",children:[(0,l.jsx)(a.A,{className:"mt-1 size-4 shrink-0"}),e]},"feature-".concat(t)))})]}),(0,l.jsxs)("div",{className:"flex flex-col gap-2",children:[e.button&&(0,l.jsxs)(f.$,{className:"w-full flex items-center justify-center gap-2 font-semibold",disabled:w,onClick:()=>{!w&&C(e)},children:[(!w||w&&E!==e.product_id)&&(0,l.jsx)("p",{children:e.button.title}),w&&E===e.product_id&&(0,l.jsx)("p",{children:e.button.title}),w&&E===e.product_id&&(0,l.jsx)(s.A,{className:"mr-2 h-4 w-4 animate-spin"}),e.button.icon&&(0,l.jsx)(p.default,{name:e.button.icon,className:"size-4"})]}),e.tip&&(0,l.jsx)("p",{className:"text-muted-foreground text-sm mt-2",children:e.tip})]})]})},t))})]})]})})}},88999:(e,t,r)=>{r.d(t,{wD:()=>l});let l={Theme:"THEME",InviteCode:"INVITE_CODE",LanguagePreference:"LANGUAGE_PREFERENCE",LanguageSwitchAsked:"LANGUAGE_SWITCH_ASKED"}}}]);