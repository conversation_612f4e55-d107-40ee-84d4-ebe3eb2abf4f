(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[6131],{5657:e=>{e.exports=function({client_id:e,auto_select:t=!1,cancel_on_tap_outside:r=!1,context:n="signin",...o},i){if(!e)throw Error("client_id is required");if("undefined"!=typeof window&&window.document){let a=["signin","signup","use"].includes(n)?n:"signin",s=document.createElement("script");s.src="https://accounts.google.com/gsi/client",s.async=!0,s.defer=!0,document.head.appendChild(s),window.onload=function(){window.google.accounts.id.initialize({client_id:e,callback:i,auto_select:t,cancel_on_tap_outside:r,context:a,...o}),window.google.accounts.id.prompt()}}}},38675:(e,t,r)=>{"use strict";r.d(t,{X:()=>i});var n=r(95361),o=r(53765);function i(e){let[t,r]=n.useState(void 0);return(0,o.N)(()=>{if(e){r({width:e.offsetWidth,height:e.offsetHeight});let t=new ResizeObserver(t=>{let n,o;if(!Array.isArray(t)||!t.length)return;let i=t[0];if("borderBoxSize"in i){let e=i.borderBoxSize,t=Array.isArray(e)?e[0]:e;n=t.inlineSize,o=t.blockSize}else n=e.offsetWidth,o=e.offsetHeight;r({width:n,height:o})});return t.observe(e,{box:"border-box"}),()=>t.unobserve(e)}r(void 0)},[e]),t}},43083:(e,t,r)=>{"use strict";r.d(t,{b:()=>s});var n=r(95361),o=r(72637),i=r(16261),a=n.forwardRef((e,t)=>(0,i.jsx)(o.sG.label,{...e,ref:t,onMouseDown:t=>{var r;t.target.closest("button, input, select, textarea")||(null===(r=e.onMouseDown)||void 0===r||r.call(e,t),!t.defaultPrevented&&t.detail>1&&t.preventDefault())}}));a.displayName="Label";var s=a},64595:(e,t,r)=>{"use strict";r.d(t,{Z:()=>o});var n=r(95361);function o(e){let t=n.useRef({value:e,previous:e});return n.useMemo(()=>(t.current.value!==e&&(t.current.previous=t.current.value,t.current.value=e),t.current.previous),[e])}},64852:(e,t,r)=>{"use strict";r.d(t,{A:()=>n});let n=(0,r(1316).A)("Loader",[["path",{d:"M12 2v4",key:"3427ic"}],["path",{d:"m16.2 7.8 2.9-2.9",key:"r700ao"}],["path",{d:"M18 12h4",key:"wj9ykh"}],["path",{d:"m16.2 16.2 2.9 2.9",key:"1bxg5t"}],["path",{d:"M12 18v4",key:"jadmvz"}],["path",{d:"m4.9 19.1 2.9-2.9",key:"bwix9q"}],["path",{d:"M2 12h4",key:"j09sii"}],["path",{d:"m4.9 4.9 2.9 2.9",key:"giyufr"}]])},67689:(e,t,r)=>{"use strict";r.d(t,{C1:()=>q,bL:()=>T,q7:()=>M});var n=r(95361),o=r(75030),i=r(94286),a=r(22408),s=r(72637),u=r(88295),l=r(4471),c=r(37651),d=r(38675),f=r(64595),p=r(12177),v=r(16261),m="Radio",[h,w]=(0,a.A)(m),[y,b]=h(m),g=n.forwardRef((e,t)=>{let{__scopeRadio:r,name:a,checked:u=!1,required:l,disabled:c,value:d="on",onCheck:f,form:p,...m}=e,[h,w]=n.useState(null),b=(0,i.s)(t,e=>w(e)),g=n.useRef(!1),k=!h||p||!!h.closest("form");return(0,v.jsxs)(y,{scope:r,checked:u,disabled:c,children:[(0,v.jsx)(s.sG.button,{type:"button",role:"radio","aria-checked":u,"data-state":R(u),"data-disabled":c?"":void 0,disabled:c,value:d,...m,ref:b,onClick:(0,o.m)(e.onClick,e=>{u||null==f||f(),k&&(g.current=e.isPropagationStopped(),g.current||e.stopPropagation())})}),k&&(0,v.jsx)(x,{control:h,bubbles:!g.current,name:a,value:d,checked:u,required:l,disabled:c,form:p,style:{transform:"translateX(-100%)"}})]})});g.displayName=m;var k="RadioIndicator",j=n.forwardRef((e,t)=>{let{__scopeRadio:r,forceMount:n,...o}=e,i=b(k,r);return(0,v.jsx)(p.C,{present:n||i.checked,children:(0,v.jsx)(s.sG.span,{"data-state":R(i.checked),"data-disabled":i.disabled?"":void 0,...o,ref:t})})});j.displayName=k;var x=n.forwardRef((e,t)=>{let{__scopeRadio:r,control:o,checked:a,bubbles:u=!0,...l}=e,c=n.useRef(null),p=(0,i.s)(c,t),m=(0,f.Z)(a),h=(0,d.X)(o);return n.useEffect(()=>{let e=c.current;if(!e)return;let t=Object.getOwnPropertyDescriptor(window.HTMLInputElement.prototype,"checked").set;if(m!==a&&t){let r=new Event("click",{bubbles:u});t.call(e,a),e.dispatchEvent(r)}},[m,a,u]),(0,v.jsx)(s.sG.input,{type:"radio","aria-hidden":!0,defaultChecked:a,...l,tabIndex:-1,ref:p,style:{...l.style,...h,position:"absolute",pointerEvents:"none",opacity:0,margin:0}})});function R(e){return e?"checked":"unchecked"}x.displayName="RadioBubbleInput";var E=["ArrowUp","ArrowDown","ArrowLeft","ArrowRight"],A="RadioGroup",[S,C]=(0,a.A)(A,[u.RG,w]),F=(0,u.RG)(),L=w(),[D,I]=S(A),G=n.forwardRef((e,t)=>{let{__scopeRadioGroup:r,name:n,defaultValue:o,value:i,required:a=!1,disabled:d=!1,orientation:f,dir:p,loop:m=!0,onValueChange:h,...w}=e,y=F(r),b=(0,c.jH)(p),[g,k]=(0,l.i)({prop:i,defaultProp:null!=o?o:null,onChange:h,caller:A});return(0,v.jsx)(D,{scope:r,name:n,required:a,disabled:d,value:g,onValueChange:k,children:(0,v.jsx)(u.bL,{asChild:!0,...y,orientation:f,dir:b,loop:m,children:(0,v.jsx)(s.sG.div,{role:"radiogroup","aria-required":a,"aria-orientation":f,"data-disabled":d?"":void 0,dir:b,...w,ref:t})})})});G.displayName=A;var _="RadioGroupItem",N=n.forwardRef((e,t)=>{let{__scopeRadioGroup:r,disabled:a,...s}=e,l=I(_,r),c=l.disabled||a,d=F(r),f=L(r),p=n.useRef(null),m=(0,i.s)(t,p),h=l.value===s.value,w=n.useRef(!1);return n.useEffect(()=>{let e=e=>{E.includes(e.key)&&(w.current=!0)},t=()=>w.current=!1;return document.addEventListener("keydown",e),document.addEventListener("keyup",t),()=>{document.removeEventListener("keydown",e),document.removeEventListener("keyup",t)}},[]),(0,v.jsx)(u.q7,{asChild:!0,...d,focusable:!c,active:h,children:(0,v.jsx)(g,{disabled:c,required:l.required,checked:h,...f,...s,name:l.name,ref:m,onCheck:()=>l.onValueChange(s.value),onKeyDown:(0,o.m)(e=>{"Enter"===e.key&&e.preventDefault()}),onFocus:(0,o.m)(s.onFocus,()=>{var e;w.current&&(null===(e=p.current)||void 0===e||e.click())})})})});N.displayName=_;var P=n.forwardRef((e,t)=>{let{__scopeRadioGroup:r,...n}=e,o=L(r);return(0,v.jsx)(j,{...o,...n,ref:t})});P.displayName="RadioGroupIndicator";var T=G,M=N,q=P},69397:(e,t,r)=>{"use strict";r.d(t,{A:()=>n});let n=(0,r(1316).A)("Check",[["path",{d:"M20 6 9 17l-5-5",key:"1gmf2c"}]])},77666:(e,t,r)=>{"use strict";r.d(t,{c:()=>w});var n,o="https://js.stripe.com",i="".concat(o,"/v3"),a=/^https:\/\/js\.stripe\.com\/v3\/?(\?.*)?$/,s=/^https:\/\/js\.stripe\.com\/(v3|[a-z]+)\/stripe\.js(\?.*)?$/,u=function(){for(var e=document.querySelectorAll('script[src^="'.concat(o,'"]')),t=0;t<e.length;t++){var r,n=e[t];if(r=n.src,a.test(r)||s.test(r))return n}return null},l=function(e){var t=e&&!e.advancedFraudSignals?"?advancedFraudSignals=false":"",r=document.createElement("script");r.src="".concat(i).concat(t);var n=document.head||document.body;if(!n)throw Error("Expected document.body not to be null. Stripe.js requires a <body> element.");return n.appendChild(r),r},c=function(e,t){e&&e._registerWrapper&&e._registerWrapper({name:"stripe-js",version:"5.10.0",startTime:t})},d=null,f=null,p=null,v=function(e,t,r){if(null===e)return null;var n,o=t[0].match(/^pk_test/),i=3===(n=e.version)?"v3":n;o&&"v3"!==i&&console.warn("Stripe.js@".concat(i," was loaded on the page, but @stripe/stripe-js@").concat("5.10.0"," expected Stripe.js@").concat("v3",". This may result in unexpected behavior. For more information, see https://docs.stripe.com/sdks/stripejs-versioning"));var a=e.apply(void 0,t);return c(a,r),a},m=!1,h=function(){return n?n:n=(null!==d?d:(d=new Promise(function(e,t){if("undefined"==typeof window||"undefined"==typeof document){e(null);return}if(window.Stripe,window.Stripe){e(window.Stripe);return}try{var r,n=u();n?n&&null!==p&&null!==f&&(n.removeEventListener("load",p),n.removeEventListener("error",f),null===(r=n.parentNode)||void 0===r||r.removeChild(n),n=l(null)):n=l(null),p=function(){window.Stripe?e(window.Stripe):t(Error("Stripe.js not available"))},f=function(e){t(Error("Failed to load Stripe.js",{cause:e}))},n.addEventListener("load",p),n.addEventListener("error",f)}catch(e){t(e);return}})).catch(function(e){return d=null,Promise.reject(e)})).catch(function(e){return n=null,Promise.reject(e)})};Promise.resolve().then(function(){return h()}).catch(function(e){m||console.warn(e)});var w=function(){for(var e=arguments.length,t=Array(e),r=0;r<e;r++)t[r]=arguments[r];m=!0;var n=Date.now();return h().then(function(e){return v(e,t,n)})}},88295:(e,t,r)=>{"use strict";r.d(t,{RG:()=>k,bL:()=>L,q7:()=>D});var n=r(95361),o=r(75030),i=r(77002),a=r(94286),s=r(22408),u=r(25747),l=r(72637),c=r(21751),d=r(4471),f=r(37651),p=r(16261),v="rovingFocusGroup.onEntryFocus",m={bubbles:!1,cancelable:!0},h="RovingFocusGroup",[w,y,b]=(0,i.N)(h),[g,k]=(0,s.A)(h,[b]),[j,x]=g(h),R=n.forwardRef((e,t)=>(0,p.jsx)(w.Provider,{scope:e.__scopeRovingFocusGroup,children:(0,p.jsx)(w.Slot,{scope:e.__scopeRovingFocusGroup,children:(0,p.jsx)(E,{...e,ref:t})})}));R.displayName=h;var E=n.forwardRef((e,t)=>{let{__scopeRovingFocusGroup:r,orientation:i,loop:s=!1,dir:u,currentTabStopId:w,defaultCurrentTabStopId:b,onCurrentTabStopIdChange:g,onEntryFocus:k,preventScrollOnEntryFocus:x=!1,...R}=e,E=n.useRef(null),A=(0,a.s)(t,E),S=(0,f.jH)(u),[C,L]=(0,d.i)({prop:w,defaultProp:null!=b?b:null,onChange:g,caller:h}),[D,I]=n.useState(!1),G=(0,c.c)(k),_=y(r),N=n.useRef(!1),[P,T]=n.useState(0);return n.useEffect(()=>{let e=E.current;if(e)return e.addEventListener(v,G),()=>e.removeEventListener(v,G)},[G]),(0,p.jsx)(j,{scope:r,orientation:i,dir:S,loop:s,currentTabStopId:C,onItemFocus:n.useCallback(e=>L(e),[L]),onItemShiftTab:n.useCallback(()=>I(!0),[]),onFocusableItemAdd:n.useCallback(()=>T(e=>e+1),[]),onFocusableItemRemove:n.useCallback(()=>T(e=>e-1),[]),children:(0,p.jsx)(l.sG.div,{tabIndex:D||0===P?-1:0,"data-orientation":i,...R,ref:A,style:{outline:"none",...e.style},onMouseDown:(0,o.m)(e.onMouseDown,()=>{N.current=!0}),onFocus:(0,o.m)(e.onFocus,e=>{let t=!N.current;if(e.target===e.currentTarget&&t&&!D){let t=new CustomEvent(v,m);if(e.currentTarget.dispatchEvent(t),!t.defaultPrevented){let e=_().filter(e=>e.focusable);F([e.find(e=>e.active),e.find(e=>e.id===C),...e].filter(Boolean).map(e=>e.ref.current),x)}}N.current=!1}),onBlur:(0,o.m)(e.onBlur,()=>I(!1))})})}),A="RovingFocusGroupItem",S=n.forwardRef((e,t)=>{let{__scopeRovingFocusGroup:r,focusable:i=!0,active:a=!1,tabStopId:s,children:c,...d}=e,f=(0,u.B)(),v=s||f,m=x(A,r),h=m.currentTabStopId===v,b=y(r),{onFocusableItemAdd:g,onFocusableItemRemove:k,currentTabStopId:j}=m;return n.useEffect(()=>{if(i)return g(),()=>k()},[i,g,k]),(0,p.jsx)(w.ItemSlot,{scope:r,id:v,focusable:i,active:a,children:(0,p.jsx)(l.sG.span,{tabIndex:h?0:-1,"data-orientation":m.orientation,...d,ref:t,onMouseDown:(0,o.m)(e.onMouseDown,e=>{i?m.onItemFocus(v):e.preventDefault()}),onFocus:(0,o.m)(e.onFocus,()=>m.onItemFocus(v)),onKeyDown:(0,o.m)(e.onKeyDown,e=>{if("Tab"===e.key&&e.shiftKey){m.onItemShiftTab();return}if(e.target!==e.currentTarget)return;let t=function(e,t,r){var n;let o=(n=e.key,"rtl"!==r?n:"ArrowLeft"===n?"ArrowRight":"ArrowRight"===n?"ArrowLeft":n);if(!("vertical"===t&&["ArrowLeft","ArrowRight"].includes(o))&&!("horizontal"===t&&["ArrowUp","ArrowDown"].includes(o)))return C[o]}(e,m.orientation,m.dir);if(void 0!==t){if(e.metaKey||e.ctrlKey||e.altKey||e.shiftKey)return;e.preventDefault();let r=b().filter(e=>e.focusable).map(e=>e.ref.current);if("last"===t)r.reverse();else if("prev"===t||"next"===t){"prev"===t&&r.reverse();let n=r.indexOf(e.currentTarget);r=m.loop?function(e,t){return e.map((r,n)=>e[(t+n)%e.length])}(r,n+1):r.slice(n+1)}setTimeout(()=>F(r))}}),children:"function"==typeof c?c({isCurrentTabStop:h,hasTabStop:null!=j}):c})})});S.displayName=A;var C={ArrowLeft:"prev",ArrowUp:"prev",ArrowRight:"next",ArrowDown:"next",PageUp:"first",Home:"first",PageDown:"last",End:"last"};function F(e){let t=arguments.length>1&&void 0!==arguments[1]&&arguments[1],r=document.activeElement;for(let n of e)if(n===r||(n.focus({preventScroll:t}),document.activeElement!==r))return}var L=R,D=S},91571:(e,t,r)=>{"use strict";r.d(t,{A:()=>n});let n=(0,r(1316).A)("Circle",[["circle",{cx:"12",cy:"12",r:"10",key:"1mglay"}]])}}]);