"use strict";(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[3044],{8624:(e,t,r)=>{r.d(t,{c3:()=>l});var n=r(6002);function o(e,t){return(...e)=>{try{return t(...e)}catch{throw Error(void 0)}}}let l=o(0,n.c3);o(0,n.kc)},21494:(e,t,r)=>{r.d(t,{UC:()=>er,VY:()=>eo,ZL:()=>ee,bL:()=>Q,bm:()=>el,hE:()=>en,hJ:()=>et,l9:()=>$});var n=r(95361),o=r(75030),l=r(94286),i=r(22408),a=r(25747),s=r(4471),c=r(10730),u=r(70908),d=r(70200),f=r(12177),p=r(72637),v=r(57750),g=r(99126),m=r(91783),y=r(76012),b=r(16261),h="Dialog",[O,j]=(0,i.A)(h),[w,D]=O(h),x=e=>{let{__scopeDialog:t,children:r,open:o,defaultOpen:l,onOpenChange:i,modal:c=!0}=e,u=n.useRef(null),d=n.useRef(null),[f,p]=(0,s.i)({prop:o,defaultProp:null!=l&&l,onChange:i,caller:h});return(0,b.jsx)(w,{scope:t,triggerRef:u,contentRef:d,contentId:(0,a.B)(),titleId:(0,a.B)(),descriptionId:(0,a.B)(),open:f,onOpenChange:p,onOpenToggle:n.useCallback(()=>p(e=>!e),[p]),modal:c,children:r})};x.displayName=h;var C="DialogTrigger",E=n.forwardRef((e,t)=>{let{__scopeDialog:r,...n}=e,i=D(C,r),a=(0,l.s)(t,i.triggerRef);return(0,b.jsx)(p.sG.button,{type:"button","aria-haspopup":"dialog","aria-expanded":i.open,"aria-controls":i.contentId,"data-state":V(i.open),...n,ref:a,onClick:(0,o.m)(e.onClick,i.onOpenToggle)})});E.displayName=C;var P="DialogPortal",[N,R]=O(P,{forceMount:void 0}),I=e=>{let{__scopeDialog:t,forceMount:r,children:o,container:l}=e,i=D(P,t);return(0,b.jsx)(N,{scope:t,forceMount:r,children:n.Children.map(o,e=>(0,b.jsx)(f.C,{present:r||i.open,children:(0,b.jsx)(d.Z,{asChild:!0,container:l,children:e})}))})};I.displayName=P;var k="DialogOverlay",_=n.forwardRef((e,t)=>{let r=R(k,e.__scopeDialog),{forceMount:n=r.forceMount,...o}=e,l=D(k,e.__scopeDialog);return l.modal?(0,b.jsx)(f.C,{present:n||l.open,children:(0,b.jsx)(A,{...o,ref:t})}):null});_.displayName=k;var F=(0,y.TL)("DialogOverlay.RemoveScroll"),A=n.forwardRef((e,t)=>{let{__scopeDialog:r,...n}=e,o=D(k,r);return(0,b.jsx)(g.A,{as:F,allowPinchZoom:!0,shards:[o.contentRef],children:(0,b.jsx)(p.sG.div,{"data-state":V(o.open),...n,ref:t,style:{pointerEvents:"auto",...n.style}})})}),S="DialogContent",T=n.forwardRef((e,t)=>{let r=R(S,e.__scopeDialog),{forceMount:n=r.forceMount,...o}=e,l=D(S,e.__scopeDialog);return(0,b.jsx)(f.C,{present:n||l.open,children:l.modal?(0,b.jsx)(M,{...o,ref:t}):(0,b.jsx)(B,{...o,ref:t})})});T.displayName=S;var M=n.forwardRef((e,t)=>{let r=D(S,e.__scopeDialog),i=n.useRef(null),a=(0,l.s)(t,r.contentRef,i);return n.useEffect(()=>{let e=i.current;if(e)return(0,m.Eq)(e)},[]),(0,b.jsx)(G,{...e,ref:a,trapFocus:r.open,disableOutsidePointerEvents:!0,onCloseAutoFocus:(0,o.m)(e.onCloseAutoFocus,e=>{var t;e.preventDefault(),null===(t=r.triggerRef.current)||void 0===t||t.focus()}),onPointerDownOutside:(0,o.m)(e.onPointerDownOutside,e=>{let t=e.detail.originalEvent,r=0===t.button&&!0===t.ctrlKey;(2===t.button||r)&&e.preventDefault()}),onFocusOutside:(0,o.m)(e.onFocusOutside,e=>e.preventDefault())})}),B=n.forwardRef((e,t)=>{let r=D(S,e.__scopeDialog),o=n.useRef(!1),l=n.useRef(!1);return(0,b.jsx)(G,{...e,ref:t,trapFocus:!1,disableOutsidePointerEvents:!1,onCloseAutoFocus:t=>{var n,i;null===(n=e.onCloseAutoFocus)||void 0===n||n.call(e,t),t.defaultPrevented||(o.current||null===(i=r.triggerRef.current)||void 0===i||i.focus(),t.preventDefault()),o.current=!1,l.current=!1},onInteractOutside:t=>{var n,i;null===(n=e.onInteractOutside)||void 0===n||n.call(e,t),t.defaultPrevented||(o.current=!0,"pointerdown"!==t.detail.originalEvent.type||(l.current=!0));let a=t.target;(null===(i=r.triggerRef.current)||void 0===i?void 0:i.contains(a))&&t.preventDefault(),"focusin"===t.detail.originalEvent.type&&l.current&&t.preventDefault()}})}),G=n.forwardRef((e,t)=>{let{__scopeDialog:r,trapFocus:o,onOpenAutoFocus:i,onCloseAutoFocus:a,...s}=e,d=D(S,r),f=n.useRef(null),p=(0,l.s)(t,f);return(0,v.Oh)(),(0,b.jsxs)(b.Fragment,{children:[(0,b.jsx)(u.n,{asChild:!0,loop:!0,trapped:o,onMountAutoFocus:i,onUnmountAutoFocus:a,children:(0,b.jsx)(c.qW,{role:"dialog",id:d.contentId,"aria-describedby":d.descriptionId,"aria-labelledby":d.titleId,"data-state":V(d.open),...s,ref:p,onDismiss:()=>d.onOpenChange(!1)})}),(0,b.jsxs)(b.Fragment,{children:[(0,b.jsx)(X,{titleId:d.titleId}),(0,b.jsx)(Y,{contentRef:f,descriptionId:d.descriptionId})]})]})}),W="DialogTitle",q=n.forwardRef((e,t)=>{let{__scopeDialog:r,...n}=e,o=D(W,r);return(0,b.jsx)(p.sG.h2,{id:o.titleId,...n,ref:t})});q.displayName=W;var z="DialogDescription",L=n.forwardRef((e,t)=>{let{__scopeDialog:r,...n}=e,o=D(z,r);return(0,b.jsx)(p.sG.p,{id:o.descriptionId,...n,ref:t})});L.displayName=z;var Z="DialogClose",U=n.forwardRef((e,t)=>{let{__scopeDialog:r,...n}=e,l=D(Z,r);return(0,b.jsx)(p.sG.button,{type:"button",...n,ref:t,onClick:(0,o.m)(e.onClick,()=>l.onOpenChange(!1))})});function V(e){return e?"open":"closed"}U.displayName=Z;var H="DialogTitleWarning",[J,K]=(0,i.q)(H,{contentName:S,titleName:W,docsSlug:"dialog"}),X=e=>{let{titleId:t}=e,r=K(H),o="`".concat(r.contentName,"` requires a `").concat(r.titleName,"` for the component to be accessible for screen reader users.\n\nIf you want to hide the `").concat(r.titleName,"`, you can wrap it with our VisuallyHidden component.\n\nFor more information, see https://radix-ui.com/primitives/docs/components/").concat(r.docsSlug);return n.useEffect(()=>{t&&!document.getElementById(t)&&console.error(o)},[o,t]),null},Y=e=>{let{contentRef:t,descriptionId:r}=e,o=K("DialogDescriptionWarning"),l="Warning: Missing `Description` or `aria-describedby={undefined}` for {".concat(o.contentName,"}.");return n.useEffect(()=>{var e;let n=null===(e=t.current)||void 0===e?void 0:e.getAttribute("aria-describedby");r&&n&&!document.getElementById(r)&&console.warn(l)},[l,t,r]),null},Q=x,$=E,ee=I,et=_,er=T,en=q,eo=L,el=U},44756:(e,t,r)=>{r.d(t,{k5:()=>u});var n=r(95361),o={color:void 0,size:void 0,className:void 0,style:void 0,attr:void 0},l=n.createContext&&n.createContext(o),i=["attr","size","title"];function a(){return(a=Object.assign?Object.assign.bind():function(e){for(var t=1;t<arguments.length;t++){var r=arguments[t];for(var n in r)Object.prototype.hasOwnProperty.call(r,n)&&(e[n]=r[n])}return e}).apply(this,arguments)}function s(e,t){var r=Object.keys(e);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(e);t&&(n=n.filter(function(t){return Object.getOwnPropertyDescriptor(e,t).enumerable})),r.push.apply(r,n)}return r}function c(e){for(var t=1;t<arguments.length;t++){var r=null!=arguments[t]?arguments[t]:{};t%2?s(Object(r),!0).forEach(function(t){var n,o,l;n=e,o=t,l=r[t],(o=function(e){var t=function(e,t){if("object"!=typeof e||!e)return e;var r=e[Symbol.toPrimitive];if(void 0!==r){var n=r.call(e,t||"default");if("object"!=typeof n)return n;throw TypeError("@@toPrimitive must return a primitive value.")}return("string"===t?String:Number)(e)}(e,"string");return"symbol"==typeof t?t:t+""}(o))in n?Object.defineProperty(n,o,{value:l,enumerable:!0,configurable:!0,writable:!0}):n[o]=l}):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(r)):s(Object(r)).forEach(function(t){Object.defineProperty(e,t,Object.getOwnPropertyDescriptor(r,t))})}return e}function u(e){return t=>n.createElement(d,a({attr:c({},e.attr)},t),function e(t){return t&&t.map((t,r)=>n.createElement(t.tag,c({key:r},t.attr),e(t.child)))}(e.child))}function d(e){var t=t=>{var r,{attr:o,size:l,title:s}=e,u=function(e,t){if(null==e)return{};var r,n,o=function(e,t){if(null==e)return{};var r={};for(var n in e)if(Object.prototype.hasOwnProperty.call(e,n)){if(t.indexOf(n)>=0)continue;r[n]=e[n]}return r}(e,t);if(Object.getOwnPropertySymbols){var l=Object.getOwnPropertySymbols(e);for(n=0;n<l.length;n++)r=l[n],!(t.indexOf(r)>=0)&&Object.prototype.propertyIsEnumerable.call(e,r)&&(o[r]=e[r])}return o}(e,i),d=l||t.size||"1em";return t.className&&(r=t.className),e.className&&(r=(r?r+" ":"")+e.className),n.createElement("svg",a({stroke:"currentColor",fill:"currentColor",strokeWidth:"0"},t.attr,o,u,{className:r,style:c(c({color:e.color||t.color},t.style),e.style),height:d,width:d,xmlns:"http://www.w3.org/2000/svg"}),s&&n.createElement("title",null,s),e.children)};return void 0!==l?n.createElement(l.Consumer,null,e=>t(e)):t(o)}},87605:(e,t,r)=>{r.d(t,{A:()=>n});let n=(0,r(1316).A)("X",[["path",{d:"M18 6 6 18",key:"1bl5f8"}],["path",{d:"m6 6 12 12",key:"d8bk6v"}]])}}]);