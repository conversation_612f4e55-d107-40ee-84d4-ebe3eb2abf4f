"use strict";(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[5598],{39669:(e,t,a)=>{a.d(t,{CG:()=>d,Fm:()=>b,Qs:()=>g,cj:()=>o,h:()=>u,qp:()=>f});var r=a(16261);a(95361);var i=a(21494),n=a(87605),s=a(61063);function o(e){let{...t}=e;return(0,r.jsx)(i.bL,{"data-slot":"sheet",...t})}function d(e){let{...t}=e;return(0,r.jsx)(i.l9,{"data-slot":"sheet-trigger",...t})}function l(e){let{...t}=e;return(0,r.jsx)(i.ZL,{"data-slot":"sheet-portal",...t})}function c(e){let{className:t,...a}=e;return(0,r.jsx)(i.hJ,{"data-slot":"sheet-overlay",className:(0,s.cn)("data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=open]:fade-in-0 fixed inset-0 z-50 bg-black/50",t),...a})}function u(e){let{className:t,children:a,side:o="right",...d}=e;return(0,r.jsxs)(l,{children:[(0,r.jsx)(c,{}),(0,r.jsxs)(i.UC,{"data-slot":"sheet-content",className:(0,s.cn)("bg-background data-[state=open]:animate-in data-[state=closed]:animate-out fixed z-50 flex flex-col gap-4 shadow-lg transition ease-in-out data-[state=closed]:duration-300 data-[state=open]:duration-500","right"===o&&"data-[state=closed]:slide-out-to-right data-[state=open]:slide-in-from-right inset-y-0 right-0 h-full w-3/4 border-l sm:max-w-sm","left"===o&&"data-[state=closed]:slide-out-to-left data-[state=open]:slide-in-from-left inset-y-0 left-0 h-full w-3/4 border-r sm:max-w-sm","top"===o&&"data-[state=closed]:slide-out-to-top data-[state=open]:slide-in-from-top inset-x-0 top-0 h-auto border-b","bottom"===o&&"data-[state=closed]:slide-out-to-bottom data-[state=open]:slide-in-from-bottom inset-x-0 bottom-0 h-auto border-t",t),...d,children:[a,(0,r.jsxs)(i.bm,{className:"ring-offset-background focus:ring-ring data-[state=open]:bg-secondary absolute top-4 right-4 rounded-xs opacity-70 transition-opacity hover:opacity-100 focus:ring-2 focus:ring-offset-2 focus:outline-hidden disabled:pointer-events-none",children:[(0,r.jsx)(n.A,{className:"size-4"}),(0,r.jsx)("span",{className:"sr-only",children:"Close"})]})]})]})}function b(e){let{className:t,...a}=e;return(0,r.jsx)("div",{"data-slot":"sheet-header",className:(0,s.cn)("flex flex-col gap-1.5 p-4",t),...a})}function f(e){let{className:t,...a}=e;return(0,r.jsx)(i.hE,{"data-slot":"sheet-title",className:(0,s.cn)("text-foreground font-semibold",t),...a})}function g(e){let{className:t,...a}=e;return(0,r.jsx)(i.VY,{"data-slot":"sheet-description",className:(0,s.cn)("text-muted-foreground text-sm",t),...a})}},44381:(e,t,a)=>{a.d(t,{Separator:()=>s});var r=a(16261);a(95361);var i=a(66157),n=a(61063);function s(e){let{className:t,orientation:a="horizontal",decorative:s=!0,...o}=e;return(0,r.jsx)(i.b,{"data-slot":"separator-root",decorative:s,orientation:a,className:(0,n.cn)("bg-border shrink-0 data-[orientation=horizontal]:h-px data-[orientation=horizontal]:w-full data-[orientation=vertical]:h-full data-[orientation=vertical]:w-px",t),...o})}},61063:(e,t,a)=>{a.d(t,{cn:()=>n});var r=a(87786),i=a(34835);function n(){for(var e=arguments.length,t=Array(e),a=0;a<e;a++)t[a]=arguments[a];return(0,i.QP)((0,r.$)(t))}},63911:(e,t,a)=>{a.d(t,{a:()=>i});var r=a(95361);function i(){let[e,t]=r.useState(void 0);return r.useEffect(()=>{let e=window.matchMedia("(max-width: ".concat(767,"px)")),a=()=>{t(window.innerWidth<768)};return e.addEventListener("change",a),t(window.innerWidth<768),()=>e.removeEventListener("change",a)},[]),!!e}},81828:(e,t,a)=>{a.d(t,{p:()=>n});var r=a(16261);a(95361);var i=a(61063);function n(e){let{className:t,type:a,...n}=e;return(0,r.jsx)("input",{type:a,"data-slot":"input",className:(0,i.cn)("file:text-foreground placeholder:text-muted-foreground selection:bg-primary selection:text-primary-foreground dark:bg-input/30 border-input flex h-9 w-full min-w-0 rounded-md border bg-transparent px-3 py-1 text-base shadow-xs transition-[color,box-shadow] outline-none file:inline-flex file:h-7 file:border-0 file:bg-transparent file:text-sm file:font-medium disabled:pointer-events-none disabled:cursor-not-allowed disabled:opacity-50 md:text-sm","focus-visible:border-ring focus-visible:ring-ring/50 focus-visible:ring-[3px]","aria-invalid:ring-destructive/20 dark:aria-invalid:ring-destructive/40 aria-invalid:border-destructive",t),...n})}},82888:(e,t,a)=>{a.d(t,{$:()=>d,r:()=>o});var r=a(16261);a(95361);var i=a(76012),n=a(20604),s=a(61063);let o=(0,n.F)("inline-flex items-center justify-center gap-2 whitespace-nowrap rounded-lg text-sm font-medium transition-all duration-200 disabled:pointer-events-none disabled:opacity-50 [&_svg]:pointer-events-none [&_svg:not([class*='size-'])]:size-4 shrink-0 [&_svg]:shrink-0 outline-none focus-visible:border-ring focus-visible:ring-ring/50 focus-visible:ring-[3px] aria-invalid:ring-destructive/20 dark:aria-invalid:ring-destructive/40 aria-invalid:border-destructive",{variants:{variant:{default:"bg-gradient-to-r from-primary to-accent text-primary-foreground shadow-md hover:shadow-lg hover:scale-[1.02] active:scale-[0.98]",destructive:"bg-destructive text-destructive-foreground shadow-md hover:bg-destructive/90 hover:shadow-lg hover:scale-[1.02] active:scale-[0.98] focus-visible:ring-destructive/20 dark:focus-visible:ring-destructive/40",outline:"border-2 border-primary/20 bg-background/50 backdrop-blur-sm shadow-sm hover:bg-accent/50 hover:text-accent-foreground hover:border-primary/40 hover:shadow-md hover:scale-[1.02] active:scale-[0.98] dark:bg-input/30 dark:border-input dark:hover:bg-input/50",secondary:"bg-gradient-to-r from-secondary to-muted text-secondary-foreground shadow-sm hover:shadow-md hover:scale-[1.02] active:scale-[0.98]",ghost:"hover:bg-accent/50 hover:text-accent-foreground hover:scale-[1.02] active:scale-[0.98] dark:hover:bg-accent/30",link:"text-primary underline-offset-4 hover:underline hover:text-accent"},size:{default:"h-10 px-6 py-2 has-[>svg]:px-4",sm:"h-8 rounded-lg gap-1.5 px-4 has-[>svg]:px-3",lg:"h-12 rounded-lg px-8 has-[>svg]:px-6 text-base",icon:"size-10 rounded-full"}},defaultVariants:{variant:"default",size:"default"}});function d(e){let{className:t,variant:a,size:n,asChild:d=!1,...l}=e,c=d?i.DX:"button";return(0,r.jsx)(c,{"data-slot":"button",className:(0,s.cn)(o({variant:a,size:n,className:t})),...l})}},85598:(e,t,a)=>{a.d(t,{Bx:()=>w,Yv:()=>z,CG:()=>k,Cn:()=>_,rQ:()=>S,jj:()=>C,Gh:()=>N,SidebarInset:()=>y,wZ:()=>E,Uj:()=>F,FX:()=>L,SidebarProvider:()=>x,SidebarTrigger:()=>j,cL:()=>m});var r=a(16261),i=a(95361),n=a(76012),s=a(20604),o=a(69907),d=a(63911),l=a(61063),c=a(82888);a(81828),a(44381);var u=a(39669),b=a(45861);function f(e){let{delayDuration:t=0,...a}=e;return(0,r.jsx)(b.Kq,{"data-slot":"tooltip-provider",delayDuration:t,...a})}function g(e){let{...t}=e;return(0,r.jsx)(f,{children:(0,r.jsx)(b.bL,{"data-slot":"tooltip",...t})})}function p(e){let{...t}=e;return(0,r.jsx)(b.l9,{"data-slot":"tooltip-trigger",...t})}function h(e){let{className:t,sideOffset:a=0,children:i,...n}=e;return(0,r.jsx)(b.ZL,{children:(0,r.jsxs)(b.UC,{"data-slot":"tooltip-content",sideOffset:a,className:(0,l.cn)("bg-primary text-primary-foreground animate-in fade-in-0 zoom-in-95 data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=closed]:zoom-out-95 data-[side=bottom]:slide-in-from-top-2 data-[side=left]:slide-in-from-right-2 data-[side=right]:slide-in-from-left-2 data-[side=top]:slide-in-from-bottom-2 z-50 w-fit origin-(--radix-tooltip-content-transform-origin) rounded-md px-3 py-1.5 text-xs text-balance",t),...n,children:[i,(0,r.jsx)(b.i3,{className:"bg-primary fill-primary z-50 size-2.5 translate-y-[calc(-50%_-_2px)] rotate-45 rounded-[2px]"})]})})}let v=i.createContext(null);function m(){let e=i.useContext(v);if(!e)throw Error("useSidebar must be used within a SidebarProvider.");return e}function x(e){let{defaultOpen:t=!0,open:a,onOpenChange:n,className:s,style:o,children:c,...u}=e,b=(0,d.a)(),[g,p]=i.useState(!1),[h,m]=i.useState(t),x=null!=a?a:h,w=i.useCallback(e=>{let t="function"==typeof e?e(x):e;n?n(t):m(t),document.cookie="".concat("sidebar_state","=").concat(t,"; path=/; max-age=").concat(604800)},[n,x]),j=i.useCallback(()=>b?p(e=>!e):w(e=>!e),[b,w,p]);i.useEffect(()=>{let e=e=>{"b"===e.key&&(e.metaKey||e.ctrlKey)&&(e.preventDefault(),j())};return window.addEventListener("keydown",e),()=>window.removeEventListener("keydown",e)},[j]);let y=x?"expanded":"collapsed",N=i.useMemo(()=>({state:y,open:x,setOpen:w,isMobile:b,openMobile:g,setOpenMobile:p,toggleSidebar:j}),[y,x,w,b,g,p,j]);return(0,r.jsx)(v.Provider,{value:N,children:(0,r.jsx)(f,{delayDuration:0,children:(0,r.jsx)("div",{"data-slot":"sidebar-wrapper",style:{"--sidebar-width":"16rem","--sidebar-width-icon":"3rem",...o},className:(0,l.cn)("group/sidebar-wrapper has-data-[variant=inset]:bg-sidebar flex min-h-svh w-full",s),...u,children:c})})})}function w(e){let{side:t="left",variant:a="sidebar",collapsible:i="offcanvas",className:n,children:s,...o}=e,{isMobile:d,state:c,openMobile:b,setOpenMobile:f}=m();return"none"===i?(0,r.jsx)("div",{"data-slot":"sidebar",className:(0,l.cn)("bg-sidebar text-sidebar-foreground flex h-full w-(--sidebar-width) flex-col",n),...o,children:s}):d?(0,r.jsx)(u.cj,{open:b,onOpenChange:f,...o,children:(0,r.jsxs)(u.h,{"data-sidebar":"sidebar","data-slot":"sidebar","data-mobile":"true",className:"bg-sidebar text-sidebar-foreground w-(--sidebar-width) p-0 [&>button]:hidden",style:{"--sidebar-width":"18rem"},side:t,children:[(0,r.jsxs)(u.Fm,{className:"sr-only",children:[(0,r.jsx)(u.qp,{children:"Sidebar"}),(0,r.jsx)(u.Qs,{children:"Displays the mobile sidebar."})]}),(0,r.jsx)("div",{className:"flex h-full w-full flex-col",children:s})]})}):(0,r.jsxs)("div",{className:"group peer text-sidebar-foreground hidden md:block","data-state":c,"data-collapsible":"collapsed"===c?i:"","data-variant":a,"data-side":t,"data-slot":"sidebar",children:[(0,r.jsx)("div",{"data-slot":"sidebar-gap",className:(0,l.cn)("relative w-(--sidebar-width) bg-transparent transition-[width] duration-200 ease-linear","group-data-[collapsible=offcanvas]:w-0","group-data-[side=right]:rotate-180","floating"===a||"inset"===a?"group-data-[collapsible=icon]:w-[calc(var(--sidebar-width-icon)+(--spacing(4)))]":"group-data-[collapsible=icon]:w-(--sidebar-width-icon)")}),(0,r.jsx)("div",{"data-slot":"sidebar-container",className:(0,l.cn)("fixed inset-y-0 z-10 hidden h-svh w-(--sidebar-width) transition-[left,right,width] duration-200 ease-linear md:flex","left"===t?"left-0 group-data-[collapsible=offcanvas]:left-[calc(var(--sidebar-width)*-1)]":"right-0 group-data-[collapsible=offcanvas]:right-[calc(var(--sidebar-width)*-1)]","floating"===a||"inset"===a?"p-2 group-data-[collapsible=icon]:w-[calc(var(--sidebar-width-icon)+(--spacing(4))+2px)]":"group-data-[collapsible=icon]:w-(--sidebar-width-icon) group-data-[side=left]:border-r group-data-[side=right]:border-l",n),...o,children:(0,r.jsx)("div",{"data-sidebar":"sidebar","data-slot":"sidebar-inner",className:"bg-sidebar group-data-[variant=floating]:border-sidebar-border flex h-full w-full flex-col group-data-[variant=floating]:rounded-lg group-data-[variant=floating]:border group-data-[variant=floating]:shadow-sm",children:s})})]})}function j(e){let{className:t,onClick:a,...i}=e,{toggleSidebar:n}=m();return(0,r.jsxs)(c.$,{"data-sidebar":"trigger","data-slot":"sidebar-trigger",variant:"ghost",size:"icon",className:(0,l.cn)("size-7",t),onClick:e=>{null==a||a(e),n()},...i,children:[(0,r.jsx)(o.A,{}),(0,r.jsx)("span",{className:"sr-only",children:"Toggle Sidebar"})]})}function y(e){let{className:t,...a}=e;return(0,r.jsx)("main",{"data-slot":"sidebar-inset",className:(0,l.cn)("bg-background relative flex w-full flex-1 flex-col","md:peer-data-[variant=inset]:m-2 md:peer-data-[variant=inset]:ml-0 md:peer-data-[variant=inset]:rounded-xl md:peer-data-[variant=inset]:shadow-sm md:peer-data-[variant=inset]:peer-data-[state=collapsed]:ml-2",t),...a})}function N(e){let{className:t,...a}=e;return(0,r.jsx)("div",{"data-slot":"sidebar-header","data-sidebar":"header",className:(0,l.cn)("flex flex-col gap-2 p-2",t),...a})}function k(e){let{className:t,...a}=e;return(0,r.jsx)("div",{"data-slot":"sidebar-footer","data-sidebar":"footer",className:(0,l.cn)("flex flex-col gap-2 p-2",t),...a})}function z(e){let{className:t,...a}=e;return(0,r.jsx)("div",{"data-slot":"sidebar-content","data-sidebar":"content",className:(0,l.cn)("flex min-h-0 flex-1 flex-col gap-2 overflow-auto group-data-[collapsible=icon]:overflow-hidden",t),...a})}function _(e){let{className:t,...a}=e;return(0,r.jsx)("div",{"data-slot":"sidebar-group","data-sidebar":"group",className:(0,l.cn)("relative flex w-full min-w-0 flex-col p-2",t),...a})}function C(e){let{className:t,asChild:a=!1,...i}=e,s=a?n.DX:"div";return(0,r.jsx)(s,{"data-slot":"sidebar-group-label","data-sidebar":"group-label",className:(0,l.cn)("text-sidebar-foreground/70 ring-sidebar-ring flex h-8 shrink-0 items-center rounded-md px-2 text-xs font-medium outline-hidden transition-[margin,opacity] duration-200 ease-linear focus-visible:ring-2 [&>svg]:size-4 [&>svg]:shrink-0","group-data-[collapsible=icon]:-mt-8 group-data-[collapsible=icon]:opacity-0",t),...i})}function S(e){let{className:t,...a}=e;return(0,r.jsx)("div",{"data-slot":"sidebar-group-content","data-sidebar":"group-content",className:(0,l.cn)("w-full text-sm",t),...a})}function E(e){let{className:t,...a}=e;return(0,r.jsx)("ul",{"data-slot":"sidebar-menu","data-sidebar":"menu",className:(0,l.cn)("flex w-full min-w-0 flex-col gap-1",t),...a})}function L(e){let{className:t,...a}=e;return(0,r.jsx)("li",{"data-slot":"sidebar-menu-item","data-sidebar":"menu-item",className:(0,l.cn)("group/menu-item relative",t),...a})}let D=(0,s.F)("peer/menu-button flex w-full items-center gap-2 overflow-hidden rounded-md p-2 text-left text-sm outline-hidden ring-sidebar-ring transition-[width,height,padding] hover:bg-sidebar-accent hover:text-sidebar-accent-foreground focus-visible:ring-2 active:bg-sidebar-accent active:text-sidebar-accent-foreground disabled:pointer-events-none disabled:opacity-50 group-has-data-[sidebar=menu-action]/menu-item:pr-8 aria-disabled:pointer-events-none aria-disabled:opacity-50 data-[active=true]:bg-sidebar-accent data-[active=true]:font-medium data-[active=true]:text-sidebar-accent-foreground data-[state=open]:hover:bg-sidebar-accent data-[state=open]:hover:text-sidebar-accent-foreground group-data-[collapsible=icon]:size-8! group-data-[collapsible=icon]:p-2! [&>span:last-child]:truncate [&>svg]:size-4 [&>svg]:shrink-0",{variants:{variant:{default:"hover:bg-sidebar-accent hover:text-sidebar-accent-foreground",outline:"bg-background shadow-[0_0_0_1px_hsl(var(--sidebar-border))] hover:bg-sidebar-accent hover:text-sidebar-accent-foreground hover:shadow-[0_0_0_1px_hsl(var(--sidebar-accent))]"},size:{default:"h-8 text-sm",sm:"h-7 text-xs",lg:"h-12 text-sm group-data-[collapsible=icon]:p-0!"}},defaultVariants:{variant:"default",size:"default"}});function F(e){let{asChild:t=!1,isActive:a=!1,variant:i="default",size:s="default",tooltip:o,className:d,...c}=e,u=t?n.DX:"button",{isMobile:b,state:f}=m(),v=(0,r.jsx)(u,{"data-slot":"sidebar-menu-button","data-sidebar":"menu-button","data-size":s,"data-active":a,className:(0,l.cn)(D({variant:i,size:s}),d),...c});return o?("string"==typeof o&&(o={children:o}),(0,r.jsxs)(g,{children:[(0,r.jsx)(p,{asChild:!0,children:v}),(0,r.jsx)(h,{side:"right",align:"center",hidden:"collapsed"!==f||b,...o})]})):v}}}]);