"use client";

import { <PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON> } from "@/components/ui/card";
import { But<PERSON> } from "@/components/ui/button";
import { Badge } from "@/components/ui/badge";
import { useState } from "react";
import { Co<PERSON>, Check, ChevronDown, ChevronUp } from "lucide-react";
import Image from "next/image";
import { PromptShowcaseProps } from "@/types/blocks/showcase";

export default function PromptShowcase({ section }: PromptShowcaseProps) {
  const [copiedIndex, setCopiedIndex] = useState<number | null>(null);
  const [expandedPrompts, setExpandedPrompts] = useState<Set<number>>(new Set());

  if (section.disabled) {
    return null;
  }

  const handleCopyPrompt = async (prompt: string, index: number) => {
    try {
      await navigator.clipboard.writeText(prompt);
      setCopiedIndex(index);
      setTimeout(() => setCopiedIndex(null), 2000);
    } catch (err) {
      console.error('Failed to copy prompt:', err);
    }
  };

  const togglePrompt = (index: number) => {
    const newExpanded = new Set(expandedPrompts);
    if (newExpanded.has(index)) {
      newExpanded.delete(index);
    } else {
      newExpanded.add(index);
    }
    setExpandedPrompts(newExpanded);
  };

  return (
    <section className="container py-16">
      <div className="mx-auto mb-12 text-center">
        <h2 className="mb-6 text-pretty text-3xl font-bold lg:text-4xl">
          {section.title}
        </h2>
        <p className="mb-4 max-w-xl text-muted-foreground lg:max-w-none lg:text-lg">
          {section.description}
        </p>
      </div>

      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
        {section.items?.map((item, index) => (
          <Card
            key={index}
            className="overflow-hidden transition-all hover:shadow-lg dark:hover:shadow-primary/10 flex flex-col h-full"
          >
            {/* 主要图片区域 */}
            {item.image && (
              <div className="relative aspect-[4/3] w-full overflow-hidden">
                <Image
                  src={item.image.src}
                  alt={item.image.alt || item.title}
                  fill
                  className="object-cover transition-transform duration-300 hover:scale-105"
                />
                {item.category && (
                  <div className="absolute top-3 right-3">
                    <Badge variant="outline" className="bg-background/80 backdrop-blur-sm">
                      {item.category}
                    </Badge>
                  </div>
                )}
              </div>
            )}

            <CardHeader className="pb-3">
              <CardTitle className="text-lg font-semibold line-clamp-2">
                {item.title}
              </CardTitle>
              {item.description && (
                <p className="text-sm text-muted-foreground line-clamp-2 mt-1">
                  {item.description}
                </p>
              )}
            </CardHeader>

            <CardContent className="flex-1 flex flex-col space-y-3">
              {/* 标签区域 */}
              {item.tags && item.tags.length > 0 && (
                <div className="flex flex-wrap gap-1">
                  {item.tags.slice(0, 3).map((tag, tagIndex) => (
                    <Badge
                      key={tagIndex}
                      variant="secondary"
                      className="text-xs px-2 py-0.5"
                    >
                      {tag}
                    </Badge>
                  ))}
                  {item.tags.length > 3 && (
                    <Badge variant="secondary" className="text-xs px-2 py-0.5">
                      +{item.tags.length - 3}
                    </Badge>
                  )}
                </div>
              )}

              {/* Prompt 展开/收起区域 */}
              <div className="flex-1">
                {expandedPrompts.has(index) && (
                  <div className="mb-3">
                    <div className="bg-muted/50 rounded-lg p-3 border border-border/50">
                      <p className="text-xs font-mono leading-relaxed text-foreground/90 whitespace-pre-wrap break-words">
                        {item.prompt}
                      </p>
                    </div>
                  </div>
                )}
              </div>

              {/* 按钮区域 */}
              <div className="space-y-2">
                <Button
                  onClick={() => togglePrompt(index)}
                  variant="outline"
                  size="sm"
                  className="w-full transition-all duration-200"
                >
                  {expandedPrompts.has(index) ? (
                    <>
                      <ChevronUp className="w-4 h-4 mr-2" />
                      隐藏 Prompt
                    </>
                  ) : (
                    <>
                      <ChevronDown className="w-4 h-4 mr-2" />
                      显示 Prompt
                    </>
                  )}
                </Button>

                {expandedPrompts.has(index) && (
                  <Button
                    onClick={() => handleCopyPrompt(item.prompt, index)}
                    variant="default"
                    size="sm"
                    className="w-full transition-all duration-200"
                  >
                    {copiedIndex === index ? (
                      <>
                        <Check className="w-4 h-4 mr-2" />
                        已复制
                      </>
                    ) : (
                      <>
                        <Copy className="w-4 h-4 mr-2" />
                        复制 Prompt
                      </>
                    )}
                  </Button>
                )}
              </div>
            </CardContent>
          </Card>
        ))}
      </div>
    </section>
  );
}
