"use client";

import { PromptShowcase, ComparisonShowcase } from "./index";
import { PromptShowcaseProps, ComparisonShowcaseProps } from "@/types/blocks/showcase";

export default function ShowcaseDemoPage() {
  // Prompt Showcase 示例数据 - 基于现有图片资源
  const promptShowcaseData: PromptShowcaseProps = {
    section: {
      title: "FLUX & KREA AI 创作 Prompt 精选",
      description: "精心调试的高质量 Prompt，助您在 FLUX 和 KREA 平台创作出令人惊艳的 AI 作品",
      items: [
        {
          title: "超现实主义人像",
          description: "创造具有梦幻色彩和超现实元素的人物肖像",
          prompt: "Surreal portrait of a person with flowing liquid hair that transforms into cosmic nebula, ethereal lighting, dreamlike atmosphere, hyperrealistic details, vibrant colors blending into abstract patterns, professional photography style, 8K ultra-detailed",
          image: {
            src: "/imgs/showcases/flux-krea-showcase-2-1.webp",
            alt: "超现实主义人像作品"
          },
          category: "人像创作",
          tags: ["超现实", "梦幻", "宇宙", "液体"]
        },
        {
          title: "科幻场景设计",
          prompt: "Futuristic sci-fi environment with holographic interfaces, neon-lit corridors, advanced technology panels, atmospheric fog, dramatic lighting with blue and purple tones, cyberpunk aesthetic, highly detailed architecture, cinematic composition",
          image: {
            src: "/imgs/showcases/flux-krea-showcase-2-2.webp",
            alt: "科幻场景设计作品"
          }
        },
        {
          title: "抽象艺术创作",
          description: "充满创意的抽象艺术风格图像",
          prompt: "Abstract digital art with flowing geometric shapes, gradient colors transitioning from warm to cool tones, dynamic composition with depth and movement, modern artistic style, high contrast, professional digital artwork, 4K resolution",
          image: {
            src: "/imgs/showcases/flux-krea-showcase-2-3.webp",
            alt: "抽象艺术创作作品"
          },
          category: "抽象艺术",
          tags: ["抽象", "几何", "渐变", "动态"]
        },
        {
          title: "自然风光摄影",
          description: "壮观的自然景观和风光摄影效果",
          prompt: "Breathtaking landscape photography of mountain peaks during golden hour, dramatic clouds, perfect lighting, ultra-wide angle shot, professional nature photography, vivid colors, high dynamic range, crystal clear details, award-winning composition",
          image: {
            src: "/imgs/showcases/flux-krea-showcase-1-1.webp",
            alt: "自然风光摄影作品"
          },
          category: "风光摄影",
          tags: ["山峰", "黄金时刻", "自然", "广角"]
        },
        {
          title: "时尚人物摄影",
          description: "高端时尚杂志风格的人物摄影",
          prompt: "High-fashion portrait photography, model wearing avant-garde clothing, studio lighting setup, clean background, professional makeup and styling, editorial magazine quality, sharp focus, luxury aesthetic, commercial photography style",
          image: {
            src: "/imgs/showcases/flux-krea-showcase-1-2.webp",
            alt: "时尚人物摄影作品"
          },
          category: "时尚摄影",
          tags: ["时尚", "杂志", "工作室", "高端"]
        },
        {
          title: "建筑空间设计",
          description: "现代建筑和室内空间的设计概念",
          prompt: "Modern architectural interior design, minimalist aesthetic, natural lighting through large windows, clean lines and geometric forms, neutral color palette, high-end materials, professional architectural photography, ultra-detailed",
          image: {
            src: "/imgs/showcases/flux-krea-showcase-1-3.webp",
            alt: "建筑空间设计作品"
          },
          category: "建筑设计",
          tags: ["现代", "极简", "室内", "几何"]
        }
      ]
    }
  };

  // Comparison Showcase 示例数据 - 基于现有图片资源
  const comparisonShowcaseData: ComparisonShowcaseProps = {
    section: {
      title: "FLUX & KREA AI 图像处理前后对比",
      description: "展示 FLUX 和 KREA AI 强大的图像处理能力，从普通照片到专业级作品的华丽转变",
      items: [
        {
          title: "艺术风格转换效果",
          description: "将普通图像转换为具有艺术感的创作作品",
          prompt: "Transform this image with artistic enhancement: apply cinematic lighting, enhance colors and contrast, add artistic depth and atmosphere, maintain natural proportions while creating a more visually striking and professional appearance. Style: photorealistic with artistic flair, high detail, dramatic lighting.",
          beforeImage: {
            src: "/imgs/showcases/flux-krea-showcase-3-1-1.jpg",
            alt: "AI处理前的原始图像"
          },
          afterImage: {
            src: "/imgs/showcases/flux-krea-showcase-3-1-2.jpg",
            alt: "AI艺术风格转换后的图像"
          },
          category: "艺术转换",
          tags: ["风格转换", "艺术增强", "色彩优化", "专业级"]
        }
      ]
    }
  };

  return (
    <div className="min-h-screen bg-background">
      {/* 页面标题 */}
      <div className="container py-16">
        <div className="text-center mb-16">
          <h1 className="text-4xl font-bold mb-4 bg-gradient-to-r from-primary to-accent bg-clip-text text-transparent">
            Showcase 组件演示
          </h1>
          <p className="text-lg text-muted-foreground max-w-2xl mx-auto">
            展示 PromptShowcase 和 ComparisonShowcase 两个组件的实际效果
          </p>
        </div>
      </div>

      {/* Prompt Showcase 演示 */}
      <PromptShowcase {...promptShowcaseData} />

      {/* 分隔线 */}
      <div className="container py-8">
        <div className="border-t border-border/50"></div>
      </div>

      {/* Comparison Showcase 演示 */}
      <ComparisonShowcase {...comparisonShowcaseData} />

      {/* 底部说明 */}
      <div className="container py-16">
        <div className="text-center">
          <div className="bg-muted/50 rounded-lg p-8 max-w-4xl mx-auto">
            <h3 className="text-xl font-semibold mb-4">组件特性说明</h3>
            <div className="grid md:grid-cols-2 gap-6 text-sm text-muted-foreground">
              <div>
                <h4 className="font-medium text-foreground mb-2">PromptShowcase</h4>
                <ul className="space-y-1 text-left">
                  <li>• 支持 Prompt 一键复制</li>
                  <li>• 响应式网格布局</li>
                  <li>• 分类标签和多标签支持</li>
                  <li>• 复制状态反馈</li>
                </ul>
              </div>
              <div>
                <h4 className="font-medium text-foreground mb-2">ComparisonShowcase</h4>
                <ul className="space-y-1 text-left">
                  <li>• 前后图片对比展示</li>
                  <li>• 移动端垂直布局适配</li>
                  <li>• 图片悬停缩放效果</li>
                  <li>• Prompt 展示和复制</li>
                </ul>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
}
