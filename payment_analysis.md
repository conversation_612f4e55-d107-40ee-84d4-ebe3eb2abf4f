[text](../my-exp3/open-next.config.ts)### 支付系统分析报告

#### 1. 核心技术栈

*   **支付网关**: Stripe
*   **后端框架**: Next.js (API Routes)
*   **数据库**: Supabase (PostgreSQL)
*   **主要语言**: TypeScript

#### 2. 支付流程

整个支付流程从创建订单开始，到接收 Stripe 的支付成功通知结束，核心流程如下：

1.  **创建订单 (`app/api/checkout/route.ts`)**
    *   用户在前端选择一个定价方案（Pricing Plan）后，前端会向 `/api/checkout` 发起一个 POST 请求。
    *   请求参数包括：`credits`, `currency`, `amount`, `interval` (支付周期，如 `month`, `year`, `one-time`), `product_id` 等。
    *   **安全验证**: 后端会根据 `product_id` 从 `getPricingPage("en")` 获取的定价信息中查找对应的产品，并严格验证前端传来的 `amount`, `interval`, `currency` 等参数是否与后端配置一致，防止篡改价格。
    *   **用户验证**: 通过 `getUserUuid()` 和 `getUserEmail()` 获取当前登录用户信息，未登录用户无法创建订单。
    *   **生成订单号**: 使用 `getSnowId()` 生成一个唯一的雪花ID作为订单号 `order_no`。
    *   **创建本地订单**: 在数据库中插入一条新的订单记录，初始状态为 `created`。
        ```typescript
        // app/api/checkout/route.ts

        const order: Order = {
          order_no: order_no,
          created_at: created_at,
          user_uuid: user_uuid,
          user_email: user_email,
          amount: amount,
          interval: interval,
          expired_at: expired_at,
          status: "created", // 初始状态
          credits: credits,
          currency: currency,
          product_id: product_id,
          product_name: product_name,
          valid_months: valid_months,
        };
        await insertOrder(order);
        ```
    *   **创建 Stripe Checkout Session**:
        *   初始化 Stripe SDK: `const stripe = new Stripe(process.env.STRIPE_PRIVATE_KEY || "");`
        *   构造 `Stripe.Checkout.SessionCreateParams` 对象，其中包含了订单信息、成功和取消的 URL、以及元数据 `metadata` (非常重要，包含了 `order_no`, `user_uuid` 等关键信息)。
        *   调用 `stripe.checkout.sessions.create(options)` 创建一个支付会话。
        *   **更新订单**: 将返回的 `stripe_session_id` 更新到本地订单记录中。
    *   **返回给前端**: 将 `session_id` 和 `order_no` 返回给前端，前端使用 `session_id` 跳转到 Stripe 的支付页面。

2.  **支付状态通知 (`app/api/stripe-notify/route.ts`)**
    *   这是一个 Webhook 接口，用于接收 Stripe 发送的支付状态更新事件。
    *   **安全验证**: 通过 `stripe.webhooks.constructEventAsync` 验证请求签名 `stripe-signature`，确保是 Stripe官方发送的合法请求。
    *   **事件处理**:
        *   只处理 `checkout.session.completed` 事件，即支付成功事件。
        *   当收到支付成功事件时，调用 `handleOrderSession(session)` 函数处理后续逻辑。
        ```typescript
        // app/api/stripe-notify/route.ts

        switch (event.type) {
          case "checkout.session.completed": {
            const session = event.data.object;

            await handleOrderSession(session);
            break;
          }

          default:
            console.log("not handle event: ", event.type);
        }
        ```

3.  **支付完成处理 (`services/order.ts`)**
    *   `handleOrderSession` 函数是支付完成后的核心处理逻辑。
    *   **校验**:
        *   检查 `session.payment_status` 是否为 `paid`。
        *   从 `session.metadata` 中获取 `order_no`，并查询数据库中是否存在该订单，且订单状态为 `created`。
    *   **更新订单状态**:
        *   如果订单有效，则调用 `updateOrderStatus` 将订单状态更新为 `paid`，并记录支付时间 `paid_at` 和支付者邮箱 `paid_email`。
        ```typescript
        // services/order.ts

        const order = await findOrderByOrderNo(order_no);
        if (!order || order.status !== "created") {
          throw new Error("invalid order");
        }

        const paid_at = getIsoTimestr();
        await updateOrderStatus(order_no, "paid", paid_at, paid_email, paid_detail);
        ```
    *   **发放权益**:
        *   如果订单包含 `credits`，则调用 `updateCreditForOrder(order)` 为用户增加相应的积分。
        *   调用 `updateAffiliateForOrder(order)` 处理分销/联盟相关的逻辑。

#### 3. 支付异常状态管理

系统通过以下几种方式管理异常状态：

1.  **订单创建失败**:
    *   在 `app/api/checkout/route.ts` 中，如果参数校验失败、用户未登录或定价信息不匹配，会直接返回错误信息 `respErr("...")`，前端会收到错误提示，支付流程中断。
    *   整个创建过程被一个 `try...catch` 块包围，任何未预料的异常（如 Stripe API 调用失败）都会被捕获，并返回 `500` 错误。

2.  **用户取消支付**:
    *   在创建 Stripe Checkout Session 时，设置了 `cancel_url`。如果用户在 Stripe 支付页面取消支付，会被重定向到这个 URL，系统无需做特殊处理，订单状态在数据库中将保持为 `created`。这些未完成的订单可以用于后续的数据分析或用户召回。

3.  **Webhook 通知失败**:
    *   `app/api/stripe-notify/route.ts` 中的 `try...catch` 块会捕获处理过程中的所有异常（如数据库更新失败）。
    *   如果处理失败，会返回 `500` 错误给 Stripe。Stripe 会根据其重试机制，在未来的某个时间点重新发送 Webhook 通知，从而保证了支付状态的最终一致性。

4.  **重复处理 Webhook**:
    *   在 `handleOrderSession` 中，会检查订单状态是否为 `created`。如果订单状态已经是 `paid` 或其他状态，会抛出 `invalid order` 异常，防止了因为 Stripe 重复发送通知而导致的重复发放权益（如重复增加积分）的问题。

#### 4. 总结

该支付系统是一个设计良好、流程清晰、异常处理机制健全的系统。它通过在创建订单时进行严格的参数校验、使用 Stripe Webhook 保证支付状态的可靠更新、以及在核心处理逻辑中加入幂等性判断（检查订单状态），有效地保证了支付流程的稳定和安全。
