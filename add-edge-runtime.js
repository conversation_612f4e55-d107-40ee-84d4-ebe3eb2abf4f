#!/usr/bin/env node

const fs = require('fs');
const path = require('path');

// API 路由文件列表
const apiRoutes = [
  'app/api/demo/gen-stream-text/route.ts',
  'app/api/demo/gen-image/route.ts',
  'app/api/demo/gen-text/route.ts',
  'app/api/download-proxy/route.ts',
  'app/api/ping/route.ts',
  'app/api/test/replicate/route.ts',
  'app/api/test/replicate-unified/route.ts',
  'app/api/auth/[...nextauth]/route.ts',
  'app/api/get-user-info/route.ts',
  'app/api/admin/models/translations/route.ts',
  'app/api/volcengine/voice-clone/status/route.ts',
  'app/api/volcengine/voice-clone/upload/route.ts',
  'app/api/volcengine/asr/submit/route.ts',
  'app/api/volcengine/asr/query/route.ts',
  'app/api/volcengine/tts/route.ts',
  'app/api/volcengine/callback/route.ts',
  'app/api/volcengine/tts-async/submit/route.ts',
  'app/api/volcengine/tts-async/query/route.ts',
  'app/api/stripe-notify/route.ts',
  'app/api/checkout/route.ts',
  'app/api/update-invite/route.ts',
  'app/api/update-invite-code/route.ts',
  'app/api/ai/estimate-cost/route.ts',
  'app/api/ai/result/route.ts',
  'app/api/ai/usage/route.ts',
  'app/api/ai/models/route.ts',
  'app/api/ai/upload-image/route.ts',
  'app/api/ai/generate/route.ts',
  'app/api/ai/transfer-file/route.ts',
  'app/api/add-feedback/route.ts',
  'app/api/get-user-credits/route.ts'
];

function addEdgeRuntime(filePath) {
  try {
    const content = fs.readFileSync(filePath, 'utf8');
    
    // 检查是否已经有 runtime 配置
    if (content.includes('export const runtime')) {
      console.log(`✓ ${filePath} already has runtime config`);
      return;
    }
    
    // 在文件末尾添加 runtime 配置
    const newContent = content + '\n\nexport const runtime = "edge";\n';
    
    fs.writeFileSync(filePath, newContent, 'utf8');
    console.log(`✓ Added edge runtime to ${filePath}`);
  } catch (error) {
    console.error(`✗ Error processing ${filePath}:`, error.message);
  }
}

// 处理所有 API 路由文件
console.log('Adding edge runtime to API routes...\n');

apiRoutes.forEach(route => {
  if (fs.existsSync(route)) {
    addEdgeRuntime(route);
  } else {
    console.log(`⚠ File not found: ${route}`);
  }
});

console.log('\nDone!');
