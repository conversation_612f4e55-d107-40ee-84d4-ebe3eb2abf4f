import { Metadata } from "next";
import { WorkspaceLayout } from "@/components/ai-dashboard/workspace-layout";
import Branding from "@/components/blocks/branding";
import CTA from "@/components/blocks/cta";
import FAQ from "@/components/blocks/faq";
import Feature from "@/components/blocks/feature";
import Feature1 from "@/components/blocks/feature1";
import Feature2 from "@/components/blocks/feature2";
import Feature3 from "@/components/blocks/feature3";
import Hero from "@/components/blocks/hero";
import Pricing from "@/components/blocks/pricing";
import Showcase from "@/components/blocks/showcase";
import Stats from "@/components/blocks/stats";
import Testimonial from "@/components/blocks/testimonial";
import { getLandingPage } from "@/services/page";
import Schema from "@/components/seo";
import type { SchemaProps, BreadcrumbItem } from "@/types/schema";

import { getMessages, getTranslations } from "next-intl/server";


export async function generateMetadata({
  params,
}: {
  params: Promise<{ locale: string }>;
}) {
  const { locale } = await params;
  const page = await getLandingPage(locale);
  let canonicalUrl = `${process.env.NEXT_PUBLIC_WEB_URL}/`;

  if (locale !== "en") {
    canonicalUrl = `${process.env.NEXT_PUBLIC_WEB_URL}/${locale}/`;
  }
  return {
    title: {
      template: `%s`,
      default: page.metadata.title || "",
    },
    description: page.metadata.description || "",
    keywords: page.metadata.keywords || "",
        alternates: {
      canonical: canonicalUrl,
    },
  };



}

export default async function LandingPage({
  params,
}: {
  params: Promise<{ locale: string }>;
}) {
  const { locale } = await params;
  const page = await getLandingPage(locale);

  // 构建 Schema 数据
  const siteUrl = process.env.NEXT_PUBLIC_WEB_URL || '';
  const currentUrl = locale === 'en'
    ? `${siteUrl}`
    : `${siteUrl}/${locale}`;

  // 构建面包屑导航
  // 首页不需要面包屑导航
  const breadcrumb: BreadcrumbItem[] = [];

  // 构建 Schema 属性
  const schemaProps: SchemaProps = {
    title: page.hero?.title || 'FLUX2 - Advanced Image to Image AI Platform',
    description: page.hero?.description?.replace(/<br\s*\/?>/gi, ' ') || page.feature?.description || 'Revolutionary image to image AI technology that transforms, enhances, and converts images with precision. Professional AI image transformation tools.',
    image: `${siteUrl}/imgs/features/ai_dashboard_interface.png`,
    url: currentUrl,
    schemaType: 'SoftwareApplication',
    applicationCategory: 'WebApplication',
    operatingSystem: 'Web Browser, iOS, Android',
    datePublished: '2024-01-01T00:00:00Z',
    dateModified: new Date().toISOString(),
    breadcrumb
  };

  return (
    <>
      <Schema {...schemaProps} />
      {page.hero && <Hero hero={page.hero} />}
       <WorkspaceLayout />
      {page.branding && <Branding section={page.branding} />}
      {page.introduce && <Feature1 section={page.introduce} />}
      {page.benefit && <Feature2 section={page.benefit} />}
      {page.usage && <Feature3 section={page.usage} />}
      {page.feature && <Feature section={page.feature} />}
      {page.showcase && <Showcase section={page.showcase} />}
      {page.stats && <Stats section={page.stats} />}
      {page.pricing && <Pricing pricing={page.pricing} />}
      {/* {page.testimonial && <Testimonial section={page.testimonial} />} */}
      {page.faq && <FAQ section={page.faq} />}
      {page.cta && <CTA section={page.cta} />}
    </>
  );
}
