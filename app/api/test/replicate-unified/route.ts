import { respData, respErr } from "@/lib/resp";
import { AIService } from "@/services/grsai-provider";
import { AIRequest } from "@/types/ai-model";



export async function POST(req: Request) {
  try {
    const { prompt, options } = await req.json();
    
    if (!prompt) {
      return respErr("Prompt is required");
    }

    console.log(`[Test Replicate Unified] Testing with prompt: ${prompt}`);

    // 构建AI请求
    const aiRequest: AIRequest = {
      model: 'black-forest-labs/flux-krea-dev',
      type: 'image',
      prompt,
      options: {
        output_quality: 90,
        output_format: 'webp',
        ...options
      }
    };

    // 创建AI服务实例
    const aiService = new AIService();

    // 使用虚拟用户UUID进行测试（绕过用户认证）
    const testUserUuid = 'test-user-uuid';
    
    console.log(`[Test Replicate Unified] Calling AI service with request:`, aiRequest);

    // 处理AI请求
    const response = await aiService.processRequest(testUserUuid, aiRequest);

    console.log(`[Test Replicate Unified] AI service response:`, response);

    return respData({
      success: true,
      response
    });

  } catch (error) {
    console.error(`[Test Replicate Unified] Error:`, error);
    return respErr(error instanceof Error ? error.message : 'Unknown error');
  }
}
